package com.caict.bsm.project.api.open.service;
//import com.alibaba.fastjson.JSONObject;
//import org.springframework.stereotype.Repository;
//
//import java.util.List;
//import java.util.Map;
//
///**
// * Created by dengsy on 2019-7-5.
// * 自定义restTemplate接口
// * T：类
// */
//@Repository
//public interface MyRestRepository<T> {
//
//    /**
//     * 无参数的get请求
//     * url:请求地址
//     * 返回集合
//     * */
//    List<T> getForEntityNpList(String url);
//
//    /**
//     * 无参数的get请求
//     * url:请求地址
//     * 返回string
//     * */
//    String getForStringNpList(String url);
//
//    /**
//     * 无参数的get请求
//     * url：请求地址
//     * myClass：T对应的class
//     * 返回对象
//     * */
//    T getForEntityNpT(String url, Class<T> clazz);
//
//    /**
//     * 有参数的get请求
//     * 参数为url路径占位符："http://localhost:8080/getForEntityListP/{?}"；
//     * map:参数集合，键值对应参数?；
//     * myClass：T对应的class
//     * 返回集合
//     * */
//    List<T> getForEntityPList(String url, Map<String, String> map);
//
//    /**
//     * 有参数的get请求
//     * 参数为url路径占位符：http://localhost:8080/getForEntityListP/{?}"；
//     * map:参数集合，键值对应参数？；
//     * 返回对象
//     * */
//    T getForEntityPT(String url, Map<String, String> map, Class<T> clazz);
//
//    /**
//     * Url无参数的post请求
//     * url:请求地址
//     * */
//    String saveNp(String url, T t);
//
//    /**
//     * Url有参数的Post请求
//     * 参数为url路径占位符：http://localhost:8080/getForEntityListP/{?}"
//     * map:参数集合，键值对应参数?；
//     * */
//    String saveP(String url, Map<String, String> map, T t);
//
//    /**
//     * 有参数的get请求
//     * 参数为url路径占位符：http://localhost:8080/getForStringPT/{?}"；
//     * ap:参数集合，键值对应参数？；
//     * 返回字符串
//     * */
//    String getForStringPT(String url, String param);
//
//    /**
//     * 有参数的post请求
//     * 参数为url路径占位符：http://localhost:8080/postForStringPT/{?}"；
//     * ap:参数集合，键值对应参数？；
//     * 返回字符串
//     * */
//    String postForStringPT(String url, String param);
//
//    /**
//     * 有参数的post请求
//     * 参数为json方式
//     * */
//    JSONObject postForJsonPT(String url, JSONObject jsonObject);
//}
