package com.caict.bsm.project.api.open.test;

import com.caict.bsm.project.api.open.test.addorup.AddOrUpdateWS;
import com.caict.bsm.project.api.open.test.calculation.SelectStationInfoWS;
import com.caict.bsm.project.api.open.test.stationdata.GetStationInfoWS;
import org.apache.cxf.Bus;
import org.apache.cxf.bus.spring.SpringBus;
import org.apache.cxf.jaxws.EndpointImpl;
import org.apache.cxf.transport.servlet.CXFServlet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.servlet.ServletRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.xml.ws.Endpoint;


/**
 * Created by yanchengpeng on 2019/11/19.
 */
@Configuration
public class CxfConfig {

    @Autowired
    private SelectStationInfoWS selectStationInfoWS;
    @Autowired
    private GetStationInfoWS getStationInfoWS;
    @Autowired
    private AddOrUpdateWS addOrUpdateWS;
    @Autowired
    private XmlOutInterceptor xmlOutInterceptor;

    /** JAX-WS **/
    /**
     * 注入servlet  bean name不能dispatcherServlet 否则会覆盖dispatcherServlet
     * @return
     */
    @Bean(name = "cxfServlet")
    public ServletRegistrationBean cxfServlet() {
        return new ServletRegistrationBean(new CXFServlet(),"/*");
    }


    @Bean(name = Bus.DEFAULT_BUS_ID)
    public SpringBus springBus() {
        return new SpringBus();
    }

    /**
     * 注册WebServiceDemoService接口到webservice服务
     * @return
     */
    @Bean
    public Endpoint sweptPayEndpoint() {
        EndpointImpl endpoint = new EndpointImpl(springBus(), selectStationInfoWS);
        endpoint.publish("/printLicenseBE");
        //endpoint.getInInterceptors().add(xmlInterceptor);
        endpoint.getOutInterceptors().add(xmlOutInterceptor);
        return endpoint;
    }

    @Bean
    public Endpoint twoPayEndpoint() {
        EndpointImpl endpoint = new EndpointImpl(springBus(), getStationInfoWS);
        endpoint.publish("/stationDelBE");
        //endpoint.getInInterceptors().add(xmlInterceptor);
        endpoint.getOutInterceptors().add(xmlOutInterceptor);
        return endpoint;
    }

    @Bean
    public Endpoint threePayEndpoint() {
        EndpointImpl endpoint = new EndpointImpl(springBus(), addOrUpdateWS);
        endpoint.publish("/stationAddorUpBE");
        //endpoint.getInInterceptors().add(xmlInterceptor);
        endpoint.getOutInterceptors().add(xmlOutInterceptor);
        return endpoint;
    }
}
