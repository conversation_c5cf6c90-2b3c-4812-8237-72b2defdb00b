package com.caict.bsm.project.api.open.test;

import org.apache.cxf.binding.soap.SoapMessage;
import org.apache.cxf.helpers.IOUtils;
import org.apache.cxf.interceptor.Fault;
import org.apache.cxf.phase.AbstractPhaseInterceptor;
import org.apache.cxf.phase.Phase;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Collection;

/**
 * Created by yanchengpeng on 2019/11/26.
 */
@Component
public class XmlInterceptor extends AbstractPhaseInterceptor<SoapMessage> {

    public XmlInterceptor(){
        super(Phase.RECEIVE);
    }

    @Override
    public void addAfter(Collection<String> i) {
        System.out.println("aaaaaa");
        super.addAfter(i);
    }

    @Override
    public void handleMessage(SoapMessage message) throws Fault {
        try {
            /*//需要放置在根节点的命名空间
            Map<String, String> envMap = new HashMap<>();
            envMap.put("soap-env", "http://schemas.xmlsoap.org/soap/envelope/");
            envMap.put("soap-enc", "http://schemas.xmlsoap.org/soap/encoding/");
            envMap.put("cwmp", "urn:dslforum-org:cwmp-1-0");

            //在命名空间下的元素都以自定义前缀生成
            Map<String, String> namespaceMap = new HashMap<>();
            namespaceMap.put("urn:dslforum-org:cwmp-1-0", "cwmp");
            namespaceMap.put("http://schemas.xmlsoap.org/soap/envelope/", "soap-env");
            namespaceMap.put("http://schemas.xmlsoap.org/soap/encoding/", "soap-enc");
            JAXBDataBinding dataBinding = (JAXBDataBinding) message.getExchange().getEndpoint().getService()
                    .getDataBinding();
            dataBinding.setNamespaceMap(namespaceMap);

            message.put("soap.env.ns.map", envMap);
            message.put("disable.outputstream.optimization", true);*/
            InputStream is = message.getContent(InputStream.class);
            if (is != null) {
                try {
                    String str = IOUtils.toString(is);
                    // 原请求报文
                    System.out.println("====> request xml=\r\n" + str);

                    // monitorHeader替换
                    str = str.replace("srrc:TransId","TransId");
                    str = str.replace("srrc:BizKey","BizKey");
                    str = str.replace("srrc:PSCode","PSCode");
                    str = str.replace("srrc:BSCode","BSCode");
                    str = str.replace("srrc:appCode","appCode");
                    str = str.replace("srrc:appName","appName");
                    str = str.replace("srrc:platFormCode","platFormCode");
                    str = str.replace("srrc:platFormName","platFormName");
                    //requestbody替换
                    str = str.replace("srrc:page","page");
                    str = str.replace("srrc:rows","rows");
                    str = str.replace("srrc:assetNumber","assetNumber");
                    // 替换后的报文
                    System.out.println("====>request replace xml=\r\n" + str);

                    InputStream ism = new ByteArrayInputStream(str.getBytes());
                    message.setContent(InputStream.class, ism);

                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


}
