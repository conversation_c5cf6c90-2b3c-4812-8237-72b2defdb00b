package com.caict.bsm.project.api.open.test.addorup;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebService;
import javax.jws.soap.SOAPBinding;
import javax.xml.ws.Holder;

@WebService(name = "AddOrUpdateWS", targetNamespace = "http://www.srrc.org.cn")
@SOAPBinding(parameterStyle = SOAPBinding.ParameterStyle.BARE)
public interface AddOrUpdateWS {

    @WebMethod
    public void getStationInfo(
            @WebParam(name = "MonitorHeader", targetNamespace = "http://www.srrc.org.cn", header = true, partName = "MonitorHeader") MonitorHeader monitorHeader,
            @WebParam(name = "requestBody", targetNamespace = "http://www.srrc.org.cn", partName = "requestBody") RequestBody requestBody,
            @WebParam(name = "ProviderResponse", targetNamespace = "http://www.srrc.org.cn", header = true, mode = WebParam.Mode.OUT, partName = "ProviderResponse") Holder<ProviderResponse> providerResponse,
            @WebParam(name = "responseBody", targetNamespace = "http://www.srrc.org.cn", mode = WebParam.Mode.OUT, partName = "responseBody") Holder<ReturnStationXML> responseBody);
}
