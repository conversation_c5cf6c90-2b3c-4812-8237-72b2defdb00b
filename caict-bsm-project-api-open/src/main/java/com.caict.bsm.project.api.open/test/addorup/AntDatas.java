package com.caict.bsm.project.api.open.test.addorup;

import com.caict.bsm.project.api.open.test.NameSpaceUrlConst;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(namespace = NameSpaceUrlConst.stationNamespaceURI,name = "antDatas",
        propOrder = { "serialNum", "antAngle", "antType","antModel","antManu",
                "antHight","antRgain","antEgain","feedLose","antPole","atCcode","atCsgn","atRang",
        "atEang"})
public class AntDatas {

    @XmlElement(name = "SERIAL_NUM",namespace = NameSpaceUrlConst.stationNamespaceURI)
    protected String serialNum;
    @XmlElement(name = "ANT_ANGLE",namespace = NameSpaceUrlConst.stationNamespaceURI)
    protected Double antAngle;
    @XmlElement(name = "ANT_TYPE",namespace = NameSpaceUrlConst.stationNamespaceURI)
    protected String antType;
    @XmlElement(name = "ANT_MODEL",namespace = NameSpaceUrlConst.stationNamespaceURI)
    protected String antModel;
    @XmlElement(name = "ANT_MANU",namespace = NameSpaceUrlConst.stationNamespaceURI)
    protected String antManu;
    @XmlElement(name = "ANT_HIGHT",namespace = NameSpaceUrlConst.stationNamespaceURI)
    protected Double antHight;
    @XmlElement(name = "ANT_RGAIN",namespace = NameSpaceUrlConst.stationNamespaceURI)
    protected Double antRgain;
    @XmlElement(name = "ANT_EGAIN",namespace = NameSpaceUrlConst.stationNamespaceURI)
    protected Double antEgain;
    @XmlElement(name = "FEED_LOSE",namespace = NameSpaceUrlConst.stationNamespaceURI)
    protected String feedLose;
    @XmlElement(name = "ANT_POLE",namespace = NameSpaceUrlConst.stationNamespaceURI)
    protected String antPole;
    @XmlElement(name = "AT_CCODE",namespace = NameSpaceUrlConst.stationNamespaceURI)
    protected String atCcode;
    @XmlElement(name = "AT_CSGN",namespace = NameSpaceUrlConst.stationNamespaceURI)
    protected String atCsgn;
    @XmlElement(name = "AT_RANG",namespace = NameSpaceUrlConst.stationNamespaceURI)
    protected Double atRang;
    @XmlElement(name = "AT_EANG",namespace = NameSpaceUrlConst.stationNamespaceURI)
    protected Double atEang;

    public AntDatas() {
    }

    public String getSerialNum() {
        return serialNum;
    }

    public void setSerialNum(String serialNum) {
        this.serialNum = serialNum;
    }

    public Double getAntAngle() {
        return antAngle;
    }

    public void setAntAngle(Double antAngle) {
        this.antAngle = antAngle;
    }

    public String getAntType() {
        return antType;
    }

    public void setAntType(String antType) {
        this.antType = antType;
    }

    public String getAntModel() {
        return antModel;
    }

    public void setAntModel(String antModel) {
        this.antModel = antModel;
    }

    public String getAntManu() {
        return antManu;
    }

    public void setAntManu(String antManu) {
        this.antManu = antManu;
    }

    public Double getAntHight() {
        return antHight;
    }

    public void setAntHight(Double antHight) {
        this.antHight = antHight;
    }

    public Double getAntRgain() {
        return antRgain;
    }

    public void setAntRgain(Double antRgain) {
        this.antRgain = antRgain;
    }

    public Double getAntEgain() {
        return antEgain;
    }

    public void setAntEgain(Double antEgain) {
        this.antEgain = antEgain;
    }

    public String getFeedLose() {
        return feedLose;
    }

    public void setFeedLose(String feedLose) {
        this.feedLose = feedLose;
    }

    public String getAntPole() {
        return antPole;
    }

    public void setAntPole(String antPole) {
        this.antPole = antPole;
    }

    public String getAtCcode() {
        return atCcode;
    }

    public void setAtCcode(String atCcode) {
        this.atCcode = atCcode;
    }

    public String getAtCsgn() {
        return atCsgn;
    }

    public void setAtCsgn(String atCsgn) {
        this.atCsgn = atCsgn;
    }

    public Double getAtRang() {
        return atRang;
    }

    public void setAtRang(Double atRang) {
        this.atRang = atRang;
    }

    public Double getAtEang() {
        return atEang;
    }

    public void setAtEang(Double atEang) {
        this.atEang = atEang;
    }
}
