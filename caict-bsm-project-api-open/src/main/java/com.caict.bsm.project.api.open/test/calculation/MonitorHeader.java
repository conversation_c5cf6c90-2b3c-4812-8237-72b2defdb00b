package com.caict.bsm.project.api.open.test.calculation;

import com.caict.bsm.project.api.open.test.NameSpaceUrlConst;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

/**
 * <p>
 * Java class for MonitorHeader complex type.
 *
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 *
 * <pre>
 * &lt;complexType name="MonitorHeader">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="TransId" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="BizKey" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="PSCode" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="BSCode" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="appCode" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="appName" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="platFormCode" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="platFormName" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(namespace = NameSpaceUrlConst.stationNamespaceURI,name = "MonitorHeader", propOrder = { "transId", "bizKey", "psCode",
		"bsCode", "appCode", "appName", "platFormCode", "platFormName","resCode1","resCode2" })
public class MonitorHeader {

	@XmlElement(name = "TransId",namespace = NameSpaceUrlConst.stationNamespaceURI)
	protected String transId;
	@XmlElement(name = "BizKey",namespace = NameSpaceUrlConst.stationNamespaceURI)
	protected String bizKey;
	@XmlElement(name = "PSCode",namespace = NameSpaceUrlConst.stationNamespaceURI)
	protected String psCode;
	@XmlElement(name = "BSCode",namespace = NameSpaceUrlConst.stationNamespaceURI)
	protected String bsCode;
	@XmlElement(name = "appCode",namespace = NameSpaceUrlConst.stationNamespaceURI)
	protected String appCode;
	@XmlElement(name = "appName",namespace = NameSpaceUrlConst.stationNamespaceURI)
	protected String appName;
	@XmlElement(name = "platFormCode",namespace = NameSpaceUrlConst.stationNamespaceURI)
	protected String platFormCode;
	@XmlElement(name = "platFormName",namespace = NameSpaceUrlConst.stationNamespaceURI)
	protected String platFormName;
	@XmlElement(name = "resCode1",namespace = NameSpaceUrlConst.stationNamespaceURI)
	protected String resCode1;
	@XmlElement(name = "resCode2",namespace = NameSpaceUrlConst.stationNamespaceURI)
	protected String resCode2;

	/**
	 * Gets the value of the transId property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public String getTransId() {
		return transId;
	}

	/**
	 * Sets the value of the transId property.
	 *
	 * @param value
	 *            allowed object is {@link String }
	 *
	 */
	public void setTransId(String value) {
		this.transId = value;
	}

	/**
	 * Gets the value of the bizKey property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public String getBizKey() {
		return bizKey;
	}

	/**
	 * Sets the value of the bizKey property.
	 *
	 * @param value
	 *            allowed object is {@link String }
	 *
	 */
	public void setBizKey(String value) {
		this.bizKey = value;
	}

	/**
	 * Gets the value of the psCode property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public String getPSCode() {
		return psCode;
	}

	/**
	 * Sets the value of the psCode property.
	 *
	 * @param value
	 *            allowed object is {@link String }
	 *
	 */
	public void setPSCode(String value) {
		this.psCode = value;
	}

	/**
	 * Gets the value of the bsCode property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public String getBSCode() {
		return bsCode;
	}

	/**
	 * Sets the value of the bsCode property.
	 *
	 * @param value
	 *            allowed object is {@link String }
	 *
	 */
	public void setBSCode(String value) {
		this.bsCode = value;
	}

	/**
	 * Gets the value of the appCode property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public String getAppCode() {
		return appCode;
	}

	/**
	 * Sets the value of the appCode property.
	 *
	 * @param value
	 *            allowed object is {@link String }
	 *
	 */
	public void setAppCode(String value) {
		this.appCode = value;
	}

	/**
	 * Gets the value of the appName property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public String getAppName() {
		return appName;
	}

	/**
	 * Sets the value of the appName property.
	 *
	 * @param value
	 *            allowed object is {@link String }
	 *
	 */
	public void setAppName(String value) {
		this.appName = value;
	}

	/**
	 * Gets the value of the platFormCode property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public String getPlatFormCode() {
		return platFormCode;
	}

	/**
	 * Sets the value of the platFormCode property.
	 *
	 * @param value
	 *            allowed object is {@link String }
	 *
	 */
	public void setPlatFormCode(String value) {
		this.platFormCode = value;
	}

	/**
	 * Gets the value of the platFormName property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public String getPlatFormName() {
		return platFormName;
	}

	/**
	 * Sets the value of the platFormName property.
	 *
	 * @param value
	 *            allowed object is {@link String }
	 *
	 */
	public void setPlatFormName(String value) {
		this.platFormName = value;
	}

	public String getPsCode() {
		return psCode;
	}

	public void setPsCode(String psCode) {
		this.psCode = psCode;
	}

	public String getBsCode() {
		return bsCode;
	}

	public void setBsCode(String bsCode) {
		this.bsCode = bsCode;
	}

	public String getResCode1() {
		return resCode1;
	}

	public void setResCode1(String resCode1) {
		this.resCode1 = resCode1;
	}

	public String getResCode2() {
		return resCode2;
	}

	public void setResCode2(String resCode2) {
		this.resCode2 = resCode2;
	}
}
