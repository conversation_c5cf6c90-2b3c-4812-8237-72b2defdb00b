package com.caict.bsm.project.api.open.test.calculation;//package com.caict.bsm.project.api.open.test.calculation;
//
//import com.caict.bsm.project.api.open.test.NameSpaceUrlConst;
//
//import javax.xml.bind.JAXBElement;
//import javax.xml.bind.annotation.XmlElementDecl;
//import javax.xml.bind.annotation.XmlRegistry;
//import javax.xml.namespace.QName;
//
///**
// * This object contains factory methods for each Java content interface and Java
// * element interface generated in the com.caict.bsm.project.api.open.test
// * package.
// * <p>
// * An ObjectFactory allows you to programatically construct new instances of the
// * Java representation for XML content. The Java representation of XML content
// * can consist of schema derived interfaces and classes representing the binding
// * of schema type definitions, element declarations and model groups. Factory
// * methods for each of these are provided in this class.
// *
// */
//@XmlRegistry
//public class ObjectFactory {
//
//	private final static QName _ProviderResponse_QNAME = new QName(
//			NameSpaceUrlConst.stationNamespaceURI, "ProviderResponse");
//	private final static QName _ResponseBody_QNAME = new QName(
//			NameSpaceUrlConst.stationNamespaceURI, "responseBody");
//	private final static QName _MonitorHeader_QNAME = new QName(
//			NameSpaceUrlConst.stationNamespaceURI, "MonitorHeader");
//	private final static QName _RequestBody_QNAME = new QName(
//			NameSpaceUrlConst.stationNamespaceURI, "requestBody");
//
//	/**
//	 * Create a new ObjectFactory that can be used to create new instances of
//	 * schema derived classes for package: com.caict.bsm.project.api.open.test
//	 *
//	 */
//	public ObjectFactory() {
//	}
//
//	/**
//	 * Create an instance of {@link RequestBody }
//	 *
//	 */
//	public RequestBody createRequestBody() {
//		return new RequestBody();
//	}
//
//	/**
//	 * Create an instance of {@link ReturnStationXML }
//	 *
//	 */
//	public ReturnStationXML createReturnOrgXML() {
//		return new ReturnStationXML();
//	}
//
//	/**
//	 * Create an instance of {@link ProviderResponse }
//	 *
//	 */
//	public ProviderResponse createProviderResponse() {
//		return new ProviderResponse();
//	}
//
//	/**
//	 * Create an instance of {@link MonitorHeader }
//	 *
//	 */
//	public MonitorHeader createMonitorHeader() {
//		return new MonitorHeader();
//	}
//
//
//	/**
//	 * Create an instance of {@link JAXBElement }{@code <}
//	 * {@link ProviderResponse }{@code >}
//	 *
//	 */
//	@XmlElementDecl(namespace = "", name = "ProviderResponse")
//	public JAXBElement<ProviderResponse> createProviderResponse(
//			ProviderResponse value) {
//		return new JAXBElement<ProviderResponse>(_ProviderResponse_QNAME,
//				ProviderResponse.class, null, value);
//	}
//
//	/**
//	 * Create an instance of {@link JAXBElement }{@code <}{@link ReturnStationXML }
//	 * {@code >}
//	 *
//	 */
//	@XmlElementDecl(namespace = "", name = "responseBody")
//	public JAXBElement<ReturnStationXML> createResponseBody(ReturnStationXML value) {
//		return new JAXBElement<ReturnStationXML>(_ResponseBody_QNAME,
//				ReturnStationXML.class, null, value);
//	}
//
//	/**
//	 * Create an instance of {@link JAXBElement }{@code <}{@link MonitorHeader }
//	 * {@code >}
//	 *
//	 */
//	@XmlElementDecl(namespace = NameSpaceUrlConst.stationNamespaceURI, name = "MonitorHeader")
//	public JAXBElement<MonitorHeader> createMonitorHeader(MonitorHeader value) {
//		return new JAXBElement<MonitorHeader>(_MonitorHeader_QNAME,
//				MonitorHeader.class, null, value);
//	}
//
//	/**
//	 * Create an instance of {@link JAXBElement }{@code <}{@link RequestBody }
//	 * {@code >}
//	 *
//	 */
//	@XmlElementDecl(namespace = NameSpaceUrlConst.stationNamespaceURI, name = "requestBody")
//	public JAXBElement<RequestBody> createRequestBody(RequestBody value) {
//		return new JAXBElement<RequestBody>(_RequestBody_QNAME,
//				RequestBody.class, null, value);
//	}
//
//}
