package com.caict.bsm.project.api.open.test.calculation;

import com.caict.bsm.project.api.open.test.NameSpaceUrlConst;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * Java class for requestBody complex type.
 *
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 *
 * <pre>
 * &lt;complexType name="requestBody">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="page" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="rows" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="assetNumber" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(namespace = NameSpaceUrlConst.stationNamespaceURI,name = "requestBody",
		propOrder = { "licenseCode", "licenseStartDate", "licenseEndDate","stationName","stationCode",
				"licensePerson","licenseeNo","licenseeCounty","longitude","latitude","licenseNo",
				"sectionList"})
public class RequestBody {

	@XmlElement(name = "licenseCode",namespace = NameSpaceUrlConst.stationNamespaceURI)
	protected String licenseCode;
	@XmlElement(name = "licenseStartDate",namespace = NameSpaceUrlConst.stationNamespaceURI)
	protected Date licenseStartDate;
	@XmlElement(name = "licenseEndDate",namespace = NameSpaceUrlConst.stationNamespaceURI)
	protected Date licenseEndDate;
	@XmlElement(name = "stationName",namespace = NameSpaceUrlConst.stationNamespaceURI)
	protected String stationName;
	@XmlElement(name = "stationCode",namespace = NameSpaceUrlConst.stationNamespaceURI)
	protected String stationCode;
	@XmlElement(name = "licensePerson",namespace = NameSpaceUrlConst.stationNamespaceURI)
	protected String licensePerson;
	@XmlElement(name = "licenseeNo",namespace = NameSpaceUrlConst.stationNamespaceURI)
	protected String licenseeNo;
	@XmlElement(name = "licenseeCounty",namespace = NameSpaceUrlConst.stationNamespaceURI)
	protected String licenseeCounty;
	@XmlElement(name = "longitude",namespace = NameSpaceUrlConst.stationNamespaceURI)
	protected String longitude;
	@XmlElement(name = "latitude",namespace = NameSpaceUrlConst.stationNamespaceURI)
	protected String latitude;
	@XmlElement(name = "licenseNo",namespace = NameSpaceUrlConst.stationNamespaceURI)
	protected String licenseNo;
	@XmlElement(name = "sectionList",namespace = NameSpaceUrlConst.stationNamespaceURI)
	protected List<SectionList> sectionList;

	public String getLicenseCode() {
		return licenseCode;
	}

	public void setLicenseCode(String licenseCode) {
		this.licenseCode = licenseCode;
	}

	public Date getLicenseStartDate() {
		return licenseStartDate;
	}

	public void setLicenseStartDate(Date licenseStartDate) {
		this.licenseStartDate = licenseStartDate;
	}

	public Date getLicenseEndDate() {
		return licenseEndDate;
	}

	public void setLicenseEndDate(Date licenseEndDate) {
		this.licenseEndDate = licenseEndDate;
	}

	public String getStationName() {
		return stationName;
	}

	public void setStationName(String stationName) {
		this.stationName = stationName;
	}

	public String getStationCode() {
		return stationCode;
	}

	public void setStationCode(String stationCode) {
		this.stationCode = stationCode;
	}

	public String getLicensePerson() {
		return licensePerson;
	}

	public void setLicensePerson(String licensePerson) {
		this.licensePerson = licensePerson;
	}

	public String getLicenseeNo() {
		return licenseeNo;
	}

	public void setLicenseeNo(String licenseeNo) {
		this.licenseeNo = licenseeNo;
	}

	public String getLicenseeCounty() {
		return licenseeCounty;
	}

	public void setLicenseeCounty(String licenseeCounty) {
		this.licenseeCounty = licenseeCounty;
	}

	public String getLongitude() {
		return longitude;
	}

	public void setLongitude(String longitude) {
		this.longitude = longitude;
	}

	public String getLatitude() {
		return latitude;
	}

	public void setLatitude(String latitude) {
		this.latitude = latitude;
	}

	public String getLicenseNo() {
		return licenseNo;
	}

	public void setLicenseNo(String licenseNo) {
		this.licenseNo = licenseNo;
	}

	public List<SectionList> getSectionList() {
		return sectionList;
	}

	public void setSectionList(List<SectionList> sectionList) {
		this.sectionList = sectionList;
	}
}
