package com.caict.bsm.project.api.open.test.calculation;

import com.caict.bsm.project.api.open.test.NameSpaceUrlConst;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(namespace = NameSpaceUrlConst.stationNamespaceURI,name = "sectionList",
        propOrder = { "freqEf", "freqRf", "equPow","freqBand","freqOther",
                "equAuth","antGain","antPole","antHight","specificRequirements1","specificRequirements2"})
public class SectionList {

    @XmlElement(name = "freqEf",namespace = NameSpaceUrlConst.stationNamespaceURI)
    protected String freqEf;
    @XmlElement(name = "freqRf",namespace = NameSpaceUrlConst.stationNamespaceURI)
    protected String freqRf;
    @XmlElement(name = "equPow",namespace = NameSpaceUrlConst.stationNamespaceURI)
    protected String equPow;
    @XmlElement(name = "freqBand",namespace = NameSpaceUrlConst.stationNamespaceURI)
    protected String freqBand;
    @XmlElement(name = "freqOther",namespace = NameSpaceUrlConst.stationNamespaceURI)
    protected String freqOther;
    @XmlElement(name = "equAuth",namespace = NameSpaceUrlConst.stationNamespaceURI)
    protected String equAuth;
    @XmlElement(name = "antGain",namespace = NameSpaceUrlConst.stationNamespaceURI)
    protected String antGain;
    @XmlElement(name = "antPole",namespace = NameSpaceUrlConst.stationNamespaceURI)
    protected String antPole;
    @XmlElement(name = "antHight",namespace = NameSpaceUrlConst.stationNamespaceURI)
    protected String antHight;
    @XmlElement(name = "specificRequirements1",namespace = NameSpaceUrlConst.stationNamespaceURI)
    protected String specificRequirements1;
    @XmlElement(name = "specificRequirements2",namespace = NameSpaceUrlConst.stationNamespaceURI)
    protected String specificRequirements2;

    public String getFreqEf() {
        return freqEf;
    }

    public void setFreqEf(String freqEf) {
        this.freqEf = freqEf;
    }

    public String getFreqRf() {
        return freqRf;
    }

    public void setFreqRf(String freqRf) {
        this.freqRf = freqRf;
    }

    public String getEquPow() {
        return equPow;
    }

    public void setEquPow(String equPow) {
        this.equPow = equPow;
    }

    public String getFreqBand() {
        return freqBand;
    }

    public void setFreqBand(String freqBand) {
        this.freqBand = freqBand;
    }

    public String getFreqOther() {
        return freqOther;
    }

    public void setFreqOther(String freqOther) {
        this.freqOther = freqOther;
    }

    public String getEquAuth() {
        return equAuth;
    }

    public void setEquAuth(String equAuth) {
        this.equAuth = equAuth;
    }

    public String getAntGain() {
        return antGain;
    }

    public void setAntGain(String antGain) {
        this.antGain = antGain;
    }

    public String getAntPole() {
        return antPole;
    }

    public void setAntPole(String antPole) {
        this.antPole = antPole;
    }

    public String getAntHight() {
        return antHight;
    }

    public void setAntHight(String antHight) {
        this.antHight = antHight;
    }

    public String getSpecificRequirements1() {
        return specificRequirements1;
    }

    public void setSpecificRequirements1(String specificRequirements1) {
        this.specificRequirements1 = specificRequirements1;
    }

    public String getSpecificRequirements2() {
        return specificRequirements2;
    }

    public void setSpecificRequirements2(String specificRequirements2) {
        this.specificRequirements2 = specificRequirements2;
    }
}
