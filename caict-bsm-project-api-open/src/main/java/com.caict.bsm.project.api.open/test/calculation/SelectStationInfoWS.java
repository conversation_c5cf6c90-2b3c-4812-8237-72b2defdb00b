package com.caict.bsm.project.api.open.test.calculation;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebService;
import javax.jws.soap.SOAPBinding;
import javax.xml.ws.Holder;

/**
 * This class was generated by the JAX-WS RI. JAX-WS RI 2.1.3-hudson-390-
 * Generated source version: 2.0
 *
 */
@WebService(name = "SelectStationInfoWS", targetNamespace = "http://www.srrc.org.cn")
@SOAPBinding(parameterStyle = SOAPBinding.ParameterStyle.BARE)
public interface SelectStationInfoWS {

	/**
	 *
	 * @param requestBody
	 * @param responseBody
	 * @param monitorHeader
	 * @param providerResponse
	 */
	@WebMethod
	public void getStationInfo(
            @WebParam(name = "MonitorHeader", targetNamespace = "http://www.srrc.org.cn", header = true, partName = "MonitorHeader") MonitorHeader monitorHeader,
            @WebParam(name = "requestBody", targetNamespace = "http://www.srrc.org.cn", partName = "requestBody") RequestBody requestBody,
            @WebParam(name = "ProviderResponse", targetNamespace = "http://www.srrc.org.cn", header = true, mode = WebParam.Mode.OUT, partName = "ProviderResponse") Holder<ProviderResponse> providerResponse,
            @WebParam(name = "responseBody", targetNamespace = "http://www.srrc.org.cn", mode = WebParam.Mode.OUT, partName = "responseBody") Holder<ReturnStationXML> responseBody);

}
