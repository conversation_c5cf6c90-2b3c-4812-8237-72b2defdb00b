package com.caict.bsm.project.api.open.test.stationdata;

import com.alibaba.fastjson.JSONObject;


import com.caict.bsm.project.system.utils.repository.MyRestRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.jws.WebService;
import javax.xml.ws.Holder;
import java.util.Map;

/**
 * 提供波尔基站注销接口
 */
@WebService(endpointInterface = "com.caict.bsm.project.api.open.test.stationdata.GetStationInfoWS", targetNamespace = "http://www.srrc.org.cn", serviceName = "GetStationInfoWS")
@Service
public class GetStationInfoWSPortImpl implements GetStationInfoWS {

	@Autowired
	private MyRestRepository myRestRepository;
	@Value("${caict.sync.url}")
	private String syncUrl;

	public String staDel(RequestBody requestBody){
		return myRestRepository.saveNp(syncUrl + "/api/boer/station/stationDelBE",requestBody);
	}


	@Override
	public void findByCondition(MonitorHeader monitorHeader, RequestBody requestBody, Holder<ResponseHeader> providerResponse,
                                Holder<ResponseBody> responseBody) {
		ResponseHeader responseHeader = new ResponseHeader();
		responseHeader.setBizResCd(monitorHeader.getBizKey());
		responseHeader.setBizResText("业务请求访问成功");
		providerResponse.value = responseHeader;
		ResponseBody response = new ResponseBody();
		try {
			if (requestBody.getStationCodeList()!=null ){
				Map<String,Object> result = JSONObject.parseObject(staDel(requestBody));
				if ((boolean)result.get("success")){
					response.setStatus("1");
				}else {
					response.setStatus("2");
				}
			}
		}catch (Exception e){
			response.setStatus("2");
		}finally {
			responseBody.value = response;
		}
	}
}
