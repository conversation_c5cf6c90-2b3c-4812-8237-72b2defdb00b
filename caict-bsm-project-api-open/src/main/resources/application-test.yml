server:
  port: 8084

caict:
  select:
    rows: 10000
  mobile: baa51cbb-7046-4623-8e37-4a17f58efe2a
  unicom: dda01c5c-5a1e-4552-86df-411e1b77a1ec
  telecom: a500fe23-c571-4c41-9b60-442f2705c1f7
  sync:
    url: http://127.0.0.1:8087/
  requestUrl: http://127.0.0.1:8084/webservice/getCalResult?wsdl

spring:
  application:
    name: bsm sync datasource
  mvc:
    static-path-pattern: /**
  resources:
    static-locations: classpath:/META-INF/resources/,classpath:/resources/,classpath:/static/,classpath:/public/
  datasource:
    oracle:
      driver-class-name: oracle.jdbc.driver.OracleDriver
      url: jdbc:oracle:thin:@*************:1521:ORCL
      username: CAICT_BSM
      password: caict
      max-idle: 10
      min-idle: 5
      initial-size: 5
      validation-query: select 1 from dual
      max-wait: 10000
    oracleStandard:
      driver-class-name: oracle.jdbc.driver.OracleDriver
      url: jdbc:oracle:thin:@*************:1521:ORCL
      username: BSM_SYNC
      password: caict
      max-idle: 10
      min-idle: 5
      initial-size: 5
      validation-query: select 1 from dual
      max-wait: 10000
    oracleKeyStation:
      driver-class-name: oracle.jdbc.driver.OracleDriver
      url: jdbc:oracle:thin:@*************:1521:ORCL
      username: caict_bsm
      password: caict
      max-idle: 10
      min-idle: 5
      initial-size: 5
      validation-query: select 1 from dual
      max-wait: 10000
  redis:
    database: 0
    host: 127.0.0.1
    port: 6379
    password:
    timeout: 10000
    pool:
      max-active: 200
      max-wait: -1
      max-idle: 10
      min-idle: 0
