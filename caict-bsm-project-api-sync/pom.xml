<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>caict-bsm-project</artifactId>
        <groupId>com.caict.bsm</groupId>
        <version>3.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>caict-bsm-project-api-sync</artifactId>
    <packaging>jar</packaging>

    <name>caict-bsm-project-api-sync</name>
    <description>数据同步api</description>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>com.caict.bsm</groupId>
            <artifactId>caict-bsm-project-system-utils</artifactId>
            <version>${system-utils.version}</version>
        </dependency>
        <dependency>
            <groupId>com.caict.bsm</groupId>
            <artifactId>caict-bsm-project-system-model</artifactId>
            <version>${system-model.version}</version>
        </dependency>
        <dependency>
            <groupId>com.caict.bsm</groupId>
            <artifactId>caict-bsm-project-domain-business</artifactId>
            <version>${domain-business.version}</version>
        </dependency>
        <dependency>
            <groupId>com.caict.bsm</groupId>
            <artifactId>caict-bsm-project-extension-document</artifactId>
            <version>${extension-document.version}</version>
        </dependency>
        <dependency>
            <groupId>com.caict.bsm</groupId>
            <artifactId>caict-bsm-project-datacenter-realtime</artifactId>
            <version>${datacenter-realtime.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.axis</groupId>
            <artifactId>axis</artifactId>
            <version>1.4</version>
        </dependency>
        <dependency>
            <groupId>javax.xml.rpc</groupId>
            <artifactId>javax.xml.rpc-api</artifactId>
            <version>1.1.1</version>
        </dependency>
        <dependency>
            <groupId>commons-discovery</groupId>
            <artifactId>commons-discovery</artifactId>
            <version>0.4</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <!--<plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>2.6</version>
                <configuration>
                    <archive>
                        <manifest>
                            <addClasspath>true</addClasspath>
                            <classpathPrefix>lib/</classpathPrefix>
                            <mainClass>com.caict.bsm.project.api.sync.SyncApplication</mainClass>
                        </manifest>
                    </archive>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <version>3.0.1</version>
                <executions>
                    <execution>
                        <id>copy-dependencies</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/lib</outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>-->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <mainClass>com.caict.bsm.project.api.sync.SyncApplication</mainClass>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>