/**
 * FS_APP.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.caict.bsm.project.api.sync.boer.applytable.org.datacontract.schemas._2004._07.FS_ServerSolution_Data;

public class FS_APP  implements java.io.Serializable {
    private String APP_CODE;

    private java.util.Calendar APP_DATE;

    private java.util.Calendar APP_FTLB;

    private java.util.Calendar APP_FTLE;

    private String APP_GUID;

    private String APP_STATUS;

    private String APP_SUB_TYPE;

    private String APP_TYPE;

    private String BUSINESS_FLAG;

    private java.util.Calendar CONFIRM_DATE;

    private String CONTACT_GUID;

    private java.util.Calendar CREATE_TIME;

    private java.util.Calendar EXPIRED_DATE;

    private String FEE_CONTACT_GUID;

    private String FEE_GUID;

    private String IS_NET;

    private String MEMO;

    private java.util.Calendar MODIFIED_TIME;

    private String NET_AREA;

    private String NET_BAND;

    private String NET_BAND_UNIT;

    private String NET_LG;

    private String NET_NAME;

    private String NET_SAT_NAME;

    private String NET_SP;

    private String NET_SVN;

    private String NET_TS;

    private String NET_USE;

    private String NEW_AREA_CODE;

    private String OPERATOR;

    private String ORG_GUID;

    private java.util.Calendar START_DATE;

    private String ST_CLASS1;

    private String TASK_GUID;

    public FS_APP() {
    }

    public FS_APP(
           String APP_CODE,
           java.util.Calendar APP_DATE,
           java.util.Calendar APP_FTLB,
           java.util.Calendar APP_FTLE,
           String APP_GUID,
           String APP_STATUS,
           String APP_SUB_TYPE,
           String APP_TYPE,
           String BUSINESS_FLAG,
           java.util.Calendar CONFIRM_DATE,
           String CONTACT_GUID,
           java.util.Calendar CREATE_TIME,
           java.util.Calendar EXPIRED_DATE,
           String FEE_CONTACT_GUID,
           String FEE_GUID,
           String IS_NET,
           String MEMO,
           java.util.Calendar MODIFIED_TIME,
           String NET_AREA,
           String NET_BAND,
           String NET_BAND_UNIT,
           String NET_LG,
           String NET_NAME,
           String NET_SAT_NAME,
           String NET_SP,
           String NET_SVN,
           String NET_TS,
           String NET_USE,
           String NEW_AREA_CODE,
           String OPERATOR,
           String ORG_GUID,
           java.util.Calendar START_DATE,
           String ST_CLASS1,
           String TASK_GUID) {
           this.APP_CODE = APP_CODE;
           this.APP_DATE = APP_DATE;
           this.APP_FTLB = APP_FTLB;
           this.APP_FTLE = APP_FTLE;
           this.APP_GUID = APP_GUID;
           this.APP_STATUS = APP_STATUS;
           this.APP_SUB_TYPE = APP_SUB_TYPE;
           this.APP_TYPE = APP_TYPE;
           this.BUSINESS_FLAG = BUSINESS_FLAG;
           this.CONFIRM_DATE = CONFIRM_DATE;
           this.CONTACT_GUID = CONTACT_GUID;
           this.CREATE_TIME = CREATE_TIME;
           this.EXPIRED_DATE = EXPIRED_DATE;
           this.FEE_CONTACT_GUID = FEE_CONTACT_GUID;
           this.FEE_GUID = FEE_GUID;
           this.IS_NET = IS_NET;
           this.MEMO = MEMO;
           this.MODIFIED_TIME = MODIFIED_TIME;
           this.NET_AREA = NET_AREA;
           this.NET_BAND = NET_BAND;
           this.NET_BAND_UNIT = NET_BAND_UNIT;
           this.NET_LG = NET_LG;
           this.NET_NAME = NET_NAME;
           this.NET_SAT_NAME = NET_SAT_NAME;
           this.NET_SP = NET_SP;
           this.NET_SVN = NET_SVN;
           this.NET_TS = NET_TS;
           this.NET_USE = NET_USE;
           this.NEW_AREA_CODE = NEW_AREA_CODE;
           this.OPERATOR = OPERATOR;
           this.ORG_GUID = ORG_GUID;
           this.START_DATE = START_DATE;
           this.ST_CLASS1 = ST_CLASS1;
           this.TASK_GUID = TASK_GUID;
    }


    /**
     * Gets the APP_CODE value for this FS_APP.
     * 
     * @return APP_CODE
     */
    public String getAPP_CODE() {
        return APP_CODE;
    }


    /**
     * Sets the APP_CODE value for this FS_APP.
     * 
     * @param APP_CODE
     */
    public void setAPP_CODE(String APP_CODE) {
        this.APP_CODE = APP_CODE;
    }


    /**
     * Gets the APP_DATE value for this FS_APP.
     * 
     * @return APP_DATE
     */
    public java.util.Calendar getAPP_DATE() {
        return APP_DATE;
    }


    /**
     * Sets the APP_DATE value for this FS_APP.
     * 
     * @param APP_DATE
     */
    public void setAPP_DATE(java.util.Calendar APP_DATE) {
        this.APP_DATE = APP_DATE;
    }


    /**
     * Gets the APP_FTLB value for this FS_APP.
     * 
     * @return APP_FTLB
     */
    public java.util.Calendar getAPP_FTLB() {
        return APP_FTLB;
    }


    /**
     * Sets the APP_FTLB value for this FS_APP.
     * 
     * @param APP_FTLB
     */
    public void setAPP_FTLB(java.util.Calendar APP_FTLB) {
        this.APP_FTLB = APP_FTLB;
    }


    /**
     * Gets the APP_FTLE value for this FS_APP.
     * 
     * @return APP_FTLE
     */
    public java.util.Calendar getAPP_FTLE() {
        return APP_FTLE;
    }


    /**
     * Sets the APP_FTLE value for this FS_APP.
     * 
     * @param APP_FTLE
     */
    public void setAPP_FTLE(java.util.Calendar APP_FTLE) {
        this.APP_FTLE = APP_FTLE;
    }


    /**
     * Gets the APP_GUID value for this FS_APP.
     * 
     * @return APP_GUID
     */
    public String getAPP_GUID() {
        return APP_GUID;
    }


    /**
     * Sets the APP_GUID value for this FS_APP.
     * 
     * @param APP_GUID
     */
    public void setAPP_GUID(String APP_GUID) {
        this.APP_GUID = APP_GUID;
    }


    /**
     * Gets the APP_STATUS value for this FS_APP.
     * 
     * @return APP_STATUS
     */
    public String getAPP_STATUS() {
        return APP_STATUS;
    }


    /**
     * Sets the APP_STATUS value for this FS_APP.
     * 
     * @param APP_STATUS
     */
    public void setAPP_STATUS(String APP_STATUS) {
        this.APP_STATUS = APP_STATUS;
    }


    /**
     * Gets the APP_SUB_TYPE value for this FS_APP.
     * 
     * @return APP_SUB_TYPE
     */
    public String getAPP_SUB_TYPE() {
        return APP_SUB_TYPE;
    }


    /**
     * Sets the APP_SUB_TYPE value for this FS_APP.
     * 
     * @param APP_SUB_TYPE
     */
    public void setAPP_SUB_TYPE(String APP_SUB_TYPE) {
        this.APP_SUB_TYPE = APP_SUB_TYPE;
    }


    /**
     * Gets the APP_TYPE value for this FS_APP.
     * 
     * @return APP_TYPE
     */
    public String getAPP_TYPE() {
        return APP_TYPE;
    }


    /**
     * Sets the APP_TYPE value for this FS_APP.
     * 
     * @param APP_TYPE
     */
    public void setAPP_TYPE(String APP_TYPE) {
        this.APP_TYPE = APP_TYPE;
    }


    /**
     * Gets the BUSINESS_FLAG value for this FS_APP.
     * 
     * @return BUSINESS_FLAG
     */
    public String getBUSINESS_FLAG() {
        return BUSINESS_FLAG;
    }


    /**
     * Sets the BUSINESS_FLAG value for this FS_APP.
     * 
     * @param BUSINESS_FLAG
     */
    public void setBUSINESS_FLAG(String BUSINESS_FLAG) {
        this.BUSINESS_FLAG = BUSINESS_FLAG;
    }


    /**
     * Gets the CONFIRM_DATE value for this FS_APP.
     * 
     * @return CONFIRM_DATE
     */
    public java.util.Calendar getCONFIRM_DATE() {
        return CONFIRM_DATE;
    }


    /**
     * Sets the CONFIRM_DATE value for this FS_APP.
     * 
     * @param CONFIRM_DATE
     */
    public void setCONFIRM_DATE(java.util.Calendar CONFIRM_DATE) {
        this.CONFIRM_DATE = CONFIRM_DATE;
    }


    /**
     * Gets the CONTACT_GUID value for this FS_APP.
     * 
     * @return CONTACT_GUID
     */
    public String getCONTACT_GUID() {
        return CONTACT_GUID;
    }


    /**
     * Sets the CONTACT_GUID value for this FS_APP.
     * 
     * @param CONTACT_GUID
     */
    public void setCONTACT_GUID(String CONTACT_GUID) {
        this.CONTACT_GUID = CONTACT_GUID;
    }


    /**
     * Gets the CREATE_TIME value for this FS_APP.
     * 
     * @return CREATE_TIME
     */
    public java.util.Calendar getCREATE_TIME() {
        return CREATE_TIME;
    }


    /**
     * Sets the CREATE_TIME value for this FS_APP.
     * 
     * @param CREATE_TIME
     */
    public void setCREATE_TIME(java.util.Calendar CREATE_TIME) {
        this.CREATE_TIME = CREATE_TIME;
    }


    /**
     * Gets the EXPIRED_DATE value for this FS_APP.
     * 
     * @return EXPIRED_DATE
     */
    public java.util.Calendar getEXPIRED_DATE() {
        return EXPIRED_DATE;
    }


    /**
     * Sets the EXPIRED_DATE value for this FS_APP.
     * 
     * @param EXPIRED_DATE
     */
    public void setEXPIRED_DATE(java.util.Calendar EXPIRED_DATE) {
        this.EXPIRED_DATE = EXPIRED_DATE;
    }


    /**
     * Gets the FEE_CONTACT_GUID value for this FS_APP.
     * 
     * @return FEE_CONTACT_GUID
     */
    public String getFEE_CONTACT_GUID() {
        return FEE_CONTACT_GUID;
    }


    /**
     * Sets the FEE_CONTACT_GUID value for this FS_APP.
     * 
     * @param FEE_CONTACT_GUID
     */
    public void setFEE_CONTACT_GUID(String FEE_CONTACT_GUID) {
        this.FEE_CONTACT_GUID = FEE_CONTACT_GUID;
    }


    /**
     * Gets the FEE_GUID value for this FS_APP.
     * 
     * @return FEE_GUID
     */
    public String getFEE_GUID() {
        return FEE_GUID;
    }


    /**
     * Sets the FEE_GUID value for this FS_APP.
     * 
     * @param FEE_GUID
     */
    public void setFEE_GUID(String FEE_GUID) {
        this.FEE_GUID = FEE_GUID;
    }


    /**
     * Gets the IS_NET value for this FS_APP.
     * 
     * @return IS_NET
     */
    public String getIS_NET() {
        return IS_NET;
    }


    /**
     * Sets the IS_NET value for this FS_APP.
     * 
     * @param IS_NET
     */
    public void setIS_NET(String IS_NET) {
        this.IS_NET = IS_NET;
    }


    /**
     * Gets the MEMO value for this FS_APP.
     * 
     * @return MEMO
     */
    public String getMEMO() {
        return MEMO;
    }


    /**
     * Sets the MEMO value for this FS_APP.
     * 
     * @param MEMO
     */
    public void setMEMO(String MEMO) {
        this.MEMO = MEMO;
    }


    /**
     * Gets the MODIFIED_TIME value for this FS_APP.
     * 
     * @return MODIFIED_TIME
     */
    public java.util.Calendar getMODIFIED_TIME() {
        return MODIFIED_TIME;
    }


    /**
     * Sets the MODIFIED_TIME value for this FS_APP.
     * 
     * @param MODIFIED_TIME
     */
    public void setMODIFIED_TIME(java.util.Calendar MODIFIED_TIME) {
        this.MODIFIED_TIME = MODIFIED_TIME;
    }


    /**
     * Gets the NET_AREA value for this FS_APP.
     * 
     * @return NET_AREA
     */
    public String getNET_AREA() {
        return NET_AREA;
    }


    /**
     * Sets the NET_AREA value for this FS_APP.
     * 
     * @param NET_AREA
     */
    public void setNET_AREA(String NET_AREA) {
        this.NET_AREA = NET_AREA;
    }


    /**
     * Gets the NET_BAND value for this FS_APP.
     * 
     * @return NET_BAND
     */
    public String getNET_BAND() {
        return NET_BAND;
    }


    /**
     * Sets the NET_BAND value for this FS_APP.
     * 
     * @param NET_BAND
     */
    public void setNET_BAND(String NET_BAND) {
        this.NET_BAND = NET_BAND;
    }


    /**
     * Gets the NET_BAND_UNIT value for this FS_APP.
     * 
     * @return NET_BAND_UNIT
     */
    public String getNET_BAND_UNIT() {
        return NET_BAND_UNIT;
    }


    /**
     * Sets the NET_BAND_UNIT value for this FS_APP.
     * 
     * @param NET_BAND_UNIT
     */
    public void setNET_BAND_UNIT(String NET_BAND_UNIT) {
        this.NET_BAND_UNIT = NET_BAND_UNIT;
    }


    /**
     * Gets the NET_LG value for this FS_APP.
     * 
     * @return NET_LG
     */
    public String getNET_LG() {
        return NET_LG;
    }


    /**
     * Sets the NET_LG value for this FS_APP.
     * 
     * @param NET_LG
     */
    public void setNET_LG(String NET_LG) {
        this.NET_LG = NET_LG;
    }


    /**
     * Gets the NET_NAME value for this FS_APP.
     * 
     * @return NET_NAME
     */
    public String getNET_NAME() {
        return NET_NAME;
    }


    /**
     * Sets the NET_NAME value for this FS_APP.
     * 
     * @param NET_NAME
     */
    public void setNET_NAME(String NET_NAME) {
        this.NET_NAME = NET_NAME;
    }


    /**
     * Gets the NET_SAT_NAME value for this FS_APP.
     * 
     * @return NET_SAT_NAME
     */
    public String getNET_SAT_NAME() {
        return NET_SAT_NAME;
    }


    /**
     * Sets the NET_SAT_NAME value for this FS_APP.
     * 
     * @param NET_SAT_NAME
     */
    public void setNET_SAT_NAME(String NET_SAT_NAME) {
        this.NET_SAT_NAME = NET_SAT_NAME;
    }


    /**
     * Gets the NET_SP value for this FS_APP.
     * 
     * @return NET_SP
     */
    public String getNET_SP() {
        return NET_SP;
    }


    /**
     * Sets the NET_SP value for this FS_APP.
     * 
     * @param NET_SP
     */
    public void setNET_SP(String NET_SP) {
        this.NET_SP = NET_SP;
    }


    /**
     * Gets the NET_SVN value for this FS_APP.
     * 
     * @return NET_SVN
     */
    public String getNET_SVN() {
        return NET_SVN;
    }


    /**
     * Sets the NET_SVN value for this FS_APP.
     * 
     * @param NET_SVN
     */
    public void setNET_SVN(String NET_SVN) {
        this.NET_SVN = NET_SVN;
    }


    /**
     * Gets the NET_TS value for this FS_APP.
     * 
     * @return NET_TS
     */
    public String getNET_TS() {
        return NET_TS;
    }


    /**
     * Sets the NET_TS value for this FS_APP.
     * 
     * @param NET_TS
     */
    public void setNET_TS(String NET_TS) {
        this.NET_TS = NET_TS;
    }


    /**
     * Gets the NET_USE value for this FS_APP.
     * 
     * @return NET_USE
     */
    public String getNET_USE() {
        return NET_USE;
    }


    /**
     * Sets the NET_USE value for this FS_APP.
     * 
     * @param NET_USE
     */
    public void setNET_USE(String NET_USE) {
        this.NET_USE = NET_USE;
    }


    /**
     * Gets the NEW_AREA_CODE value for this FS_APP.
     * 
     * @return NEW_AREA_CODE
     */
    public String getNEW_AREA_CODE() {
        return NEW_AREA_CODE;
    }


    /**
     * Sets the NEW_AREA_CODE value for this FS_APP.
     * 
     * @param NEW_AREA_CODE
     */
    public void setNEW_AREA_CODE(String NEW_AREA_CODE) {
        this.NEW_AREA_CODE = NEW_AREA_CODE;
    }


    /**
     * Gets the OPERATOR value for this FS_APP.
     * 
     * @return OPERATOR
     */
    public String getOPERATOR() {
        return OPERATOR;
    }


    /**
     * Sets the OPERATOR value for this FS_APP.
     * 
     * @param OPERATOR
     */
    public void setOPERATOR(String OPERATOR) {
        this.OPERATOR = OPERATOR;
    }


    /**
     * Gets the ORG_GUID value for this FS_APP.
     * 
     * @return ORG_GUID
     */
    public String getORG_GUID() {
        return ORG_GUID;
    }


    /**
     * Sets the ORG_GUID value for this FS_APP.
     * 
     * @param ORG_GUID
     */
    public void setORG_GUID(String ORG_GUID) {
        this.ORG_GUID = ORG_GUID;
    }


    /**
     * Gets the START_DATE value for this FS_APP.
     * 
     * @return START_DATE
     */
    public java.util.Calendar getSTART_DATE() {
        return START_DATE;
    }


    /**
     * Sets the START_DATE value for this FS_APP.
     * 
     * @param START_DATE
     */
    public void setSTART_DATE(java.util.Calendar START_DATE) {
        this.START_DATE = START_DATE;
    }


    /**
     * Gets the ST_CLASS1 value for this FS_APP.
     * 
     * @return ST_CLASS1
     */
    public String getST_CLASS1() {
        return ST_CLASS1;
    }


    /**
     * Sets the ST_CLASS1 value for this FS_APP.
     * 
     * @param ST_CLASS1
     */
    public void setST_CLASS1(String ST_CLASS1) {
        this.ST_CLASS1 = ST_CLASS1;
    }


    /**
     * Gets the TASK_GUID value for this FS_APP.
     * 
     * @return TASK_GUID
     */
    public String getTASK_GUID() {
        return TASK_GUID;
    }


    /**
     * Sets the TASK_GUID value for this FS_APP.
     * 
     * @param TASK_GUID
     */
    public void setTASK_GUID(String TASK_GUID) {
        this.TASK_GUID = TASK_GUID;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof FS_APP)) return false;
        FS_APP other = (FS_APP) obj;
        if (obj == null) return false;
        if (this == obj) return true;
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.APP_CODE==null && other.getAPP_CODE()==null) || 
             (this.APP_CODE!=null &&
              this.APP_CODE.equals(other.getAPP_CODE()))) &&
            ((this.APP_DATE==null && other.getAPP_DATE()==null) || 
             (this.APP_DATE!=null &&
              this.APP_DATE.equals(other.getAPP_DATE()))) &&
            ((this.APP_FTLB==null && other.getAPP_FTLB()==null) || 
             (this.APP_FTLB!=null &&
              this.APP_FTLB.equals(other.getAPP_FTLB()))) &&
            ((this.APP_FTLE==null && other.getAPP_FTLE()==null) || 
             (this.APP_FTLE!=null &&
              this.APP_FTLE.equals(other.getAPP_FTLE()))) &&
            ((this.APP_GUID==null && other.getAPP_GUID()==null) || 
             (this.APP_GUID!=null &&
              this.APP_GUID.equals(other.getAPP_GUID()))) &&
            ((this.APP_STATUS==null && other.getAPP_STATUS()==null) || 
             (this.APP_STATUS!=null &&
              this.APP_STATUS.equals(other.getAPP_STATUS()))) &&
            ((this.APP_SUB_TYPE==null && other.getAPP_SUB_TYPE()==null) || 
             (this.APP_SUB_TYPE!=null &&
              this.APP_SUB_TYPE.equals(other.getAPP_SUB_TYPE()))) &&
            ((this.APP_TYPE==null && other.getAPP_TYPE()==null) || 
             (this.APP_TYPE!=null &&
              this.APP_TYPE.equals(other.getAPP_TYPE()))) &&
            ((this.BUSINESS_FLAG==null && other.getBUSINESS_FLAG()==null) || 
             (this.BUSINESS_FLAG!=null &&
              this.BUSINESS_FLAG.equals(other.getBUSINESS_FLAG()))) &&
            ((this.CONFIRM_DATE==null && other.getCONFIRM_DATE()==null) || 
             (this.CONFIRM_DATE!=null &&
              this.CONFIRM_DATE.equals(other.getCONFIRM_DATE()))) &&
            ((this.CONTACT_GUID==null && other.getCONTACT_GUID()==null) || 
             (this.CONTACT_GUID!=null &&
              this.CONTACT_GUID.equals(other.getCONTACT_GUID()))) &&
            ((this.CREATE_TIME==null && other.getCREATE_TIME()==null) || 
             (this.CREATE_TIME!=null &&
              this.CREATE_TIME.equals(other.getCREATE_TIME()))) &&
            ((this.EXPIRED_DATE==null && other.getEXPIRED_DATE()==null) || 
             (this.EXPIRED_DATE!=null &&
              this.EXPIRED_DATE.equals(other.getEXPIRED_DATE()))) &&
            ((this.FEE_CONTACT_GUID==null && other.getFEE_CONTACT_GUID()==null) || 
             (this.FEE_CONTACT_GUID!=null &&
              this.FEE_CONTACT_GUID.equals(other.getFEE_CONTACT_GUID()))) &&
            ((this.FEE_GUID==null && other.getFEE_GUID()==null) || 
             (this.FEE_GUID!=null &&
              this.FEE_GUID.equals(other.getFEE_GUID()))) &&
            ((this.IS_NET==null && other.getIS_NET()==null) || 
             (this.IS_NET!=null &&
              this.IS_NET.equals(other.getIS_NET()))) &&
            ((this.MEMO==null && other.getMEMO()==null) || 
             (this.MEMO!=null &&
              this.MEMO.equals(other.getMEMO()))) &&
            ((this.MODIFIED_TIME==null && other.getMODIFIED_TIME()==null) || 
             (this.MODIFIED_TIME!=null &&
              this.MODIFIED_TIME.equals(other.getMODIFIED_TIME()))) &&
            ((this.NET_AREA==null && other.getNET_AREA()==null) || 
             (this.NET_AREA!=null &&
              this.NET_AREA.equals(other.getNET_AREA()))) &&
            ((this.NET_BAND==null && other.getNET_BAND()==null) || 
             (this.NET_BAND!=null &&
              this.NET_BAND.equals(other.getNET_BAND()))) &&
            ((this.NET_BAND_UNIT==null && other.getNET_BAND_UNIT()==null) || 
             (this.NET_BAND_UNIT!=null &&
              this.NET_BAND_UNIT.equals(other.getNET_BAND_UNIT()))) &&
            ((this.NET_LG==null && other.getNET_LG()==null) || 
             (this.NET_LG!=null &&
              this.NET_LG.equals(other.getNET_LG()))) &&
            ((this.NET_NAME==null && other.getNET_NAME()==null) || 
             (this.NET_NAME!=null &&
              this.NET_NAME.equals(other.getNET_NAME()))) &&
            ((this.NET_SAT_NAME==null && other.getNET_SAT_NAME()==null) || 
             (this.NET_SAT_NAME!=null &&
              this.NET_SAT_NAME.equals(other.getNET_SAT_NAME()))) &&
            ((this.NET_SP==null && other.getNET_SP()==null) || 
             (this.NET_SP!=null &&
              this.NET_SP.equals(other.getNET_SP()))) &&
            ((this.NET_SVN==null && other.getNET_SVN()==null) || 
             (this.NET_SVN!=null &&
              this.NET_SVN.equals(other.getNET_SVN()))) &&
            ((this.NET_TS==null && other.getNET_TS()==null) || 
             (this.NET_TS!=null &&
              this.NET_TS.equals(other.getNET_TS()))) &&
            ((this.NET_USE==null && other.getNET_USE()==null) || 
             (this.NET_USE!=null &&
              this.NET_USE.equals(other.getNET_USE()))) &&
            ((this.NEW_AREA_CODE==null && other.getNEW_AREA_CODE()==null) || 
             (this.NEW_AREA_CODE!=null &&
              this.NEW_AREA_CODE.equals(other.getNEW_AREA_CODE()))) &&
            ((this.OPERATOR==null && other.getOPERATOR()==null) || 
             (this.OPERATOR!=null &&
              this.OPERATOR.equals(other.getOPERATOR()))) &&
            ((this.ORG_GUID==null && other.getORG_GUID()==null) || 
             (this.ORG_GUID!=null &&
              this.ORG_GUID.equals(other.getORG_GUID()))) &&
            ((this.START_DATE==null && other.getSTART_DATE()==null) || 
             (this.START_DATE!=null &&
              this.START_DATE.equals(other.getSTART_DATE()))) &&
            ((this.ST_CLASS1==null && other.getST_CLASS1()==null) || 
             (this.ST_CLASS1!=null &&
              this.ST_CLASS1.equals(other.getST_CLASS1()))) &&
            ((this.TASK_GUID==null && other.getTASK_GUID()==null) || 
             (this.TASK_GUID!=null &&
              this.TASK_GUID.equals(other.getTASK_GUID())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getAPP_CODE() != null) {
            _hashCode += getAPP_CODE().hashCode();
        }
        if (getAPP_DATE() != null) {
            _hashCode += getAPP_DATE().hashCode();
        }
        if (getAPP_FTLB() != null) {
            _hashCode += getAPP_FTLB().hashCode();
        }
        if (getAPP_FTLE() != null) {
            _hashCode += getAPP_FTLE().hashCode();
        }
        if (getAPP_GUID() != null) {
            _hashCode += getAPP_GUID().hashCode();
        }
        if (getAPP_STATUS() != null) {
            _hashCode += getAPP_STATUS().hashCode();
        }
        if (getAPP_SUB_TYPE() != null) {
            _hashCode += getAPP_SUB_TYPE().hashCode();
        }
        if (getAPP_TYPE() != null) {
            _hashCode += getAPP_TYPE().hashCode();
        }
        if (getBUSINESS_FLAG() != null) {
            _hashCode += getBUSINESS_FLAG().hashCode();
        }
        if (getCONFIRM_DATE() != null) {
            _hashCode += getCONFIRM_DATE().hashCode();
        }
        if (getCONTACT_GUID() != null) {
            _hashCode += getCONTACT_GUID().hashCode();
        }
        if (getCREATE_TIME() != null) {
            _hashCode += getCREATE_TIME().hashCode();
        }
        if (getEXPIRED_DATE() != null) {
            _hashCode += getEXPIRED_DATE().hashCode();
        }
        if (getFEE_CONTACT_GUID() != null) {
            _hashCode += getFEE_CONTACT_GUID().hashCode();
        }
        if (getFEE_GUID() != null) {
            _hashCode += getFEE_GUID().hashCode();
        }
        if (getIS_NET() != null) {
            _hashCode += getIS_NET().hashCode();
        }
        if (getMEMO() != null) {
            _hashCode += getMEMO().hashCode();
        }
        if (getMODIFIED_TIME() != null) {
            _hashCode += getMODIFIED_TIME().hashCode();
        }
        if (getNET_AREA() != null) {
            _hashCode += getNET_AREA().hashCode();
        }
        if (getNET_BAND() != null) {
            _hashCode += getNET_BAND().hashCode();
        }
        if (getNET_BAND_UNIT() != null) {
            _hashCode += getNET_BAND_UNIT().hashCode();
        }
        if (getNET_LG() != null) {
            _hashCode += getNET_LG().hashCode();
        }
        if (getNET_NAME() != null) {
            _hashCode += getNET_NAME().hashCode();
        }
        if (getNET_SAT_NAME() != null) {
            _hashCode += getNET_SAT_NAME().hashCode();
        }
        if (getNET_SP() != null) {
            _hashCode += getNET_SP().hashCode();
        }
        if (getNET_SVN() != null) {
            _hashCode += getNET_SVN().hashCode();
        }
        if (getNET_TS() != null) {
            _hashCode += getNET_TS().hashCode();
        }
        if (getNET_USE() != null) {
            _hashCode += getNET_USE().hashCode();
        }
        if (getNEW_AREA_CODE() != null) {
            _hashCode += getNEW_AREA_CODE().hashCode();
        }
        if (getOPERATOR() != null) {
            _hashCode += getOPERATOR().hashCode();
        }
        if (getORG_GUID() != null) {
            _hashCode += getORG_GUID().hashCode();
        }
        if (getSTART_DATE() != null) {
            _hashCode += getSTART_DATE().hashCode();
        }
        if (getST_CLASS1() != null) {
            _hashCode += getST_CLASS1().hashCode();
        }
        if (getTASK_GUID() != null) {
            _hashCode += getTASK_GUID().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(FS_APP.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FS_APP"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("APP_CODE");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "APP_CODE"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("APP_DATE");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "APP_DATE"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "dateTime"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("APP_FTLB");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "APP_FTLB"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "dateTime"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("APP_FTLE");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "APP_FTLE"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "dateTime"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("APP_GUID");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "APP_GUID"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("APP_STATUS");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "APP_STATUS"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("APP_SUB_TYPE");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "APP_SUB_TYPE"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("APP_TYPE");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "APP_TYPE"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("BUSINESS_FLAG");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "BUSINESS_FLAG"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("CONFIRM_DATE");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "CONFIRM_DATE"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "dateTime"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("CONTACT_GUID");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "CONTACT_GUID"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("CREATE_TIME");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "CREATE_TIME"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "dateTime"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("EXPIRED_DATE");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "EXPIRED_DATE"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "dateTime"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FEE_CONTACT_GUID");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FEE_CONTACT_GUID"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FEE_GUID");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FEE_GUID"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("IS_NET");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "IS_NET"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("MEMO");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "MEMO"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("MODIFIED_TIME");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "MODIFIED_TIME"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "dateTime"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("NET_AREA");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "NET_AREA"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("NET_BAND");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "NET_BAND"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("NET_BAND_UNIT");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "NET_BAND_UNIT"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("NET_LG");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "NET_LG"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("NET_NAME");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "NET_NAME"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("NET_SAT_NAME");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "NET_SAT_NAME"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("NET_SP");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "NET_SP"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("NET_SVN");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "NET_SVN"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("NET_TS");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "NET_TS"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("NET_USE");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "NET_USE"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("NEW_AREA_CODE");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "NEW_AREA_CODE"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("OPERATOR");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "OPERATOR"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ORG_GUID");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "ORG_GUID"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("START_DATE");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "START_DATE"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "dateTime"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ST_CLASS1");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "ST_CLASS1"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("TASK_GUID");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "TASK_GUID"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
