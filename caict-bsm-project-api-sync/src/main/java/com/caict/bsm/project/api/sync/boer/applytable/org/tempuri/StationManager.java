/**
 * StationManager.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.caict.bsm.project.api.sync.boer.applytable.org.tempuri;

public interface StationManager extends javax.xml.rpc.Service {
    public String getBasicHttpBinding_IApplyTableAddress();

    IApplyTable getBasicHttpBinding_IApplyTable() throws javax.xml.rpc.ServiceException;

    IApplyTable getBasicHttpBinding_IApplyTable(java.net.URL portAddress) throws javax.xml.rpc.ServiceException;
}
