/**
 * FS_MNG_IMP_PARAMS_CONFIG.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.caict.bsm.project.api.sync.boer.bigdata.org.datacontract.schemas._2004._07.FS_ServerSolution_Data;

public class FS_MNG_IMP_PARAMS_CONFIG  implements java.io.Serializable {
    private Double ANT_ANGLE;

    private String ANT_ANGLE_PLAN;

    private Double ANT_GAIN;

    private String ANT_GAIN_PLAN;

    private Double ANT_HIGHT;

    private String ANT_HIGHT_PLAN;

    private String ANT_MANU;

    private String ANT_MANU_PLAN;

    private String ANT_MODEL;

    private String ANT_MODEL_PLAN;

    private String ANT_SIZE;

    private String ANT_SIZE_PLAN;

    private String ANT_TYPE;

    private String ANT_TYPE_PLAN;

    private Double AT_3DB;

    private String AT_3DB_PLAN;

    private Double AT_ANG;

    private String AT_ANG_PLAN;

    private Double AT_EANG;

    private String AT_EANG_PLAN;

    private Double AT_RANG;

    private String AT_RANG_PLAN;

    private String DATA_TYPE;

    private Double DN_FREQ_EFB_RANGE;

    private String DN_FREQ_EFB_RANGE_PLAN;

    private Double DN_FREQ_EFE_RANGE;

    private String DN_FREQ_EFE_RANGE_PLAN;

    private Double DN_FREQ_RFB_RANGE;

    private String DN_FREQ_RFB_RANGE_PLAN;

    private Double DN_FREQ_RFE_RANGE;

    private String DN_FREQ_RFE_RANGE_PLAN;

    private Double DN_NET_BAND;

    private String DN_NET_BAND_PLAN;

    private java.util.Calendar ENABLE_DATE;

    private String ENABLE_DATE_PLAN;

    private String EQU_AUTH;

    private String EQU_AUTH_PLAN;

    private String EQU_MANU;

    private String EQU_MANU_PLAN;

    private String EQU_MODEL;

    private String EQU_MODEL_PLAN;

    private String EQU_PF;

    private String EQU_PF_PLAN;

    private Double EQU_POW;

    private String EQU_POW_PLAN;

    private Double EQU_RNQ;

    private String EQU_RNQ_PLAN;

    private Double EQU_TL;

    private String EQU_TL_PLAN;

    private Double FEED_LOSE;

    private String FEED_LOSE_PLAN;

    private Double FREQ_BAND;

    private String FREQ_BAND_PLAN;

    private String FREQ_MOD;

    private String FREQ_MOD_PLAN;

    private String GUID;

    private String MODEL_NAME;

    private String ORG_TYPE;

    private Double STAT_AT;

    private String STAT_AT_PLAN;

    private String STAT_TYPE;

    private String STAT_TYPE_PLAN;

    private String ST_USER_AREA;

    private String ST_USER_AREA_PLAN;

    private Double UP_FREQ_EFB_RANGE;

    private String UP_FREQ_EFB_RANGE_PLAN;

    private Double UP_FREQ_EFE_RANGE;

    private String UP_FREQ_EFE_RANGE_PLAN;

    private Double UP_FREQ_RFB_RANGE;

    private String UP_FREQ_RFB_RANGE_PLAN;

    private Double UP_FREQ_RFE_RANGE;

    private String UP_FREQ_RFE_RANGE_PLAN;

    private Double UP_NET_BAND;

    private String UP_NET_BAND_PLAN;

    public FS_MNG_IMP_PARAMS_CONFIG() {
    }

    public FS_MNG_IMP_PARAMS_CONFIG(
           Double ANT_ANGLE,
           String ANT_ANGLE_PLAN,
           Double ANT_GAIN,
           String ANT_GAIN_PLAN,
           Double ANT_HIGHT,
           String ANT_HIGHT_PLAN,
           String ANT_MANU,
           String ANT_MANU_PLAN,
           String ANT_MODEL,
           String ANT_MODEL_PLAN,
           String ANT_SIZE,
           String ANT_SIZE_PLAN,
           String ANT_TYPE,
           String ANT_TYPE_PLAN,
           Double AT_3DB,
           String AT_3DB_PLAN,
           Double AT_ANG,
           String AT_ANG_PLAN,
           Double AT_EANG,
           String AT_EANG_PLAN,
           Double AT_RANG,
           String AT_RANG_PLAN,
           String DATA_TYPE,
           Double DN_FREQ_EFB_RANGE,
           String DN_FREQ_EFB_RANGE_PLAN,
           Double DN_FREQ_EFE_RANGE,
           String DN_FREQ_EFE_RANGE_PLAN,
           Double DN_FREQ_RFB_RANGE,
           String DN_FREQ_RFB_RANGE_PLAN,
           Double DN_FREQ_RFE_RANGE,
           String DN_FREQ_RFE_RANGE_PLAN,
           Double DN_NET_BAND,
           String DN_NET_BAND_PLAN,
           java.util.Calendar ENABLE_DATE,
           String ENABLE_DATE_PLAN,
           String EQU_AUTH,
           String EQU_AUTH_PLAN,
           String EQU_MANU,
           String EQU_MANU_PLAN,
           String EQU_MODEL,
           String EQU_MODEL_PLAN,
           String EQU_PF,
           String EQU_PF_PLAN,
           Double EQU_POW,
           String EQU_POW_PLAN,
           Double EQU_RNQ,
           String EQU_RNQ_PLAN,
           Double EQU_TL,
           String EQU_TL_PLAN,
           Double FEED_LOSE,
           String FEED_LOSE_PLAN,
           Double FREQ_BAND,
           String FREQ_BAND_PLAN,
           String FREQ_MOD,
           String FREQ_MOD_PLAN,
           String GUID,
           String MODEL_NAME,
           String ORG_TYPE,
           Double STAT_AT,
           String STAT_AT_PLAN,
           String STAT_TYPE,
           String STAT_TYPE_PLAN,
           String ST_USER_AREA,
           String ST_USER_AREA_PLAN,
           Double UP_FREQ_EFB_RANGE,
           String UP_FREQ_EFB_RANGE_PLAN,
           Double UP_FREQ_EFE_RANGE,
           String UP_FREQ_EFE_RANGE_PLAN,
           Double UP_FREQ_RFB_RANGE,
           String UP_FREQ_RFB_RANGE_PLAN,
           Double UP_FREQ_RFE_RANGE,
           String UP_FREQ_RFE_RANGE_PLAN,
           Double UP_NET_BAND,
           String UP_NET_BAND_PLAN) {
           this.ANT_ANGLE = ANT_ANGLE;
           this.ANT_ANGLE_PLAN = ANT_ANGLE_PLAN;
           this.ANT_GAIN = ANT_GAIN;
           this.ANT_GAIN_PLAN = ANT_GAIN_PLAN;
           this.ANT_HIGHT = ANT_HIGHT;
           this.ANT_HIGHT_PLAN = ANT_HIGHT_PLAN;
           this.ANT_MANU = ANT_MANU;
           this.ANT_MANU_PLAN = ANT_MANU_PLAN;
           this.ANT_MODEL = ANT_MODEL;
           this.ANT_MODEL_PLAN = ANT_MODEL_PLAN;
           this.ANT_SIZE = ANT_SIZE;
           this.ANT_SIZE_PLAN = ANT_SIZE_PLAN;
           this.ANT_TYPE = ANT_TYPE;
           this.ANT_TYPE_PLAN = ANT_TYPE_PLAN;
           this.AT_3DB = AT_3DB;
           this.AT_3DB_PLAN = AT_3DB_PLAN;
           this.AT_ANG = AT_ANG;
           this.AT_ANG_PLAN = AT_ANG_PLAN;
           this.AT_EANG = AT_EANG;
           this.AT_EANG_PLAN = AT_EANG_PLAN;
           this.AT_RANG = AT_RANG;
           this.AT_RANG_PLAN = AT_RANG_PLAN;
           this.DATA_TYPE = DATA_TYPE;
           this.DN_FREQ_EFB_RANGE = DN_FREQ_EFB_RANGE;
           this.DN_FREQ_EFB_RANGE_PLAN = DN_FREQ_EFB_RANGE_PLAN;
           this.DN_FREQ_EFE_RANGE = DN_FREQ_EFE_RANGE;
           this.DN_FREQ_EFE_RANGE_PLAN = DN_FREQ_EFE_RANGE_PLAN;
           this.DN_FREQ_RFB_RANGE = DN_FREQ_RFB_RANGE;
           this.DN_FREQ_RFB_RANGE_PLAN = DN_FREQ_RFB_RANGE_PLAN;
           this.DN_FREQ_RFE_RANGE = DN_FREQ_RFE_RANGE;
           this.DN_FREQ_RFE_RANGE_PLAN = DN_FREQ_RFE_RANGE_PLAN;
           this.DN_NET_BAND = DN_NET_BAND;
           this.DN_NET_BAND_PLAN = DN_NET_BAND_PLAN;
           this.ENABLE_DATE = ENABLE_DATE;
           this.ENABLE_DATE_PLAN = ENABLE_DATE_PLAN;
           this.EQU_AUTH = EQU_AUTH;
           this.EQU_AUTH_PLAN = EQU_AUTH_PLAN;
           this.EQU_MANU = EQU_MANU;
           this.EQU_MANU_PLAN = EQU_MANU_PLAN;
           this.EQU_MODEL = EQU_MODEL;
           this.EQU_MODEL_PLAN = EQU_MODEL_PLAN;
           this.EQU_PF = EQU_PF;
           this.EQU_PF_PLAN = EQU_PF_PLAN;
           this.EQU_POW = EQU_POW;
           this.EQU_POW_PLAN = EQU_POW_PLAN;
           this.EQU_RNQ = EQU_RNQ;
           this.EQU_RNQ_PLAN = EQU_RNQ_PLAN;
           this.EQU_TL = EQU_TL;
           this.EQU_TL_PLAN = EQU_TL_PLAN;
           this.FEED_LOSE = FEED_LOSE;
           this.FEED_LOSE_PLAN = FEED_LOSE_PLAN;
           this.FREQ_BAND = FREQ_BAND;
           this.FREQ_BAND_PLAN = FREQ_BAND_PLAN;
           this.FREQ_MOD = FREQ_MOD;
           this.FREQ_MOD_PLAN = FREQ_MOD_PLAN;
           this.GUID = GUID;
           this.MODEL_NAME = MODEL_NAME;
           this.ORG_TYPE = ORG_TYPE;
           this.STAT_AT = STAT_AT;
           this.STAT_AT_PLAN = STAT_AT_PLAN;
           this.STAT_TYPE = STAT_TYPE;
           this.STAT_TYPE_PLAN = STAT_TYPE_PLAN;
           this.ST_USER_AREA = ST_USER_AREA;
           this.ST_USER_AREA_PLAN = ST_USER_AREA_PLAN;
           this.UP_FREQ_EFB_RANGE = UP_FREQ_EFB_RANGE;
           this.UP_FREQ_EFB_RANGE_PLAN = UP_FREQ_EFB_RANGE_PLAN;
           this.UP_FREQ_EFE_RANGE = UP_FREQ_EFE_RANGE;
           this.UP_FREQ_EFE_RANGE_PLAN = UP_FREQ_EFE_RANGE_PLAN;
           this.UP_FREQ_RFB_RANGE = UP_FREQ_RFB_RANGE;
           this.UP_FREQ_RFB_RANGE_PLAN = UP_FREQ_RFB_RANGE_PLAN;
           this.UP_FREQ_RFE_RANGE = UP_FREQ_RFE_RANGE;
           this.UP_FREQ_RFE_RANGE_PLAN = UP_FREQ_RFE_RANGE_PLAN;
           this.UP_NET_BAND = UP_NET_BAND;
           this.UP_NET_BAND_PLAN = UP_NET_BAND_PLAN;
    }


    /**
     * Gets the ANT_ANGLE value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return ANT_ANGLE
     */
    public Double getANT_ANGLE() {
        return ANT_ANGLE;
    }


    /**
     * Sets the ANT_ANGLE value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param ANT_ANGLE
     */
    public void setANT_ANGLE(Double ANT_ANGLE) {
        this.ANT_ANGLE = ANT_ANGLE;
    }


    /**
     * Gets the ANT_ANGLE_PLAN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return ANT_ANGLE_PLAN
     */
    public String getANT_ANGLE_PLAN() {
        return ANT_ANGLE_PLAN;
    }


    /**
     * Sets the ANT_ANGLE_PLAN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param ANT_ANGLE_PLAN
     */
    public void setANT_ANGLE_PLAN(String ANT_ANGLE_PLAN) {
        this.ANT_ANGLE_PLAN = ANT_ANGLE_PLAN;
    }


    /**
     * Gets the ANT_GAIN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return ANT_GAIN
     */
    public Double getANT_GAIN() {
        return ANT_GAIN;
    }


    /**
     * Sets the ANT_GAIN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param ANT_GAIN
     */
    public void setANT_GAIN(Double ANT_GAIN) {
        this.ANT_GAIN = ANT_GAIN;
    }


    /**
     * Gets the ANT_GAIN_PLAN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return ANT_GAIN_PLAN
     */
    public String getANT_GAIN_PLAN() {
        return ANT_GAIN_PLAN;
    }


    /**
     * Sets the ANT_GAIN_PLAN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param ANT_GAIN_PLAN
     */
    public void setANT_GAIN_PLAN(String ANT_GAIN_PLAN) {
        this.ANT_GAIN_PLAN = ANT_GAIN_PLAN;
    }


    /**
     * Gets the ANT_HIGHT value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return ANT_HIGHT
     */
    public Double getANT_HIGHT() {
        return ANT_HIGHT;
    }


    /**
     * Sets the ANT_HIGHT value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param ANT_HIGHT
     */
    public void setANT_HIGHT(Double ANT_HIGHT) {
        this.ANT_HIGHT = ANT_HIGHT;
    }


    /**
     * Gets the ANT_HIGHT_PLAN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return ANT_HIGHT_PLAN
     */
    public String getANT_HIGHT_PLAN() {
        return ANT_HIGHT_PLAN;
    }


    /**
     * Sets the ANT_HIGHT_PLAN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param ANT_HIGHT_PLAN
     */
    public void setANT_HIGHT_PLAN(String ANT_HIGHT_PLAN) {
        this.ANT_HIGHT_PLAN = ANT_HIGHT_PLAN;
    }


    /**
     * Gets the ANT_MANU value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return ANT_MANU
     */
    public String getANT_MANU() {
        return ANT_MANU;
    }


    /**
     * Sets the ANT_MANU value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param ANT_MANU
     */
    public void setANT_MANU(String ANT_MANU) {
        this.ANT_MANU = ANT_MANU;
    }


    /**
     * Gets the ANT_MANU_PLAN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return ANT_MANU_PLAN
     */
    public String getANT_MANU_PLAN() {
        return ANT_MANU_PLAN;
    }


    /**
     * Sets the ANT_MANU_PLAN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param ANT_MANU_PLAN
     */
    public void setANT_MANU_PLAN(String ANT_MANU_PLAN) {
        this.ANT_MANU_PLAN = ANT_MANU_PLAN;
    }


    /**
     * Gets the ANT_MODEL value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return ANT_MODEL
     */
    public String getANT_MODEL() {
        return ANT_MODEL;
    }


    /**
     * Sets the ANT_MODEL value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param ANT_MODEL
     */
    public void setANT_MODEL(String ANT_MODEL) {
        this.ANT_MODEL = ANT_MODEL;
    }


    /**
     * Gets the ANT_MODEL_PLAN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return ANT_MODEL_PLAN
     */
    public String getANT_MODEL_PLAN() {
        return ANT_MODEL_PLAN;
    }


    /**
     * Sets the ANT_MODEL_PLAN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param ANT_MODEL_PLAN
     */
    public void setANT_MODEL_PLAN(String ANT_MODEL_PLAN) {
        this.ANT_MODEL_PLAN = ANT_MODEL_PLAN;
    }


    /**
     * Gets the ANT_SIZE value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return ANT_SIZE
     */
    public String getANT_SIZE() {
        return ANT_SIZE;
    }


    /**
     * Sets the ANT_SIZE value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param ANT_SIZE
     */
    public void setANT_SIZE(String ANT_SIZE) {
        this.ANT_SIZE = ANT_SIZE;
    }


    /**
     * Gets the ANT_SIZE_PLAN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return ANT_SIZE_PLAN
     */
    public String getANT_SIZE_PLAN() {
        return ANT_SIZE_PLAN;
    }


    /**
     * Sets the ANT_SIZE_PLAN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param ANT_SIZE_PLAN
     */
    public void setANT_SIZE_PLAN(String ANT_SIZE_PLAN) {
        this.ANT_SIZE_PLAN = ANT_SIZE_PLAN;
    }


    /**
     * Gets the ANT_TYPE value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return ANT_TYPE
     */
    public String getANT_TYPE() {
        return ANT_TYPE;
    }


    /**
     * Sets the ANT_TYPE value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param ANT_TYPE
     */
    public void setANT_TYPE(String ANT_TYPE) {
        this.ANT_TYPE = ANT_TYPE;
    }


    /**
     * Gets the ANT_TYPE_PLAN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return ANT_TYPE_PLAN
     */
    public String getANT_TYPE_PLAN() {
        return ANT_TYPE_PLAN;
    }


    /**
     * Sets the ANT_TYPE_PLAN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param ANT_TYPE_PLAN
     */
    public void setANT_TYPE_PLAN(String ANT_TYPE_PLAN) {
        this.ANT_TYPE_PLAN = ANT_TYPE_PLAN;
    }


    /**
     * Gets the AT_3DB value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return AT_3DB
     */
    public Double getAT_3DB() {
        return AT_3DB;
    }


    /**
     * Sets the AT_3DB value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param AT_3DB
     */
    public void setAT_3DB(Double AT_3DB) {
        this.AT_3DB = AT_3DB;
    }


    /**
     * Gets the AT_3DB_PLAN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return AT_3DB_PLAN
     */
    public String getAT_3DB_PLAN() {
        return AT_3DB_PLAN;
    }


    /**
     * Sets the AT_3DB_PLAN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param AT_3DB_PLAN
     */
    public void setAT_3DB_PLAN(String AT_3DB_PLAN) {
        this.AT_3DB_PLAN = AT_3DB_PLAN;
    }


    /**
     * Gets the AT_ANG value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return AT_ANG
     */
    public Double getAT_ANG() {
        return AT_ANG;
    }


    /**
     * Sets the AT_ANG value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param AT_ANG
     */
    public void setAT_ANG(Double AT_ANG) {
        this.AT_ANG = AT_ANG;
    }


    /**
     * Gets the AT_ANG_PLAN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return AT_ANG_PLAN
     */
    public String getAT_ANG_PLAN() {
        return AT_ANG_PLAN;
    }


    /**
     * Sets the AT_ANG_PLAN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param AT_ANG_PLAN
     */
    public void setAT_ANG_PLAN(String AT_ANG_PLAN) {
        this.AT_ANG_PLAN = AT_ANG_PLAN;
    }


    /**
     * Gets the AT_EANG value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return AT_EANG
     */
    public Double getAT_EANG() {
        return AT_EANG;
    }


    /**
     * Sets the AT_EANG value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param AT_EANG
     */
    public void setAT_EANG(Double AT_EANG) {
        this.AT_EANG = AT_EANG;
    }


    /**
     * Gets the AT_EANG_PLAN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return AT_EANG_PLAN
     */
    public String getAT_EANG_PLAN() {
        return AT_EANG_PLAN;
    }


    /**
     * Sets the AT_EANG_PLAN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param AT_EANG_PLAN
     */
    public void setAT_EANG_PLAN(String AT_EANG_PLAN) {
        this.AT_EANG_PLAN = AT_EANG_PLAN;
    }


    /**
     * Gets the AT_RANG value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return AT_RANG
     */
    public Double getAT_RANG() {
        return AT_RANG;
    }


    /**
     * Sets the AT_RANG value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param AT_RANG
     */
    public void setAT_RANG(Double AT_RANG) {
        this.AT_RANG = AT_RANG;
    }


    /**
     * Gets the AT_RANG_PLAN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return AT_RANG_PLAN
     */
    public String getAT_RANG_PLAN() {
        return AT_RANG_PLAN;
    }


    /**
     * Sets the AT_RANG_PLAN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param AT_RANG_PLAN
     */
    public void setAT_RANG_PLAN(String AT_RANG_PLAN) {
        this.AT_RANG_PLAN = AT_RANG_PLAN;
    }


    /**
     * Gets the DATA_TYPE value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return DATA_TYPE
     */
    public String getDATA_TYPE() {
        return DATA_TYPE;
    }


    /**
     * Sets the DATA_TYPE value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param DATA_TYPE
     */
    public void setDATA_TYPE(String DATA_TYPE) {
        this.DATA_TYPE = DATA_TYPE;
    }


    /**
     * Gets the DN_FREQ_EFB_RANGE value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return DN_FREQ_EFB_RANGE
     */
    public Double getDN_FREQ_EFB_RANGE() {
        return DN_FREQ_EFB_RANGE;
    }


    /**
     * Sets the DN_FREQ_EFB_RANGE value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param DN_FREQ_EFB_RANGE
     */
    public void setDN_FREQ_EFB_RANGE(Double DN_FREQ_EFB_RANGE) {
        this.DN_FREQ_EFB_RANGE = DN_FREQ_EFB_RANGE;
    }


    /**
     * Gets the DN_FREQ_EFB_RANGE_PLAN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return DN_FREQ_EFB_RANGE_PLAN
     */
    public String getDN_FREQ_EFB_RANGE_PLAN() {
        return DN_FREQ_EFB_RANGE_PLAN;
    }


    /**
     * Sets the DN_FREQ_EFB_RANGE_PLAN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param DN_FREQ_EFB_RANGE_PLAN
     */
    public void setDN_FREQ_EFB_RANGE_PLAN(String DN_FREQ_EFB_RANGE_PLAN) {
        this.DN_FREQ_EFB_RANGE_PLAN = DN_FREQ_EFB_RANGE_PLAN;
    }


    /**
     * Gets the DN_FREQ_EFE_RANGE value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return DN_FREQ_EFE_RANGE
     */
    public Double getDN_FREQ_EFE_RANGE() {
        return DN_FREQ_EFE_RANGE;
    }


    /**
     * Sets the DN_FREQ_EFE_RANGE value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param DN_FREQ_EFE_RANGE
     */
    public void setDN_FREQ_EFE_RANGE(Double DN_FREQ_EFE_RANGE) {
        this.DN_FREQ_EFE_RANGE = DN_FREQ_EFE_RANGE;
    }


    /**
     * Gets the DN_FREQ_EFE_RANGE_PLAN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return DN_FREQ_EFE_RANGE_PLAN
     */
    public String getDN_FREQ_EFE_RANGE_PLAN() {
        return DN_FREQ_EFE_RANGE_PLAN;
    }


    /**
     * Sets the DN_FREQ_EFE_RANGE_PLAN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param DN_FREQ_EFE_RANGE_PLAN
     */
    public void setDN_FREQ_EFE_RANGE_PLAN(String DN_FREQ_EFE_RANGE_PLAN) {
        this.DN_FREQ_EFE_RANGE_PLAN = DN_FREQ_EFE_RANGE_PLAN;
    }


    /**
     * Gets the DN_FREQ_RFB_RANGE value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return DN_FREQ_RFB_RANGE
     */
    public Double getDN_FREQ_RFB_RANGE() {
        return DN_FREQ_RFB_RANGE;
    }


    /**
     * Sets the DN_FREQ_RFB_RANGE value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param DN_FREQ_RFB_RANGE
     */
    public void setDN_FREQ_RFB_RANGE(Double DN_FREQ_RFB_RANGE) {
        this.DN_FREQ_RFB_RANGE = DN_FREQ_RFB_RANGE;
    }


    /**
     * Gets the DN_FREQ_RFB_RANGE_PLAN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return DN_FREQ_RFB_RANGE_PLAN
     */
    public String getDN_FREQ_RFB_RANGE_PLAN() {
        return DN_FREQ_RFB_RANGE_PLAN;
    }


    /**
     * Sets the DN_FREQ_RFB_RANGE_PLAN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param DN_FREQ_RFB_RANGE_PLAN
     */
    public void setDN_FREQ_RFB_RANGE_PLAN(String DN_FREQ_RFB_RANGE_PLAN) {
        this.DN_FREQ_RFB_RANGE_PLAN = DN_FREQ_RFB_RANGE_PLAN;
    }


    /**
     * Gets the DN_FREQ_RFE_RANGE value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return DN_FREQ_RFE_RANGE
     */
    public Double getDN_FREQ_RFE_RANGE() {
        return DN_FREQ_RFE_RANGE;
    }


    /**
     * Sets the DN_FREQ_RFE_RANGE value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param DN_FREQ_RFE_RANGE
     */
    public void setDN_FREQ_RFE_RANGE(Double DN_FREQ_RFE_RANGE) {
        this.DN_FREQ_RFE_RANGE = DN_FREQ_RFE_RANGE;
    }


    /**
     * Gets the DN_FREQ_RFE_RANGE_PLAN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return DN_FREQ_RFE_RANGE_PLAN
     */
    public String getDN_FREQ_RFE_RANGE_PLAN() {
        return DN_FREQ_RFE_RANGE_PLAN;
    }


    /**
     * Sets the DN_FREQ_RFE_RANGE_PLAN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param DN_FREQ_RFE_RANGE_PLAN
     */
    public void setDN_FREQ_RFE_RANGE_PLAN(String DN_FREQ_RFE_RANGE_PLAN) {
        this.DN_FREQ_RFE_RANGE_PLAN = DN_FREQ_RFE_RANGE_PLAN;
    }


    /**
     * Gets the DN_NET_BAND value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return DN_NET_BAND
     */
    public Double getDN_NET_BAND() {
        return DN_NET_BAND;
    }


    /**
     * Sets the DN_NET_BAND value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param DN_NET_BAND
     */
    public void setDN_NET_BAND(Double DN_NET_BAND) {
        this.DN_NET_BAND = DN_NET_BAND;
    }


    /**
     * Gets the DN_NET_BAND_PLAN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return DN_NET_BAND_PLAN
     */
    public String getDN_NET_BAND_PLAN() {
        return DN_NET_BAND_PLAN;
    }


    /**
     * Sets the DN_NET_BAND_PLAN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param DN_NET_BAND_PLAN
     */
    public void setDN_NET_BAND_PLAN(String DN_NET_BAND_PLAN) {
        this.DN_NET_BAND_PLAN = DN_NET_BAND_PLAN;
    }


    /**
     * Gets the ENABLE_DATE value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return ENABLE_DATE
     */
    public java.util.Calendar getENABLE_DATE() {
        return ENABLE_DATE;
    }


    /**
     * Sets the ENABLE_DATE value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param ENABLE_DATE
     */
    public void setENABLE_DATE(java.util.Calendar ENABLE_DATE) {
        this.ENABLE_DATE = ENABLE_DATE;
    }


    /**
     * Gets the ENABLE_DATE_PLAN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return ENABLE_DATE_PLAN
     */
    public String getENABLE_DATE_PLAN() {
        return ENABLE_DATE_PLAN;
    }


    /**
     * Sets the ENABLE_DATE_PLAN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param ENABLE_DATE_PLAN
     */
    public void setENABLE_DATE_PLAN(String ENABLE_DATE_PLAN) {
        this.ENABLE_DATE_PLAN = ENABLE_DATE_PLAN;
    }


    /**
     * Gets the EQU_AUTH value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return EQU_AUTH
     */
    public String getEQU_AUTH() {
        return EQU_AUTH;
    }


    /**
     * Sets the EQU_AUTH value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param EQU_AUTH
     */
    public void setEQU_AUTH(String EQU_AUTH) {
        this.EQU_AUTH = EQU_AUTH;
    }


    /**
     * Gets the EQU_AUTH_PLAN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return EQU_AUTH_PLAN
     */
    public String getEQU_AUTH_PLAN() {
        return EQU_AUTH_PLAN;
    }


    /**
     * Sets the EQU_AUTH_PLAN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param EQU_AUTH_PLAN
     */
    public void setEQU_AUTH_PLAN(String EQU_AUTH_PLAN) {
        this.EQU_AUTH_PLAN = EQU_AUTH_PLAN;
    }


    /**
     * Gets the EQU_MANU value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return EQU_MANU
     */
    public String getEQU_MANU() {
        return EQU_MANU;
    }


    /**
     * Sets the EQU_MANU value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param EQU_MANU
     */
    public void setEQU_MANU(String EQU_MANU) {
        this.EQU_MANU = EQU_MANU;
    }


    /**
     * Gets the EQU_MANU_PLAN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return EQU_MANU_PLAN
     */
    public String getEQU_MANU_PLAN() {
        return EQU_MANU_PLAN;
    }


    /**
     * Sets the EQU_MANU_PLAN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param EQU_MANU_PLAN
     */
    public void setEQU_MANU_PLAN(String EQU_MANU_PLAN) {
        this.EQU_MANU_PLAN = EQU_MANU_PLAN;
    }


    /**
     * Gets the EQU_MODEL value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return EQU_MODEL
     */
    public String getEQU_MODEL() {
        return EQU_MODEL;
    }


    /**
     * Sets the EQU_MODEL value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param EQU_MODEL
     */
    public void setEQU_MODEL(String EQU_MODEL) {
        this.EQU_MODEL = EQU_MODEL;
    }


    /**
     * Gets the EQU_MODEL_PLAN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return EQU_MODEL_PLAN
     */
    public String getEQU_MODEL_PLAN() {
        return EQU_MODEL_PLAN;
    }


    /**
     * Sets the EQU_MODEL_PLAN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param EQU_MODEL_PLAN
     */
    public void setEQU_MODEL_PLAN(String EQU_MODEL_PLAN) {
        this.EQU_MODEL_PLAN = EQU_MODEL_PLAN;
    }


    /**
     * Gets the EQU_PF value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return EQU_PF
     */
    public String getEQU_PF() {
        return EQU_PF;
    }


    /**
     * Sets the EQU_PF value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param EQU_PF
     */
    public void setEQU_PF(String EQU_PF) {
        this.EQU_PF = EQU_PF;
    }


    /**
     * Gets the EQU_PF_PLAN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return EQU_PF_PLAN
     */
    public String getEQU_PF_PLAN() {
        return EQU_PF_PLAN;
    }


    /**
     * Sets the EQU_PF_PLAN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param EQU_PF_PLAN
     */
    public void setEQU_PF_PLAN(String EQU_PF_PLAN) {
        this.EQU_PF_PLAN = EQU_PF_PLAN;
    }


    /**
     * Gets the EQU_POW value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return EQU_POW
     */
    public Double getEQU_POW() {
        return EQU_POW;
    }


    /**
     * Sets the EQU_POW value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param EQU_POW
     */
    public void setEQU_POW(Double EQU_POW) {
        this.EQU_POW = EQU_POW;
    }


    /**
     * Gets the EQU_POW_PLAN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return EQU_POW_PLAN
     */
    public String getEQU_POW_PLAN() {
        return EQU_POW_PLAN;
    }


    /**
     * Sets the EQU_POW_PLAN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param EQU_POW_PLAN
     */
    public void setEQU_POW_PLAN(String EQU_POW_PLAN) {
        this.EQU_POW_PLAN = EQU_POW_PLAN;
    }


    /**
     * Gets the EQU_RNQ value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return EQU_RNQ
     */
    public Double getEQU_RNQ() {
        return EQU_RNQ;
    }


    /**
     * Sets the EQU_RNQ value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param EQU_RNQ
     */
    public void setEQU_RNQ(Double EQU_RNQ) {
        this.EQU_RNQ = EQU_RNQ;
    }


    /**
     * Gets the EQU_RNQ_PLAN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return EQU_RNQ_PLAN
     */
    public String getEQU_RNQ_PLAN() {
        return EQU_RNQ_PLAN;
    }


    /**
     * Sets the EQU_RNQ_PLAN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param EQU_RNQ_PLAN
     */
    public void setEQU_RNQ_PLAN(String EQU_RNQ_PLAN) {
        this.EQU_RNQ_PLAN = EQU_RNQ_PLAN;
    }


    /**
     * Gets the EQU_TL value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return EQU_TL
     */
    public Double getEQU_TL() {
        return EQU_TL;
    }


    /**
     * Sets the EQU_TL value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param EQU_TL
     */
    public void setEQU_TL(Double EQU_TL) {
        this.EQU_TL = EQU_TL;
    }


    /**
     * Gets the EQU_TL_PLAN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return EQU_TL_PLAN
     */
    public String getEQU_TL_PLAN() {
        return EQU_TL_PLAN;
    }


    /**
     * Sets the EQU_TL_PLAN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param EQU_TL_PLAN
     */
    public void setEQU_TL_PLAN(String EQU_TL_PLAN) {
        this.EQU_TL_PLAN = EQU_TL_PLAN;
    }


    /**
     * Gets the FEED_LOSE value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return FEED_LOSE
     */
    public Double getFEED_LOSE() {
        return FEED_LOSE;
    }


    /**
     * Sets the FEED_LOSE value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param FEED_LOSE
     */
    public void setFEED_LOSE(Double FEED_LOSE) {
        this.FEED_LOSE = FEED_LOSE;
    }


    /**
     * Gets the FEED_LOSE_PLAN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return FEED_LOSE_PLAN
     */
    public String getFEED_LOSE_PLAN() {
        return FEED_LOSE_PLAN;
    }


    /**
     * Sets the FEED_LOSE_PLAN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param FEED_LOSE_PLAN
     */
    public void setFEED_LOSE_PLAN(String FEED_LOSE_PLAN) {
        this.FEED_LOSE_PLAN = FEED_LOSE_PLAN;
    }


    /**
     * Gets the FREQ_BAND value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return FREQ_BAND
     */
    public Double getFREQ_BAND() {
        return FREQ_BAND;
    }


    /**
     * Sets the FREQ_BAND value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param FREQ_BAND
     */
    public void setFREQ_BAND(Double FREQ_BAND) {
        this.FREQ_BAND = FREQ_BAND;
    }


    /**
     * Gets the FREQ_BAND_PLAN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return FREQ_BAND_PLAN
     */
    public String getFREQ_BAND_PLAN() {
        return FREQ_BAND_PLAN;
    }


    /**
     * Sets the FREQ_BAND_PLAN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param FREQ_BAND_PLAN
     */
    public void setFREQ_BAND_PLAN(String FREQ_BAND_PLAN) {
        this.FREQ_BAND_PLAN = FREQ_BAND_PLAN;
    }


    /**
     * Gets the FREQ_MOD value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return FREQ_MOD
     */
    public String getFREQ_MOD() {
        return FREQ_MOD;
    }


    /**
     * Sets the FREQ_MOD value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param FREQ_MOD
     */
    public void setFREQ_MOD(String FREQ_MOD) {
        this.FREQ_MOD = FREQ_MOD;
    }


    /**
     * Gets the FREQ_MOD_PLAN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return FREQ_MOD_PLAN
     */
    public String getFREQ_MOD_PLAN() {
        return FREQ_MOD_PLAN;
    }


    /**
     * Sets the FREQ_MOD_PLAN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param FREQ_MOD_PLAN
     */
    public void setFREQ_MOD_PLAN(String FREQ_MOD_PLAN) {
        this.FREQ_MOD_PLAN = FREQ_MOD_PLAN;
    }


    /**
     * Gets the GUID value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return GUID
     */
    public String getGUID() {
        return GUID;
    }


    /**
     * Sets the GUID value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param GUID
     */
    public void setGUID(String GUID) {
        this.GUID = GUID;
    }


    /**
     * Gets the MODEL_NAME value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return MODEL_NAME
     */
    public String getMODEL_NAME() {
        return MODEL_NAME;
    }


    /**
     * Sets the MODEL_NAME value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param MODEL_NAME
     */
    public void setMODEL_NAME(String MODEL_NAME) {
        this.MODEL_NAME = MODEL_NAME;
    }


    /**
     * Gets the ORG_TYPE value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return ORG_TYPE
     */
    public String getORG_TYPE() {
        return ORG_TYPE;
    }


    /**
     * Sets the ORG_TYPE value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param ORG_TYPE
     */
    public void setORG_TYPE(String ORG_TYPE) {
        this.ORG_TYPE = ORG_TYPE;
    }


    /**
     * Gets the STAT_AT value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return STAT_AT
     */
    public Double getSTAT_AT() {
        return STAT_AT;
    }


    /**
     * Sets the STAT_AT value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param STAT_AT
     */
    public void setSTAT_AT(Double STAT_AT) {
        this.STAT_AT = STAT_AT;
    }


    /**
     * Gets the STAT_AT_PLAN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return STAT_AT_PLAN
     */
    public String getSTAT_AT_PLAN() {
        return STAT_AT_PLAN;
    }


    /**
     * Sets the STAT_AT_PLAN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param STAT_AT_PLAN
     */
    public void setSTAT_AT_PLAN(String STAT_AT_PLAN) {
        this.STAT_AT_PLAN = STAT_AT_PLAN;
    }


    /**
     * Gets the STAT_TYPE value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return STAT_TYPE
     */
    public String getSTAT_TYPE() {
        return STAT_TYPE;
    }


    /**
     * Sets the STAT_TYPE value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param STAT_TYPE
     */
    public void setSTAT_TYPE(String STAT_TYPE) {
        this.STAT_TYPE = STAT_TYPE;
    }


    /**
     * Gets the STAT_TYPE_PLAN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return STAT_TYPE_PLAN
     */
    public String getSTAT_TYPE_PLAN() {
        return STAT_TYPE_PLAN;
    }


    /**
     * Sets the STAT_TYPE_PLAN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param STAT_TYPE_PLAN
     */
    public void setSTAT_TYPE_PLAN(String STAT_TYPE_PLAN) {
        this.STAT_TYPE_PLAN = STAT_TYPE_PLAN;
    }


    /**
     * Gets the ST_USER_AREA value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return ST_USER_AREA
     */
    public String getST_USER_AREA() {
        return ST_USER_AREA;
    }


    /**
     * Sets the ST_USER_AREA value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param ST_USER_AREA
     */
    public void setST_USER_AREA(String ST_USER_AREA) {
        this.ST_USER_AREA = ST_USER_AREA;
    }


    /**
     * Gets the ST_USER_AREA_PLAN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return ST_USER_AREA_PLAN
     */
    public String getST_USER_AREA_PLAN() {
        return ST_USER_AREA_PLAN;
    }


    /**
     * Sets the ST_USER_AREA_PLAN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param ST_USER_AREA_PLAN
     */
    public void setST_USER_AREA_PLAN(String ST_USER_AREA_PLAN) {
        this.ST_USER_AREA_PLAN = ST_USER_AREA_PLAN;
    }


    /**
     * Gets the UP_FREQ_EFB_RANGE value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return UP_FREQ_EFB_RANGE
     */
    public Double getUP_FREQ_EFB_RANGE() {
        return UP_FREQ_EFB_RANGE;
    }


    /**
     * Sets the UP_FREQ_EFB_RANGE value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param UP_FREQ_EFB_RANGE
     */
    public void setUP_FREQ_EFB_RANGE(Double UP_FREQ_EFB_RANGE) {
        this.UP_FREQ_EFB_RANGE = UP_FREQ_EFB_RANGE;
    }


    /**
     * Gets the UP_FREQ_EFB_RANGE_PLAN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return UP_FREQ_EFB_RANGE_PLAN
     */
    public String getUP_FREQ_EFB_RANGE_PLAN() {
        return UP_FREQ_EFB_RANGE_PLAN;
    }


    /**
     * Sets the UP_FREQ_EFB_RANGE_PLAN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param UP_FREQ_EFB_RANGE_PLAN
     */
    public void setUP_FREQ_EFB_RANGE_PLAN(String UP_FREQ_EFB_RANGE_PLAN) {
        this.UP_FREQ_EFB_RANGE_PLAN = UP_FREQ_EFB_RANGE_PLAN;
    }


    /**
     * Gets the UP_FREQ_EFE_RANGE value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return UP_FREQ_EFE_RANGE
     */
    public Double getUP_FREQ_EFE_RANGE() {
        return UP_FREQ_EFE_RANGE;
    }


    /**
     * Sets the UP_FREQ_EFE_RANGE value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param UP_FREQ_EFE_RANGE
     */
    public void setUP_FREQ_EFE_RANGE(Double UP_FREQ_EFE_RANGE) {
        this.UP_FREQ_EFE_RANGE = UP_FREQ_EFE_RANGE;
    }


    /**
     * Gets the UP_FREQ_EFE_RANGE_PLAN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return UP_FREQ_EFE_RANGE_PLAN
     */
    public String getUP_FREQ_EFE_RANGE_PLAN() {
        return UP_FREQ_EFE_RANGE_PLAN;
    }


    /**
     * Sets the UP_FREQ_EFE_RANGE_PLAN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param UP_FREQ_EFE_RANGE_PLAN
     */
    public void setUP_FREQ_EFE_RANGE_PLAN(String UP_FREQ_EFE_RANGE_PLAN) {
        this.UP_FREQ_EFE_RANGE_PLAN = UP_FREQ_EFE_RANGE_PLAN;
    }


    /**
     * Gets the UP_FREQ_RFB_RANGE value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return UP_FREQ_RFB_RANGE
     */
    public Double getUP_FREQ_RFB_RANGE() {
        return UP_FREQ_RFB_RANGE;
    }


    /**
     * Sets the UP_FREQ_RFB_RANGE value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param UP_FREQ_RFB_RANGE
     */
    public void setUP_FREQ_RFB_RANGE(Double UP_FREQ_RFB_RANGE) {
        this.UP_FREQ_RFB_RANGE = UP_FREQ_RFB_RANGE;
    }


    /**
     * Gets the UP_FREQ_RFB_RANGE_PLAN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return UP_FREQ_RFB_RANGE_PLAN
     */
    public String getUP_FREQ_RFB_RANGE_PLAN() {
        return UP_FREQ_RFB_RANGE_PLAN;
    }


    /**
     * Sets the UP_FREQ_RFB_RANGE_PLAN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param UP_FREQ_RFB_RANGE_PLAN
     */
    public void setUP_FREQ_RFB_RANGE_PLAN(String UP_FREQ_RFB_RANGE_PLAN) {
        this.UP_FREQ_RFB_RANGE_PLAN = UP_FREQ_RFB_RANGE_PLAN;
    }


    /**
     * Gets the UP_FREQ_RFE_RANGE value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return UP_FREQ_RFE_RANGE
     */
    public Double getUP_FREQ_RFE_RANGE() {
        return UP_FREQ_RFE_RANGE;
    }


    /**
     * Sets the UP_FREQ_RFE_RANGE value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param UP_FREQ_RFE_RANGE
     */
    public void setUP_FREQ_RFE_RANGE(Double UP_FREQ_RFE_RANGE) {
        this.UP_FREQ_RFE_RANGE = UP_FREQ_RFE_RANGE;
    }


    /**
     * Gets the UP_FREQ_RFE_RANGE_PLAN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return UP_FREQ_RFE_RANGE_PLAN
     */
    public String getUP_FREQ_RFE_RANGE_PLAN() {
        return UP_FREQ_RFE_RANGE_PLAN;
    }


    /**
     * Sets the UP_FREQ_RFE_RANGE_PLAN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param UP_FREQ_RFE_RANGE_PLAN
     */
    public void setUP_FREQ_RFE_RANGE_PLAN(String UP_FREQ_RFE_RANGE_PLAN) {
        this.UP_FREQ_RFE_RANGE_PLAN = UP_FREQ_RFE_RANGE_PLAN;
    }


    /**
     * Gets the UP_NET_BAND value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return UP_NET_BAND
     */
    public Double getUP_NET_BAND() {
        return UP_NET_BAND;
    }


    /**
     * Sets the UP_NET_BAND value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param UP_NET_BAND
     */
    public void setUP_NET_BAND(Double UP_NET_BAND) {
        this.UP_NET_BAND = UP_NET_BAND;
    }


    /**
     * Gets the UP_NET_BAND_PLAN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @return UP_NET_BAND_PLAN
     */
    public String getUP_NET_BAND_PLAN() {
        return UP_NET_BAND_PLAN;
    }


    /**
     * Sets the UP_NET_BAND_PLAN value for this FS_MNG_IMP_PARAMS_CONFIG.
     * 
     * @param UP_NET_BAND_PLAN
     */
    public void setUP_NET_BAND_PLAN(String UP_NET_BAND_PLAN) {
        this.UP_NET_BAND_PLAN = UP_NET_BAND_PLAN;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof FS_MNG_IMP_PARAMS_CONFIG)) return false;
        FS_MNG_IMP_PARAMS_CONFIG other = (FS_MNG_IMP_PARAMS_CONFIG) obj;
        if (obj == null) return false;
        if (this == obj) return true;
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.ANT_ANGLE==null && other.getANT_ANGLE()==null) || 
             (this.ANT_ANGLE!=null &&
              this.ANT_ANGLE.equals(other.getANT_ANGLE()))) &&
            ((this.ANT_ANGLE_PLAN==null && other.getANT_ANGLE_PLAN()==null) || 
             (this.ANT_ANGLE_PLAN!=null &&
              this.ANT_ANGLE_PLAN.equals(other.getANT_ANGLE_PLAN()))) &&
            ((this.ANT_GAIN==null && other.getANT_GAIN()==null) || 
             (this.ANT_GAIN!=null &&
              this.ANT_GAIN.equals(other.getANT_GAIN()))) &&
            ((this.ANT_GAIN_PLAN==null && other.getANT_GAIN_PLAN()==null) || 
             (this.ANT_GAIN_PLAN!=null &&
              this.ANT_GAIN_PLAN.equals(other.getANT_GAIN_PLAN()))) &&
            ((this.ANT_HIGHT==null && other.getANT_HIGHT()==null) || 
             (this.ANT_HIGHT!=null &&
              this.ANT_HIGHT.equals(other.getANT_HIGHT()))) &&
            ((this.ANT_HIGHT_PLAN==null && other.getANT_HIGHT_PLAN()==null) || 
             (this.ANT_HIGHT_PLAN!=null &&
              this.ANT_HIGHT_PLAN.equals(other.getANT_HIGHT_PLAN()))) &&
            ((this.ANT_MANU==null && other.getANT_MANU()==null) || 
             (this.ANT_MANU!=null &&
              this.ANT_MANU.equals(other.getANT_MANU()))) &&
            ((this.ANT_MANU_PLAN==null && other.getANT_MANU_PLAN()==null) || 
             (this.ANT_MANU_PLAN!=null &&
              this.ANT_MANU_PLAN.equals(other.getANT_MANU_PLAN()))) &&
            ((this.ANT_MODEL==null && other.getANT_MODEL()==null) || 
             (this.ANT_MODEL!=null &&
              this.ANT_MODEL.equals(other.getANT_MODEL()))) &&
            ((this.ANT_MODEL_PLAN==null && other.getANT_MODEL_PLAN()==null) || 
             (this.ANT_MODEL_PLAN!=null &&
              this.ANT_MODEL_PLAN.equals(other.getANT_MODEL_PLAN()))) &&
            ((this.ANT_SIZE==null && other.getANT_SIZE()==null) || 
             (this.ANT_SIZE!=null &&
              this.ANT_SIZE.equals(other.getANT_SIZE()))) &&
            ((this.ANT_SIZE_PLAN==null && other.getANT_SIZE_PLAN()==null) || 
             (this.ANT_SIZE_PLAN!=null &&
              this.ANT_SIZE_PLAN.equals(other.getANT_SIZE_PLAN()))) &&
            ((this.ANT_TYPE==null && other.getANT_TYPE()==null) || 
             (this.ANT_TYPE!=null &&
              this.ANT_TYPE.equals(other.getANT_TYPE()))) &&
            ((this.ANT_TYPE_PLAN==null && other.getANT_TYPE_PLAN()==null) || 
             (this.ANT_TYPE_PLAN!=null &&
              this.ANT_TYPE_PLAN.equals(other.getANT_TYPE_PLAN()))) &&
            ((this.AT_3DB==null && other.getAT_3DB()==null) || 
             (this.AT_3DB!=null &&
              this.AT_3DB.equals(other.getAT_3DB()))) &&
            ((this.AT_3DB_PLAN==null && other.getAT_3DB_PLAN()==null) || 
             (this.AT_3DB_PLAN!=null &&
              this.AT_3DB_PLAN.equals(other.getAT_3DB_PLAN()))) &&
            ((this.AT_ANG==null && other.getAT_ANG()==null) || 
             (this.AT_ANG!=null &&
              this.AT_ANG.equals(other.getAT_ANG()))) &&
            ((this.AT_ANG_PLAN==null && other.getAT_ANG_PLAN()==null) || 
             (this.AT_ANG_PLAN!=null &&
              this.AT_ANG_PLAN.equals(other.getAT_ANG_PLAN()))) &&
            ((this.AT_EANG==null && other.getAT_EANG()==null) || 
             (this.AT_EANG!=null &&
              this.AT_EANG.equals(other.getAT_EANG()))) &&
            ((this.AT_EANG_PLAN==null && other.getAT_EANG_PLAN()==null) || 
             (this.AT_EANG_PLAN!=null &&
              this.AT_EANG_PLAN.equals(other.getAT_EANG_PLAN()))) &&
            ((this.AT_RANG==null && other.getAT_RANG()==null) || 
             (this.AT_RANG!=null &&
              this.AT_RANG.equals(other.getAT_RANG()))) &&
            ((this.AT_RANG_PLAN==null && other.getAT_RANG_PLAN()==null) || 
             (this.AT_RANG_PLAN!=null &&
              this.AT_RANG_PLAN.equals(other.getAT_RANG_PLAN()))) &&
            ((this.DATA_TYPE==null && other.getDATA_TYPE()==null) || 
             (this.DATA_TYPE!=null &&
              this.DATA_TYPE.equals(other.getDATA_TYPE()))) &&
            ((this.DN_FREQ_EFB_RANGE==null && other.getDN_FREQ_EFB_RANGE()==null) || 
             (this.DN_FREQ_EFB_RANGE!=null &&
              this.DN_FREQ_EFB_RANGE.equals(other.getDN_FREQ_EFB_RANGE()))) &&
            ((this.DN_FREQ_EFB_RANGE_PLAN==null && other.getDN_FREQ_EFB_RANGE_PLAN()==null) || 
             (this.DN_FREQ_EFB_RANGE_PLAN!=null &&
              this.DN_FREQ_EFB_RANGE_PLAN.equals(other.getDN_FREQ_EFB_RANGE_PLAN()))) &&
            ((this.DN_FREQ_EFE_RANGE==null && other.getDN_FREQ_EFE_RANGE()==null) || 
             (this.DN_FREQ_EFE_RANGE!=null &&
              this.DN_FREQ_EFE_RANGE.equals(other.getDN_FREQ_EFE_RANGE()))) &&
            ((this.DN_FREQ_EFE_RANGE_PLAN==null && other.getDN_FREQ_EFE_RANGE_PLAN()==null) || 
             (this.DN_FREQ_EFE_RANGE_PLAN!=null &&
              this.DN_FREQ_EFE_RANGE_PLAN.equals(other.getDN_FREQ_EFE_RANGE_PLAN()))) &&
            ((this.DN_FREQ_RFB_RANGE==null && other.getDN_FREQ_RFB_RANGE()==null) || 
             (this.DN_FREQ_RFB_RANGE!=null &&
              this.DN_FREQ_RFB_RANGE.equals(other.getDN_FREQ_RFB_RANGE()))) &&
            ((this.DN_FREQ_RFB_RANGE_PLAN==null && other.getDN_FREQ_RFB_RANGE_PLAN()==null) || 
             (this.DN_FREQ_RFB_RANGE_PLAN!=null &&
              this.DN_FREQ_RFB_RANGE_PLAN.equals(other.getDN_FREQ_RFB_RANGE_PLAN()))) &&
            ((this.DN_FREQ_RFE_RANGE==null && other.getDN_FREQ_RFE_RANGE()==null) || 
             (this.DN_FREQ_RFE_RANGE!=null &&
              this.DN_FREQ_RFE_RANGE.equals(other.getDN_FREQ_RFE_RANGE()))) &&
            ((this.DN_FREQ_RFE_RANGE_PLAN==null && other.getDN_FREQ_RFE_RANGE_PLAN()==null) || 
             (this.DN_FREQ_RFE_RANGE_PLAN!=null &&
              this.DN_FREQ_RFE_RANGE_PLAN.equals(other.getDN_FREQ_RFE_RANGE_PLAN()))) &&
            ((this.DN_NET_BAND==null && other.getDN_NET_BAND()==null) || 
             (this.DN_NET_BAND!=null &&
              this.DN_NET_BAND.equals(other.getDN_NET_BAND()))) &&
            ((this.DN_NET_BAND_PLAN==null && other.getDN_NET_BAND_PLAN()==null) || 
             (this.DN_NET_BAND_PLAN!=null &&
              this.DN_NET_BAND_PLAN.equals(other.getDN_NET_BAND_PLAN()))) &&
            ((this.ENABLE_DATE==null && other.getENABLE_DATE()==null) || 
             (this.ENABLE_DATE!=null &&
              this.ENABLE_DATE.equals(other.getENABLE_DATE()))) &&
            ((this.ENABLE_DATE_PLAN==null && other.getENABLE_DATE_PLAN()==null) || 
             (this.ENABLE_DATE_PLAN!=null &&
              this.ENABLE_DATE_PLAN.equals(other.getENABLE_DATE_PLAN()))) &&
            ((this.EQU_AUTH==null && other.getEQU_AUTH()==null) || 
             (this.EQU_AUTH!=null &&
              this.EQU_AUTH.equals(other.getEQU_AUTH()))) &&
            ((this.EQU_AUTH_PLAN==null && other.getEQU_AUTH_PLAN()==null) || 
             (this.EQU_AUTH_PLAN!=null &&
              this.EQU_AUTH_PLAN.equals(other.getEQU_AUTH_PLAN()))) &&
            ((this.EQU_MANU==null && other.getEQU_MANU()==null) || 
             (this.EQU_MANU!=null &&
              this.EQU_MANU.equals(other.getEQU_MANU()))) &&
            ((this.EQU_MANU_PLAN==null && other.getEQU_MANU_PLAN()==null) || 
             (this.EQU_MANU_PLAN!=null &&
              this.EQU_MANU_PLAN.equals(other.getEQU_MANU_PLAN()))) &&
            ((this.EQU_MODEL==null && other.getEQU_MODEL()==null) || 
             (this.EQU_MODEL!=null &&
              this.EQU_MODEL.equals(other.getEQU_MODEL()))) &&
            ((this.EQU_MODEL_PLAN==null && other.getEQU_MODEL_PLAN()==null) || 
             (this.EQU_MODEL_PLAN!=null &&
              this.EQU_MODEL_PLAN.equals(other.getEQU_MODEL_PLAN()))) &&
            ((this.EQU_PF==null && other.getEQU_PF()==null) || 
             (this.EQU_PF!=null &&
              this.EQU_PF.equals(other.getEQU_PF()))) &&
            ((this.EQU_PF_PLAN==null && other.getEQU_PF_PLAN()==null) || 
             (this.EQU_PF_PLAN!=null &&
              this.EQU_PF_PLAN.equals(other.getEQU_PF_PLAN()))) &&
            ((this.EQU_POW==null && other.getEQU_POW()==null) || 
             (this.EQU_POW!=null &&
              this.EQU_POW.equals(other.getEQU_POW()))) &&
            ((this.EQU_POW_PLAN==null && other.getEQU_POW_PLAN()==null) || 
             (this.EQU_POW_PLAN!=null &&
              this.EQU_POW_PLAN.equals(other.getEQU_POW_PLAN()))) &&
            ((this.EQU_RNQ==null && other.getEQU_RNQ()==null) || 
             (this.EQU_RNQ!=null &&
              this.EQU_RNQ.equals(other.getEQU_RNQ()))) &&
            ((this.EQU_RNQ_PLAN==null && other.getEQU_RNQ_PLAN()==null) || 
             (this.EQU_RNQ_PLAN!=null &&
              this.EQU_RNQ_PLAN.equals(other.getEQU_RNQ_PLAN()))) &&
            ((this.EQU_TL==null && other.getEQU_TL()==null) || 
             (this.EQU_TL!=null &&
              this.EQU_TL.equals(other.getEQU_TL()))) &&
            ((this.EQU_TL_PLAN==null && other.getEQU_TL_PLAN()==null) || 
             (this.EQU_TL_PLAN!=null &&
              this.EQU_TL_PLAN.equals(other.getEQU_TL_PLAN()))) &&
            ((this.FEED_LOSE==null && other.getFEED_LOSE()==null) || 
             (this.FEED_LOSE!=null &&
              this.FEED_LOSE.equals(other.getFEED_LOSE()))) &&
            ((this.FEED_LOSE_PLAN==null && other.getFEED_LOSE_PLAN()==null) || 
             (this.FEED_LOSE_PLAN!=null &&
              this.FEED_LOSE_PLAN.equals(other.getFEED_LOSE_PLAN()))) &&
            ((this.FREQ_BAND==null && other.getFREQ_BAND()==null) || 
             (this.FREQ_BAND!=null &&
              this.FREQ_BAND.equals(other.getFREQ_BAND()))) &&
            ((this.FREQ_BAND_PLAN==null && other.getFREQ_BAND_PLAN()==null) || 
             (this.FREQ_BAND_PLAN!=null &&
              this.FREQ_BAND_PLAN.equals(other.getFREQ_BAND_PLAN()))) &&
            ((this.FREQ_MOD==null && other.getFREQ_MOD()==null) || 
             (this.FREQ_MOD!=null &&
              this.FREQ_MOD.equals(other.getFREQ_MOD()))) &&
            ((this.FREQ_MOD_PLAN==null && other.getFREQ_MOD_PLAN()==null) || 
             (this.FREQ_MOD_PLAN!=null &&
              this.FREQ_MOD_PLAN.equals(other.getFREQ_MOD_PLAN()))) &&
            ((this.GUID==null && other.getGUID()==null) || 
             (this.GUID!=null &&
              this.GUID.equals(other.getGUID()))) &&
            ((this.MODEL_NAME==null && other.getMODEL_NAME()==null) || 
             (this.MODEL_NAME!=null &&
              this.MODEL_NAME.equals(other.getMODEL_NAME()))) &&
            ((this.ORG_TYPE==null && other.getORG_TYPE()==null) || 
             (this.ORG_TYPE!=null &&
              this.ORG_TYPE.equals(other.getORG_TYPE()))) &&
            ((this.STAT_AT==null && other.getSTAT_AT()==null) || 
             (this.STAT_AT!=null &&
              this.STAT_AT.equals(other.getSTAT_AT()))) &&
            ((this.STAT_AT_PLAN==null && other.getSTAT_AT_PLAN()==null) || 
             (this.STAT_AT_PLAN!=null &&
              this.STAT_AT_PLAN.equals(other.getSTAT_AT_PLAN()))) &&
            ((this.STAT_TYPE==null && other.getSTAT_TYPE()==null) || 
             (this.STAT_TYPE!=null &&
              this.STAT_TYPE.equals(other.getSTAT_TYPE()))) &&
            ((this.STAT_TYPE_PLAN==null && other.getSTAT_TYPE_PLAN()==null) || 
             (this.STAT_TYPE_PLAN!=null &&
              this.STAT_TYPE_PLAN.equals(other.getSTAT_TYPE_PLAN()))) &&
            ((this.ST_USER_AREA==null && other.getST_USER_AREA()==null) || 
             (this.ST_USER_AREA!=null &&
              this.ST_USER_AREA.equals(other.getST_USER_AREA()))) &&
            ((this.ST_USER_AREA_PLAN==null && other.getST_USER_AREA_PLAN()==null) || 
             (this.ST_USER_AREA_PLAN!=null &&
              this.ST_USER_AREA_PLAN.equals(other.getST_USER_AREA_PLAN()))) &&
            ((this.UP_FREQ_EFB_RANGE==null && other.getUP_FREQ_EFB_RANGE()==null) || 
             (this.UP_FREQ_EFB_RANGE!=null &&
              this.UP_FREQ_EFB_RANGE.equals(other.getUP_FREQ_EFB_RANGE()))) &&
            ((this.UP_FREQ_EFB_RANGE_PLAN==null && other.getUP_FREQ_EFB_RANGE_PLAN()==null) || 
             (this.UP_FREQ_EFB_RANGE_PLAN!=null &&
              this.UP_FREQ_EFB_RANGE_PLAN.equals(other.getUP_FREQ_EFB_RANGE_PLAN()))) &&
            ((this.UP_FREQ_EFE_RANGE==null && other.getUP_FREQ_EFE_RANGE()==null) || 
             (this.UP_FREQ_EFE_RANGE!=null &&
              this.UP_FREQ_EFE_RANGE.equals(other.getUP_FREQ_EFE_RANGE()))) &&
            ((this.UP_FREQ_EFE_RANGE_PLAN==null && other.getUP_FREQ_EFE_RANGE_PLAN()==null) || 
             (this.UP_FREQ_EFE_RANGE_PLAN!=null &&
              this.UP_FREQ_EFE_RANGE_PLAN.equals(other.getUP_FREQ_EFE_RANGE_PLAN()))) &&
            ((this.UP_FREQ_RFB_RANGE==null && other.getUP_FREQ_RFB_RANGE()==null) || 
             (this.UP_FREQ_RFB_RANGE!=null &&
              this.UP_FREQ_RFB_RANGE.equals(other.getUP_FREQ_RFB_RANGE()))) &&
            ((this.UP_FREQ_RFB_RANGE_PLAN==null && other.getUP_FREQ_RFB_RANGE_PLAN()==null) || 
             (this.UP_FREQ_RFB_RANGE_PLAN!=null &&
              this.UP_FREQ_RFB_RANGE_PLAN.equals(other.getUP_FREQ_RFB_RANGE_PLAN()))) &&
            ((this.UP_FREQ_RFE_RANGE==null && other.getUP_FREQ_RFE_RANGE()==null) || 
             (this.UP_FREQ_RFE_RANGE!=null &&
              this.UP_FREQ_RFE_RANGE.equals(other.getUP_FREQ_RFE_RANGE()))) &&
            ((this.UP_FREQ_RFE_RANGE_PLAN==null && other.getUP_FREQ_RFE_RANGE_PLAN()==null) || 
             (this.UP_FREQ_RFE_RANGE_PLAN!=null &&
              this.UP_FREQ_RFE_RANGE_PLAN.equals(other.getUP_FREQ_RFE_RANGE_PLAN()))) &&
            ((this.UP_NET_BAND==null && other.getUP_NET_BAND()==null) || 
             (this.UP_NET_BAND!=null &&
              this.UP_NET_BAND.equals(other.getUP_NET_BAND()))) &&
            ((this.UP_NET_BAND_PLAN==null && other.getUP_NET_BAND_PLAN()==null) || 
             (this.UP_NET_BAND_PLAN!=null &&
              this.UP_NET_BAND_PLAN.equals(other.getUP_NET_BAND_PLAN())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getANT_ANGLE() != null) {
            _hashCode += getANT_ANGLE().hashCode();
        }
        if (getANT_ANGLE_PLAN() != null) {
            _hashCode += getANT_ANGLE_PLAN().hashCode();
        }
        if (getANT_GAIN() != null) {
            _hashCode += getANT_GAIN().hashCode();
        }
        if (getANT_GAIN_PLAN() != null) {
            _hashCode += getANT_GAIN_PLAN().hashCode();
        }
        if (getANT_HIGHT() != null) {
            _hashCode += getANT_HIGHT().hashCode();
        }
        if (getANT_HIGHT_PLAN() != null) {
            _hashCode += getANT_HIGHT_PLAN().hashCode();
        }
        if (getANT_MANU() != null) {
            _hashCode += getANT_MANU().hashCode();
        }
        if (getANT_MANU_PLAN() != null) {
            _hashCode += getANT_MANU_PLAN().hashCode();
        }
        if (getANT_MODEL() != null) {
            _hashCode += getANT_MODEL().hashCode();
        }
        if (getANT_MODEL_PLAN() != null) {
            _hashCode += getANT_MODEL_PLAN().hashCode();
        }
        if (getANT_SIZE() != null) {
            _hashCode += getANT_SIZE().hashCode();
        }
        if (getANT_SIZE_PLAN() != null) {
            _hashCode += getANT_SIZE_PLAN().hashCode();
        }
        if (getANT_TYPE() != null) {
            _hashCode += getANT_TYPE().hashCode();
        }
        if (getANT_TYPE_PLAN() != null) {
            _hashCode += getANT_TYPE_PLAN().hashCode();
        }
        if (getAT_3DB() != null) {
            _hashCode += getAT_3DB().hashCode();
        }
        if (getAT_3DB_PLAN() != null) {
            _hashCode += getAT_3DB_PLAN().hashCode();
        }
        if (getAT_ANG() != null) {
            _hashCode += getAT_ANG().hashCode();
        }
        if (getAT_ANG_PLAN() != null) {
            _hashCode += getAT_ANG_PLAN().hashCode();
        }
        if (getAT_EANG() != null) {
            _hashCode += getAT_EANG().hashCode();
        }
        if (getAT_EANG_PLAN() != null) {
            _hashCode += getAT_EANG_PLAN().hashCode();
        }
        if (getAT_RANG() != null) {
            _hashCode += getAT_RANG().hashCode();
        }
        if (getAT_RANG_PLAN() != null) {
            _hashCode += getAT_RANG_PLAN().hashCode();
        }
        if (getDATA_TYPE() != null) {
            _hashCode += getDATA_TYPE().hashCode();
        }
        if (getDN_FREQ_EFB_RANGE() != null) {
            _hashCode += getDN_FREQ_EFB_RANGE().hashCode();
        }
        if (getDN_FREQ_EFB_RANGE_PLAN() != null) {
            _hashCode += getDN_FREQ_EFB_RANGE_PLAN().hashCode();
        }
        if (getDN_FREQ_EFE_RANGE() != null) {
            _hashCode += getDN_FREQ_EFE_RANGE().hashCode();
        }
        if (getDN_FREQ_EFE_RANGE_PLAN() != null) {
            _hashCode += getDN_FREQ_EFE_RANGE_PLAN().hashCode();
        }
        if (getDN_FREQ_RFB_RANGE() != null) {
            _hashCode += getDN_FREQ_RFB_RANGE().hashCode();
        }
        if (getDN_FREQ_RFB_RANGE_PLAN() != null) {
            _hashCode += getDN_FREQ_RFB_RANGE_PLAN().hashCode();
        }
        if (getDN_FREQ_RFE_RANGE() != null) {
            _hashCode += getDN_FREQ_RFE_RANGE().hashCode();
        }
        if (getDN_FREQ_RFE_RANGE_PLAN() != null) {
            _hashCode += getDN_FREQ_RFE_RANGE_PLAN().hashCode();
        }
        if (getDN_NET_BAND() != null) {
            _hashCode += getDN_NET_BAND().hashCode();
        }
        if (getDN_NET_BAND_PLAN() != null) {
            _hashCode += getDN_NET_BAND_PLAN().hashCode();
        }
        if (getENABLE_DATE() != null) {
            _hashCode += getENABLE_DATE().hashCode();
        }
        if (getENABLE_DATE_PLAN() != null) {
            _hashCode += getENABLE_DATE_PLAN().hashCode();
        }
        if (getEQU_AUTH() != null) {
            _hashCode += getEQU_AUTH().hashCode();
        }
        if (getEQU_AUTH_PLAN() != null) {
            _hashCode += getEQU_AUTH_PLAN().hashCode();
        }
        if (getEQU_MANU() != null) {
            _hashCode += getEQU_MANU().hashCode();
        }
        if (getEQU_MANU_PLAN() != null) {
            _hashCode += getEQU_MANU_PLAN().hashCode();
        }
        if (getEQU_MODEL() != null) {
            _hashCode += getEQU_MODEL().hashCode();
        }
        if (getEQU_MODEL_PLAN() != null) {
            _hashCode += getEQU_MODEL_PLAN().hashCode();
        }
        if (getEQU_PF() != null) {
            _hashCode += getEQU_PF().hashCode();
        }
        if (getEQU_PF_PLAN() != null) {
            _hashCode += getEQU_PF_PLAN().hashCode();
        }
        if (getEQU_POW() != null) {
            _hashCode += getEQU_POW().hashCode();
        }
        if (getEQU_POW_PLAN() != null) {
            _hashCode += getEQU_POW_PLAN().hashCode();
        }
        if (getEQU_RNQ() != null) {
            _hashCode += getEQU_RNQ().hashCode();
        }
        if (getEQU_RNQ_PLAN() != null) {
            _hashCode += getEQU_RNQ_PLAN().hashCode();
        }
        if (getEQU_TL() != null) {
            _hashCode += getEQU_TL().hashCode();
        }
        if (getEQU_TL_PLAN() != null) {
            _hashCode += getEQU_TL_PLAN().hashCode();
        }
        if (getFEED_LOSE() != null) {
            _hashCode += getFEED_LOSE().hashCode();
        }
        if (getFEED_LOSE_PLAN() != null) {
            _hashCode += getFEED_LOSE_PLAN().hashCode();
        }
        if (getFREQ_BAND() != null) {
            _hashCode += getFREQ_BAND().hashCode();
        }
        if (getFREQ_BAND_PLAN() != null) {
            _hashCode += getFREQ_BAND_PLAN().hashCode();
        }
        if (getFREQ_MOD() != null) {
            _hashCode += getFREQ_MOD().hashCode();
        }
        if (getFREQ_MOD_PLAN() != null) {
            _hashCode += getFREQ_MOD_PLAN().hashCode();
        }
        if (getGUID() != null) {
            _hashCode += getGUID().hashCode();
        }
        if (getMODEL_NAME() != null) {
            _hashCode += getMODEL_NAME().hashCode();
        }
        if (getORG_TYPE() != null) {
            _hashCode += getORG_TYPE().hashCode();
        }
        if (getSTAT_AT() != null) {
            _hashCode += getSTAT_AT().hashCode();
        }
        if (getSTAT_AT_PLAN() != null) {
            _hashCode += getSTAT_AT_PLAN().hashCode();
        }
        if (getSTAT_TYPE() != null) {
            _hashCode += getSTAT_TYPE().hashCode();
        }
        if (getSTAT_TYPE_PLAN() != null) {
            _hashCode += getSTAT_TYPE_PLAN().hashCode();
        }
        if (getST_USER_AREA() != null) {
            _hashCode += getST_USER_AREA().hashCode();
        }
        if (getST_USER_AREA_PLAN() != null) {
            _hashCode += getST_USER_AREA_PLAN().hashCode();
        }
        if (getUP_FREQ_EFB_RANGE() != null) {
            _hashCode += getUP_FREQ_EFB_RANGE().hashCode();
        }
        if (getUP_FREQ_EFB_RANGE_PLAN() != null) {
            _hashCode += getUP_FREQ_EFB_RANGE_PLAN().hashCode();
        }
        if (getUP_FREQ_EFE_RANGE() != null) {
            _hashCode += getUP_FREQ_EFE_RANGE().hashCode();
        }
        if (getUP_FREQ_EFE_RANGE_PLAN() != null) {
            _hashCode += getUP_FREQ_EFE_RANGE_PLAN().hashCode();
        }
        if (getUP_FREQ_RFB_RANGE() != null) {
            _hashCode += getUP_FREQ_RFB_RANGE().hashCode();
        }
        if (getUP_FREQ_RFB_RANGE_PLAN() != null) {
            _hashCode += getUP_FREQ_RFB_RANGE_PLAN().hashCode();
        }
        if (getUP_FREQ_RFE_RANGE() != null) {
            _hashCode += getUP_FREQ_RFE_RANGE().hashCode();
        }
        if (getUP_FREQ_RFE_RANGE_PLAN() != null) {
            _hashCode += getUP_FREQ_RFE_RANGE_PLAN().hashCode();
        }
        if (getUP_NET_BAND() != null) {
            _hashCode += getUP_NET_BAND().hashCode();
        }
        if (getUP_NET_BAND_PLAN() != null) {
            _hashCode += getUP_NET_BAND_PLAN().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(FS_MNG_IMP_PARAMS_CONFIG.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FS_MNG_IMP_PARAMS_CONFIG"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ANT_ANGLE");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "ANT_ANGLE"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "double"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ANT_ANGLE_PLAN");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "ANT_ANGLE_PLAN"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ANT_GAIN");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "ANT_GAIN"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "double"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ANT_GAIN_PLAN");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "ANT_GAIN_PLAN"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ANT_HIGHT");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "ANT_HIGHT"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "double"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ANT_HIGHT_PLAN");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "ANT_HIGHT_PLAN"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ANT_MANU");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "ANT_MANU"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ANT_MANU_PLAN");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "ANT_MANU_PLAN"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ANT_MODEL");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "ANT_MODEL"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ANT_MODEL_PLAN");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "ANT_MODEL_PLAN"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ANT_SIZE");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "ANT_SIZE"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ANT_SIZE_PLAN");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "ANT_SIZE_PLAN"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ANT_TYPE");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "ANT_TYPE"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ANT_TYPE_PLAN");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "ANT_TYPE_PLAN"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("AT_3DB");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "AT_3DB"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "double"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("AT_3DB_PLAN");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "AT_3DB_PLAN"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("AT_ANG");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "AT_ANG"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "double"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("AT_ANG_PLAN");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "AT_ANG_PLAN"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("AT_EANG");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "AT_EANG"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "double"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("AT_EANG_PLAN");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "AT_EANG_PLAN"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("AT_RANG");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "AT_RANG"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "double"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("AT_RANG_PLAN");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "AT_RANG_PLAN"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("DATA_TYPE");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "DATA_TYPE"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("DN_FREQ_EFB_RANGE");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "DN_FREQ_EFB_RANGE"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "double"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("DN_FREQ_EFB_RANGE_PLAN");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "DN_FREQ_EFB_RANGE_PLAN"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("DN_FREQ_EFE_RANGE");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "DN_FREQ_EFE_RANGE"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "double"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("DN_FREQ_EFE_RANGE_PLAN");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "DN_FREQ_EFE_RANGE_PLAN"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("DN_FREQ_RFB_RANGE");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "DN_FREQ_RFB_RANGE"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "double"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("DN_FREQ_RFB_RANGE_PLAN");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "DN_FREQ_RFB_RANGE_PLAN"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("DN_FREQ_RFE_RANGE");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "DN_FREQ_RFE_RANGE"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "double"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("DN_FREQ_RFE_RANGE_PLAN");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "DN_FREQ_RFE_RANGE_PLAN"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("DN_NET_BAND");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "DN_NET_BAND"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "double"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("DN_NET_BAND_PLAN");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "DN_NET_BAND_PLAN"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ENABLE_DATE");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "ENABLE_DATE"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "dateTime"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ENABLE_DATE_PLAN");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "ENABLE_DATE_PLAN"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("EQU_AUTH");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "EQU_AUTH"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("EQU_AUTH_PLAN");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "EQU_AUTH_PLAN"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("EQU_MANU");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "EQU_MANU"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("EQU_MANU_PLAN");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "EQU_MANU_PLAN"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("EQU_MODEL");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "EQU_MODEL"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("EQU_MODEL_PLAN");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "EQU_MODEL_PLAN"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("EQU_PF");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "EQU_PF"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("EQU_PF_PLAN");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "EQU_PF_PLAN"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("EQU_POW");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "EQU_POW"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "double"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("EQU_POW_PLAN");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "EQU_POW_PLAN"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("EQU_RNQ");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "EQU_RNQ"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "double"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("EQU_RNQ_PLAN");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "EQU_RNQ_PLAN"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("EQU_TL");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "EQU_TL"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "double"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("EQU_TL_PLAN");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "EQU_TL_PLAN"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FEED_LOSE");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FEED_LOSE"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "double"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FEED_LOSE_PLAN");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FEED_LOSE_PLAN"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FREQ_BAND");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FREQ_BAND"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "double"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FREQ_BAND_PLAN");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FREQ_BAND_PLAN"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FREQ_MOD");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FREQ_MOD"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FREQ_MOD_PLAN");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FREQ_MOD_PLAN"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("GUID");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "GUID"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("MODEL_NAME");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "MODEL_NAME"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ORG_TYPE");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "ORG_TYPE"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("STAT_AT");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "STAT_AT"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "double"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("STAT_AT_PLAN");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "STAT_AT_PLAN"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("STAT_TYPE");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "STAT_TYPE"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("STAT_TYPE_PLAN");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "STAT_TYPE_PLAN"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ST_USER_AREA");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "ST_USER_AREA"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ST_USER_AREA_PLAN");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "ST_USER_AREA_PLAN"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("UP_FREQ_EFB_RANGE");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "UP_FREQ_EFB_RANGE"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "double"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("UP_FREQ_EFB_RANGE_PLAN");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "UP_FREQ_EFB_RANGE_PLAN"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("UP_FREQ_EFE_RANGE");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "UP_FREQ_EFE_RANGE"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "double"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("UP_FREQ_EFE_RANGE_PLAN");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "UP_FREQ_EFE_RANGE_PLAN"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("UP_FREQ_RFB_RANGE");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "UP_FREQ_RFB_RANGE"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "double"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("UP_FREQ_RFB_RANGE_PLAN");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "UP_FREQ_RFB_RANGE_PLAN"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("UP_FREQ_RFE_RANGE");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "UP_FREQ_RFE_RANGE"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "double"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("UP_FREQ_RFE_RANGE_PLAN");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "UP_FREQ_RFE_RANGE_PLAN"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("UP_NET_BAND");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "UP_NET_BAND"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "double"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("UP_NET_BAND_PLAN");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "UP_NET_BAND_PLAN"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
