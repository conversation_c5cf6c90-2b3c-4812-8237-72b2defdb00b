/**
 * FS_ST_ANTENNA_ALL.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.caict.bsm.project.api.sync.boer.bigdata.org.datacontract.schemas._2004._07.FS_ServerSolution_Data;

public class FS_ST_ANTENNA_ALL  implements java.io.Serializable {
    private String ANT_ANGLE;

    private String ANT_ANGLE_DESCRIPTION;

    private String ANT_COEF;

    private String ANT_EGAIN;

    private String ANT_EPOLE;

    private String ANT_GAIN;

    private String ANT_GAIN_DESCRIPTION;

    private String ANT_GAIN_UNIT;

    private String ANT_GTRATIO;

    private String ANT_HIGHT;

    private String ANT_HIGHT_DESCRIPTION;

    private String ANT_LOWEFREQ;

    private String ANT_MANU;

    private String ANT_MODEL;

    private String ANT_NAME;

    private String ANT_NOISTEMP;

    private String ANT_POLE;

    private String ANT_RGAIN;

    private String ANT_RPOLE;

    private String ANT_SIZE;

    private String ANT_TYPE;

    private String ANT_UPPEFREQ;

    private String ANT_WORK_TYPE;

    private String AT_3DBE;

    private String AT_3DBR;

    private String AT_ANG_B;

    private String AT_ANG_E;

    private Integer AT_ANT_NO;

    private String AT_ANT_UPANG;

    private String AT_BWID;

    private String AT_CCODE;

    private String AT_CSGN;

    private String AT_EANG;

    private String AT_H_HPIC;

    private String AT_H_VPIC;

    private String AT_LEL;

    private String AT_QUA;

    private String AT_RANG;

    private String AT_RNT;

    private String AT_SE_B;

    private String AT_SE_E;

    private String AT_SSPEED;

    private String AT_SUM;

    private String AT_UNIT_TYPE;

    private String AT_UPDN;

    private String FC_CHARIMPE;

    private String FC_TYPE;

    private String FEED_LENGTH;

    private String FEED_LOSE;

    private String FEED_LOSE_DESCRIPTION;

    private String FEED_MENU;

    private String FEED_MODEL;

    private Integer SERIAL_NUM;

    public FS_ST_ANTENNA_ALL() {
    }

    public FS_ST_ANTENNA_ALL(
           String ANT_ANGLE,
           String ANT_ANGLE_DESCRIPTION,
           String ANT_COEF,
           String ANT_EGAIN,
           String ANT_EPOLE,
           String ANT_GAIN,
           String ANT_GAIN_DESCRIPTION,
           String ANT_GAIN_UNIT,
           String ANT_GTRATIO,
           String ANT_HIGHT,
           String ANT_HIGHT_DESCRIPTION,
           String ANT_LOWEFREQ,
           String ANT_MANU,
           String ANT_MODEL,
           String ANT_NAME,
           String ANT_NOISTEMP,
           String ANT_POLE,
           String ANT_RGAIN,
           String ANT_RPOLE,
           String ANT_SIZE,
           String ANT_TYPE,
           String ANT_UPPEFREQ,
           String ANT_WORK_TYPE,
           String AT_3DBE,
           String AT_3DBR,
           String AT_ANG_B,
           String AT_ANG_E,
           Integer AT_ANT_NO,
           String AT_ANT_UPANG,
           String AT_BWID,
           String AT_CCODE,
           String AT_CSGN,
           String AT_EANG,
           String AT_H_HPIC,
           String AT_H_VPIC,
           String AT_LEL,
           String AT_QUA,
           String AT_RANG,
           String AT_RNT,
           String AT_SE_B,
           String AT_SE_E,
           String AT_SSPEED,
           String AT_SUM,
           String AT_UNIT_TYPE,
           String AT_UPDN,
           String FC_CHARIMPE,
           String FC_TYPE,
           String FEED_LENGTH,
           String FEED_LOSE,
           String FEED_LOSE_DESCRIPTION,
           String FEED_MENU,
           String FEED_MODEL,
           Integer SERIAL_NUM) {
           this.ANT_ANGLE = ANT_ANGLE;
           this.ANT_ANGLE_DESCRIPTION = ANT_ANGLE_DESCRIPTION;
           this.ANT_COEF = ANT_COEF;
           this.ANT_EGAIN = ANT_EGAIN;
           this.ANT_EPOLE = ANT_EPOLE;
           this.ANT_GAIN = ANT_GAIN;
           this.ANT_GAIN_DESCRIPTION = ANT_GAIN_DESCRIPTION;
           this.ANT_GAIN_UNIT = ANT_GAIN_UNIT;
           this.ANT_GTRATIO = ANT_GTRATIO;
           this.ANT_HIGHT = ANT_HIGHT;
           this.ANT_HIGHT_DESCRIPTION = ANT_HIGHT_DESCRIPTION;
           this.ANT_LOWEFREQ = ANT_LOWEFREQ;
           this.ANT_MANU = ANT_MANU;
           this.ANT_MODEL = ANT_MODEL;
           this.ANT_NAME = ANT_NAME;
           this.ANT_NOISTEMP = ANT_NOISTEMP;
           this.ANT_POLE = ANT_POLE;
           this.ANT_RGAIN = ANT_RGAIN;
           this.ANT_RPOLE = ANT_RPOLE;
           this.ANT_SIZE = ANT_SIZE;
           this.ANT_TYPE = ANT_TYPE;
           this.ANT_UPPEFREQ = ANT_UPPEFREQ;
           this.ANT_WORK_TYPE = ANT_WORK_TYPE;
           this.AT_3DBE = AT_3DBE;
           this.AT_3DBR = AT_3DBR;
           this.AT_ANG_B = AT_ANG_B;
           this.AT_ANG_E = AT_ANG_E;
           this.AT_ANT_NO = AT_ANT_NO;
           this.AT_ANT_UPANG = AT_ANT_UPANG;
           this.AT_BWID = AT_BWID;
           this.AT_CCODE = AT_CCODE;
           this.AT_CSGN = AT_CSGN;
           this.AT_EANG = AT_EANG;
           this.AT_H_HPIC = AT_H_HPIC;
           this.AT_H_VPIC = AT_H_VPIC;
           this.AT_LEL = AT_LEL;
           this.AT_QUA = AT_QUA;
           this.AT_RANG = AT_RANG;
           this.AT_RNT = AT_RNT;
           this.AT_SE_B = AT_SE_B;
           this.AT_SE_E = AT_SE_E;
           this.AT_SSPEED = AT_SSPEED;
           this.AT_SUM = AT_SUM;
           this.AT_UNIT_TYPE = AT_UNIT_TYPE;
           this.AT_UPDN = AT_UPDN;
           this.FC_CHARIMPE = FC_CHARIMPE;
           this.FC_TYPE = FC_TYPE;
           this.FEED_LENGTH = FEED_LENGTH;
           this.FEED_LOSE = FEED_LOSE;
           this.FEED_LOSE_DESCRIPTION = FEED_LOSE_DESCRIPTION;
           this.FEED_MENU = FEED_MENU;
           this.FEED_MODEL = FEED_MODEL;
           this.SERIAL_NUM = SERIAL_NUM;
    }


    /**
     * Gets the ANT_ANGLE value for this FS_ST_ANTENNA_ALL.
     * 
     * @return ANT_ANGLE
     */
    public String getANT_ANGLE() {
        return ANT_ANGLE;
    }


    /**
     * Sets the ANT_ANGLE value for this FS_ST_ANTENNA_ALL.
     * 
     * @param ANT_ANGLE
     */
    public void setANT_ANGLE(String ANT_ANGLE) {
        this.ANT_ANGLE = ANT_ANGLE;
    }


    /**
     * Gets the ANT_ANGLE_DESCRIPTION value for this FS_ST_ANTENNA_ALL.
     * 
     * @return ANT_ANGLE_DESCRIPTION
     */
    public String getANT_ANGLE_DESCRIPTION() {
        return ANT_ANGLE_DESCRIPTION;
    }


    /**
     * Sets the ANT_ANGLE_DESCRIPTION value for this FS_ST_ANTENNA_ALL.
     * 
     * @param ANT_ANGLE_DESCRIPTION
     */
    public void setANT_ANGLE_DESCRIPTION(String ANT_ANGLE_DESCRIPTION) {
        this.ANT_ANGLE_DESCRIPTION = ANT_ANGLE_DESCRIPTION;
    }


    /**
     * Gets the ANT_COEF value for this FS_ST_ANTENNA_ALL.
     * 
     * @return ANT_COEF
     */
    public String getANT_COEF() {
        return ANT_COEF;
    }


    /**
     * Sets the ANT_COEF value for this FS_ST_ANTENNA_ALL.
     * 
     * @param ANT_COEF
     */
    public void setANT_COEF(String ANT_COEF) {
        this.ANT_COEF = ANT_COEF;
    }


    /**
     * Gets the ANT_EGAIN value for this FS_ST_ANTENNA_ALL.
     * 
     * @return ANT_EGAIN
     */
    public String getANT_EGAIN() {
        return ANT_EGAIN;
    }


    /**
     * Sets the ANT_EGAIN value for this FS_ST_ANTENNA_ALL.
     * 
     * @param ANT_EGAIN
     */
    public void setANT_EGAIN(String ANT_EGAIN) {
        this.ANT_EGAIN = ANT_EGAIN;
    }


    /**
     * Gets the ANT_EPOLE value for this FS_ST_ANTENNA_ALL.
     * 
     * @return ANT_EPOLE
     */
    public String getANT_EPOLE() {
        return ANT_EPOLE;
    }


    /**
     * Sets the ANT_EPOLE value for this FS_ST_ANTENNA_ALL.
     * 
     * @param ANT_EPOLE
     */
    public void setANT_EPOLE(String ANT_EPOLE) {
        this.ANT_EPOLE = ANT_EPOLE;
    }


    /**
     * Gets the ANT_GAIN value for this FS_ST_ANTENNA_ALL.
     * 
     * @return ANT_GAIN
     */
    public String getANT_GAIN() {
        return ANT_GAIN;
    }


    /**
     * Sets the ANT_GAIN value for this FS_ST_ANTENNA_ALL.
     * 
     * @param ANT_GAIN
     */
    public void setANT_GAIN(String ANT_GAIN) {
        this.ANT_GAIN = ANT_GAIN;
    }


    /**
     * Gets the ANT_GAIN_DESCRIPTION value for this FS_ST_ANTENNA_ALL.
     * 
     * @return ANT_GAIN_DESCRIPTION
     */
    public String getANT_GAIN_DESCRIPTION() {
        return ANT_GAIN_DESCRIPTION;
    }


    /**
     * Sets the ANT_GAIN_DESCRIPTION value for this FS_ST_ANTENNA_ALL.
     * 
     * @param ANT_GAIN_DESCRIPTION
     */
    public void setANT_GAIN_DESCRIPTION(String ANT_GAIN_DESCRIPTION) {
        this.ANT_GAIN_DESCRIPTION = ANT_GAIN_DESCRIPTION;
    }


    /**
     * Gets the ANT_GAIN_UNIT value for this FS_ST_ANTENNA_ALL.
     * 
     * @return ANT_GAIN_UNIT
     */
    public String getANT_GAIN_UNIT() {
        return ANT_GAIN_UNIT;
    }


    /**
     * Sets the ANT_GAIN_UNIT value for this FS_ST_ANTENNA_ALL.
     * 
     * @param ANT_GAIN_UNIT
     */
    public void setANT_GAIN_UNIT(String ANT_GAIN_UNIT) {
        this.ANT_GAIN_UNIT = ANT_GAIN_UNIT;
    }


    /**
     * Gets the ANT_GTRATIO value for this FS_ST_ANTENNA_ALL.
     * 
     * @return ANT_GTRATIO
     */
    public String getANT_GTRATIO() {
        return ANT_GTRATIO;
    }


    /**
     * Sets the ANT_GTRATIO value for this FS_ST_ANTENNA_ALL.
     * 
     * @param ANT_GTRATIO
     */
    public void setANT_GTRATIO(String ANT_GTRATIO) {
        this.ANT_GTRATIO = ANT_GTRATIO;
    }


    /**
     * Gets the ANT_HIGHT value for this FS_ST_ANTENNA_ALL.
     * 
     * @return ANT_HIGHT
     */
    public String getANT_HIGHT() {
        return ANT_HIGHT;
    }


    /**
     * Sets the ANT_HIGHT value for this FS_ST_ANTENNA_ALL.
     * 
     * @param ANT_HIGHT
     */
    public void setANT_HIGHT(String ANT_HIGHT) {
        this.ANT_HIGHT = ANT_HIGHT;
    }


    /**
     * Gets the ANT_HIGHT_DESCRIPTION value for this FS_ST_ANTENNA_ALL.
     * 
     * @return ANT_HIGHT_DESCRIPTION
     */
    public String getANT_HIGHT_DESCRIPTION() {
        return ANT_HIGHT_DESCRIPTION;
    }


    /**
     * Sets the ANT_HIGHT_DESCRIPTION value for this FS_ST_ANTENNA_ALL.
     * 
     * @param ANT_HIGHT_DESCRIPTION
     */
    public void setANT_HIGHT_DESCRIPTION(String ANT_HIGHT_DESCRIPTION) {
        this.ANT_HIGHT_DESCRIPTION = ANT_HIGHT_DESCRIPTION;
    }


    /**
     * Gets the ANT_LOWEFREQ value for this FS_ST_ANTENNA_ALL.
     * 
     * @return ANT_LOWEFREQ
     */
    public String getANT_LOWEFREQ() {
        return ANT_LOWEFREQ;
    }


    /**
     * Sets the ANT_LOWEFREQ value for this FS_ST_ANTENNA_ALL.
     * 
     * @param ANT_LOWEFREQ
     */
    public void setANT_LOWEFREQ(String ANT_LOWEFREQ) {
        this.ANT_LOWEFREQ = ANT_LOWEFREQ;
    }


    /**
     * Gets the ANT_MANU value for this FS_ST_ANTENNA_ALL.
     * 
     * @return ANT_MANU
     */
    public String getANT_MANU() {
        return ANT_MANU;
    }


    /**
     * Sets the ANT_MANU value for this FS_ST_ANTENNA_ALL.
     * 
     * @param ANT_MANU
     */
    public void setANT_MANU(String ANT_MANU) {
        this.ANT_MANU = ANT_MANU;
    }


    /**
     * Gets the ANT_MODEL value for this FS_ST_ANTENNA_ALL.
     * 
     * @return ANT_MODEL
     */
    public String getANT_MODEL() {
        return ANT_MODEL;
    }


    /**
     * Sets the ANT_MODEL value for this FS_ST_ANTENNA_ALL.
     * 
     * @param ANT_MODEL
     */
    public void setANT_MODEL(String ANT_MODEL) {
        this.ANT_MODEL = ANT_MODEL;
    }


    /**
     * Gets the ANT_NAME value for this FS_ST_ANTENNA_ALL.
     * 
     * @return ANT_NAME
     */
    public String getANT_NAME() {
        return ANT_NAME;
    }


    /**
     * Sets the ANT_NAME value for this FS_ST_ANTENNA_ALL.
     * 
     * @param ANT_NAME
     */
    public void setANT_NAME(String ANT_NAME) {
        this.ANT_NAME = ANT_NAME;
    }


    /**
     * Gets the ANT_NOISTEMP value for this FS_ST_ANTENNA_ALL.
     * 
     * @return ANT_NOISTEMP
     */
    public String getANT_NOISTEMP() {
        return ANT_NOISTEMP;
    }


    /**
     * Sets the ANT_NOISTEMP value for this FS_ST_ANTENNA_ALL.
     * 
     * @param ANT_NOISTEMP
     */
    public void setANT_NOISTEMP(String ANT_NOISTEMP) {
        this.ANT_NOISTEMP = ANT_NOISTEMP;
    }


    /**
     * Gets the ANT_POLE value for this FS_ST_ANTENNA_ALL.
     * 
     * @return ANT_POLE
     */
    public String getANT_POLE() {
        return ANT_POLE;
    }


    /**
     * Sets the ANT_POLE value for this FS_ST_ANTENNA_ALL.
     * 
     * @param ANT_POLE
     */
    public void setANT_POLE(String ANT_POLE) {
        this.ANT_POLE = ANT_POLE;
    }


    /**
     * Gets the ANT_RGAIN value for this FS_ST_ANTENNA_ALL.
     * 
     * @return ANT_RGAIN
     */
    public String getANT_RGAIN() {
        return ANT_RGAIN;
    }


    /**
     * Sets the ANT_RGAIN value for this FS_ST_ANTENNA_ALL.
     * 
     * @param ANT_RGAIN
     */
    public void setANT_RGAIN(String ANT_RGAIN) {
        this.ANT_RGAIN = ANT_RGAIN;
    }


    /**
     * Gets the ANT_RPOLE value for this FS_ST_ANTENNA_ALL.
     * 
     * @return ANT_RPOLE
     */
    public String getANT_RPOLE() {
        return ANT_RPOLE;
    }


    /**
     * Sets the ANT_RPOLE value for this FS_ST_ANTENNA_ALL.
     * 
     * @param ANT_RPOLE
     */
    public void setANT_RPOLE(String ANT_RPOLE) {
        this.ANT_RPOLE = ANT_RPOLE;
    }


    /**
     * Gets the ANT_SIZE value for this FS_ST_ANTENNA_ALL.
     * 
     * @return ANT_SIZE
     */
    public String getANT_SIZE() {
        return ANT_SIZE;
    }


    /**
     * Sets the ANT_SIZE value for this FS_ST_ANTENNA_ALL.
     * 
     * @param ANT_SIZE
     */
    public void setANT_SIZE(String ANT_SIZE) {
        this.ANT_SIZE = ANT_SIZE;
    }


    /**
     * Gets the ANT_TYPE value for this FS_ST_ANTENNA_ALL.
     * 
     * @return ANT_TYPE
     */
    public String getANT_TYPE() {
        return ANT_TYPE;
    }


    /**
     * Sets the ANT_TYPE value for this FS_ST_ANTENNA_ALL.
     * 
     * @param ANT_TYPE
     */
    public void setANT_TYPE(String ANT_TYPE) {
        this.ANT_TYPE = ANT_TYPE;
    }


    /**
     * Gets the ANT_UPPEFREQ value for this FS_ST_ANTENNA_ALL.
     * 
     * @return ANT_UPPEFREQ
     */
    public String getANT_UPPEFREQ() {
        return ANT_UPPEFREQ;
    }


    /**
     * Sets the ANT_UPPEFREQ value for this FS_ST_ANTENNA_ALL.
     * 
     * @param ANT_UPPEFREQ
     */
    public void setANT_UPPEFREQ(String ANT_UPPEFREQ) {
        this.ANT_UPPEFREQ = ANT_UPPEFREQ;
    }


    /**
     * Gets the ANT_WORK_TYPE value for this FS_ST_ANTENNA_ALL.
     * 
     * @return ANT_WORK_TYPE
     */
    public String getANT_WORK_TYPE() {
        return ANT_WORK_TYPE;
    }


    /**
     * Sets the ANT_WORK_TYPE value for this FS_ST_ANTENNA_ALL.
     * 
     * @param ANT_WORK_TYPE
     */
    public void setANT_WORK_TYPE(String ANT_WORK_TYPE) {
        this.ANT_WORK_TYPE = ANT_WORK_TYPE;
    }


    /**
     * Gets the AT_3DBE value for this FS_ST_ANTENNA_ALL.
     * 
     * @return AT_3DBE
     */
    public String getAT_3DBE() {
        return AT_3DBE;
    }


    /**
     * Sets the AT_3DBE value for this FS_ST_ANTENNA_ALL.
     * 
     * @param AT_3DBE
     */
    public void setAT_3DBE(String AT_3DBE) {
        this.AT_3DBE = AT_3DBE;
    }


    /**
     * Gets the AT_3DBR value for this FS_ST_ANTENNA_ALL.
     * 
     * @return AT_3DBR
     */
    public String getAT_3DBR() {
        return AT_3DBR;
    }


    /**
     * Sets the AT_3DBR value for this FS_ST_ANTENNA_ALL.
     * 
     * @param AT_3DBR
     */
    public void setAT_3DBR(String AT_3DBR) {
        this.AT_3DBR = AT_3DBR;
    }


    /**
     * Gets the AT_ANG_B value for this FS_ST_ANTENNA_ALL.
     * 
     * @return AT_ANG_B
     */
    public String getAT_ANG_B() {
        return AT_ANG_B;
    }


    /**
     * Sets the AT_ANG_B value for this FS_ST_ANTENNA_ALL.
     * 
     * @param AT_ANG_B
     */
    public void setAT_ANG_B(String AT_ANG_B) {
        this.AT_ANG_B = AT_ANG_B;
    }


    /**
     * Gets the AT_ANG_E value for this FS_ST_ANTENNA_ALL.
     * 
     * @return AT_ANG_E
     */
    public String getAT_ANG_E() {
        return AT_ANG_E;
    }


    /**
     * Sets the AT_ANG_E value for this FS_ST_ANTENNA_ALL.
     * 
     * @param AT_ANG_E
     */
    public void setAT_ANG_E(String AT_ANG_E) {
        this.AT_ANG_E = AT_ANG_E;
    }


    /**
     * Gets the AT_ANT_NO value for this FS_ST_ANTENNA_ALL.
     * 
     * @return AT_ANT_NO
     */
    public Integer getAT_ANT_NO() {
        return AT_ANT_NO;
    }


    /**
     * Sets the AT_ANT_NO value for this FS_ST_ANTENNA_ALL.
     * 
     * @param AT_ANT_NO
     */
    public void setAT_ANT_NO(Integer AT_ANT_NO) {
        this.AT_ANT_NO = AT_ANT_NO;
    }


    /**
     * Gets the AT_ANT_UPANG value for this FS_ST_ANTENNA_ALL.
     * 
     * @return AT_ANT_UPANG
     */
    public String getAT_ANT_UPANG() {
        return AT_ANT_UPANG;
    }


    /**
     * Sets the AT_ANT_UPANG value for this FS_ST_ANTENNA_ALL.
     * 
     * @param AT_ANT_UPANG
     */
    public void setAT_ANT_UPANG(String AT_ANT_UPANG) {
        this.AT_ANT_UPANG = AT_ANT_UPANG;
    }


    /**
     * Gets the AT_BWID value for this FS_ST_ANTENNA_ALL.
     * 
     * @return AT_BWID
     */
    public String getAT_BWID() {
        return AT_BWID;
    }


    /**
     * Sets the AT_BWID value for this FS_ST_ANTENNA_ALL.
     * 
     * @param AT_BWID
     */
    public void setAT_BWID(String AT_BWID) {
        this.AT_BWID = AT_BWID;
    }


    /**
     * Gets the AT_CCODE value for this FS_ST_ANTENNA_ALL.
     * 
     * @return AT_CCODE
     */
    public String getAT_CCODE() {
        return AT_CCODE;
    }


    /**
     * Sets the AT_CCODE value for this FS_ST_ANTENNA_ALL.
     * 
     * @param AT_CCODE
     */
    public void setAT_CCODE(String AT_CCODE) {
        this.AT_CCODE = AT_CCODE;
    }


    /**
     * Gets the AT_CSGN value for this FS_ST_ANTENNA_ALL.
     * 
     * @return AT_CSGN
     */
    public String getAT_CSGN() {
        return AT_CSGN;
    }


    /**
     * Sets the AT_CSGN value for this FS_ST_ANTENNA_ALL.
     * 
     * @param AT_CSGN
     */
    public void setAT_CSGN(String AT_CSGN) {
        this.AT_CSGN = AT_CSGN;
    }


    /**
     * Gets the AT_EANG value for this FS_ST_ANTENNA_ALL.
     * 
     * @return AT_EANG
     */
    public String getAT_EANG() {
        return AT_EANG;
    }


    /**
     * Sets the AT_EANG value for this FS_ST_ANTENNA_ALL.
     * 
     * @param AT_EANG
     */
    public void setAT_EANG(String AT_EANG) {
        this.AT_EANG = AT_EANG;
    }


    /**
     * Gets the AT_H_HPIC value for this FS_ST_ANTENNA_ALL.
     * 
     * @return AT_H_HPIC
     */
    public String getAT_H_HPIC() {
        return AT_H_HPIC;
    }


    /**
     * Sets the AT_H_HPIC value for this FS_ST_ANTENNA_ALL.
     * 
     * @param AT_H_HPIC
     */
    public void setAT_H_HPIC(String AT_H_HPIC) {
        this.AT_H_HPIC = AT_H_HPIC;
    }


    /**
     * Gets the AT_H_VPIC value for this FS_ST_ANTENNA_ALL.
     * 
     * @return AT_H_VPIC
     */
    public String getAT_H_VPIC() {
        return AT_H_VPIC;
    }


    /**
     * Sets the AT_H_VPIC value for this FS_ST_ANTENNA_ALL.
     * 
     * @param AT_H_VPIC
     */
    public void setAT_H_VPIC(String AT_H_VPIC) {
        this.AT_H_VPIC = AT_H_VPIC;
    }


    /**
     * Gets the AT_LEL value for this FS_ST_ANTENNA_ALL.
     * 
     * @return AT_LEL
     */
    public String getAT_LEL() {
        return AT_LEL;
    }


    /**
     * Sets the AT_LEL value for this FS_ST_ANTENNA_ALL.
     * 
     * @param AT_LEL
     */
    public void setAT_LEL(String AT_LEL) {
        this.AT_LEL = AT_LEL;
    }


    /**
     * Gets the AT_QUA value for this FS_ST_ANTENNA_ALL.
     * 
     * @return AT_QUA
     */
    public String getAT_QUA() {
        return AT_QUA;
    }


    /**
     * Sets the AT_QUA value for this FS_ST_ANTENNA_ALL.
     * 
     * @param AT_QUA
     */
    public void setAT_QUA(String AT_QUA) {
        this.AT_QUA = AT_QUA;
    }


    /**
     * Gets the AT_RANG value for this FS_ST_ANTENNA_ALL.
     * 
     * @return AT_RANG
     */
    public String getAT_RANG() {
        return AT_RANG;
    }


    /**
     * Sets the AT_RANG value for this FS_ST_ANTENNA_ALL.
     * 
     * @param AT_RANG
     */
    public void setAT_RANG(String AT_RANG) {
        this.AT_RANG = AT_RANG;
    }


    /**
     * Gets the AT_RNT value for this FS_ST_ANTENNA_ALL.
     * 
     * @return AT_RNT
     */
    public String getAT_RNT() {
        return AT_RNT;
    }


    /**
     * Sets the AT_RNT value for this FS_ST_ANTENNA_ALL.
     * 
     * @param AT_RNT
     */
    public void setAT_RNT(String AT_RNT) {
        this.AT_RNT = AT_RNT;
    }


    /**
     * Gets the AT_SE_B value for this FS_ST_ANTENNA_ALL.
     * 
     * @return AT_SE_B
     */
    public String getAT_SE_B() {
        return AT_SE_B;
    }


    /**
     * Sets the AT_SE_B value for this FS_ST_ANTENNA_ALL.
     * 
     * @param AT_SE_B
     */
    public void setAT_SE_B(String AT_SE_B) {
        this.AT_SE_B = AT_SE_B;
    }


    /**
     * Gets the AT_SE_E value for this FS_ST_ANTENNA_ALL.
     * 
     * @return AT_SE_E
     */
    public String getAT_SE_E() {
        return AT_SE_E;
    }


    /**
     * Sets the AT_SE_E value for this FS_ST_ANTENNA_ALL.
     * 
     * @param AT_SE_E
     */
    public void setAT_SE_E(String AT_SE_E) {
        this.AT_SE_E = AT_SE_E;
    }


    /**
     * Gets the AT_SSPEED value for this FS_ST_ANTENNA_ALL.
     * 
     * @return AT_SSPEED
     */
    public String getAT_SSPEED() {
        return AT_SSPEED;
    }


    /**
     * Sets the AT_SSPEED value for this FS_ST_ANTENNA_ALL.
     * 
     * @param AT_SSPEED
     */
    public void setAT_SSPEED(String AT_SSPEED) {
        this.AT_SSPEED = AT_SSPEED;
    }


    /**
     * Gets the AT_SUM value for this FS_ST_ANTENNA_ALL.
     * 
     * @return AT_SUM
     */
    public String getAT_SUM() {
        return AT_SUM;
    }


    /**
     * Sets the AT_SUM value for this FS_ST_ANTENNA_ALL.
     * 
     * @param AT_SUM
     */
    public void setAT_SUM(String AT_SUM) {
        this.AT_SUM = AT_SUM;
    }


    /**
     * Gets the AT_UNIT_TYPE value for this FS_ST_ANTENNA_ALL.
     * 
     * @return AT_UNIT_TYPE
     */
    public String getAT_UNIT_TYPE() {
        return AT_UNIT_TYPE;
    }


    /**
     * Sets the AT_UNIT_TYPE value for this FS_ST_ANTENNA_ALL.
     * 
     * @param AT_UNIT_TYPE
     */
    public void setAT_UNIT_TYPE(String AT_UNIT_TYPE) {
        this.AT_UNIT_TYPE = AT_UNIT_TYPE;
    }


    /**
     * Gets the AT_UPDN value for this FS_ST_ANTENNA_ALL.
     * 
     * @return AT_UPDN
     */
    public String getAT_UPDN() {
        return AT_UPDN;
    }


    /**
     * Sets the AT_UPDN value for this FS_ST_ANTENNA_ALL.
     * 
     * @param AT_UPDN
     */
    public void setAT_UPDN(String AT_UPDN) {
        this.AT_UPDN = AT_UPDN;
    }


    /**
     * Gets the FC_CHARIMPE value for this FS_ST_ANTENNA_ALL.
     * 
     * @return FC_CHARIMPE
     */
    public String getFC_CHARIMPE() {
        return FC_CHARIMPE;
    }


    /**
     * Sets the FC_CHARIMPE value for this FS_ST_ANTENNA_ALL.
     * 
     * @param FC_CHARIMPE
     */
    public void setFC_CHARIMPE(String FC_CHARIMPE) {
        this.FC_CHARIMPE = FC_CHARIMPE;
    }


    /**
     * Gets the FC_TYPE value for this FS_ST_ANTENNA_ALL.
     * 
     * @return FC_TYPE
     */
    public String getFC_TYPE() {
        return FC_TYPE;
    }


    /**
     * Sets the FC_TYPE value for this FS_ST_ANTENNA_ALL.
     * 
     * @param FC_TYPE
     */
    public void setFC_TYPE(String FC_TYPE) {
        this.FC_TYPE = FC_TYPE;
    }


    /**
     * Gets the FEED_LENGTH value for this FS_ST_ANTENNA_ALL.
     * 
     * @return FEED_LENGTH
     */
    public String getFEED_LENGTH() {
        return FEED_LENGTH;
    }


    /**
     * Sets the FEED_LENGTH value for this FS_ST_ANTENNA_ALL.
     * 
     * @param FEED_LENGTH
     */
    public void setFEED_LENGTH(String FEED_LENGTH) {
        this.FEED_LENGTH = FEED_LENGTH;
    }


    /**
     * Gets the FEED_LOSE value for this FS_ST_ANTENNA_ALL.
     * 
     * @return FEED_LOSE
     */
    public String getFEED_LOSE() {
        return FEED_LOSE;
    }


    /**
     * Sets the FEED_LOSE value for this FS_ST_ANTENNA_ALL.
     * 
     * @param FEED_LOSE
     */
    public void setFEED_LOSE(String FEED_LOSE) {
        this.FEED_LOSE = FEED_LOSE;
    }


    /**
     * Gets the FEED_LOSE_DESCRIPTION value for this FS_ST_ANTENNA_ALL.
     * 
     * @return FEED_LOSE_DESCRIPTION
     */
    public String getFEED_LOSE_DESCRIPTION() {
        return FEED_LOSE_DESCRIPTION;
    }


    /**
     * Sets the FEED_LOSE_DESCRIPTION value for this FS_ST_ANTENNA_ALL.
     * 
     * @param FEED_LOSE_DESCRIPTION
     */
    public void setFEED_LOSE_DESCRIPTION(String FEED_LOSE_DESCRIPTION) {
        this.FEED_LOSE_DESCRIPTION = FEED_LOSE_DESCRIPTION;
    }


    /**
     * Gets the FEED_MENU value for this FS_ST_ANTENNA_ALL.
     * 
     * @return FEED_MENU
     */
    public String getFEED_MENU() {
        return FEED_MENU;
    }


    /**
     * Sets the FEED_MENU value for this FS_ST_ANTENNA_ALL.
     * 
     * @param FEED_MENU
     */
    public void setFEED_MENU(String FEED_MENU) {
        this.FEED_MENU = FEED_MENU;
    }


    /**
     * Gets the FEED_MODEL value for this FS_ST_ANTENNA_ALL.
     * 
     * @return FEED_MODEL
     */
    public String getFEED_MODEL() {
        return FEED_MODEL;
    }


    /**
     * Sets the FEED_MODEL value for this FS_ST_ANTENNA_ALL.
     * 
     * @param FEED_MODEL
     */
    public void setFEED_MODEL(String FEED_MODEL) {
        this.FEED_MODEL = FEED_MODEL;
    }


    /**
     * Gets the SERIAL_NUM value for this FS_ST_ANTENNA_ALL.
     * 
     * @return SERIAL_NUM
     */
    public Integer getSERIAL_NUM() {
        return SERIAL_NUM;
    }


    /**
     * Sets the SERIAL_NUM value for this FS_ST_ANTENNA_ALL.
     * 
     * @param SERIAL_NUM
     */
    public void setSERIAL_NUM(Integer SERIAL_NUM) {
        this.SERIAL_NUM = SERIAL_NUM;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof FS_ST_ANTENNA_ALL)) return false;
        FS_ST_ANTENNA_ALL other = (FS_ST_ANTENNA_ALL) obj;
        if (obj == null) return false;
        if (this == obj) return true;
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.ANT_ANGLE==null && other.getANT_ANGLE()==null) || 
             (this.ANT_ANGLE!=null &&
              this.ANT_ANGLE.equals(other.getANT_ANGLE()))) &&
            ((this.ANT_ANGLE_DESCRIPTION==null && other.getANT_ANGLE_DESCRIPTION()==null) || 
             (this.ANT_ANGLE_DESCRIPTION!=null &&
              this.ANT_ANGLE_DESCRIPTION.equals(other.getANT_ANGLE_DESCRIPTION()))) &&
            ((this.ANT_COEF==null && other.getANT_COEF()==null) || 
             (this.ANT_COEF!=null &&
              this.ANT_COEF.equals(other.getANT_COEF()))) &&
            ((this.ANT_EGAIN==null && other.getANT_EGAIN()==null) || 
             (this.ANT_EGAIN!=null &&
              this.ANT_EGAIN.equals(other.getANT_EGAIN()))) &&
            ((this.ANT_EPOLE==null && other.getANT_EPOLE()==null) || 
             (this.ANT_EPOLE!=null &&
              this.ANT_EPOLE.equals(other.getANT_EPOLE()))) &&
            ((this.ANT_GAIN==null && other.getANT_GAIN()==null) || 
             (this.ANT_GAIN!=null &&
              this.ANT_GAIN.equals(other.getANT_GAIN()))) &&
            ((this.ANT_GAIN_DESCRIPTION==null && other.getANT_GAIN_DESCRIPTION()==null) || 
             (this.ANT_GAIN_DESCRIPTION!=null &&
              this.ANT_GAIN_DESCRIPTION.equals(other.getANT_GAIN_DESCRIPTION()))) &&
            ((this.ANT_GAIN_UNIT==null && other.getANT_GAIN_UNIT()==null) || 
             (this.ANT_GAIN_UNIT!=null &&
              this.ANT_GAIN_UNIT.equals(other.getANT_GAIN_UNIT()))) &&
            ((this.ANT_GTRATIO==null && other.getANT_GTRATIO()==null) || 
             (this.ANT_GTRATIO!=null &&
              this.ANT_GTRATIO.equals(other.getANT_GTRATIO()))) &&
            ((this.ANT_HIGHT==null && other.getANT_HIGHT()==null) || 
             (this.ANT_HIGHT!=null &&
              this.ANT_HIGHT.equals(other.getANT_HIGHT()))) &&
            ((this.ANT_HIGHT_DESCRIPTION==null && other.getANT_HIGHT_DESCRIPTION()==null) || 
             (this.ANT_HIGHT_DESCRIPTION!=null &&
              this.ANT_HIGHT_DESCRIPTION.equals(other.getANT_HIGHT_DESCRIPTION()))) &&
            ((this.ANT_LOWEFREQ==null && other.getANT_LOWEFREQ()==null) || 
             (this.ANT_LOWEFREQ!=null &&
              this.ANT_LOWEFREQ.equals(other.getANT_LOWEFREQ()))) &&
            ((this.ANT_MANU==null && other.getANT_MANU()==null) || 
             (this.ANT_MANU!=null &&
              this.ANT_MANU.equals(other.getANT_MANU()))) &&
            ((this.ANT_MODEL==null && other.getANT_MODEL()==null) || 
             (this.ANT_MODEL!=null &&
              this.ANT_MODEL.equals(other.getANT_MODEL()))) &&
            ((this.ANT_NAME==null && other.getANT_NAME()==null) || 
             (this.ANT_NAME!=null &&
              this.ANT_NAME.equals(other.getANT_NAME()))) &&
            ((this.ANT_NOISTEMP==null && other.getANT_NOISTEMP()==null) || 
             (this.ANT_NOISTEMP!=null &&
              this.ANT_NOISTEMP.equals(other.getANT_NOISTEMP()))) &&
            ((this.ANT_POLE==null && other.getANT_POLE()==null) || 
             (this.ANT_POLE!=null &&
              this.ANT_POLE.equals(other.getANT_POLE()))) &&
            ((this.ANT_RGAIN==null && other.getANT_RGAIN()==null) || 
             (this.ANT_RGAIN!=null &&
              this.ANT_RGAIN.equals(other.getANT_RGAIN()))) &&
            ((this.ANT_RPOLE==null && other.getANT_RPOLE()==null) || 
             (this.ANT_RPOLE!=null &&
              this.ANT_RPOLE.equals(other.getANT_RPOLE()))) &&
            ((this.ANT_SIZE==null && other.getANT_SIZE()==null) || 
             (this.ANT_SIZE!=null &&
              this.ANT_SIZE.equals(other.getANT_SIZE()))) &&
            ((this.ANT_TYPE==null && other.getANT_TYPE()==null) || 
             (this.ANT_TYPE!=null &&
              this.ANT_TYPE.equals(other.getANT_TYPE()))) &&
            ((this.ANT_UPPEFREQ==null && other.getANT_UPPEFREQ()==null) || 
             (this.ANT_UPPEFREQ!=null &&
              this.ANT_UPPEFREQ.equals(other.getANT_UPPEFREQ()))) &&
            ((this.ANT_WORK_TYPE==null && other.getANT_WORK_TYPE()==null) || 
             (this.ANT_WORK_TYPE!=null &&
              this.ANT_WORK_TYPE.equals(other.getANT_WORK_TYPE()))) &&
            ((this.AT_3DBE==null && other.getAT_3DBE()==null) || 
             (this.AT_3DBE!=null &&
              this.AT_3DBE.equals(other.getAT_3DBE()))) &&
            ((this.AT_3DBR==null && other.getAT_3DBR()==null) || 
             (this.AT_3DBR!=null &&
              this.AT_3DBR.equals(other.getAT_3DBR()))) &&
            ((this.AT_ANG_B==null && other.getAT_ANG_B()==null) || 
             (this.AT_ANG_B!=null &&
              this.AT_ANG_B.equals(other.getAT_ANG_B()))) &&
            ((this.AT_ANG_E==null && other.getAT_ANG_E()==null) || 
             (this.AT_ANG_E!=null &&
              this.AT_ANG_E.equals(other.getAT_ANG_E()))) &&
            ((this.AT_ANT_NO==null && other.getAT_ANT_NO()==null) || 
             (this.AT_ANT_NO!=null &&
              this.AT_ANT_NO.equals(other.getAT_ANT_NO()))) &&
            ((this.AT_ANT_UPANG==null && other.getAT_ANT_UPANG()==null) || 
             (this.AT_ANT_UPANG!=null &&
              this.AT_ANT_UPANG.equals(other.getAT_ANT_UPANG()))) &&
            ((this.AT_BWID==null && other.getAT_BWID()==null) || 
             (this.AT_BWID!=null &&
              this.AT_BWID.equals(other.getAT_BWID()))) &&
            ((this.AT_CCODE==null && other.getAT_CCODE()==null) || 
             (this.AT_CCODE!=null &&
              this.AT_CCODE.equals(other.getAT_CCODE()))) &&
            ((this.AT_CSGN==null && other.getAT_CSGN()==null) || 
             (this.AT_CSGN!=null &&
              this.AT_CSGN.equals(other.getAT_CSGN()))) &&
            ((this.AT_EANG==null && other.getAT_EANG()==null) || 
             (this.AT_EANG!=null &&
              this.AT_EANG.equals(other.getAT_EANG()))) &&
            ((this.AT_H_HPIC==null && other.getAT_H_HPIC()==null) || 
             (this.AT_H_HPIC!=null &&
              this.AT_H_HPIC.equals(other.getAT_H_HPIC()))) &&
            ((this.AT_H_VPIC==null && other.getAT_H_VPIC()==null) || 
             (this.AT_H_VPIC!=null &&
              this.AT_H_VPIC.equals(other.getAT_H_VPIC()))) &&
            ((this.AT_LEL==null && other.getAT_LEL()==null) || 
             (this.AT_LEL!=null &&
              this.AT_LEL.equals(other.getAT_LEL()))) &&
            ((this.AT_QUA==null && other.getAT_QUA()==null) || 
             (this.AT_QUA!=null &&
              this.AT_QUA.equals(other.getAT_QUA()))) &&
            ((this.AT_RANG==null && other.getAT_RANG()==null) || 
             (this.AT_RANG!=null &&
              this.AT_RANG.equals(other.getAT_RANG()))) &&
            ((this.AT_RNT==null && other.getAT_RNT()==null) || 
             (this.AT_RNT!=null &&
              this.AT_RNT.equals(other.getAT_RNT()))) &&
            ((this.AT_SE_B==null && other.getAT_SE_B()==null) || 
             (this.AT_SE_B!=null &&
              this.AT_SE_B.equals(other.getAT_SE_B()))) &&
            ((this.AT_SE_E==null && other.getAT_SE_E()==null) || 
             (this.AT_SE_E!=null &&
              this.AT_SE_E.equals(other.getAT_SE_E()))) &&
            ((this.AT_SSPEED==null && other.getAT_SSPEED()==null) || 
             (this.AT_SSPEED!=null &&
              this.AT_SSPEED.equals(other.getAT_SSPEED()))) &&
            ((this.AT_SUM==null && other.getAT_SUM()==null) || 
             (this.AT_SUM!=null &&
              this.AT_SUM.equals(other.getAT_SUM()))) &&
            ((this.AT_UNIT_TYPE==null && other.getAT_UNIT_TYPE()==null) || 
             (this.AT_UNIT_TYPE!=null &&
              this.AT_UNIT_TYPE.equals(other.getAT_UNIT_TYPE()))) &&
            ((this.AT_UPDN==null && other.getAT_UPDN()==null) || 
             (this.AT_UPDN!=null &&
              this.AT_UPDN.equals(other.getAT_UPDN()))) &&
            ((this.FC_CHARIMPE==null && other.getFC_CHARIMPE()==null) || 
             (this.FC_CHARIMPE!=null &&
              this.FC_CHARIMPE.equals(other.getFC_CHARIMPE()))) &&
            ((this.FC_TYPE==null && other.getFC_TYPE()==null) || 
             (this.FC_TYPE!=null &&
              this.FC_TYPE.equals(other.getFC_TYPE()))) &&
            ((this.FEED_LENGTH==null && other.getFEED_LENGTH()==null) || 
             (this.FEED_LENGTH!=null &&
              this.FEED_LENGTH.equals(other.getFEED_LENGTH()))) &&
            ((this.FEED_LOSE==null && other.getFEED_LOSE()==null) || 
             (this.FEED_LOSE!=null &&
              this.FEED_LOSE.equals(other.getFEED_LOSE()))) &&
            ((this.FEED_LOSE_DESCRIPTION==null && other.getFEED_LOSE_DESCRIPTION()==null) || 
             (this.FEED_LOSE_DESCRIPTION!=null &&
              this.FEED_LOSE_DESCRIPTION.equals(other.getFEED_LOSE_DESCRIPTION()))) &&
            ((this.FEED_MENU==null && other.getFEED_MENU()==null) || 
             (this.FEED_MENU!=null &&
              this.FEED_MENU.equals(other.getFEED_MENU()))) &&
            ((this.FEED_MODEL==null && other.getFEED_MODEL()==null) || 
             (this.FEED_MODEL!=null &&
              this.FEED_MODEL.equals(other.getFEED_MODEL()))) &&
            ((this.SERIAL_NUM==null && other.getSERIAL_NUM()==null) || 
             (this.SERIAL_NUM!=null &&
              this.SERIAL_NUM.equals(other.getSERIAL_NUM())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getANT_ANGLE() != null) {
            _hashCode += getANT_ANGLE().hashCode();
        }
        if (getANT_ANGLE_DESCRIPTION() != null) {
            _hashCode += getANT_ANGLE_DESCRIPTION().hashCode();
        }
        if (getANT_COEF() != null) {
            _hashCode += getANT_COEF().hashCode();
        }
        if (getANT_EGAIN() != null) {
            _hashCode += getANT_EGAIN().hashCode();
        }
        if (getANT_EPOLE() != null) {
            _hashCode += getANT_EPOLE().hashCode();
        }
        if (getANT_GAIN() != null) {
            _hashCode += getANT_GAIN().hashCode();
        }
        if (getANT_GAIN_DESCRIPTION() != null) {
            _hashCode += getANT_GAIN_DESCRIPTION().hashCode();
        }
        if (getANT_GAIN_UNIT() != null) {
            _hashCode += getANT_GAIN_UNIT().hashCode();
        }
        if (getANT_GTRATIO() != null) {
            _hashCode += getANT_GTRATIO().hashCode();
        }
        if (getANT_HIGHT() != null) {
            _hashCode += getANT_HIGHT().hashCode();
        }
        if (getANT_HIGHT_DESCRIPTION() != null) {
            _hashCode += getANT_HIGHT_DESCRIPTION().hashCode();
        }
        if (getANT_LOWEFREQ() != null) {
            _hashCode += getANT_LOWEFREQ().hashCode();
        }
        if (getANT_MANU() != null) {
            _hashCode += getANT_MANU().hashCode();
        }
        if (getANT_MODEL() != null) {
            _hashCode += getANT_MODEL().hashCode();
        }
        if (getANT_NAME() != null) {
            _hashCode += getANT_NAME().hashCode();
        }
        if (getANT_NOISTEMP() != null) {
            _hashCode += getANT_NOISTEMP().hashCode();
        }
        if (getANT_POLE() != null) {
            _hashCode += getANT_POLE().hashCode();
        }
        if (getANT_RGAIN() != null) {
            _hashCode += getANT_RGAIN().hashCode();
        }
        if (getANT_RPOLE() != null) {
            _hashCode += getANT_RPOLE().hashCode();
        }
        if (getANT_SIZE() != null) {
            _hashCode += getANT_SIZE().hashCode();
        }
        if (getANT_TYPE() != null) {
            _hashCode += getANT_TYPE().hashCode();
        }
        if (getANT_UPPEFREQ() != null) {
            _hashCode += getANT_UPPEFREQ().hashCode();
        }
        if (getANT_WORK_TYPE() != null) {
            _hashCode += getANT_WORK_TYPE().hashCode();
        }
        if (getAT_3DBE() != null) {
            _hashCode += getAT_3DBE().hashCode();
        }
        if (getAT_3DBR() != null) {
            _hashCode += getAT_3DBR().hashCode();
        }
        if (getAT_ANG_B() != null) {
            _hashCode += getAT_ANG_B().hashCode();
        }
        if (getAT_ANG_E() != null) {
            _hashCode += getAT_ANG_E().hashCode();
        }
        if (getAT_ANT_NO() != null) {
            _hashCode += getAT_ANT_NO().hashCode();
        }
        if (getAT_ANT_UPANG() != null) {
            _hashCode += getAT_ANT_UPANG().hashCode();
        }
        if (getAT_BWID() != null) {
            _hashCode += getAT_BWID().hashCode();
        }
        if (getAT_CCODE() != null) {
            _hashCode += getAT_CCODE().hashCode();
        }
        if (getAT_CSGN() != null) {
            _hashCode += getAT_CSGN().hashCode();
        }
        if (getAT_EANG() != null) {
            _hashCode += getAT_EANG().hashCode();
        }
        if (getAT_H_HPIC() != null) {
            _hashCode += getAT_H_HPIC().hashCode();
        }
        if (getAT_H_VPIC() != null) {
            _hashCode += getAT_H_VPIC().hashCode();
        }
        if (getAT_LEL() != null) {
            _hashCode += getAT_LEL().hashCode();
        }
        if (getAT_QUA() != null) {
            _hashCode += getAT_QUA().hashCode();
        }
        if (getAT_RANG() != null) {
            _hashCode += getAT_RANG().hashCode();
        }
        if (getAT_RNT() != null) {
            _hashCode += getAT_RNT().hashCode();
        }
        if (getAT_SE_B() != null) {
            _hashCode += getAT_SE_B().hashCode();
        }
        if (getAT_SE_E() != null) {
            _hashCode += getAT_SE_E().hashCode();
        }
        if (getAT_SSPEED() != null) {
            _hashCode += getAT_SSPEED().hashCode();
        }
        if (getAT_SUM() != null) {
            _hashCode += getAT_SUM().hashCode();
        }
        if (getAT_UNIT_TYPE() != null) {
            _hashCode += getAT_UNIT_TYPE().hashCode();
        }
        if (getAT_UPDN() != null) {
            _hashCode += getAT_UPDN().hashCode();
        }
        if (getFC_CHARIMPE() != null) {
            _hashCode += getFC_CHARIMPE().hashCode();
        }
        if (getFC_TYPE() != null) {
            _hashCode += getFC_TYPE().hashCode();
        }
        if (getFEED_LENGTH() != null) {
            _hashCode += getFEED_LENGTH().hashCode();
        }
        if (getFEED_LOSE() != null) {
            _hashCode += getFEED_LOSE().hashCode();
        }
        if (getFEED_LOSE_DESCRIPTION() != null) {
            _hashCode += getFEED_LOSE_DESCRIPTION().hashCode();
        }
        if (getFEED_MENU() != null) {
            _hashCode += getFEED_MENU().hashCode();
        }
        if (getFEED_MODEL() != null) {
            _hashCode += getFEED_MODEL().hashCode();
        }
        if (getSERIAL_NUM() != null) {
            _hashCode += getSERIAL_NUM().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(FS_ST_ANTENNA_ALL.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FS_ST_ANTENNA_ALL"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ANT_ANGLE");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "ANT_ANGLE"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ANT_ANGLE_DESCRIPTION");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "ANT_ANGLE_DESCRIPTION"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ANT_COEF");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "ANT_COEF"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ANT_EGAIN");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "ANT_EGAIN"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ANT_EPOLE");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "ANT_EPOLE"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ANT_GAIN");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "ANT_GAIN"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ANT_GAIN_DESCRIPTION");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "ANT_GAIN_DESCRIPTION"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ANT_GAIN_UNIT");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "ANT_GAIN_UNIT"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ANT_GTRATIO");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "ANT_GTRATIO"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ANT_HIGHT");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "ANT_HIGHT"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ANT_HIGHT_DESCRIPTION");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "ANT_HIGHT_DESCRIPTION"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ANT_LOWEFREQ");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "ANT_LOWEFREQ"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ANT_MANU");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "ANT_MANU"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ANT_MODEL");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "ANT_MODEL"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ANT_NAME");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "ANT_NAME"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ANT_NOISTEMP");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "ANT_NOISTEMP"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ANT_POLE");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "ANT_POLE"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ANT_RGAIN");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "ANT_RGAIN"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ANT_RPOLE");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "ANT_RPOLE"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ANT_SIZE");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "ANT_SIZE"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ANT_TYPE");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "ANT_TYPE"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ANT_UPPEFREQ");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "ANT_UPPEFREQ"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ANT_WORK_TYPE");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "ANT_WORK_TYPE"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("AT_3DBE");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "AT_3DBE"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("AT_3DBR");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "AT_3DBR"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("AT_ANG_B");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "AT_ANG_B"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("AT_ANG_E");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "AT_ANG_E"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("AT_ANT_NO");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "AT_ANT_NO"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("AT_ANT_UPANG");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "AT_ANT_UPANG"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("AT_BWID");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "AT_BWID"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("AT_CCODE");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "AT_CCODE"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("AT_CSGN");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "AT_CSGN"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("AT_EANG");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "AT_EANG"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("AT_H_HPIC");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "AT_H_HPIC"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("AT_H_VPIC");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "AT_H_VPIC"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("AT_LEL");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "AT_LEL"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("AT_QUA");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "AT_QUA"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("AT_RANG");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "AT_RANG"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("AT_RNT");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "AT_RNT"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("AT_SE_B");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "AT_SE_B"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("AT_SE_E");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "AT_SE_E"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("AT_SSPEED");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "AT_SSPEED"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("AT_SUM");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "AT_SUM"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("AT_UNIT_TYPE");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "AT_UNIT_TYPE"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("AT_UPDN");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "AT_UPDN"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FC_CHARIMPE");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FC_CHARIMPE"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FC_TYPE");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FC_TYPE"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FEED_LENGTH");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FEED_LENGTH"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FEED_LOSE");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FEED_LOSE"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FEED_LOSE_DESCRIPTION");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FEED_LOSE_DESCRIPTION"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FEED_MENU");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FEED_MENU"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FEED_MODEL");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FEED_MODEL"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("SERIAL_NUM");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "SERIAL_NUM"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
