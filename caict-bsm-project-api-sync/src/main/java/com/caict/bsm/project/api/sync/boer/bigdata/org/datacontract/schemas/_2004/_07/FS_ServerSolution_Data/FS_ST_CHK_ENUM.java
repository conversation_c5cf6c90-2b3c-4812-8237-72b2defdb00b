/**
 * FS_ST_CHK_ENUM.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.caict.bsm.project.api.sync.boer.bigdata.org.datacontract.schemas._2004._07.FS_ServerSolution_Data;

public class FS_ST_CHK_ENUM  implements java.io.Serializable {
    private String CHK_ITEM_ID;

    private String CLASS_NAME;

    private String CODE;

    private String DB_TNAME;

    private String IS_DEFAULT;

    private String NAME;

    private String STAT_APP_TYPE;

    private String ST_CLASS_CODE;

    public FS_ST_CHK_ENUM() {
    }

    public FS_ST_CHK_ENUM(
           String CHK_ITEM_ID,
           String CLASS_NAME,
           String CODE,
           String DB_TNAME,
           String IS_DEFAULT,
           String NAME,
           String STAT_APP_TYPE,
           String ST_CLASS_CODE) {
           this.CHK_ITEM_ID = CHK_ITEM_ID;
           this.CLASS_NAME = CLASS_NAME;
           this.CODE = CODE;
           this.DB_TNAME = DB_TNAME;
           this.IS_DEFAULT = IS_DEFAULT;
           this.NAME = NAME;
           this.STAT_APP_TYPE = STAT_APP_TYPE;
           this.ST_CLASS_CODE = ST_CLASS_CODE;
    }


    /**
     * Gets the CHK_ITEM_ID value for this FS_ST_CHK_ENUM.
     * 
     * @return CHK_ITEM_ID
     */
    public String getCHK_ITEM_ID() {
        return CHK_ITEM_ID;
    }


    /**
     * Sets the CHK_ITEM_ID value for this FS_ST_CHK_ENUM.
     * 
     * @param CHK_ITEM_ID
     */
    public void setCHK_ITEM_ID(String CHK_ITEM_ID) {
        this.CHK_ITEM_ID = CHK_ITEM_ID;
    }


    /**
     * Gets the CLASS_NAME value for this FS_ST_CHK_ENUM.
     * 
     * @return CLASS_NAME
     */
    public String getCLASS_NAME() {
        return CLASS_NAME;
    }


    /**
     * Sets the CLASS_NAME value for this FS_ST_CHK_ENUM.
     * 
     * @param CLASS_NAME
     */
    public void setCLASS_NAME(String CLASS_NAME) {
        this.CLASS_NAME = CLASS_NAME;
    }


    /**
     * Gets the CODE value for this FS_ST_CHK_ENUM.
     * 
     * @return CODE
     */
    public String getCODE() {
        return CODE;
    }


    /**
     * Sets the CODE value for this FS_ST_CHK_ENUM.
     * 
     * @param CODE
     */
    public void setCODE(String CODE) {
        this.CODE = CODE;
    }


    /**
     * Gets the DB_TNAME value for this FS_ST_CHK_ENUM.
     * 
     * @return DB_TNAME
     */
    public String getDB_TNAME() {
        return DB_TNAME;
    }


    /**
     * Sets the DB_TNAME value for this FS_ST_CHK_ENUM.
     * 
     * @param DB_TNAME
     */
    public void setDB_TNAME(String DB_TNAME) {
        this.DB_TNAME = DB_TNAME;
    }


    /**
     * Gets the IS_DEFAULT value for this FS_ST_CHK_ENUM.
     * 
     * @return IS_DEFAULT
     */
    public String getIS_DEFAULT() {
        return IS_DEFAULT;
    }


    /**
     * Sets the IS_DEFAULT value for this FS_ST_CHK_ENUM.
     * 
     * @param IS_DEFAULT
     */
    public void setIS_DEFAULT(String IS_DEFAULT) {
        this.IS_DEFAULT = IS_DEFAULT;
    }


    /**
     * Gets the NAME value for this FS_ST_CHK_ENUM.
     * 
     * @return NAME
     */
    public String getNAME() {
        return NAME;
    }


    /**
     * Sets the NAME value for this FS_ST_CHK_ENUM.
     * 
     * @param NAME
     */
    public void setNAME(String NAME) {
        this.NAME = NAME;
    }


    /**
     * Gets the STAT_APP_TYPE value for this FS_ST_CHK_ENUM.
     * 
     * @return STAT_APP_TYPE
     */
    public String getSTAT_APP_TYPE() {
        return STAT_APP_TYPE;
    }


    /**
     * Sets the STAT_APP_TYPE value for this FS_ST_CHK_ENUM.
     * 
     * @param STAT_APP_TYPE
     */
    public void setSTAT_APP_TYPE(String STAT_APP_TYPE) {
        this.STAT_APP_TYPE = STAT_APP_TYPE;
    }


    /**
     * Gets the ST_CLASS_CODE value for this FS_ST_CHK_ENUM.
     * 
     * @return ST_CLASS_CODE
     */
    public String getST_CLASS_CODE() {
        return ST_CLASS_CODE;
    }


    /**
     * Sets the ST_CLASS_CODE value for this FS_ST_CHK_ENUM.
     * 
     * @param ST_CLASS_CODE
     */
    public void setST_CLASS_CODE(String ST_CLASS_CODE) {
        this.ST_CLASS_CODE = ST_CLASS_CODE;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof FS_ST_CHK_ENUM)) return false;
        FS_ST_CHK_ENUM other = (FS_ST_CHK_ENUM) obj;
        if (obj == null) return false;
        if (this == obj) return true;
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.CHK_ITEM_ID==null && other.getCHK_ITEM_ID()==null) || 
             (this.CHK_ITEM_ID!=null &&
              this.CHK_ITEM_ID.equals(other.getCHK_ITEM_ID()))) &&
            ((this.CLASS_NAME==null && other.getCLASS_NAME()==null) || 
             (this.CLASS_NAME!=null &&
              this.CLASS_NAME.equals(other.getCLASS_NAME()))) &&
            ((this.CODE==null && other.getCODE()==null) || 
             (this.CODE!=null &&
              this.CODE.equals(other.getCODE()))) &&
            ((this.DB_TNAME==null && other.getDB_TNAME()==null) || 
             (this.DB_TNAME!=null &&
              this.DB_TNAME.equals(other.getDB_TNAME()))) &&
            ((this.IS_DEFAULT==null && other.getIS_DEFAULT()==null) || 
             (this.IS_DEFAULT!=null &&
              this.IS_DEFAULT.equals(other.getIS_DEFAULT()))) &&
            ((this.NAME==null && other.getNAME()==null) || 
             (this.NAME!=null &&
              this.NAME.equals(other.getNAME()))) &&
            ((this.STAT_APP_TYPE==null && other.getSTAT_APP_TYPE()==null) || 
             (this.STAT_APP_TYPE!=null &&
              this.STAT_APP_TYPE.equals(other.getSTAT_APP_TYPE()))) &&
            ((this.ST_CLASS_CODE==null && other.getST_CLASS_CODE()==null) || 
             (this.ST_CLASS_CODE!=null &&
              this.ST_CLASS_CODE.equals(other.getST_CLASS_CODE())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getCHK_ITEM_ID() != null) {
            _hashCode += getCHK_ITEM_ID().hashCode();
        }
        if (getCLASS_NAME() != null) {
            _hashCode += getCLASS_NAME().hashCode();
        }
        if (getCODE() != null) {
            _hashCode += getCODE().hashCode();
        }
        if (getDB_TNAME() != null) {
            _hashCode += getDB_TNAME().hashCode();
        }
        if (getIS_DEFAULT() != null) {
            _hashCode += getIS_DEFAULT().hashCode();
        }
        if (getNAME() != null) {
            _hashCode += getNAME().hashCode();
        }
        if (getSTAT_APP_TYPE() != null) {
            _hashCode += getSTAT_APP_TYPE().hashCode();
        }
        if (getST_CLASS_CODE() != null) {
            _hashCode += getST_CLASS_CODE().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(FS_ST_CHK_ENUM.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FS_ST_CHK_ENUM"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("CHK_ITEM_ID");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "CHK_ITEM_ID"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("CLASS_NAME");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "CLASS_NAME"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("CODE");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "CODE"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("DB_TNAME");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "DB_TNAME"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("IS_DEFAULT");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "IS_DEFAULT"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("NAME");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "NAME"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("STAT_APP_TYPE");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "STAT_APP_TYPE"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ST_CLASS_CODE");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "ST_CLASS_CODE"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
