/**
 * FS_ST_FREQ_ALL.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.caict.bsm.project.api.sync.boer.bigdata.org.datacontract.schemas._2004._07.FS_ServerSolution_Data;

public class FS_ST_FREQ_ALL  implements java.io.Serializable {
    private String FREQ_CODE;

    private String FREQ_EB_UNIT_T;

    private String FREQ_EFB;

    private String FREQ_EFB_DESCRIPTION;

    private String FREQ_EFB_T;

    private String FREQ_EFE;

    private String FREQ_EFE_T;

    private String FREQ_EIRP_T;

    private String FREQ_EIRP_UNIT_T;

    private String FREQ_E_BAND;

    private String FREQ_E_BAND_DESCRIPTION;

    private String FREQ_E_BAND_T;

    private String FREQ_E_UNIT_T;

    private String FREQ_LC;

    private String FREQ_MB;

    private String FREQ_MOD;

    private Integer FREQ_NO_T;

    private String FREQ_POW_T;

    private String FREQ_POW_UNIT_T;

    private String FREQ_RB_UNIT_T;

    private String FREQ_RFB;

    private String FREQ_RFB_DESCRIPTION;

    private String FREQ_RFB_T;

    private String FREQ_RFE;

    private String FREQ_RFE_T;

    private String FREQ_R_BAND;

    private String FREQ_R_BAND_DESCRIPTION;

    private String FREQ_R_BAND_T;

    private String FREQ_R_UNIT_T;

    private Integer FREQ_TYPE;

    private String FREQ_UC;

    private Integer FT_CCODE_T;

    private String FT_FREQ_CCODE;

    private String FT_FREQ_CSGN;

    private String FT_FREQ_DUPDN;

    private String FT_FREQ_EIRP;

    private String FT_FREQ_EPOW;

    private String FT_FREQ_FEP;

    private String FT_FREQ_FRP;

    private String FT_FREQ_HCL;

    private String FT_FREQ_INFO_TYPE;

    private String FT_FREQ_MC;

    private String FT_FREQ_MC1;

    private String FT_FREQ_MC2;

    private String FT_FREQ_MC3;

    private Integer FT_FREQ_NO;

    private String FT_FREQ_POWFLAG;

    private String FT_FREQ_POW_AVG;

    private String FT_FREQ_POW_MAX;

    private String FT_FREQ_TIMEB;

    private String FT_FREQ_TIMEE;

    private String FT_FREQ_TYPE;

    private String FT_FREQ_UNIT_TYPE;

    private Integer SERIAL_NUM;

    private String WORK_MODE;

    public FS_ST_FREQ_ALL() {
    }

    public FS_ST_FREQ_ALL(
           String FREQ_CODE,
           String FREQ_EB_UNIT_T,
           String FREQ_EFB,
           String FREQ_EFB_DESCRIPTION,
           String FREQ_EFB_T,
           String FREQ_EFE,
           String FREQ_EFE_T,
           String FREQ_EIRP_T,
           String FREQ_EIRP_UNIT_T,
           String FREQ_E_BAND,
           String FREQ_E_BAND_DESCRIPTION,
           String FREQ_E_BAND_T,
           String FREQ_E_UNIT_T,
           String FREQ_LC,
           String FREQ_MB,
           String FREQ_MOD,
           Integer FREQ_NO_T,
           String FREQ_POW_T,
           String FREQ_POW_UNIT_T,
           String FREQ_RB_UNIT_T,
           String FREQ_RFB,
           String FREQ_RFB_DESCRIPTION,
           String FREQ_RFB_T,
           String FREQ_RFE,
           String FREQ_RFE_T,
           String FREQ_R_BAND,
           String FREQ_R_BAND_DESCRIPTION,
           String FREQ_R_BAND_T,
           String FREQ_R_UNIT_T,
           Integer FREQ_TYPE,
           String FREQ_UC,
           Integer FT_CCODE_T,
           String FT_FREQ_CCODE,
           String FT_FREQ_CSGN,
           String FT_FREQ_DUPDN,
           String FT_FREQ_EIRP,
           String FT_FREQ_EPOW,
           String FT_FREQ_FEP,
           String FT_FREQ_FRP,
           String FT_FREQ_HCL,
           String FT_FREQ_INFO_TYPE,
           String FT_FREQ_MC,
           String FT_FREQ_MC1,
           String FT_FREQ_MC2,
           String FT_FREQ_MC3,
           Integer FT_FREQ_NO,
           String FT_FREQ_POWFLAG,
           String FT_FREQ_POW_AVG,
           String FT_FREQ_POW_MAX,
           String FT_FREQ_TIMEB,
           String FT_FREQ_TIMEE,
           String FT_FREQ_TYPE,
           String FT_FREQ_UNIT_TYPE,
           Integer SERIAL_NUM,
           String WORK_MODE) {
           this.FREQ_CODE = FREQ_CODE;
           this.FREQ_EB_UNIT_T = FREQ_EB_UNIT_T;
           this.FREQ_EFB = FREQ_EFB;
           this.FREQ_EFB_DESCRIPTION = FREQ_EFB_DESCRIPTION;
           this.FREQ_EFB_T = FREQ_EFB_T;
           this.FREQ_EFE = FREQ_EFE;
           this.FREQ_EFE_T = FREQ_EFE_T;
           this.FREQ_EIRP_T = FREQ_EIRP_T;
           this.FREQ_EIRP_UNIT_T = FREQ_EIRP_UNIT_T;
           this.FREQ_E_BAND = FREQ_E_BAND;
           this.FREQ_E_BAND_DESCRIPTION = FREQ_E_BAND_DESCRIPTION;
           this.FREQ_E_BAND_T = FREQ_E_BAND_T;
           this.FREQ_E_UNIT_T = FREQ_E_UNIT_T;
           this.FREQ_LC = FREQ_LC;
           this.FREQ_MB = FREQ_MB;
           this.FREQ_MOD = FREQ_MOD;
           this.FREQ_NO_T = FREQ_NO_T;
           this.FREQ_POW_T = FREQ_POW_T;
           this.FREQ_POW_UNIT_T = FREQ_POW_UNIT_T;
           this.FREQ_RB_UNIT_T = FREQ_RB_UNIT_T;
           this.FREQ_RFB = FREQ_RFB;
           this.FREQ_RFB_DESCRIPTION = FREQ_RFB_DESCRIPTION;
           this.FREQ_RFB_T = FREQ_RFB_T;
           this.FREQ_RFE = FREQ_RFE;
           this.FREQ_RFE_T = FREQ_RFE_T;
           this.FREQ_R_BAND = FREQ_R_BAND;
           this.FREQ_R_BAND_DESCRIPTION = FREQ_R_BAND_DESCRIPTION;
           this.FREQ_R_BAND_T = FREQ_R_BAND_T;
           this.FREQ_R_UNIT_T = FREQ_R_UNIT_T;
           this.FREQ_TYPE = FREQ_TYPE;
           this.FREQ_UC = FREQ_UC;
           this.FT_CCODE_T = FT_CCODE_T;
           this.FT_FREQ_CCODE = FT_FREQ_CCODE;
           this.FT_FREQ_CSGN = FT_FREQ_CSGN;
           this.FT_FREQ_DUPDN = FT_FREQ_DUPDN;
           this.FT_FREQ_EIRP = FT_FREQ_EIRP;
           this.FT_FREQ_EPOW = FT_FREQ_EPOW;
           this.FT_FREQ_FEP = FT_FREQ_FEP;
           this.FT_FREQ_FRP = FT_FREQ_FRP;
           this.FT_FREQ_HCL = FT_FREQ_HCL;
           this.FT_FREQ_INFO_TYPE = FT_FREQ_INFO_TYPE;
           this.FT_FREQ_MC = FT_FREQ_MC;
           this.FT_FREQ_MC1 = FT_FREQ_MC1;
           this.FT_FREQ_MC2 = FT_FREQ_MC2;
           this.FT_FREQ_MC3 = FT_FREQ_MC3;
           this.FT_FREQ_NO = FT_FREQ_NO;
           this.FT_FREQ_POWFLAG = FT_FREQ_POWFLAG;
           this.FT_FREQ_POW_AVG = FT_FREQ_POW_AVG;
           this.FT_FREQ_POW_MAX = FT_FREQ_POW_MAX;
           this.FT_FREQ_TIMEB = FT_FREQ_TIMEB;
           this.FT_FREQ_TIMEE = FT_FREQ_TIMEE;
           this.FT_FREQ_TYPE = FT_FREQ_TYPE;
           this.FT_FREQ_UNIT_TYPE = FT_FREQ_UNIT_TYPE;
           this.SERIAL_NUM = SERIAL_NUM;
           this.WORK_MODE = WORK_MODE;
    }


    /**
     * Gets the FREQ_CODE value for this FS_ST_FREQ_ALL.
     * 
     * @return FREQ_CODE
     */
    public String getFREQ_CODE() {
        return FREQ_CODE;
    }


    /**
     * Sets the FREQ_CODE value for this FS_ST_FREQ_ALL.
     * 
     * @param FREQ_CODE
     */
    public void setFREQ_CODE(String FREQ_CODE) {
        this.FREQ_CODE = FREQ_CODE;
    }


    /**
     * Gets the FREQ_EB_UNIT_T value for this FS_ST_FREQ_ALL.
     * 
     * @return FREQ_EB_UNIT_T
     */
    public String getFREQ_EB_UNIT_T() {
        return FREQ_EB_UNIT_T;
    }


    /**
     * Sets the FREQ_EB_UNIT_T value for this FS_ST_FREQ_ALL.
     * 
     * @param FREQ_EB_UNIT_T
     */
    public void setFREQ_EB_UNIT_T(String FREQ_EB_UNIT_T) {
        this.FREQ_EB_UNIT_T = FREQ_EB_UNIT_T;
    }


    /**
     * Gets the FREQ_EFB value for this FS_ST_FREQ_ALL.
     * 
     * @return FREQ_EFB
     */
    public String getFREQ_EFB() {
        return FREQ_EFB;
    }


    /**
     * Sets the FREQ_EFB value for this FS_ST_FREQ_ALL.
     * 
     * @param FREQ_EFB
     */
    public void setFREQ_EFB(String FREQ_EFB) {
        this.FREQ_EFB = FREQ_EFB;
    }


    /**
     * Gets the FREQ_EFB_DESCRIPTION value for this FS_ST_FREQ_ALL.
     * 
     * @return FREQ_EFB_DESCRIPTION
     */
    public String getFREQ_EFB_DESCRIPTION() {
        return FREQ_EFB_DESCRIPTION;
    }


    /**
     * Sets the FREQ_EFB_DESCRIPTION value for this FS_ST_FREQ_ALL.
     * 
     * @param FREQ_EFB_DESCRIPTION
     */
    public void setFREQ_EFB_DESCRIPTION(String FREQ_EFB_DESCRIPTION) {
        this.FREQ_EFB_DESCRIPTION = FREQ_EFB_DESCRIPTION;
    }


    /**
     * Gets the FREQ_EFB_T value for this FS_ST_FREQ_ALL.
     * 
     * @return FREQ_EFB_T
     */
    public String getFREQ_EFB_T() {
        return FREQ_EFB_T;
    }


    /**
     * Sets the FREQ_EFB_T value for this FS_ST_FREQ_ALL.
     * 
     * @param FREQ_EFB_T
     */
    public void setFREQ_EFB_T(String FREQ_EFB_T) {
        this.FREQ_EFB_T = FREQ_EFB_T;
    }


    /**
     * Gets the FREQ_EFE value for this FS_ST_FREQ_ALL.
     * 
     * @return FREQ_EFE
     */
    public String getFREQ_EFE() {
        return FREQ_EFE;
    }


    /**
     * Sets the FREQ_EFE value for this FS_ST_FREQ_ALL.
     * 
     * @param FREQ_EFE
     */
    public void setFREQ_EFE(String FREQ_EFE) {
        this.FREQ_EFE = FREQ_EFE;
    }


    /**
     * Gets the FREQ_EFE_T value for this FS_ST_FREQ_ALL.
     * 
     * @return FREQ_EFE_T
     */
    public String getFREQ_EFE_T() {
        return FREQ_EFE_T;
    }


    /**
     * Sets the FREQ_EFE_T value for this FS_ST_FREQ_ALL.
     * 
     * @param FREQ_EFE_T
     */
    public void setFREQ_EFE_T(String FREQ_EFE_T) {
        this.FREQ_EFE_T = FREQ_EFE_T;
    }


    /**
     * Gets the FREQ_EIRP_T value for this FS_ST_FREQ_ALL.
     * 
     * @return FREQ_EIRP_T
     */
    public String getFREQ_EIRP_T() {
        return FREQ_EIRP_T;
    }


    /**
     * Sets the FREQ_EIRP_T value for this FS_ST_FREQ_ALL.
     * 
     * @param FREQ_EIRP_T
     */
    public void setFREQ_EIRP_T(String FREQ_EIRP_T) {
        this.FREQ_EIRP_T = FREQ_EIRP_T;
    }


    /**
     * Gets the FREQ_EIRP_UNIT_T value for this FS_ST_FREQ_ALL.
     * 
     * @return FREQ_EIRP_UNIT_T
     */
    public String getFREQ_EIRP_UNIT_T() {
        return FREQ_EIRP_UNIT_T;
    }


    /**
     * Sets the FREQ_EIRP_UNIT_T value for this FS_ST_FREQ_ALL.
     * 
     * @param FREQ_EIRP_UNIT_T
     */
    public void setFREQ_EIRP_UNIT_T(String FREQ_EIRP_UNIT_T) {
        this.FREQ_EIRP_UNIT_T = FREQ_EIRP_UNIT_T;
    }


    /**
     * Gets the FREQ_E_BAND value for this FS_ST_FREQ_ALL.
     * 
     * @return FREQ_E_BAND
     */
    public String getFREQ_E_BAND() {
        return FREQ_E_BAND;
    }


    /**
     * Sets the FREQ_E_BAND value for this FS_ST_FREQ_ALL.
     * 
     * @param FREQ_E_BAND
     */
    public void setFREQ_E_BAND(String FREQ_E_BAND) {
        this.FREQ_E_BAND = FREQ_E_BAND;
    }


    /**
     * Gets the FREQ_E_BAND_DESCRIPTION value for this FS_ST_FREQ_ALL.
     * 
     * @return FREQ_E_BAND_DESCRIPTION
     */
    public String getFREQ_E_BAND_DESCRIPTION() {
        return FREQ_E_BAND_DESCRIPTION;
    }


    /**
     * Sets the FREQ_E_BAND_DESCRIPTION value for this FS_ST_FREQ_ALL.
     * 
     * @param FREQ_E_BAND_DESCRIPTION
     */
    public void setFREQ_E_BAND_DESCRIPTION(String FREQ_E_BAND_DESCRIPTION) {
        this.FREQ_E_BAND_DESCRIPTION = FREQ_E_BAND_DESCRIPTION;
    }


    /**
     * Gets the FREQ_E_BAND_T value for this FS_ST_FREQ_ALL.
     * 
     * @return FREQ_E_BAND_T
     */
    public String getFREQ_E_BAND_T() {
        return FREQ_E_BAND_T;
    }


    /**
     * Sets the FREQ_E_BAND_T value for this FS_ST_FREQ_ALL.
     * 
     * @param FREQ_E_BAND_T
     */
    public void setFREQ_E_BAND_T(String FREQ_E_BAND_T) {
        this.FREQ_E_BAND_T = FREQ_E_BAND_T;
    }


    /**
     * Gets the FREQ_E_UNIT_T value for this FS_ST_FREQ_ALL.
     * 
     * @return FREQ_E_UNIT_T
     */
    public String getFREQ_E_UNIT_T() {
        return FREQ_E_UNIT_T;
    }


    /**
     * Sets the FREQ_E_UNIT_T value for this FS_ST_FREQ_ALL.
     * 
     * @param FREQ_E_UNIT_T
     */
    public void setFREQ_E_UNIT_T(String FREQ_E_UNIT_T) {
        this.FREQ_E_UNIT_T = FREQ_E_UNIT_T;
    }


    /**
     * Gets the FREQ_LC value for this FS_ST_FREQ_ALL.
     * 
     * @return FREQ_LC
     */
    public String getFREQ_LC() {
        return FREQ_LC;
    }


    /**
     * Sets the FREQ_LC value for this FS_ST_FREQ_ALL.
     * 
     * @param FREQ_LC
     */
    public void setFREQ_LC(String FREQ_LC) {
        this.FREQ_LC = FREQ_LC;
    }


    /**
     * Gets the FREQ_MB value for this FS_ST_FREQ_ALL.
     * 
     * @return FREQ_MB
     */
    public String getFREQ_MB() {
        return FREQ_MB;
    }


    /**
     * Sets the FREQ_MB value for this FS_ST_FREQ_ALL.
     * 
     * @param FREQ_MB
     */
    public void setFREQ_MB(String FREQ_MB) {
        this.FREQ_MB = FREQ_MB;
    }


    /**
     * Gets the FREQ_MOD value for this FS_ST_FREQ_ALL.
     * 
     * @return FREQ_MOD
     */
    public String getFREQ_MOD() {
        return FREQ_MOD;
    }


    /**
     * Sets the FREQ_MOD value for this FS_ST_FREQ_ALL.
     * 
     * @param FREQ_MOD
     */
    public void setFREQ_MOD(String FREQ_MOD) {
        this.FREQ_MOD = FREQ_MOD;
    }


    /**
     * Gets the FREQ_NO_T value for this FS_ST_FREQ_ALL.
     * 
     * @return FREQ_NO_T
     */
    public Integer getFREQ_NO_T() {
        return FREQ_NO_T;
    }


    /**
     * Sets the FREQ_NO_T value for this FS_ST_FREQ_ALL.
     * 
     * @param FREQ_NO_T
     */
    public void setFREQ_NO_T(Integer FREQ_NO_T) {
        this.FREQ_NO_T = FREQ_NO_T;
    }


    /**
     * Gets the FREQ_POW_T value for this FS_ST_FREQ_ALL.
     * 
     * @return FREQ_POW_T
     */
    public String getFREQ_POW_T() {
        return FREQ_POW_T;
    }


    /**
     * Sets the FREQ_POW_T value for this FS_ST_FREQ_ALL.
     * 
     * @param FREQ_POW_T
     */
    public void setFREQ_POW_T(String FREQ_POW_T) {
        this.FREQ_POW_T = FREQ_POW_T;
    }


    /**
     * Gets the FREQ_POW_UNIT_T value for this FS_ST_FREQ_ALL.
     * 
     * @return FREQ_POW_UNIT_T
     */
    public String getFREQ_POW_UNIT_T() {
        return FREQ_POW_UNIT_T;
    }


    /**
     * Sets the FREQ_POW_UNIT_T value for this FS_ST_FREQ_ALL.
     * 
     * @param FREQ_POW_UNIT_T
     */
    public void setFREQ_POW_UNIT_T(String FREQ_POW_UNIT_T) {
        this.FREQ_POW_UNIT_T = FREQ_POW_UNIT_T;
    }


    /**
     * Gets the FREQ_RB_UNIT_T value for this FS_ST_FREQ_ALL.
     * 
     * @return FREQ_RB_UNIT_T
     */
    public String getFREQ_RB_UNIT_T() {
        return FREQ_RB_UNIT_T;
    }


    /**
     * Sets the FREQ_RB_UNIT_T value for this FS_ST_FREQ_ALL.
     * 
     * @param FREQ_RB_UNIT_T
     */
    public void setFREQ_RB_UNIT_T(String FREQ_RB_UNIT_T) {
        this.FREQ_RB_UNIT_T = FREQ_RB_UNIT_T;
    }


    /**
     * Gets the FREQ_RFB value for this FS_ST_FREQ_ALL.
     * 
     * @return FREQ_RFB
     */
    public String getFREQ_RFB() {
        return FREQ_RFB;
    }


    /**
     * Sets the FREQ_RFB value for this FS_ST_FREQ_ALL.
     * 
     * @param FREQ_RFB
     */
    public void setFREQ_RFB(String FREQ_RFB) {
        this.FREQ_RFB = FREQ_RFB;
    }


    /**
     * Gets the FREQ_RFB_DESCRIPTION value for this FS_ST_FREQ_ALL.
     * 
     * @return FREQ_RFB_DESCRIPTION
     */
    public String getFREQ_RFB_DESCRIPTION() {
        return FREQ_RFB_DESCRIPTION;
    }


    /**
     * Sets the FREQ_RFB_DESCRIPTION value for this FS_ST_FREQ_ALL.
     * 
     * @param FREQ_RFB_DESCRIPTION
     */
    public void setFREQ_RFB_DESCRIPTION(String FREQ_RFB_DESCRIPTION) {
        this.FREQ_RFB_DESCRIPTION = FREQ_RFB_DESCRIPTION;
    }


    /**
     * Gets the FREQ_RFB_T value for this FS_ST_FREQ_ALL.
     * 
     * @return FREQ_RFB_T
     */
    public String getFREQ_RFB_T() {
        return FREQ_RFB_T;
    }


    /**
     * Sets the FREQ_RFB_T value for this FS_ST_FREQ_ALL.
     * 
     * @param FREQ_RFB_T
     */
    public void setFREQ_RFB_T(String FREQ_RFB_T) {
        this.FREQ_RFB_T = FREQ_RFB_T;
    }


    /**
     * Gets the FREQ_RFE value for this FS_ST_FREQ_ALL.
     * 
     * @return FREQ_RFE
     */
    public String getFREQ_RFE() {
        return FREQ_RFE;
    }


    /**
     * Sets the FREQ_RFE value for this FS_ST_FREQ_ALL.
     * 
     * @param FREQ_RFE
     */
    public void setFREQ_RFE(String FREQ_RFE) {
        this.FREQ_RFE = FREQ_RFE;
    }


    /**
     * Gets the FREQ_RFE_T value for this FS_ST_FREQ_ALL.
     * 
     * @return FREQ_RFE_T
     */
    public String getFREQ_RFE_T() {
        return FREQ_RFE_T;
    }


    /**
     * Sets the FREQ_RFE_T value for this FS_ST_FREQ_ALL.
     * 
     * @param FREQ_RFE_T
     */
    public void setFREQ_RFE_T(String FREQ_RFE_T) {
        this.FREQ_RFE_T = FREQ_RFE_T;
    }


    /**
     * Gets the FREQ_R_BAND value for this FS_ST_FREQ_ALL.
     * 
     * @return FREQ_R_BAND
     */
    public String getFREQ_R_BAND() {
        return FREQ_R_BAND;
    }


    /**
     * Sets the FREQ_R_BAND value for this FS_ST_FREQ_ALL.
     * 
     * @param FREQ_R_BAND
     */
    public void setFREQ_R_BAND(String FREQ_R_BAND) {
        this.FREQ_R_BAND = FREQ_R_BAND;
    }


    /**
     * Gets the FREQ_R_BAND_DESCRIPTION value for this FS_ST_FREQ_ALL.
     * 
     * @return FREQ_R_BAND_DESCRIPTION
     */
    public String getFREQ_R_BAND_DESCRIPTION() {
        return FREQ_R_BAND_DESCRIPTION;
    }


    /**
     * Sets the FREQ_R_BAND_DESCRIPTION value for this FS_ST_FREQ_ALL.
     * 
     * @param FREQ_R_BAND_DESCRIPTION
     */
    public void setFREQ_R_BAND_DESCRIPTION(String FREQ_R_BAND_DESCRIPTION) {
        this.FREQ_R_BAND_DESCRIPTION = FREQ_R_BAND_DESCRIPTION;
    }


    /**
     * Gets the FREQ_R_BAND_T value for this FS_ST_FREQ_ALL.
     * 
     * @return FREQ_R_BAND_T
     */
    public String getFREQ_R_BAND_T() {
        return FREQ_R_BAND_T;
    }


    /**
     * Sets the FREQ_R_BAND_T value for this FS_ST_FREQ_ALL.
     * 
     * @param FREQ_R_BAND_T
     */
    public void setFREQ_R_BAND_T(String FREQ_R_BAND_T) {
        this.FREQ_R_BAND_T = FREQ_R_BAND_T;
    }


    /**
     * Gets the FREQ_R_UNIT_T value for this FS_ST_FREQ_ALL.
     * 
     * @return FREQ_R_UNIT_T
     */
    public String getFREQ_R_UNIT_T() {
        return FREQ_R_UNIT_T;
    }


    /**
     * Sets the FREQ_R_UNIT_T value for this FS_ST_FREQ_ALL.
     * 
     * @param FREQ_R_UNIT_T
     */
    public void setFREQ_R_UNIT_T(String FREQ_R_UNIT_T) {
        this.FREQ_R_UNIT_T = FREQ_R_UNIT_T;
    }


    /**
     * Gets the FREQ_TYPE value for this FS_ST_FREQ_ALL.
     * 
     * @return FREQ_TYPE
     */
    public Integer getFREQ_TYPE() {
        return FREQ_TYPE;
    }


    /**
     * Sets the FREQ_TYPE value for this FS_ST_FREQ_ALL.
     * 
     * @param FREQ_TYPE
     */
    public void setFREQ_TYPE(Integer FREQ_TYPE) {
        this.FREQ_TYPE = FREQ_TYPE;
    }


    /**
     * Gets the FREQ_UC value for this FS_ST_FREQ_ALL.
     * 
     * @return FREQ_UC
     */
    public String getFREQ_UC() {
        return FREQ_UC;
    }


    /**
     * Sets the FREQ_UC value for this FS_ST_FREQ_ALL.
     * 
     * @param FREQ_UC
     */
    public void setFREQ_UC(String FREQ_UC) {
        this.FREQ_UC = FREQ_UC;
    }


    /**
     * Gets the FT_CCODE_T value for this FS_ST_FREQ_ALL.
     * 
     * @return FT_CCODE_T
     */
    public Integer getFT_CCODE_T() {
        return FT_CCODE_T;
    }


    /**
     * Sets the FT_CCODE_T value for this FS_ST_FREQ_ALL.
     * 
     * @param FT_CCODE_T
     */
    public void setFT_CCODE_T(Integer FT_CCODE_T) {
        this.FT_CCODE_T = FT_CCODE_T;
    }


    /**
     * Gets the FT_FREQ_CCODE value for this FS_ST_FREQ_ALL.
     * 
     * @return FT_FREQ_CCODE
     */
    public String getFT_FREQ_CCODE() {
        return FT_FREQ_CCODE;
    }


    /**
     * Sets the FT_FREQ_CCODE value for this FS_ST_FREQ_ALL.
     * 
     * @param FT_FREQ_CCODE
     */
    public void setFT_FREQ_CCODE(String FT_FREQ_CCODE) {
        this.FT_FREQ_CCODE = FT_FREQ_CCODE;
    }


    /**
     * Gets the FT_FREQ_CSGN value for this FS_ST_FREQ_ALL.
     * 
     * @return FT_FREQ_CSGN
     */
    public String getFT_FREQ_CSGN() {
        return FT_FREQ_CSGN;
    }


    /**
     * Sets the FT_FREQ_CSGN value for this FS_ST_FREQ_ALL.
     * 
     * @param FT_FREQ_CSGN
     */
    public void setFT_FREQ_CSGN(String FT_FREQ_CSGN) {
        this.FT_FREQ_CSGN = FT_FREQ_CSGN;
    }


    /**
     * Gets the FT_FREQ_DUPDN value for this FS_ST_FREQ_ALL.
     * 
     * @return FT_FREQ_DUPDN
     */
    public String getFT_FREQ_DUPDN() {
        return FT_FREQ_DUPDN;
    }


    /**
     * Sets the FT_FREQ_DUPDN value for this FS_ST_FREQ_ALL.
     * 
     * @param FT_FREQ_DUPDN
     */
    public void setFT_FREQ_DUPDN(String FT_FREQ_DUPDN) {
        this.FT_FREQ_DUPDN = FT_FREQ_DUPDN;
    }


    /**
     * Gets the FT_FREQ_EIRP value for this FS_ST_FREQ_ALL.
     * 
     * @return FT_FREQ_EIRP
     */
    public String getFT_FREQ_EIRP() {
        return FT_FREQ_EIRP;
    }


    /**
     * Sets the FT_FREQ_EIRP value for this FS_ST_FREQ_ALL.
     * 
     * @param FT_FREQ_EIRP
     */
    public void setFT_FREQ_EIRP(String FT_FREQ_EIRP) {
        this.FT_FREQ_EIRP = FT_FREQ_EIRP;
    }


    /**
     * Gets the FT_FREQ_EPOW value for this FS_ST_FREQ_ALL.
     * 
     * @return FT_FREQ_EPOW
     */
    public String getFT_FREQ_EPOW() {
        return FT_FREQ_EPOW;
    }


    /**
     * Sets the FT_FREQ_EPOW value for this FS_ST_FREQ_ALL.
     * 
     * @param FT_FREQ_EPOW
     */
    public void setFT_FREQ_EPOW(String FT_FREQ_EPOW) {
        this.FT_FREQ_EPOW = FT_FREQ_EPOW;
    }


    /**
     * Gets the FT_FREQ_FEP value for this FS_ST_FREQ_ALL.
     * 
     * @return FT_FREQ_FEP
     */
    public String getFT_FREQ_FEP() {
        return FT_FREQ_FEP;
    }


    /**
     * Sets the FT_FREQ_FEP value for this FS_ST_FREQ_ALL.
     * 
     * @param FT_FREQ_FEP
     */
    public void setFT_FREQ_FEP(String FT_FREQ_FEP) {
        this.FT_FREQ_FEP = FT_FREQ_FEP;
    }


    /**
     * Gets the FT_FREQ_FRP value for this FS_ST_FREQ_ALL.
     * 
     * @return FT_FREQ_FRP
     */
    public String getFT_FREQ_FRP() {
        return FT_FREQ_FRP;
    }


    /**
     * Sets the FT_FREQ_FRP value for this FS_ST_FREQ_ALL.
     * 
     * @param FT_FREQ_FRP
     */
    public void setFT_FREQ_FRP(String FT_FREQ_FRP) {
        this.FT_FREQ_FRP = FT_FREQ_FRP;
    }


    /**
     * Gets the FT_FREQ_HCL value for this FS_ST_FREQ_ALL.
     * 
     * @return FT_FREQ_HCL
     */
    public String getFT_FREQ_HCL() {
        return FT_FREQ_HCL;
    }


    /**
     * Sets the FT_FREQ_HCL value for this FS_ST_FREQ_ALL.
     * 
     * @param FT_FREQ_HCL
     */
    public void setFT_FREQ_HCL(String FT_FREQ_HCL) {
        this.FT_FREQ_HCL = FT_FREQ_HCL;
    }


    /**
     * Gets the FT_FREQ_INFO_TYPE value for this FS_ST_FREQ_ALL.
     * 
     * @return FT_FREQ_INFO_TYPE
     */
    public String getFT_FREQ_INFO_TYPE() {
        return FT_FREQ_INFO_TYPE;
    }


    /**
     * Sets the FT_FREQ_INFO_TYPE value for this FS_ST_FREQ_ALL.
     * 
     * @param FT_FREQ_INFO_TYPE
     */
    public void setFT_FREQ_INFO_TYPE(String FT_FREQ_INFO_TYPE) {
        this.FT_FREQ_INFO_TYPE = FT_FREQ_INFO_TYPE;
    }


    /**
     * Gets the FT_FREQ_MC value for this FS_ST_FREQ_ALL.
     * 
     * @return FT_FREQ_MC
     */
    public String getFT_FREQ_MC() {
        return FT_FREQ_MC;
    }


    /**
     * Sets the FT_FREQ_MC value for this FS_ST_FREQ_ALL.
     * 
     * @param FT_FREQ_MC
     */
    public void setFT_FREQ_MC(String FT_FREQ_MC) {
        this.FT_FREQ_MC = FT_FREQ_MC;
    }


    /**
     * Gets the FT_FREQ_MC1 value for this FS_ST_FREQ_ALL.
     * 
     * @return FT_FREQ_MC1
     */
    public String getFT_FREQ_MC1() {
        return FT_FREQ_MC1;
    }


    /**
     * Sets the FT_FREQ_MC1 value for this FS_ST_FREQ_ALL.
     * 
     * @param FT_FREQ_MC1
     */
    public void setFT_FREQ_MC1(String FT_FREQ_MC1) {
        this.FT_FREQ_MC1 = FT_FREQ_MC1;
    }


    /**
     * Gets the FT_FREQ_MC2 value for this FS_ST_FREQ_ALL.
     * 
     * @return FT_FREQ_MC2
     */
    public String getFT_FREQ_MC2() {
        return FT_FREQ_MC2;
    }


    /**
     * Sets the FT_FREQ_MC2 value for this FS_ST_FREQ_ALL.
     * 
     * @param FT_FREQ_MC2
     */
    public void setFT_FREQ_MC2(String FT_FREQ_MC2) {
        this.FT_FREQ_MC2 = FT_FREQ_MC2;
    }


    /**
     * Gets the FT_FREQ_MC3 value for this FS_ST_FREQ_ALL.
     * 
     * @return FT_FREQ_MC3
     */
    public String getFT_FREQ_MC3() {
        return FT_FREQ_MC3;
    }


    /**
     * Sets the FT_FREQ_MC3 value for this FS_ST_FREQ_ALL.
     * 
     * @param FT_FREQ_MC3
     */
    public void setFT_FREQ_MC3(String FT_FREQ_MC3) {
        this.FT_FREQ_MC3 = FT_FREQ_MC3;
    }


    /**
     * Gets the FT_FREQ_NO value for this FS_ST_FREQ_ALL.
     * 
     * @return FT_FREQ_NO
     */
    public Integer getFT_FREQ_NO() {
        return FT_FREQ_NO;
    }


    /**
     * Sets the FT_FREQ_NO value for this FS_ST_FREQ_ALL.
     * 
     * @param FT_FREQ_NO
     */
    public void setFT_FREQ_NO(Integer FT_FREQ_NO) {
        this.FT_FREQ_NO = FT_FREQ_NO;
    }


    /**
     * Gets the FT_FREQ_POWFLAG value for this FS_ST_FREQ_ALL.
     * 
     * @return FT_FREQ_POWFLAG
     */
    public String getFT_FREQ_POWFLAG() {
        return FT_FREQ_POWFLAG;
    }


    /**
     * Sets the FT_FREQ_POWFLAG value for this FS_ST_FREQ_ALL.
     * 
     * @param FT_FREQ_POWFLAG
     */
    public void setFT_FREQ_POWFLAG(String FT_FREQ_POWFLAG) {
        this.FT_FREQ_POWFLAG = FT_FREQ_POWFLAG;
    }


    /**
     * Gets the FT_FREQ_POW_AVG value for this FS_ST_FREQ_ALL.
     * 
     * @return FT_FREQ_POW_AVG
     */
    public String getFT_FREQ_POW_AVG() {
        return FT_FREQ_POW_AVG;
    }


    /**
     * Sets the FT_FREQ_POW_AVG value for this FS_ST_FREQ_ALL.
     * 
     * @param FT_FREQ_POW_AVG
     */
    public void setFT_FREQ_POW_AVG(String FT_FREQ_POW_AVG) {
        this.FT_FREQ_POW_AVG = FT_FREQ_POW_AVG;
    }


    /**
     * Gets the FT_FREQ_POW_MAX value for this FS_ST_FREQ_ALL.
     * 
     * @return FT_FREQ_POW_MAX
     */
    public String getFT_FREQ_POW_MAX() {
        return FT_FREQ_POW_MAX;
    }


    /**
     * Sets the FT_FREQ_POW_MAX value for this FS_ST_FREQ_ALL.
     * 
     * @param FT_FREQ_POW_MAX
     */
    public void setFT_FREQ_POW_MAX(String FT_FREQ_POW_MAX) {
        this.FT_FREQ_POW_MAX = FT_FREQ_POW_MAX;
    }


    /**
     * Gets the FT_FREQ_TIMEB value for this FS_ST_FREQ_ALL.
     * 
     * @return FT_FREQ_TIMEB
     */
    public String getFT_FREQ_TIMEB() {
        return FT_FREQ_TIMEB;
    }


    /**
     * Sets the FT_FREQ_TIMEB value for this FS_ST_FREQ_ALL.
     * 
     * @param FT_FREQ_TIMEB
     */
    public void setFT_FREQ_TIMEB(String FT_FREQ_TIMEB) {
        this.FT_FREQ_TIMEB = FT_FREQ_TIMEB;
    }


    /**
     * Gets the FT_FREQ_TIMEE value for this FS_ST_FREQ_ALL.
     * 
     * @return FT_FREQ_TIMEE
     */
    public String getFT_FREQ_TIMEE() {
        return FT_FREQ_TIMEE;
    }


    /**
     * Sets the FT_FREQ_TIMEE value for this FS_ST_FREQ_ALL.
     * 
     * @param FT_FREQ_TIMEE
     */
    public void setFT_FREQ_TIMEE(String FT_FREQ_TIMEE) {
        this.FT_FREQ_TIMEE = FT_FREQ_TIMEE;
    }


    /**
     * Gets the FT_FREQ_TYPE value for this FS_ST_FREQ_ALL.
     * 
     * @return FT_FREQ_TYPE
     */
    public String getFT_FREQ_TYPE() {
        return FT_FREQ_TYPE;
    }


    /**
     * Sets the FT_FREQ_TYPE value for this FS_ST_FREQ_ALL.
     * 
     * @param FT_FREQ_TYPE
     */
    public void setFT_FREQ_TYPE(String FT_FREQ_TYPE) {
        this.FT_FREQ_TYPE = FT_FREQ_TYPE;
    }


    /**
     * Gets the FT_FREQ_UNIT_TYPE value for this FS_ST_FREQ_ALL.
     * 
     * @return FT_FREQ_UNIT_TYPE
     */
    public String getFT_FREQ_UNIT_TYPE() {
        return FT_FREQ_UNIT_TYPE;
    }


    /**
     * Sets the FT_FREQ_UNIT_TYPE value for this FS_ST_FREQ_ALL.
     * 
     * @param FT_FREQ_UNIT_TYPE
     */
    public void setFT_FREQ_UNIT_TYPE(String FT_FREQ_UNIT_TYPE) {
        this.FT_FREQ_UNIT_TYPE = FT_FREQ_UNIT_TYPE;
    }


    /**
     * Gets the SERIAL_NUM value for this FS_ST_FREQ_ALL.
     * 
     * @return SERIAL_NUM
     */
    public Integer getSERIAL_NUM() {
        return SERIAL_NUM;
    }


    /**
     * Sets the SERIAL_NUM value for this FS_ST_FREQ_ALL.
     * 
     * @param SERIAL_NUM
     */
    public void setSERIAL_NUM(Integer SERIAL_NUM) {
        this.SERIAL_NUM = SERIAL_NUM;
    }


    /**
     * Gets the WORK_MODE value for this FS_ST_FREQ_ALL.
     * 
     * @return WORK_MODE
     */
    public String getWORK_MODE() {
        return WORK_MODE;
    }


    /**
     * Sets the WORK_MODE value for this FS_ST_FREQ_ALL.
     * 
     * @param WORK_MODE
     */
    public void setWORK_MODE(String WORK_MODE) {
        this.WORK_MODE = WORK_MODE;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof FS_ST_FREQ_ALL)) return false;
        FS_ST_FREQ_ALL other = (FS_ST_FREQ_ALL) obj;
        if (obj == null) return false;
        if (this == obj) return true;
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.FREQ_CODE==null && other.getFREQ_CODE()==null) || 
             (this.FREQ_CODE!=null &&
              this.FREQ_CODE.equals(other.getFREQ_CODE()))) &&
            ((this.FREQ_EB_UNIT_T==null && other.getFREQ_EB_UNIT_T()==null) || 
             (this.FREQ_EB_UNIT_T!=null &&
              this.FREQ_EB_UNIT_T.equals(other.getFREQ_EB_UNIT_T()))) &&
            ((this.FREQ_EFB==null && other.getFREQ_EFB()==null) || 
             (this.FREQ_EFB!=null &&
              this.FREQ_EFB.equals(other.getFREQ_EFB()))) &&
            ((this.FREQ_EFB_DESCRIPTION==null && other.getFREQ_EFB_DESCRIPTION()==null) || 
             (this.FREQ_EFB_DESCRIPTION!=null &&
              this.FREQ_EFB_DESCRIPTION.equals(other.getFREQ_EFB_DESCRIPTION()))) &&
            ((this.FREQ_EFB_T==null && other.getFREQ_EFB_T()==null) || 
             (this.FREQ_EFB_T!=null &&
              this.FREQ_EFB_T.equals(other.getFREQ_EFB_T()))) &&
            ((this.FREQ_EFE==null && other.getFREQ_EFE()==null) || 
             (this.FREQ_EFE!=null &&
              this.FREQ_EFE.equals(other.getFREQ_EFE()))) &&
            ((this.FREQ_EFE_T==null && other.getFREQ_EFE_T()==null) || 
             (this.FREQ_EFE_T!=null &&
              this.FREQ_EFE_T.equals(other.getFREQ_EFE_T()))) &&
            ((this.FREQ_EIRP_T==null && other.getFREQ_EIRP_T()==null) || 
             (this.FREQ_EIRP_T!=null &&
              this.FREQ_EIRP_T.equals(other.getFREQ_EIRP_T()))) &&
            ((this.FREQ_EIRP_UNIT_T==null && other.getFREQ_EIRP_UNIT_T()==null) || 
             (this.FREQ_EIRP_UNIT_T!=null &&
              this.FREQ_EIRP_UNIT_T.equals(other.getFREQ_EIRP_UNIT_T()))) &&
            ((this.FREQ_E_BAND==null && other.getFREQ_E_BAND()==null) || 
             (this.FREQ_E_BAND!=null &&
              this.FREQ_E_BAND.equals(other.getFREQ_E_BAND()))) &&
            ((this.FREQ_E_BAND_DESCRIPTION==null && other.getFREQ_E_BAND_DESCRIPTION()==null) || 
             (this.FREQ_E_BAND_DESCRIPTION!=null &&
              this.FREQ_E_BAND_DESCRIPTION.equals(other.getFREQ_E_BAND_DESCRIPTION()))) &&
            ((this.FREQ_E_BAND_T==null && other.getFREQ_E_BAND_T()==null) || 
             (this.FREQ_E_BAND_T!=null &&
              this.FREQ_E_BAND_T.equals(other.getFREQ_E_BAND_T()))) &&
            ((this.FREQ_E_UNIT_T==null && other.getFREQ_E_UNIT_T()==null) || 
             (this.FREQ_E_UNIT_T!=null &&
              this.FREQ_E_UNIT_T.equals(other.getFREQ_E_UNIT_T()))) &&
            ((this.FREQ_LC==null && other.getFREQ_LC()==null) || 
             (this.FREQ_LC!=null &&
              this.FREQ_LC.equals(other.getFREQ_LC()))) &&
            ((this.FREQ_MB==null && other.getFREQ_MB()==null) || 
             (this.FREQ_MB!=null &&
              this.FREQ_MB.equals(other.getFREQ_MB()))) &&
            ((this.FREQ_MOD==null && other.getFREQ_MOD()==null) || 
             (this.FREQ_MOD!=null &&
              this.FREQ_MOD.equals(other.getFREQ_MOD()))) &&
            ((this.FREQ_NO_T==null && other.getFREQ_NO_T()==null) || 
             (this.FREQ_NO_T!=null &&
              this.FREQ_NO_T.equals(other.getFREQ_NO_T()))) &&
            ((this.FREQ_POW_T==null && other.getFREQ_POW_T()==null) || 
             (this.FREQ_POW_T!=null &&
              this.FREQ_POW_T.equals(other.getFREQ_POW_T()))) &&
            ((this.FREQ_POW_UNIT_T==null && other.getFREQ_POW_UNIT_T()==null) || 
             (this.FREQ_POW_UNIT_T!=null &&
              this.FREQ_POW_UNIT_T.equals(other.getFREQ_POW_UNIT_T()))) &&
            ((this.FREQ_RB_UNIT_T==null && other.getFREQ_RB_UNIT_T()==null) || 
             (this.FREQ_RB_UNIT_T!=null &&
              this.FREQ_RB_UNIT_T.equals(other.getFREQ_RB_UNIT_T()))) &&
            ((this.FREQ_RFB==null && other.getFREQ_RFB()==null) || 
             (this.FREQ_RFB!=null &&
              this.FREQ_RFB.equals(other.getFREQ_RFB()))) &&
            ((this.FREQ_RFB_DESCRIPTION==null && other.getFREQ_RFB_DESCRIPTION()==null) || 
             (this.FREQ_RFB_DESCRIPTION!=null &&
              this.FREQ_RFB_DESCRIPTION.equals(other.getFREQ_RFB_DESCRIPTION()))) &&
            ((this.FREQ_RFB_T==null && other.getFREQ_RFB_T()==null) || 
             (this.FREQ_RFB_T!=null &&
              this.FREQ_RFB_T.equals(other.getFREQ_RFB_T()))) &&
            ((this.FREQ_RFE==null && other.getFREQ_RFE()==null) || 
             (this.FREQ_RFE!=null &&
              this.FREQ_RFE.equals(other.getFREQ_RFE()))) &&
            ((this.FREQ_RFE_T==null && other.getFREQ_RFE_T()==null) || 
             (this.FREQ_RFE_T!=null &&
              this.FREQ_RFE_T.equals(other.getFREQ_RFE_T()))) &&
            ((this.FREQ_R_BAND==null && other.getFREQ_R_BAND()==null) || 
             (this.FREQ_R_BAND!=null &&
              this.FREQ_R_BAND.equals(other.getFREQ_R_BAND()))) &&
            ((this.FREQ_R_BAND_DESCRIPTION==null && other.getFREQ_R_BAND_DESCRIPTION()==null) || 
             (this.FREQ_R_BAND_DESCRIPTION!=null &&
              this.FREQ_R_BAND_DESCRIPTION.equals(other.getFREQ_R_BAND_DESCRIPTION()))) &&
            ((this.FREQ_R_BAND_T==null && other.getFREQ_R_BAND_T()==null) || 
             (this.FREQ_R_BAND_T!=null &&
              this.FREQ_R_BAND_T.equals(other.getFREQ_R_BAND_T()))) &&
            ((this.FREQ_R_UNIT_T==null && other.getFREQ_R_UNIT_T()==null) || 
             (this.FREQ_R_UNIT_T!=null &&
              this.FREQ_R_UNIT_T.equals(other.getFREQ_R_UNIT_T()))) &&
            ((this.FREQ_TYPE==null && other.getFREQ_TYPE()==null) || 
             (this.FREQ_TYPE!=null &&
              this.FREQ_TYPE.equals(other.getFREQ_TYPE()))) &&
            ((this.FREQ_UC==null && other.getFREQ_UC()==null) || 
             (this.FREQ_UC!=null &&
              this.FREQ_UC.equals(other.getFREQ_UC()))) &&
            ((this.FT_CCODE_T==null && other.getFT_CCODE_T()==null) || 
             (this.FT_CCODE_T!=null &&
              this.FT_CCODE_T.equals(other.getFT_CCODE_T()))) &&
            ((this.FT_FREQ_CCODE==null && other.getFT_FREQ_CCODE()==null) || 
             (this.FT_FREQ_CCODE!=null &&
              this.FT_FREQ_CCODE.equals(other.getFT_FREQ_CCODE()))) &&
            ((this.FT_FREQ_CSGN==null && other.getFT_FREQ_CSGN()==null) || 
             (this.FT_FREQ_CSGN!=null &&
              this.FT_FREQ_CSGN.equals(other.getFT_FREQ_CSGN()))) &&
            ((this.FT_FREQ_DUPDN==null && other.getFT_FREQ_DUPDN()==null) || 
             (this.FT_FREQ_DUPDN!=null &&
              this.FT_FREQ_DUPDN.equals(other.getFT_FREQ_DUPDN()))) &&
            ((this.FT_FREQ_EIRP==null && other.getFT_FREQ_EIRP()==null) || 
             (this.FT_FREQ_EIRP!=null &&
              this.FT_FREQ_EIRP.equals(other.getFT_FREQ_EIRP()))) &&
            ((this.FT_FREQ_EPOW==null && other.getFT_FREQ_EPOW()==null) || 
             (this.FT_FREQ_EPOW!=null &&
              this.FT_FREQ_EPOW.equals(other.getFT_FREQ_EPOW()))) &&
            ((this.FT_FREQ_FEP==null && other.getFT_FREQ_FEP()==null) || 
             (this.FT_FREQ_FEP!=null &&
              this.FT_FREQ_FEP.equals(other.getFT_FREQ_FEP()))) &&
            ((this.FT_FREQ_FRP==null && other.getFT_FREQ_FRP()==null) || 
             (this.FT_FREQ_FRP!=null &&
              this.FT_FREQ_FRP.equals(other.getFT_FREQ_FRP()))) &&
            ((this.FT_FREQ_HCL==null && other.getFT_FREQ_HCL()==null) || 
             (this.FT_FREQ_HCL!=null &&
              this.FT_FREQ_HCL.equals(other.getFT_FREQ_HCL()))) &&
            ((this.FT_FREQ_INFO_TYPE==null && other.getFT_FREQ_INFO_TYPE()==null) || 
             (this.FT_FREQ_INFO_TYPE!=null &&
              this.FT_FREQ_INFO_TYPE.equals(other.getFT_FREQ_INFO_TYPE()))) &&
            ((this.FT_FREQ_MC==null && other.getFT_FREQ_MC()==null) || 
             (this.FT_FREQ_MC!=null &&
              this.FT_FREQ_MC.equals(other.getFT_FREQ_MC()))) &&
            ((this.FT_FREQ_MC1==null && other.getFT_FREQ_MC1()==null) || 
             (this.FT_FREQ_MC1!=null &&
              this.FT_FREQ_MC1.equals(other.getFT_FREQ_MC1()))) &&
            ((this.FT_FREQ_MC2==null && other.getFT_FREQ_MC2()==null) || 
             (this.FT_FREQ_MC2!=null &&
              this.FT_FREQ_MC2.equals(other.getFT_FREQ_MC2()))) &&
            ((this.FT_FREQ_MC3==null && other.getFT_FREQ_MC3()==null) || 
             (this.FT_FREQ_MC3!=null &&
              this.FT_FREQ_MC3.equals(other.getFT_FREQ_MC3()))) &&
            ((this.FT_FREQ_NO==null && other.getFT_FREQ_NO()==null) || 
             (this.FT_FREQ_NO!=null &&
              this.FT_FREQ_NO.equals(other.getFT_FREQ_NO()))) &&
            ((this.FT_FREQ_POWFLAG==null && other.getFT_FREQ_POWFLAG()==null) || 
             (this.FT_FREQ_POWFLAG!=null &&
              this.FT_FREQ_POWFLAG.equals(other.getFT_FREQ_POWFLAG()))) &&
            ((this.FT_FREQ_POW_AVG==null && other.getFT_FREQ_POW_AVG()==null) || 
             (this.FT_FREQ_POW_AVG!=null &&
              this.FT_FREQ_POW_AVG.equals(other.getFT_FREQ_POW_AVG()))) &&
            ((this.FT_FREQ_POW_MAX==null && other.getFT_FREQ_POW_MAX()==null) || 
             (this.FT_FREQ_POW_MAX!=null &&
              this.FT_FREQ_POW_MAX.equals(other.getFT_FREQ_POW_MAX()))) &&
            ((this.FT_FREQ_TIMEB==null && other.getFT_FREQ_TIMEB()==null) || 
             (this.FT_FREQ_TIMEB!=null &&
              this.FT_FREQ_TIMEB.equals(other.getFT_FREQ_TIMEB()))) &&
            ((this.FT_FREQ_TIMEE==null && other.getFT_FREQ_TIMEE()==null) || 
             (this.FT_FREQ_TIMEE!=null &&
              this.FT_FREQ_TIMEE.equals(other.getFT_FREQ_TIMEE()))) &&
            ((this.FT_FREQ_TYPE==null && other.getFT_FREQ_TYPE()==null) || 
             (this.FT_FREQ_TYPE!=null &&
              this.FT_FREQ_TYPE.equals(other.getFT_FREQ_TYPE()))) &&
            ((this.FT_FREQ_UNIT_TYPE==null && other.getFT_FREQ_UNIT_TYPE()==null) || 
             (this.FT_FREQ_UNIT_TYPE!=null &&
              this.FT_FREQ_UNIT_TYPE.equals(other.getFT_FREQ_UNIT_TYPE()))) &&
            ((this.SERIAL_NUM==null && other.getSERIAL_NUM()==null) || 
             (this.SERIAL_NUM!=null &&
              this.SERIAL_NUM.equals(other.getSERIAL_NUM()))) &&
            ((this.WORK_MODE==null && other.getWORK_MODE()==null) || 
             (this.WORK_MODE!=null &&
              this.WORK_MODE.equals(other.getWORK_MODE())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getFREQ_CODE() != null) {
            _hashCode += getFREQ_CODE().hashCode();
        }
        if (getFREQ_EB_UNIT_T() != null) {
            _hashCode += getFREQ_EB_UNIT_T().hashCode();
        }
        if (getFREQ_EFB() != null) {
            _hashCode += getFREQ_EFB().hashCode();
        }
        if (getFREQ_EFB_DESCRIPTION() != null) {
            _hashCode += getFREQ_EFB_DESCRIPTION().hashCode();
        }
        if (getFREQ_EFB_T() != null) {
            _hashCode += getFREQ_EFB_T().hashCode();
        }
        if (getFREQ_EFE() != null) {
            _hashCode += getFREQ_EFE().hashCode();
        }
        if (getFREQ_EFE_T() != null) {
            _hashCode += getFREQ_EFE_T().hashCode();
        }
        if (getFREQ_EIRP_T() != null) {
            _hashCode += getFREQ_EIRP_T().hashCode();
        }
        if (getFREQ_EIRP_UNIT_T() != null) {
            _hashCode += getFREQ_EIRP_UNIT_T().hashCode();
        }
        if (getFREQ_E_BAND() != null) {
            _hashCode += getFREQ_E_BAND().hashCode();
        }
        if (getFREQ_E_BAND_DESCRIPTION() != null) {
            _hashCode += getFREQ_E_BAND_DESCRIPTION().hashCode();
        }
        if (getFREQ_E_BAND_T() != null) {
            _hashCode += getFREQ_E_BAND_T().hashCode();
        }
        if (getFREQ_E_UNIT_T() != null) {
            _hashCode += getFREQ_E_UNIT_T().hashCode();
        }
        if (getFREQ_LC() != null) {
            _hashCode += getFREQ_LC().hashCode();
        }
        if (getFREQ_MB() != null) {
            _hashCode += getFREQ_MB().hashCode();
        }
        if (getFREQ_MOD() != null) {
            _hashCode += getFREQ_MOD().hashCode();
        }
        if (getFREQ_NO_T() != null) {
            _hashCode += getFREQ_NO_T().hashCode();
        }
        if (getFREQ_POW_T() != null) {
            _hashCode += getFREQ_POW_T().hashCode();
        }
        if (getFREQ_POW_UNIT_T() != null) {
            _hashCode += getFREQ_POW_UNIT_T().hashCode();
        }
        if (getFREQ_RB_UNIT_T() != null) {
            _hashCode += getFREQ_RB_UNIT_T().hashCode();
        }
        if (getFREQ_RFB() != null) {
            _hashCode += getFREQ_RFB().hashCode();
        }
        if (getFREQ_RFB_DESCRIPTION() != null) {
            _hashCode += getFREQ_RFB_DESCRIPTION().hashCode();
        }
        if (getFREQ_RFB_T() != null) {
            _hashCode += getFREQ_RFB_T().hashCode();
        }
        if (getFREQ_RFE() != null) {
            _hashCode += getFREQ_RFE().hashCode();
        }
        if (getFREQ_RFE_T() != null) {
            _hashCode += getFREQ_RFE_T().hashCode();
        }
        if (getFREQ_R_BAND() != null) {
            _hashCode += getFREQ_R_BAND().hashCode();
        }
        if (getFREQ_R_BAND_DESCRIPTION() != null) {
            _hashCode += getFREQ_R_BAND_DESCRIPTION().hashCode();
        }
        if (getFREQ_R_BAND_T() != null) {
            _hashCode += getFREQ_R_BAND_T().hashCode();
        }
        if (getFREQ_R_UNIT_T() != null) {
            _hashCode += getFREQ_R_UNIT_T().hashCode();
        }
        if (getFREQ_TYPE() != null) {
            _hashCode += getFREQ_TYPE().hashCode();
        }
        if (getFREQ_UC() != null) {
            _hashCode += getFREQ_UC().hashCode();
        }
        if (getFT_CCODE_T() != null) {
            _hashCode += getFT_CCODE_T().hashCode();
        }
        if (getFT_FREQ_CCODE() != null) {
            _hashCode += getFT_FREQ_CCODE().hashCode();
        }
        if (getFT_FREQ_CSGN() != null) {
            _hashCode += getFT_FREQ_CSGN().hashCode();
        }
        if (getFT_FREQ_DUPDN() != null) {
            _hashCode += getFT_FREQ_DUPDN().hashCode();
        }
        if (getFT_FREQ_EIRP() != null) {
            _hashCode += getFT_FREQ_EIRP().hashCode();
        }
        if (getFT_FREQ_EPOW() != null) {
            _hashCode += getFT_FREQ_EPOW().hashCode();
        }
        if (getFT_FREQ_FEP() != null) {
            _hashCode += getFT_FREQ_FEP().hashCode();
        }
        if (getFT_FREQ_FRP() != null) {
            _hashCode += getFT_FREQ_FRP().hashCode();
        }
        if (getFT_FREQ_HCL() != null) {
            _hashCode += getFT_FREQ_HCL().hashCode();
        }
        if (getFT_FREQ_INFO_TYPE() != null) {
            _hashCode += getFT_FREQ_INFO_TYPE().hashCode();
        }
        if (getFT_FREQ_MC() != null) {
            _hashCode += getFT_FREQ_MC().hashCode();
        }
        if (getFT_FREQ_MC1() != null) {
            _hashCode += getFT_FREQ_MC1().hashCode();
        }
        if (getFT_FREQ_MC2() != null) {
            _hashCode += getFT_FREQ_MC2().hashCode();
        }
        if (getFT_FREQ_MC3() != null) {
            _hashCode += getFT_FREQ_MC3().hashCode();
        }
        if (getFT_FREQ_NO() != null) {
            _hashCode += getFT_FREQ_NO().hashCode();
        }
        if (getFT_FREQ_POWFLAG() != null) {
            _hashCode += getFT_FREQ_POWFLAG().hashCode();
        }
        if (getFT_FREQ_POW_AVG() != null) {
            _hashCode += getFT_FREQ_POW_AVG().hashCode();
        }
        if (getFT_FREQ_POW_MAX() != null) {
            _hashCode += getFT_FREQ_POW_MAX().hashCode();
        }
        if (getFT_FREQ_TIMEB() != null) {
            _hashCode += getFT_FREQ_TIMEB().hashCode();
        }
        if (getFT_FREQ_TIMEE() != null) {
            _hashCode += getFT_FREQ_TIMEE().hashCode();
        }
        if (getFT_FREQ_TYPE() != null) {
            _hashCode += getFT_FREQ_TYPE().hashCode();
        }
        if (getFT_FREQ_UNIT_TYPE() != null) {
            _hashCode += getFT_FREQ_UNIT_TYPE().hashCode();
        }
        if (getSERIAL_NUM() != null) {
            _hashCode += getSERIAL_NUM().hashCode();
        }
        if (getWORK_MODE() != null) {
            _hashCode += getWORK_MODE().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(FS_ST_FREQ_ALL.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FS_ST_FREQ_ALL"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FREQ_CODE");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FREQ_CODE"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FREQ_EB_UNIT_T");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FREQ_EB_UNIT_T"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FREQ_EFB");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FREQ_EFB"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FREQ_EFB_DESCRIPTION");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FREQ_EFB_DESCRIPTION"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FREQ_EFB_T");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FREQ_EFB_T"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FREQ_EFE");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FREQ_EFE"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FREQ_EFE_T");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FREQ_EFE_T"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FREQ_EIRP_T");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FREQ_EIRP_T"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FREQ_EIRP_UNIT_T");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FREQ_EIRP_UNIT_T"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FREQ_E_BAND");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FREQ_E_BAND"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FREQ_E_BAND_DESCRIPTION");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FREQ_E_BAND_DESCRIPTION"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FREQ_E_BAND_T");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FREQ_E_BAND_T"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FREQ_E_UNIT_T");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FREQ_E_UNIT_T"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FREQ_LC");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FREQ_LC"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FREQ_MB");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FREQ_MB"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FREQ_MOD");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FREQ_MOD"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FREQ_NO_T");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FREQ_NO_T"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FREQ_POW_T");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FREQ_POW_T"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FREQ_POW_UNIT_T");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FREQ_POW_UNIT_T"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FREQ_RB_UNIT_T");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FREQ_RB_UNIT_T"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FREQ_RFB");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FREQ_RFB"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FREQ_RFB_DESCRIPTION");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FREQ_RFB_DESCRIPTION"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FREQ_RFB_T");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FREQ_RFB_T"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FREQ_RFE");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FREQ_RFE"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FREQ_RFE_T");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FREQ_RFE_T"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FREQ_R_BAND");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FREQ_R_BAND"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FREQ_R_BAND_DESCRIPTION");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FREQ_R_BAND_DESCRIPTION"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FREQ_R_BAND_T");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FREQ_R_BAND_T"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FREQ_R_UNIT_T");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FREQ_R_UNIT_T"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FREQ_TYPE");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FREQ_TYPE"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FREQ_UC");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FREQ_UC"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FT_CCODE_T");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FT_CCODE_T"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FT_FREQ_CCODE");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FT_FREQ_CCODE"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FT_FREQ_CSGN");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FT_FREQ_CSGN"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FT_FREQ_DUPDN");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FT_FREQ_DUPDN"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FT_FREQ_EIRP");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FT_FREQ_EIRP"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FT_FREQ_EPOW");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FT_FREQ_EPOW"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FT_FREQ_FEP");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FT_FREQ_FEP"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FT_FREQ_FRP");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FT_FREQ_FRP"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FT_FREQ_HCL");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FT_FREQ_HCL"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FT_FREQ_INFO_TYPE");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FT_FREQ_INFO_TYPE"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FT_FREQ_MC");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FT_FREQ_MC"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FT_FREQ_MC1");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FT_FREQ_MC1"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FT_FREQ_MC2");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FT_FREQ_MC2"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FT_FREQ_MC3");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FT_FREQ_MC3"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FT_FREQ_NO");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FT_FREQ_NO"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FT_FREQ_POWFLAG");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FT_FREQ_POWFLAG"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FT_FREQ_POW_AVG");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FT_FREQ_POW_AVG"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FT_FREQ_POW_MAX");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FT_FREQ_POW_MAX"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FT_FREQ_TIMEB");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FT_FREQ_TIMEB"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FT_FREQ_TIMEE");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FT_FREQ_TIMEE"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FT_FREQ_TYPE");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FT_FREQ_TYPE"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("FT_FREQ_UNIT_TYPE");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FT_FREQ_UNIT_TYPE"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("SERIAL_NUM");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "SERIAL_NUM"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("WORK_MODE");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "WORK_MODE"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
