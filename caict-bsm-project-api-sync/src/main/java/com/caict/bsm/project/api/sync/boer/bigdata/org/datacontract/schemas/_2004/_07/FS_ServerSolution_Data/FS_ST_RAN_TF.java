/**
 * FS_ST_RAN_TF.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.caict.bsm.project.api.sync.boer.bigdata.org.datacontract.schemas._2004._07.FS_ServerSolution_Data;

public class FS_ST_RAN_TF  extends FS_ST_COMMON  implements java.io.Serializable {
    private String ST_MEMO;

    private String ST_TF_CODE;

    private Double ST_TF_TRANS_CA_P;

    private String ST_TF_TRANS_CA_PU;

    public FS_ST_RAN_TF() {
    }

    public FS_ST_RAN_TF(
           String AGENT_AREA_CODE,
           FS_ST_ANTENNA_ALL[] ANTFEEDERS,
           FS_ST_ANTENNA_ALL[] ANTFEEDERS_Data,
           String ANT_SIZE_MAX,
           String APP_CODE,
           String APP_GUID,
           Integer BEFORE_STATUS,
           java.util.Calendar ENABLE_DATE,
           FS_ST_EQU_ALL[] EQUS,
           FS_ST_EQU_ALL[] EQUS_Data,
           Integer EQU_COUNT,
           Double EQU_POW_MAX,
           FS_ST_FREQ_ALL[] FREQS,
           FS_ST_FREQ_ALL[] FREQS_Data,
           String IS_AGENT_FEE,
           String NET_SVN,
           String NET_TS,
           String ORA_TABLE,
           String ORG_AREA_CODE,
           String ORG_NAME,
           String STATION_GUID,
           String STAT_ADDR,
           String STAT_APP_TYPE,
           String STAT_AREA_CODE,
           Double STAT_AT,
           Integer STAT_COUNT,
           Double STAT_LA,
           Double STAT_LA2,
           Double STAT_LG,
           Double STAT_LG2,
           String STAT_NAME,
           String STAT_SET_FLAG,
           Integer STAT_STATUS,
           String STAT_TDI,
           String STAT_TYPE,
           String ST_CLASS_CODE,
           String TASK_GUID,
           String ST_MEMO,
           String ST_TF_CODE,
           Double ST_TF_TRANS_CA_P,
           String ST_TF_TRANS_CA_PU) {
        super(
            AGENT_AREA_CODE,
            ANTFEEDERS,
            ANTFEEDERS_Data,
            ANT_SIZE_MAX,
            APP_CODE,
            APP_GUID,
            BEFORE_STATUS,
            ENABLE_DATE,
            EQUS,
            EQUS_Data,
            EQU_COUNT,
            EQU_POW_MAX,
            FREQS,
            FREQS_Data,
            IS_AGENT_FEE,
            NET_SVN,
            NET_TS,
            ORA_TABLE,
            ORG_AREA_CODE,
            ORG_NAME,
            STATION_GUID,
            STAT_ADDR,
            STAT_APP_TYPE,
            STAT_AREA_CODE,
            STAT_AT,
            STAT_COUNT,
            STAT_LA,
            STAT_LA2,
            STAT_LG,
            STAT_LG2,
            STAT_NAME,
            STAT_SET_FLAG,
            STAT_STATUS,
            STAT_TDI,
            STAT_TYPE,
            ST_CLASS_CODE,
            TASK_GUID);
        this.ST_MEMO = ST_MEMO;
        this.ST_TF_CODE = ST_TF_CODE;
        this.ST_TF_TRANS_CA_P = ST_TF_TRANS_CA_P;
        this.ST_TF_TRANS_CA_PU = ST_TF_TRANS_CA_PU;
    }


    /**
     * Gets the ST_MEMO value for this FS_ST_RAN_TF.
     * 
     * @return ST_MEMO
     */
    public String getST_MEMO() {
        return ST_MEMO;
    }


    /**
     * Sets the ST_MEMO value for this FS_ST_RAN_TF.
     * 
     * @param ST_MEMO
     */
    public void setST_MEMO(String ST_MEMO) {
        this.ST_MEMO = ST_MEMO;
    }


    /**
     * Gets the ST_TF_CODE value for this FS_ST_RAN_TF.
     * 
     * @return ST_TF_CODE
     */
    public String getST_TF_CODE() {
        return ST_TF_CODE;
    }


    /**
     * Sets the ST_TF_CODE value for this FS_ST_RAN_TF.
     * 
     * @param ST_TF_CODE
     */
    public void setST_TF_CODE(String ST_TF_CODE) {
        this.ST_TF_CODE = ST_TF_CODE;
    }


    /**
     * Gets the ST_TF_TRANS_CA_P value for this FS_ST_RAN_TF.
     * 
     * @return ST_TF_TRANS_CA_P
     */
    public Double getST_TF_TRANS_CA_P() {
        return ST_TF_TRANS_CA_P;
    }


    /**
     * Sets the ST_TF_TRANS_CA_P value for this FS_ST_RAN_TF.
     * 
     * @param ST_TF_TRANS_CA_P
     */
    public void setST_TF_TRANS_CA_P(Double ST_TF_TRANS_CA_P) {
        this.ST_TF_TRANS_CA_P = ST_TF_TRANS_CA_P;
    }


    /**
     * Gets the ST_TF_TRANS_CA_PU value for this FS_ST_RAN_TF.
     * 
     * @return ST_TF_TRANS_CA_PU
     */
    public String getST_TF_TRANS_CA_PU() {
        return ST_TF_TRANS_CA_PU;
    }


    /**
     * Sets the ST_TF_TRANS_CA_PU value for this FS_ST_RAN_TF.
     * 
     * @param ST_TF_TRANS_CA_PU
     */
    public void setST_TF_TRANS_CA_PU(String ST_TF_TRANS_CA_PU) {
        this.ST_TF_TRANS_CA_PU = ST_TF_TRANS_CA_PU;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof FS_ST_RAN_TF)) return false;
        FS_ST_RAN_TF other = (FS_ST_RAN_TF) obj;
        if (obj == null) return false;
        if (this == obj) return true;
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = super.equals(obj) && 
            ((this.ST_MEMO==null && other.getST_MEMO()==null) || 
             (this.ST_MEMO!=null &&
              this.ST_MEMO.equals(other.getST_MEMO()))) &&
            ((this.ST_TF_CODE==null && other.getST_TF_CODE()==null) || 
             (this.ST_TF_CODE!=null &&
              this.ST_TF_CODE.equals(other.getST_TF_CODE()))) &&
            ((this.ST_TF_TRANS_CA_P==null && other.getST_TF_TRANS_CA_P()==null) || 
             (this.ST_TF_TRANS_CA_P!=null &&
              this.ST_TF_TRANS_CA_P.equals(other.getST_TF_TRANS_CA_P()))) &&
            ((this.ST_TF_TRANS_CA_PU==null && other.getST_TF_TRANS_CA_PU()==null) || 
             (this.ST_TF_TRANS_CA_PU!=null &&
              this.ST_TF_TRANS_CA_PU.equals(other.getST_TF_TRANS_CA_PU())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = super.hashCode();
        if (getST_MEMO() != null) {
            _hashCode += getST_MEMO().hashCode();
        }
        if (getST_TF_CODE() != null) {
            _hashCode += getST_TF_CODE().hashCode();
        }
        if (getST_TF_TRANS_CA_P() != null) {
            _hashCode += getST_TF_TRANS_CA_P().hashCode();
        }
        if (getST_TF_TRANS_CA_PU() != null) {
            _hashCode += getST_TF_TRANS_CA_PU().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(FS_ST_RAN_TF.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "FS_ST_RAN_TF"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ST_MEMO");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "ST_MEMO"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ST_TF_CODE");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "ST_TF_CODE"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ST_TF_TRANS_CA_P");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "ST_TF_TRANS_CA_P"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "double"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ST_TF_TRANS_CA_PU");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "ST_TF_TRANS_CA_PU"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
