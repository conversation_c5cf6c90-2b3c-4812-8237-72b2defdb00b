/**
 * RET_ST_STATUS_TJ.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.caict.bsm.project.api.sync.boer.bigdata.org.datacontract.schemas._2004._07.FS_ServerSolution_Data;

public class RET_ST_STATUS_TJ  implements java.io.Serializable {
    private String message;

    private String STAT_TDI;

    private String ST_C_CODE;

    private Integer status;

    private String TASK_GUID;

    public RET_ST_STATUS_TJ() {
    }

    public RET_ST_STATUS_TJ(
           String message,
           String STAT_TDI,
           String ST_C_CODE,
           Integer status,
           String TASK_GUID) {
           this.message = message;
           this.STAT_TDI = STAT_TDI;
           this.ST_C_CODE = ST_C_CODE;
           this.status = status;
           this.TASK_GUID = TASK_GUID;
    }


    /**
     * Gets the message value for this RET_ST_STATUS_TJ.
     * 
     * @return message
     */
    public String getMessage() {
        return message;
    }


    /**
     * Sets the message value for this RET_ST_STATUS_TJ.
     * 
     * @param message
     */
    public void setMessage(String message) {
        this.message = message;
    }


    /**
     * Gets the STAT_TDI value for this RET_ST_STATUS_TJ.
     * 
     * @return STAT_TDI
     */
    public String getSTAT_TDI() {
        return STAT_TDI;
    }


    /**
     * Sets the STAT_TDI value for this RET_ST_STATUS_TJ.
     * 
     * @param STAT_TDI
     */
    public void setSTAT_TDI(String STAT_TDI) {
        this.STAT_TDI = STAT_TDI;
    }


    /**
     * Gets the ST_C_CODE value for this RET_ST_STATUS_TJ.
     * 
     * @return ST_C_CODE
     */
    public String getST_C_CODE() {
        return ST_C_CODE;
    }


    /**
     * Sets the ST_C_CODE value for this RET_ST_STATUS_TJ.
     * 
     * @param ST_C_CODE
     */
    public void setST_C_CODE(String ST_C_CODE) {
        this.ST_C_CODE = ST_C_CODE;
    }


    /**
     * Gets the status value for this RET_ST_STATUS_TJ.
     * 
     * @return status
     */
    public Integer getStatus() {
        return status;
    }


    /**
     * Sets the status value for this RET_ST_STATUS_TJ.
     * 
     * @param status
     */
    public void setStatus(Integer status) {
        this.status = status;
    }


    /**
     * Gets the TASK_GUID value for this RET_ST_STATUS_TJ.
     * 
     * @return TASK_GUID
     */
    public String getTASK_GUID() {
        return TASK_GUID;
    }


    /**
     * Sets the TASK_GUID value for this RET_ST_STATUS_TJ.
     * 
     * @param TASK_GUID
     */
    public void setTASK_GUID(String TASK_GUID) {
        this.TASK_GUID = TASK_GUID;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof RET_ST_STATUS_TJ)) return false;
        RET_ST_STATUS_TJ other = (RET_ST_STATUS_TJ) obj;
        if (obj == null) return false;
        if (this == obj) return true;
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.message==null && other.getMessage()==null) || 
             (this.message!=null &&
              this.message.equals(other.getMessage()))) &&
            ((this.STAT_TDI==null && other.getSTAT_TDI()==null) || 
             (this.STAT_TDI!=null &&
              this.STAT_TDI.equals(other.getSTAT_TDI()))) &&
            ((this.ST_C_CODE==null && other.getST_C_CODE()==null) || 
             (this.ST_C_CODE!=null &&
              this.ST_C_CODE.equals(other.getST_C_CODE()))) &&
            ((this.status==null && other.getStatus()==null) || 
             (this.status!=null &&
              this.status.equals(other.getStatus()))) &&
            ((this.TASK_GUID==null && other.getTASK_GUID()==null) || 
             (this.TASK_GUID!=null &&
              this.TASK_GUID.equals(other.getTASK_GUID())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getMessage() != null) {
            _hashCode += getMessage().hashCode();
        }
        if (getSTAT_TDI() != null) {
            _hashCode += getSTAT_TDI().hashCode();
        }
        if (getST_C_CODE() != null) {
            _hashCode += getST_C_CODE().hashCode();
        }
        if (getStatus() != null) {
            _hashCode += getStatus().hashCode();
        }
        if (getTASK_GUID() != null) {
            _hashCode += getTASK_GUID().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(RET_ST_STATUS_TJ.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "RET_ST_STATUS_TJ"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("message");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "Message"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("STAT_TDI");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "STAT_TDI"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ST_C_CODE");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "ST_C_CODE"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("status");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "Status"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("TASK_GUID");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "TASK_GUID"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
