/**
 * CData_QUERY_RESULT_BigData.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.caict.bsm.project.api.sync.boer.bigdata.org.datacontract.schemas._2004._07.FS_ServerSolution_Data_CData;

public class CData_QUERY_RESULT_BigData  implements java.io.Serializable {
    private String APP_CODE;

    private String APP_FTLB;

    private String APP_FTLE;

    private String ENABLE_DATE;

    private String IS_AGENT;

    private Boolean IS_CHK;

    private String IS_SCRECT;

    private String IS_TEMP;

    private String NET_GUID;

    private String NET_NAME;

    private String NET_SP;

    private String NET_SVN;

    private String NET_SVN_CHN;

    private String NET_TS;

    private String NET_TS_CHN;

    private String ORA_TABLE;

    private String ORG_GUID;

    private String ORG_NAME;

    private String STATION_GUID;

    private String STAT_ADDR;

    private String STAT_AREA_CODE;

    private String STAT_EQU_SUM;

    private String STAT_LA_T;

    private String STAT_LG_T;

    private String STAT_NAME;

    private String STAT_STATUS;

    private String STAT_TDI;

    private String STAT_TDI_SUM;

    private String ST_CLASS1;

    public CData_QUERY_RESULT_BigData() {
    }

    public CData_QUERY_RESULT_BigData(
           String APP_CODE,
           String APP_FTLB,
           String APP_FTLE,
           String ENABLE_DATE,
           String IS_AGENT,
           Boolean IS_CHK,
           String IS_SCRECT,
           String IS_TEMP,
           String NET_GUID,
           String NET_NAME,
           String NET_SP,
           String NET_SVN,
           String NET_SVN_CHN,
           String NET_TS,
           String NET_TS_CHN,
           String ORA_TABLE,
           String ORG_GUID,
           String ORG_NAME,
           String STATION_GUID,
           String STAT_ADDR,
           String STAT_AREA_CODE,
           String STAT_EQU_SUM,
           String STAT_LA_T,
           String STAT_LG_T,
           String STAT_NAME,
           String STAT_STATUS,
           String STAT_TDI,
           String STAT_TDI_SUM,
           String ST_CLASS1) {
           this.APP_CODE = APP_CODE;
           this.APP_FTLB = APP_FTLB;
           this.APP_FTLE = APP_FTLE;
           this.ENABLE_DATE = ENABLE_DATE;
           this.IS_AGENT = IS_AGENT;
           this.IS_CHK = IS_CHK;
           this.IS_SCRECT = IS_SCRECT;
           this.IS_TEMP = IS_TEMP;
           this.NET_GUID = NET_GUID;
           this.NET_NAME = NET_NAME;
           this.NET_SP = NET_SP;
           this.NET_SVN = NET_SVN;
           this.NET_SVN_CHN = NET_SVN_CHN;
           this.NET_TS = NET_TS;
           this.NET_TS_CHN = NET_TS_CHN;
           this.ORA_TABLE = ORA_TABLE;
           this.ORG_GUID = ORG_GUID;
           this.ORG_NAME = ORG_NAME;
           this.STATION_GUID = STATION_GUID;
           this.STAT_ADDR = STAT_ADDR;
           this.STAT_AREA_CODE = STAT_AREA_CODE;
           this.STAT_EQU_SUM = STAT_EQU_SUM;
           this.STAT_LA_T = STAT_LA_T;
           this.STAT_LG_T = STAT_LG_T;
           this.STAT_NAME = STAT_NAME;
           this.STAT_STATUS = STAT_STATUS;
           this.STAT_TDI = STAT_TDI;
           this.STAT_TDI_SUM = STAT_TDI_SUM;
           this.ST_CLASS1 = ST_CLASS1;
    }


    /**
     * Gets the APP_CODE value for this CData_QUERY_RESULT_BigData.
     * 
     * @return APP_CODE
     */
    public String getAPP_CODE() {
        return APP_CODE;
    }


    /**
     * Sets the APP_CODE value for this CData_QUERY_RESULT_BigData.
     * 
     * @param APP_CODE
     */
    public void setAPP_CODE(String APP_CODE) {
        this.APP_CODE = APP_CODE;
    }


    /**
     * Gets the APP_FTLB value for this CData_QUERY_RESULT_BigData.
     * 
     * @return APP_FTLB
     */
    public String getAPP_FTLB() {
        return APP_FTLB;
    }


    /**
     * Sets the APP_FTLB value for this CData_QUERY_RESULT_BigData.
     * 
     * @param APP_FTLB
     */
    public void setAPP_FTLB(String APP_FTLB) {
        this.APP_FTLB = APP_FTLB;
    }


    /**
     * Gets the APP_FTLE value for this CData_QUERY_RESULT_BigData.
     * 
     * @return APP_FTLE
     */
    public String getAPP_FTLE() {
        return APP_FTLE;
    }


    /**
     * Sets the APP_FTLE value for this CData_QUERY_RESULT_BigData.
     * 
     * @param APP_FTLE
     */
    public void setAPP_FTLE(String APP_FTLE) {
        this.APP_FTLE = APP_FTLE;
    }


    /**
     * Gets the ENABLE_DATE value for this CData_QUERY_RESULT_BigData.
     * 
     * @return ENABLE_DATE
     */
    public String getENABLE_DATE() {
        return ENABLE_DATE;
    }


    /**
     * Sets the ENABLE_DATE value for this CData_QUERY_RESULT_BigData.
     * 
     * @param ENABLE_DATE
     */
    public void setENABLE_DATE(String ENABLE_DATE) {
        this.ENABLE_DATE = ENABLE_DATE;
    }


    /**
     * Gets the IS_AGENT value for this CData_QUERY_RESULT_BigData.
     * 
     * @return IS_AGENT
     */
    public String getIS_AGENT() {
        return IS_AGENT;
    }


    /**
     * Sets the IS_AGENT value for this CData_QUERY_RESULT_BigData.
     * 
     * @param IS_AGENT
     */
    public void setIS_AGENT(String IS_AGENT) {
        this.IS_AGENT = IS_AGENT;
    }


    /**
     * Gets the IS_CHK value for this CData_QUERY_RESULT_BigData.
     * 
     * @return IS_CHK
     */
    public Boolean getIS_CHK() {
        return IS_CHK;
    }


    /**
     * Sets the IS_CHK value for this CData_QUERY_RESULT_BigData.
     * 
     * @param IS_CHK
     */
    public void setIS_CHK(Boolean IS_CHK) {
        this.IS_CHK = IS_CHK;
    }


    /**
     * Gets the IS_SCRECT value for this CData_QUERY_RESULT_BigData.
     * 
     * @return IS_SCRECT
     */
    public String getIS_SCRECT() {
        return IS_SCRECT;
    }


    /**
     * Sets the IS_SCRECT value for this CData_QUERY_RESULT_BigData.
     * 
     * @param IS_SCRECT
     */
    public void setIS_SCRECT(String IS_SCRECT) {
        this.IS_SCRECT = IS_SCRECT;
    }


    /**
     * Gets the IS_TEMP value for this CData_QUERY_RESULT_BigData.
     * 
     * @return IS_TEMP
     */
    public String getIS_TEMP() {
        return IS_TEMP;
    }


    /**
     * Sets the IS_TEMP value for this CData_QUERY_RESULT_BigData.
     * 
     * @param IS_TEMP
     */
    public void setIS_TEMP(String IS_TEMP) {
        this.IS_TEMP = IS_TEMP;
    }


    /**
     * Gets the NET_GUID value for this CData_QUERY_RESULT_BigData.
     * 
     * @return NET_GUID
     */
    public String getNET_GUID() {
        return NET_GUID;
    }


    /**
     * Sets the NET_GUID value for this CData_QUERY_RESULT_BigData.
     * 
     * @param NET_GUID
     */
    public void setNET_GUID(String NET_GUID) {
        this.NET_GUID = NET_GUID;
    }


    /**
     * Gets the NET_NAME value for this CData_QUERY_RESULT_BigData.
     * 
     * @return NET_NAME
     */
    public String getNET_NAME() {
        return NET_NAME;
    }


    /**
     * Sets the NET_NAME value for this CData_QUERY_RESULT_BigData.
     * 
     * @param NET_NAME
     */
    public void setNET_NAME(String NET_NAME) {
        this.NET_NAME = NET_NAME;
    }


    /**
     * Gets the NET_SP value for this CData_QUERY_RESULT_BigData.
     * 
     * @return NET_SP
     */
    public String getNET_SP() {
        return NET_SP;
    }


    /**
     * Sets the NET_SP value for this CData_QUERY_RESULT_BigData.
     * 
     * @param NET_SP
     */
    public void setNET_SP(String NET_SP) {
        this.NET_SP = NET_SP;
    }


    /**
     * Gets the NET_SVN value for this CData_QUERY_RESULT_BigData.
     * 
     * @return NET_SVN
     */
    public String getNET_SVN() {
        return NET_SVN;
    }


    /**
     * Sets the NET_SVN value for this CData_QUERY_RESULT_BigData.
     * 
     * @param NET_SVN
     */
    public void setNET_SVN(String NET_SVN) {
        this.NET_SVN = NET_SVN;
    }


    /**
     * Gets the NET_SVN_CHN value for this CData_QUERY_RESULT_BigData.
     * 
     * @return NET_SVN_CHN
     */
    public String getNET_SVN_CHN() {
        return NET_SVN_CHN;
    }


    /**
     * Sets the NET_SVN_CHN value for this CData_QUERY_RESULT_BigData.
     * 
     * @param NET_SVN_CHN
     */
    public void setNET_SVN_CHN(String NET_SVN_CHN) {
        this.NET_SVN_CHN = NET_SVN_CHN;
    }


    /**
     * Gets the NET_TS value for this CData_QUERY_RESULT_BigData.
     * 
     * @return NET_TS
     */
    public String getNET_TS() {
        return NET_TS;
    }


    /**
     * Sets the NET_TS value for this CData_QUERY_RESULT_BigData.
     * 
     * @param NET_TS
     */
    public void setNET_TS(String NET_TS) {
        this.NET_TS = NET_TS;
    }


    /**
     * Gets the NET_TS_CHN value for this CData_QUERY_RESULT_BigData.
     * 
     * @return NET_TS_CHN
     */
    public String getNET_TS_CHN() {
        return NET_TS_CHN;
    }


    /**
     * Sets the NET_TS_CHN value for this CData_QUERY_RESULT_BigData.
     * 
     * @param NET_TS_CHN
     */
    public void setNET_TS_CHN(String NET_TS_CHN) {
        this.NET_TS_CHN = NET_TS_CHN;
    }


    /**
     * Gets the ORA_TABLE value for this CData_QUERY_RESULT_BigData.
     * 
     * @return ORA_TABLE
     */
    public String getORA_TABLE() {
        return ORA_TABLE;
    }


    /**
     * Sets the ORA_TABLE value for this CData_QUERY_RESULT_BigData.
     * 
     * @param ORA_TABLE
     */
    public void setORA_TABLE(String ORA_TABLE) {
        this.ORA_TABLE = ORA_TABLE;
    }


    /**
     * Gets the ORG_GUID value for this CData_QUERY_RESULT_BigData.
     * 
     * @return ORG_GUID
     */
    public String getORG_GUID() {
        return ORG_GUID;
    }


    /**
     * Sets the ORG_GUID value for this CData_QUERY_RESULT_BigData.
     * 
     * @param ORG_GUID
     */
    public void setORG_GUID(String ORG_GUID) {
        this.ORG_GUID = ORG_GUID;
    }


    /**
     * Gets the ORG_NAME value for this CData_QUERY_RESULT_BigData.
     * 
     * @return ORG_NAME
     */
    public String getORG_NAME() {
        return ORG_NAME;
    }


    /**
     * Sets the ORG_NAME value for this CData_QUERY_RESULT_BigData.
     * 
     * @param ORG_NAME
     */
    public void setORG_NAME(String ORG_NAME) {
        this.ORG_NAME = ORG_NAME;
    }


    /**
     * Gets the STATION_GUID value for this CData_QUERY_RESULT_BigData.
     * 
     * @return STATION_GUID
     */
    public String getSTATION_GUID() {
        return STATION_GUID;
    }


    /**
     * Sets the STATION_GUID value for this CData_QUERY_RESULT_BigData.
     * 
     * @param STATION_GUID
     */
    public void setSTATION_GUID(String STATION_GUID) {
        this.STATION_GUID = STATION_GUID;
    }


    /**
     * Gets the STAT_ADDR value for this CData_QUERY_RESULT_BigData.
     * 
     * @return STAT_ADDR
     */
    public String getSTAT_ADDR() {
        return STAT_ADDR;
    }


    /**
     * Sets the STAT_ADDR value for this CData_QUERY_RESULT_BigData.
     * 
     * @param STAT_ADDR
     */
    public void setSTAT_ADDR(String STAT_ADDR) {
        this.STAT_ADDR = STAT_ADDR;
    }


    /**
     * Gets the STAT_AREA_CODE value for this CData_QUERY_RESULT_BigData.
     * 
     * @return STAT_AREA_CODE
     */
    public String getSTAT_AREA_CODE() {
        return STAT_AREA_CODE;
    }


    /**
     * Sets the STAT_AREA_CODE value for this CData_QUERY_RESULT_BigData.
     * 
     * @param STAT_AREA_CODE
     */
    public void setSTAT_AREA_CODE(String STAT_AREA_CODE) {
        this.STAT_AREA_CODE = STAT_AREA_CODE;
    }


    /**
     * Gets the STAT_EQU_SUM value for this CData_QUERY_RESULT_BigData.
     * 
     * @return STAT_EQU_SUM
     */
    public String getSTAT_EQU_SUM() {
        return STAT_EQU_SUM;
    }


    /**
     * Sets the STAT_EQU_SUM value for this CData_QUERY_RESULT_BigData.
     * 
     * @param STAT_EQU_SUM
     */
    public void setSTAT_EQU_SUM(String STAT_EQU_SUM) {
        this.STAT_EQU_SUM = STAT_EQU_SUM;
    }


    /**
     * Gets the STAT_LA_T value for this CData_QUERY_RESULT_BigData.
     * 
     * @return STAT_LA_T
     */
    public String getSTAT_LA_T() {
        return STAT_LA_T;
    }


    /**
     * Sets the STAT_LA_T value for this CData_QUERY_RESULT_BigData.
     * 
     * @param STAT_LA_T
     */
    public void setSTAT_LA_T(String STAT_LA_T) {
        this.STAT_LA_T = STAT_LA_T;
    }


    /**
     * Gets the STAT_LG_T value for this CData_QUERY_RESULT_BigData.
     * 
     * @return STAT_LG_T
     */
    public String getSTAT_LG_T() {
        return STAT_LG_T;
    }


    /**
     * Sets the STAT_LG_T value for this CData_QUERY_RESULT_BigData.
     * 
     * @param STAT_LG_T
     */
    public void setSTAT_LG_T(String STAT_LG_T) {
        this.STAT_LG_T = STAT_LG_T;
    }


    /**
     * Gets the STAT_NAME value for this CData_QUERY_RESULT_BigData.
     * 
     * @return STAT_NAME
     */
    public String getSTAT_NAME() {
        return STAT_NAME;
    }


    /**
     * Sets the STAT_NAME value for this CData_QUERY_RESULT_BigData.
     * 
     * @param STAT_NAME
     */
    public void setSTAT_NAME(String STAT_NAME) {
        this.STAT_NAME = STAT_NAME;
    }


    /**
     * Gets the STAT_STATUS value for this CData_QUERY_RESULT_BigData.
     * 
     * @return STAT_STATUS
     */
    public String getSTAT_STATUS() {
        return STAT_STATUS;
    }


    /**
     * Sets the STAT_STATUS value for this CData_QUERY_RESULT_BigData.
     * 
     * @param STAT_STATUS
     */
    public void setSTAT_STATUS(String STAT_STATUS) {
        this.STAT_STATUS = STAT_STATUS;
    }


    /**
     * Gets the STAT_TDI value for this CData_QUERY_RESULT_BigData.
     * 
     * @return STAT_TDI
     */
    public String getSTAT_TDI() {
        return STAT_TDI;
    }


    /**
     * Sets the STAT_TDI value for this CData_QUERY_RESULT_BigData.
     * 
     * @param STAT_TDI
     */
    public void setSTAT_TDI(String STAT_TDI) {
        this.STAT_TDI = STAT_TDI;
    }


    /**
     * Gets the STAT_TDI_SUM value for this CData_QUERY_RESULT_BigData.
     * 
     * @return STAT_TDI_SUM
     */
    public String getSTAT_TDI_SUM() {
        return STAT_TDI_SUM;
    }


    /**
     * Sets the STAT_TDI_SUM value for this CData_QUERY_RESULT_BigData.
     * 
     * @param STAT_TDI_SUM
     */
    public void setSTAT_TDI_SUM(String STAT_TDI_SUM) {
        this.STAT_TDI_SUM = STAT_TDI_SUM;
    }


    /**
     * Gets the ST_CLASS1 value for this CData_QUERY_RESULT_BigData.
     * 
     * @return ST_CLASS1
     */
    public String getST_CLASS1() {
        return ST_CLASS1;
    }


    /**
     * Sets the ST_CLASS1 value for this CData_QUERY_RESULT_BigData.
     * 
     * @param ST_CLASS1
     */
    public void setST_CLASS1(String ST_CLASS1) {
        this.ST_CLASS1 = ST_CLASS1;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof CData_QUERY_RESULT_BigData)) return false;
        CData_QUERY_RESULT_BigData other = (CData_QUERY_RESULT_BigData) obj;
        if (obj == null) return false;
        if (this == obj) return true;
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.APP_CODE==null && other.getAPP_CODE()==null) || 
             (this.APP_CODE!=null &&
              this.APP_CODE.equals(other.getAPP_CODE()))) &&
            ((this.APP_FTLB==null && other.getAPP_FTLB()==null) || 
             (this.APP_FTLB!=null &&
              this.APP_FTLB.equals(other.getAPP_FTLB()))) &&
            ((this.APP_FTLE==null && other.getAPP_FTLE()==null) || 
             (this.APP_FTLE!=null &&
              this.APP_FTLE.equals(other.getAPP_FTLE()))) &&
            ((this.ENABLE_DATE==null && other.getENABLE_DATE()==null) || 
             (this.ENABLE_DATE!=null &&
              this.ENABLE_DATE.equals(other.getENABLE_DATE()))) &&
            ((this.IS_AGENT==null && other.getIS_AGENT()==null) || 
             (this.IS_AGENT!=null &&
              this.IS_AGENT.equals(other.getIS_AGENT()))) &&
            ((this.IS_CHK==null && other.getIS_CHK()==null) || 
             (this.IS_CHK!=null &&
              this.IS_CHK.equals(other.getIS_CHK()))) &&
            ((this.IS_SCRECT==null && other.getIS_SCRECT()==null) || 
             (this.IS_SCRECT!=null &&
              this.IS_SCRECT.equals(other.getIS_SCRECT()))) &&
            ((this.IS_TEMP==null && other.getIS_TEMP()==null) || 
             (this.IS_TEMP!=null &&
              this.IS_TEMP.equals(other.getIS_TEMP()))) &&
            ((this.NET_GUID==null && other.getNET_GUID()==null) || 
             (this.NET_GUID!=null &&
              this.NET_GUID.equals(other.getNET_GUID()))) &&
            ((this.NET_NAME==null && other.getNET_NAME()==null) || 
             (this.NET_NAME!=null &&
              this.NET_NAME.equals(other.getNET_NAME()))) &&
            ((this.NET_SP==null && other.getNET_SP()==null) || 
             (this.NET_SP!=null &&
              this.NET_SP.equals(other.getNET_SP()))) &&
            ((this.NET_SVN==null && other.getNET_SVN()==null) || 
             (this.NET_SVN!=null &&
              this.NET_SVN.equals(other.getNET_SVN()))) &&
            ((this.NET_SVN_CHN==null && other.getNET_SVN_CHN()==null) || 
             (this.NET_SVN_CHN!=null &&
              this.NET_SVN_CHN.equals(other.getNET_SVN_CHN()))) &&
            ((this.NET_TS==null && other.getNET_TS()==null) || 
             (this.NET_TS!=null &&
              this.NET_TS.equals(other.getNET_TS()))) &&
            ((this.NET_TS_CHN==null && other.getNET_TS_CHN()==null) || 
             (this.NET_TS_CHN!=null &&
              this.NET_TS_CHN.equals(other.getNET_TS_CHN()))) &&
            ((this.ORA_TABLE==null && other.getORA_TABLE()==null) || 
             (this.ORA_TABLE!=null &&
              this.ORA_TABLE.equals(other.getORA_TABLE()))) &&
            ((this.ORG_GUID==null && other.getORG_GUID()==null) || 
             (this.ORG_GUID!=null &&
              this.ORG_GUID.equals(other.getORG_GUID()))) &&
            ((this.ORG_NAME==null && other.getORG_NAME()==null) || 
             (this.ORG_NAME!=null &&
              this.ORG_NAME.equals(other.getORG_NAME()))) &&
            ((this.STATION_GUID==null && other.getSTATION_GUID()==null) || 
             (this.STATION_GUID!=null &&
              this.STATION_GUID.equals(other.getSTATION_GUID()))) &&
            ((this.STAT_ADDR==null && other.getSTAT_ADDR()==null) || 
             (this.STAT_ADDR!=null &&
              this.STAT_ADDR.equals(other.getSTAT_ADDR()))) &&
            ((this.STAT_AREA_CODE==null && other.getSTAT_AREA_CODE()==null) || 
             (this.STAT_AREA_CODE!=null &&
              this.STAT_AREA_CODE.equals(other.getSTAT_AREA_CODE()))) &&
            ((this.STAT_EQU_SUM==null && other.getSTAT_EQU_SUM()==null) || 
             (this.STAT_EQU_SUM!=null &&
              this.STAT_EQU_SUM.equals(other.getSTAT_EQU_SUM()))) &&
            ((this.STAT_LA_T==null && other.getSTAT_LA_T()==null) || 
             (this.STAT_LA_T!=null &&
              this.STAT_LA_T.equals(other.getSTAT_LA_T()))) &&
            ((this.STAT_LG_T==null && other.getSTAT_LG_T()==null) || 
             (this.STAT_LG_T!=null &&
              this.STAT_LG_T.equals(other.getSTAT_LG_T()))) &&
            ((this.STAT_NAME==null && other.getSTAT_NAME()==null) || 
             (this.STAT_NAME!=null &&
              this.STAT_NAME.equals(other.getSTAT_NAME()))) &&
            ((this.STAT_STATUS==null && other.getSTAT_STATUS()==null) || 
             (this.STAT_STATUS!=null &&
              this.STAT_STATUS.equals(other.getSTAT_STATUS()))) &&
            ((this.STAT_TDI==null && other.getSTAT_TDI()==null) || 
             (this.STAT_TDI!=null &&
              this.STAT_TDI.equals(other.getSTAT_TDI()))) &&
            ((this.STAT_TDI_SUM==null && other.getSTAT_TDI_SUM()==null) || 
             (this.STAT_TDI_SUM!=null &&
              this.STAT_TDI_SUM.equals(other.getSTAT_TDI_SUM()))) &&
            ((this.ST_CLASS1==null && other.getST_CLASS1()==null) || 
             (this.ST_CLASS1!=null &&
              this.ST_CLASS1.equals(other.getST_CLASS1())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getAPP_CODE() != null) {
            _hashCode += getAPP_CODE().hashCode();
        }
        if (getAPP_FTLB() != null) {
            _hashCode += getAPP_FTLB().hashCode();
        }
        if (getAPP_FTLE() != null) {
            _hashCode += getAPP_FTLE().hashCode();
        }
        if (getENABLE_DATE() != null) {
            _hashCode += getENABLE_DATE().hashCode();
        }
        if (getIS_AGENT() != null) {
            _hashCode += getIS_AGENT().hashCode();
        }
        if (getIS_CHK() != null) {
            _hashCode += getIS_CHK().hashCode();
        }
        if (getIS_SCRECT() != null) {
            _hashCode += getIS_SCRECT().hashCode();
        }
        if (getIS_TEMP() != null) {
            _hashCode += getIS_TEMP().hashCode();
        }
        if (getNET_GUID() != null) {
            _hashCode += getNET_GUID().hashCode();
        }
        if (getNET_NAME() != null) {
            _hashCode += getNET_NAME().hashCode();
        }
        if (getNET_SP() != null) {
            _hashCode += getNET_SP().hashCode();
        }
        if (getNET_SVN() != null) {
            _hashCode += getNET_SVN().hashCode();
        }
        if (getNET_SVN_CHN() != null) {
            _hashCode += getNET_SVN_CHN().hashCode();
        }
        if (getNET_TS() != null) {
            _hashCode += getNET_TS().hashCode();
        }
        if (getNET_TS_CHN() != null) {
            _hashCode += getNET_TS_CHN().hashCode();
        }
        if (getORA_TABLE() != null) {
            _hashCode += getORA_TABLE().hashCode();
        }
        if (getORG_GUID() != null) {
            _hashCode += getORG_GUID().hashCode();
        }
        if (getORG_NAME() != null) {
            _hashCode += getORG_NAME().hashCode();
        }
        if (getSTATION_GUID() != null) {
            _hashCode += getSTATION_GUID().hashCode();
        }
        if (getSTAT_ADDR() != null) {
            _hashCode += getSTAT_ADDR().hashCode();
        }
        if (getSTAT_AREA_CODE() != null) {
            _hashCode += getSTAT_AREA_CODE().hashCode();
        }
        if (getSTAT_EQU_SUM() != null) {
            _hashCode += getSTAT_EQU_SUM().hashCode();
        }
        if (getSTAT_LA_T() != null) {
            _hashCode += getSTAT_LA_T().hashCode();
        }
        if (getSTAT_LG_T() != null) {
            _hashCode += getSTAT_LG_T().hashCode();
        }
        if (getSTAT_NAME() != null) {
            _hashCode += getSTAT_NAME().hashCode();
        }
        if (getSTAT_STATUS() != null) {
            _hashCode += getSTAT_STATUS().hashCode();
        }
        if (getSTAT_TDI() != null) {
            _hashCode += getSTAT_TDI().hashCode();
        }
        if (getSTAT_TDI_SUM() != null) {
            _hashCode += getSTAT_TDI_SUM().hashCode();
        }
        if (getST_CLASS1() != null) {
            _hashCode += getST_CLASS1().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(CData_QUERY_RESULT_BigData.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data.CData", "CData_QUERY_RESULT_BigData"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("APP_CODE");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data.CData", "APP_CODE"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("APP_FTLB");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data.CData", "APP_FTLB"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("APP_FTLE");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data.CData", "APP_FTLE"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ENABLE_DATE");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data.CData", "ENABLE_DATE"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("IS_AGENT");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data.CData", "IS_AGENT"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("IS_CHK");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data.CData", "IS_CHK"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "boolean"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("IS_SCRECT");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data.CData", "IS_SCRECT"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("IS_TEMP");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data.CData", "IS_TEMP"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("NET_GUID");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data.CData", "NET_GUID"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("NET_NAME");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data.CData", "NET_NAME"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("NET_SP");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data.CData", "NET_SP"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("NET_SVN");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data.CData", "NET_SVN"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("NET_SVN_CHN");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data.CData", "NET_SVN_CHN"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("NET_TS");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data.CData", "NET_TS"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("NET_TS_CHN");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data.CData", "NET_TS_CHN"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ORA_TABLE");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data.CData", "ORA_TABLE"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ORG_GUID");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data.CData", "ORG_GUID"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ORG_NAME");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data.CData", "ORG_NAME"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("STATION_GUID");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data.CData", "STATION_GUID"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("STAT_ADDR");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data.CData", "STAT_ADDR"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("STAT_AREA_CODE");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data.CData", "STAT_AREA_CODE"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("STAT_EQU_SUM");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data.CData", "STAT_EQU_SUM"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("STAT_LA_T");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data.CData", "STAT_LA_T"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("STAT_LG_T");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data.CData", "STAT_LG_T"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("STAT_NAME");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data.CData", "STAT_NAME"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("STAT_STATUS");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data.CData", "STAT_STATUS"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("STAT_TDI");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data.CData", "STAT_TDI"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("STAT_TDI_SUM");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data.CData", "STAT_TDI_SUM"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("ST_CLASS1");
        elemField.setXmlName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data.CData", "ST_CLASS1"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
