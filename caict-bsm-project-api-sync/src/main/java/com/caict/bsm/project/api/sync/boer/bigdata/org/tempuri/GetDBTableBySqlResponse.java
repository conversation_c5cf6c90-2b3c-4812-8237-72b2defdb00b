/**
 * GetDBTableBySqlResponse.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.caict.bsm.project.api.sync.boer.bigdata.org.tempuri;

import com.caict.bsm.project.api.sync.boer.bigdata.org.datacontract.schemas._2004._07.FS_ServerSolution_Data_StatQuery.DBTable;

public class GetDBTableBySqlResponse  implements java.io.Serializable {
    private DBTable getDBTableBySqlResult;

    public GetDBTableBySqlResponse() {
    }

    public GetDBTableBySqlResponse(
           DBTable getDBTableBySqlResult) {
           this.getDBTableBySqlResult = getDBTableBySqlResult;
    }


    /**
     * Gets the getDBTableBySqlResult value for this GetDBTableBySqlResponse.
     * 
     * @return getDBTableBySqlResult
     */
    public DBTable getGetDBTableBySqlResult() {
        return getDBTableBySqlResult;
    }


    /**
     * Sets the getDBTableBySqlResult value for this GetDBTableBySqlResponse.
     * 
     * @param getDBTableBySqlResult
     */
    public void setGetDBTableBySqlResult(DBTable getDBTableBySqlResult) {
        this.getDBTableBySqlResult = getDBTableBySqlResult;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof GetDBTableBySqlResponse)) return false;
        GetDBTableBySqlResponse other = (GetDBTableBySqlResponse) obj;
        if (obj == null) return false;
        if (this == obj) return true;
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.getDBTableBySqlResult==null && other.getGetDBTableBySqlResult()==null) || 
             (this.getDBTableBySqlResult!=null &&
              this.getDBTableBySqlResult.equals(other.getGetDBTableBySqlResult())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getGetDBTableBySqlResult() != null) {
            _hashCode += getGetDBTableBySqlResult().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(GetDBTableBySqlResponse.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://tempuri.org/", ">GetDBTableBySqlResponse"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("getDBTableBySqlResult");
        elemField.setXmlName(new javax.xml.namespace.QName("http://tempuri.org/", "GetDBTableBySqlResult"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data.StatQuery", "DBTable"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
