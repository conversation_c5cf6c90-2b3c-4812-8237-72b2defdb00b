/**
 * GetFsAppByAppYearRowNo.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.caict.bsm.project.api.sync.boer.bigdata.org.tempuri;

public class GetFsAppByAppYearRowNo  implements java.io.Serializable {
    private String strAreaCode;

    private String strOrgType;

    private String strNetTs;

    private String strAppYear;

    private Integer rowno;

    private Integer rowsize;

    public GetFsAppByAppYearRowNo() {
    }

    public GetFsAppByAppYearRowNo(
           String strAreaCode,
           String strOrgType,
           String strNetTs,
           String strAppYear,
           Integer rowno,
           Integer rowsize) {
           this.strAreaCode = strAreaCode;
           this.strOrgType = strOrgType;
           this.strNetTs = strNetTs;
           this.strAppYear = strAppYear;
           this.rowno = rowno;
           this.rowsize = rowsize;
    }


    /**
     * Gets the strAreaCode value for this GetFsAppByAppYearRowNo.
     * 
     * @return strAreaCode
     */
    public String getStrAreaCode() {
        return strAreaCode;
    }


    /**
     * Sets the strAreaCode value for this GetFsAppByAppYearRowNo.
     * 
     * @param strAreaCode
     */
    public void setStrAreaCode(String strAreaCode) {
        this.strAreaCode = strAreaCode;
    }


    /**
     * Gets the strOrgType value for this GetFsAppByAppYearRowNo.
     * 
     * @return strOrgType
     */
    public String getStrOrgType() {
        return strOrgType;
    }


    /**
     * Sets the strOrgType value for this GetFsAppByAppYearRowNo.
     * 
     * @param strOrgType
     */
    public void setStrOrgType(String strOrgType) {
        this.strOrgType = strOrgType;
    }


    /**
     * Gets the strNetTs value for this GetFsAppByAppYearRowNo.
     * 
     * @return strNetTs
     */
    public String getStrNetTs() {
        return strNetTs;
    }


    /**
     * Sets the strNetTs value for this GetFsAppByAppYearRowNo.
     * 
     * @param strNetTs
     */
    public void setStrNetTs(String strNetTs) {
        this.strNetTs = strNetTs;
    }


    /**
     * Gets the strAppYear value for this GetFsAppByAppYearRowNo.
     * 
     * @return strAppYear
     */
    public String getStrAppYear() {
        return strAppYear;
    }


    /**
     * Sets the strAppYear value for this GetFsAppByAppYearRowNo.
     * 
     * @param strAppYear
     */
    public void setStrAppYear(String strAppYear) {
        this.strAppYear = strAppYear;
    }


    /**
     * Gets the rowno value for this GetFsAppByAppYearRowNo.
     * 
     * @return rowno
     */
    public Integer getRowno() {
        return rowno;
    }


    /**
     * Sets the rowno value for this GetFsAppByAppYearRowNo.
     * 
     * @param rowno
     */
    public void setRowno(Integer rowno) {
        this.rowno = rowno;
    }


    /**
     * Gets the rowsize value for this GetFsAppByAppYearRowNo.
     * 
     * @return rowsize
     */
    public Integer getRowsize() {
        return rowsize;
    }


    /**
     * Sets the rowsize value for this GetFsAppByAppYearRowNo.
     * 
     * @param rowsize
     */
    public void setRowsize(Integer rowsize) {
        this.rowsize = rowsize;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof GetFsAppByAppYearRowNo)) return false;
        GetFsAppByAppYearRowNo other = (GetFsAppByAppYearRowNo) obj;
        if (obj == null) return false;
        if (this == obj) return true;
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.strAreaCode==null && other.getStrAreaCode()==null) || 
             (this.strAreaCode!=null &&
              this.strAreaCode.equals(other.getStrAreaCode()))) &&
            ((this.strOrgType==null && other.getStrOrgType()==null) || 
             (this.strOrgType!=null &&
              this.strOrgType.equals(other.getStrOrgType()))) &&
            ((this.strNetTs==null && other.getStrNetTs()==null) || 
             (this.strNetTs!=null &&
              this.strNetTs.equals(other.getStrNetTs()))) &&
            ((this.strAppYear==null && other.getStrAppYear()==null) || 
             (this.strAppYear!=null &&
              this.strAppYear.equals(other.getStrAppYear()))) &&
            ((this.rowno==null && other.getRowno()==null) || 
             (this.rowno!=null &&
              this.rowno.equals(other.getRowno()))) &&
            ((this.rowsize==null && other.getRowsize()==null) || 
             (this.rowsize!=null &&
              this.rowsize.equals(other.getRowsize())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getStrAreaCode() != null) {
            _hashCode += getStrAreaCode().hashCode();
        }
        if (getStrOrgType() != null) {
            _hashCode += getStrOrgType().hashCode();
        }
        if (getStrNetTs() != null) {
            _hashCode += getStrNetTs().hashCode();
        }
        if (getStrAppYear() != null) {
            _hashCode += getStrAppYear().hashCode();
        }
        if (getRowno() != null) {
            _hashCode += getRowno().hashCode();
        }
        if (getRowsize() != null) {
            _hashCode += getRowsize().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(GetFsAppByAppYearRowNo.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://tempuri.org/", ">GetFsAppByAppYearRowNo"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("strAreaCode");
        elemField.setXmlName(new javax.xml.namespace.QName("http://tempuri.org/", "strAreaCode"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("strOrgType");
        elemField.setXmlName(new javax.xml.namespace.QName("http://tempuri.org/", "strOrgType"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("strNetTs");
        elemField.setXmlName(new javax.xml.namespace.QName("http://tempuri.org/", "strNetTs"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("strAppYear");
        elemField.setXmlName(new javax.xml.namespace.QName("http://tempuri.org/", "strAppYear"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("rowno");
        elemField.setXmlName(new javax.xml.namespace.QName("http://tempuri.org/", "rowno"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("rowsize");
        elemField.setXmlName(new javax.xml.namespace.QName("http://tempuri.org/", "rowsize"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "int"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
