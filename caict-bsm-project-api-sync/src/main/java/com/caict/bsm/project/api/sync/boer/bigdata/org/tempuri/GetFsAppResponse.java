/**
 * GetFsAppResponse.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.caict.bsm.project.api.sync.boer.bigdata.org.tempuri;

import com.caict.bsm.project.api.sync.boer.bigdata.org.datacontract.schemas._2004._07.FS_ServerSolution_Data.V_FS_APP_YEAR;

public class GetFsAppResponse  implements java.io.Serializable {
    private V_FS_APP_YEAR[] getFsAppResult;

    public GetFsAppResponse() {
    }

    public GetFsAppResponse(
           V_FS_APP_YEAR[] getFsAppResult) {
           this.getFsAppResult = getFsAppResult;
    }


    /**
     * Gets the getFsAppResult value for this GetFsAppResponse.
     * 
     * @return getFsAppResult
     */
    public V_FS_APP_YEAR[] getGetFsAppResult() {
        return getFsAppResult;
    }


    /**
     * Sets the getFsAppResult value for this GetFsAppResponse.
     * 
     * @param getFsAppResult
     */
    public void setGetFsAppResult(V_FS_APP_YEAR[] getFsAppResult) {
        this.getFsAppResult = getFsAppResult;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof GetFsAppResponse)) return false;
        GetFsAppResponse other = (GetFsAppResponse) obj;
        if (obj == null) return false;
        if (this == obj) return true;
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.getFsAppResult==null && other.getGetFsAppResult()==null) || 
             (this.getFsAppResult!=null &&
              java.util.Arrays.equals(this.getFsAppResult, other.getGetFsAppResult())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getGetFsAppResult() != null) {
            for (int i=0;
                 i<java.lang.reflect.Array.getLength(getGetFsAppResult());
                 i++) {
                Object obj = java.lang.reflect.Array.get(getGetFsAppResult(), i);
                if (obj != null &&
                    !obj.getClass().isArray()) {
                    _hashCode += obj.hashCode();
                }
            }
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(GetFsAppResponse.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://tempuri.org/", ">GetFsAppResponse"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("getFsAppResult");
        elemField.setXmlName(new javax.xml.namespace.QName("http://tempuri.org/", "GetFsAppResult"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "V_FS_APP_YEAR"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        elemField.setItemQName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "V_FS_APP_YEAR"));
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
