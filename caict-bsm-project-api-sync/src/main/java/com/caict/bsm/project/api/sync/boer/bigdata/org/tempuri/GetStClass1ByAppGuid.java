/**
 * GetStClass1ByAppGuid.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.caict.bsm.project.api.sync.boer.bigdata.org.tempuri;

public class GetStClass1ByAppGuid  implements java.io.Serializable {
    private String strAppGuid;

    public GetStClass1ByAppGuid() {
    }

    public GetStClass1ByAppGuid(
           String strAppGuid) {
           this.strAppGuid = strAppGuid;
    }


    /**
     * Gets the strAppGuid value for this GetStClass1ByAppGuid.
     * 
     * @return strAppGuid
     */
    public String getStrAppGuid() {
        return strAppGuid;
    }


    /**
     * Sets the strAppGuid value for this GetStClass1ByAppGuid.
     * 
     * @param strAppGuid
     */
    public void setStrAppGuid(String strAppGuid) {
        this.strAppGuid = strAppGuid;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof GetStClass1ByAppGuid)) return false;
        GetStClass1ByAppGuid other = (GetStClass1ByAppGuid) obj;
        if (obj == null) return false;
        if (this == obj) return true;
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.strAppGuid==null && other.getStrAppGuid()==null) || 
             (this.strAppGuid!=null &&
              this.strAppGuid.equals(other.getStrAppGuid())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getStrAppGuid() != null) {
            _hashCode += getStrAppGuid().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(GetStClass1ByAppGuid.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://tempuri.org/", ">GetStClass1ByAppGuid"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("strAppGuid");
        elemField.setXmlName(new javax.xml.namespace.QName("http://tempuri.org/", "strAppGuid"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
