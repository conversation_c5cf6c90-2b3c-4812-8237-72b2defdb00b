/**
 * InsertLicenseNew_Data.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.caict.bsm.project.api.sync.boer.bigdata.org.tempuri;

public class InsertLicenseNew_Data  implements java.io.Serializable {
    private String p_appGuid;

    private String p_statGuid;

    private String strLoginName;

    public InsertLicenseNew_Data() {
    }

    public InsertLicenseNew_Data(
           String p_appGuid,
           String p_statGuid,
           String strLoginName) {
           this.p_appGuid = p_appGuid;
           this.p_statGuid = p_statGuid;
           this.strLoginName = strLoginName;
    }


    /**
     * Gets the p_appGuid value for this InsertLicenseNew_Data.
     * 
     * @return p_appGuid
     */
    public String getP_appGuid() {
        return p_appGuid;
    }


    /**
     * Sets the p_appGuid value for this InsertLicenseNew_Data.
     * 
     * @param p_appGuid
     */
    public void setP_appGuid(String p_appGuid) {
        this.p_appGuid = p_appGuid;
    }


    /**
     * Gets the p_statGuid value for this InsertLicenseNew_Data.
     * 
     * @return p_statGuid
     */
    public String getP_statGuid() {
        return p_statGuid;
    }


    /**
     * Sets the p_statGuid value for this InsertLicenseNew_Data.
     * 
     * @param p_statGuid
     */
    public void setP_statGuid(String p_statGuid) {
        this.p_statGuid = p_statGuid;
    }


    /**
     * Gets the strLoginName value for this InsertLicenseNew_Data.
     * 
     * @return strLoginName
     */
    public String getStrLoginName() {
        return strLoginName;
    }


    /**
     * Sets the strLoginName value for this InsertLicenseNew_Data.
     * 
     * @param strLoginName
     */
    public void setStrLoginName(String strLoginName) {
        this.strLoginName = strLoginName;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof InsertLicenseNew_Data)) return false;
        InsertLicenseNew_Data other = (InsertLicenseNew_Data) obj;
        if (obj == null) return false;
        if (this == obj) return true;
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.p_appGuid==null && other.getP_appGuid()==null) || 
             (this.p_appGuid!=null &&
              this.p_appGuid.equals(other.getP_appGuid()))) &&
            ((this.p_statGuid==null && other.getP_statGuid()==null) || 
             (this.p_statGuid!=null &&
              this.p_statGuid.equals(other.getP_statGuid()))) &&
            ((this.strLoginName==null && other.getStrLoginName()==null) || 
             (this.strLoginName!=null &&
              this.strLoginName.equals(other.getStrLoginName())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getP_appGuid() != null) {
            _hashCode += getP_appGuid().hashCode();
        }
        if (getP_statGuid() != null) {
            _hashCode += getP_statGuid().hashCode();
        }
        if (getStrLoginName() != null) {
            _hashCode += getStrLoginName().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(InsertLicenseNew_Data.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://tempuri.org/", ">InsertLicenseNew_Data"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("p_appGuid");
        elemField.setXmlName(new javax.xml.namespace.QName("http://tempuri.org/", "p_appGuid"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("p_statGuid");
        elemField.setXmlName(new javax.xml.namespace.QName("http://tempuri.org/", "p_statGuid"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("strLoginName");
        elemField.setXmlName(new javax.xml.namespace.QName("http://tempuri.org/", "strLoginName"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
