/**
 * QuickQueryStatInfoResponse.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.caict.bsm.project.api.sync.boer.bigdata.org.tempuri;

import com.caict.bsm.project.api.sync.boer.bigdata.org.datacontract.schemas._2004._07.FS_ServerSolution_Data_CData.CData_QUERY_RESULT_BigData;

public class QuickQueryStatInfoResponse  implements java.io.Serializable {
    private CData_QUERY_RESULT_BigData[] quickQueryStatInfoResult;

    public QuickQueryStatInfoResponse() {
    }

    public QuickQueryStatInfoResponse(
           CData_QUERY_RESULT_BigData[] quickQueryStatInfoResult) {
           this.quickQueryStatInfoResult = quickQueryStatInfoResult;
    }


    /**
     * Gets the quickQueryStatInfoResult value for this QuickQueryStatInfoResponse.
     * 
     * @return quickQueryStatInfoResult
     */
    public CData_QUERY_RESULT_BigData[] getQuickQueryStatInfoResult() {
        return quickQueryStatInfoResult;
    }


    /**
     * Sets the quickQueryStatInfoResult value for this QuickQueryStatInfoResponse.
     * 
     * @param quickQueryStatInfoResult
     */
    public void setQuickQueryStatInfoResult(CData_QUERY_RESULT_BigData[] quickQueryStatInfoResult) {
        this.quickQueryStatInfoResult = quickQueryStatInfoResult;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof QuickQueryStatInfoResponse)) return false;
        QuickQueryStatInfoResponse other = (QuickQueryStatInfoResponse) obj;
        if (obj == null) return false;
        if (this == obj) return true;
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.quickQueryStatInfoResult==null && other.getQuickQueryStatInfoResult()==null) || 
             (this.quickQueryStatInfoResult!=null &&
              java.util.Arrays.equals(this.quickQueryStatInfoResult, other.getQuickQueryStatInfoResult())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getQuickQueryStatInfoResult() != null) {
            for (int i=0;
                 i<java.lang.reflect.Array.getLength(getQuickQueryStatInfoResult());
                 i++) {
                Object obj = java.lang.reflect.Array.get(getQuickQueryStatInfoResult(), i);
                if (obj != null &&
                    !obj.getClass().isArray()) {
                    _hashCode += obj.hashCode();
                }
            }
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(QuickQueryStatInfoResponse.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://tempuri.org/", ">QuickQueryStatInfoResponse"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("quickQueryStatInfoResult");
        elemField.setXmlName(new javax.xml.namespace.QName("http://tempuri.org/", "QuickQueryStatInfoResult"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data.CData", "CData_QUERY_RESULT_BigData"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        elemField.setItemQName(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data.CData", "CData_QUERY_RESULT_BigData"));
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
