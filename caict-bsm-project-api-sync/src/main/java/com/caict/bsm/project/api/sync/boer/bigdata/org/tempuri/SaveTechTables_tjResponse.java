/**
 * SaveTechTables_tjResponse.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.caict.bsm.project.api.sync.boer.bigdata.org.tempuri;

import com.caict.bsm.project.api.sync.boer.bigdata.org.datacontract.schemas._2004._07.FS_ServerSolution_Data.ReturnObjectST_TJ;

public class SaveTechTables_tjResponse  implements java.io.Serializable {
    private ReturnObjectST_TJ saveTechTables_tjResult;

    public SaveTechTables_tjResponse() {
    }

    public SaveTechTables_tjResponse(
           ReturnObjectST_TJ saveTechTables_tjResult) {
           this.saveTechTables_tjResult = saveTechTables_tjResult;
    }


    /**
     * Gets the saveTechTables_tjResult value for this SaveTechTables_tjResponse.
     * 
     * @return saveTechTables_tjResult
     */
    public ReturnObjectST_TJ getSaveTechTables_tjResult() {
        return saveTechTables_tjResult;
    }


    /**
     * Sets the saveTechTables_tjResult value for this SaveTechTables_tjResponse.
     * 
     * @param saveTechTables_tjResult
     */
    public void setSaveTechTables_tjResult(ReturnObjectST_TJ saveTechTables_tjResult) {
        this.saveTechTables_tjResult = saveTechTables_tjResult;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof SaveTechTables_tjResponse)) return false;
        SaveTechTables_tjResponse other = (SaveTechTables_tjResponse) obj;
        if (obj == null) return false;
        if (this == obj) return true;
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.saveTechTables_tjResult==null && other.getSaveTechTables_tjResult()==null) || 
             (this.saveTechTables_tjResult!=null &&
              this.saveTechTables_tjResult.equals(other.getSaveTechTables_tjResult())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getSaveTechTables_tjResult() != null) {
            _hashCode += getSaveTechTables_tjResult().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(SaveTechTables_tjResponse.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://tempuri.org/", ">SaveTechTables_tjResponse"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("saveTechTables_tjResult");
        elemField.setXmlName(new javax.xml.namespace.QName("http://tempuri.org/", "SaveTechTables_tjResult"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "ReturnObjectST_TJ"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
