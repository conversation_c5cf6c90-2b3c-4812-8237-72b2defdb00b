/**
 * ToPlatformMessage_Data.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.caict.bsm.project.api.sync.boer.bigdata.org.tempuri;

import com.caict.bsm.project.api.sync.boer.bigdata.org.datacontract.schemas._2004._07.FS_ServerSolution_Data.PlatformMessageType;

public class ToPlatformMessage_Data  implements java.io.Serializable {
    private String userid;

    private String strAPP_GUID;

    private String strST_CLASS1;

    private String strORG_NAME;

    private PlatformMessageType mType;

    public ToPlatformMessage_Data() {
    }

    public ToPlatformMessage_Data(
           String userid,
           String strAPP_GUID,
           String strST_CLASS1,
           String strORG_NAME,
           PlatformMessageType mType) {
           this.userid = userid;
           this.strAPP_GUID = strAPP_GUID;
           this.strST_CLASS1 = strST_CLASS1;
           this.strORG_NAME = strORG_NAME;
           this.mType = mType;
    }


    /**
     * Gets the userid value for this ToPlatformMessage_Data.
     * 
     * @return userid
     */
    public String getUserid() {
        return userid;
    }


    /**
     * Sets the userid value for this ToPlatformMessage_Data.
     * 
     * @param userid
     */
    public void setUserid(String userid) {
        this.userid = userid;
    }


    /**
     * Gets the strAPP_GUID value for this ToPlatformMessage_Data.
     * 
     * @return strAPP_GUID
     */
    public String getStrAPP_GUID() {
        return strAPP_GUID;
    }


    /**
     * Sets the strAPP_GUID value for this ToPlatformMessage_Data.
     * 
     * @param strAPP_GUID
     */
    public void setStrAPP_GUID(String strAPP_GUID) {
        this.strAPP_GUID = strAPP_GUID;
    }


    /**
     * Gets the strST_CLASS1 value for this ToPlatformMessage_Data.
     * 
     * @return strST_CLASS1
     */
    public String getStrST_CLASS1() {
        return strST_CLASS1;
    }


    /**
     * Sets the strST_CLASS1 value for this ToPlatformMessage_Data.
     * 
     * @param strST_CLASS1
     */
    public void setStrST_CLASS1(String strST_CLASS1) {
        this.strST_CLASS1 = strST_CLASS1;
    }


    /**
     * Gets the strORG_NAME value for this ToPlatformMessage_Data.
     * 
     * @return strORG_NAME
     */
    public String getStrORG_NAME() {
        return strORG_NAME;
    }


    /**
     * Sets the strORG_NAME value for this ToPlatformMessage_Data.
     * 
     * @param strORG_NAME
     */
    public void setStrORG_NAME(String strORG_NAME) {
        this.strORG_NAME = strORG_NAME;
    }


    /**
     * Gets the mType value for this ToPlatformMessage_Data.
     * 
     * @return mType
     */
    public PlatformMessageType getMType() {
        return mType;
    }


    /**
     * Sets the mType value for this ToPlatformMessage_Data.
     * 
     * @param mType
     */
    public void setMType(PlatformMessageType mType) {
        this.mType = mType;
    }

    private Object __equalsCalc = null;
    public synchronized boolean equals(Object obj) {
        if (!(obj instanceof ToPlatformMessage_Data)) return false;
        ToPlatformMessage_Data other = (ToPlatformMessage_Data) obj;
        if (obj == null) return false;
        if (this == obj) return true;
        if (__equalsCalc != null) {
            return (__equalsCalc == obj);
        }
        __equalsCalc = obj;
        boolean _equals;
        _equals = true && 
            ((this.userid==null && other.getUserid()==null) || 
             (this.userid!=null &&
              this.userid.equals(other.getUserid()))) &&
            ((this.strAPP_GUID==null && other.getStrAPP_GUID()==null) || 
             (this.strAPP_GUID!=null &&
              this.strAPP_GUID.equals(other.getStrAPP_GUID()))) &&
            ((this.strST_CLASS1==null && other.getStrST_CLASS1()==null) || 
             (this.strST_CLASS1!=null &&
              this.strST_CLASS1.equals(other.getStrST_CLASS1()))) &&
            ((this.strORG_NAME==null && other.getStrORG_NAME()==null) || 
             (this.strORG_NAME!=null &&
              this.strORG_NAME.equals(other.getStrORG_NAME()))) &&
            ((this.mType==null && other.getMType()==null) || 
             (this.mType!=null &&
              this.mType.equals(other.getMType())));
        __equalsCalc = null;
        return _equals;
    }

    private boolean __hashCodeCalc = false;
    public synchronized int hashCode() {
        if (__hashCodeCalc) {
            return 0;
        }
        __hashCodeCalc = true;
        int _hashCode = 1;
        if (getUserid() != null) {
            _hashCode += getUserid().hashCode();
        }
        if (getStrAPP_GUID() != null) {
            _hashCode += getStrAPP_GUID().hashCode();
        }
        if (getStrST_CLASS1() != null) {
            _hashCode += getStrST_CLASS1().hashCode();
        }
        if (getStrORG_NAME() != null) {
            _hashCode += getStrORG_NAME().hashCode();
        }
        if (getMType() != null) {
            _hashCode += getMType().hashCode();
        }
        __hashCodeCalc = false;
        return _hashCode;
    }

    // Type metadata
    private static org.apache.axis.description.TypeDesc typeDesc =
        new org.apache.axis.description.TypeDesc(ToPlatformMessage_Data.class, true);

    static {
        typeDesc.setXmlType(new javax.xml.namespace.QName("http://tempuri.org/", ">ToPlatformMessage_Data"));
        org.apache.axis.description.ElementDesc elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("userid");
        elemField.setXmlName(new javax.xml.namespace.QName("http://tempuri.org/", "userid"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("strAPP_GUID");
        elemField.setXmlName(new javax.xml.namespace.QName("http://tempuri.org/", "strAPP_GUID"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("strST_CLASS1");
        elemField.setXmlName(new javax.xml.namespace.QName("http://tempuri.org/", "strST_CLASS1"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("strORG_NAME");
        elemField.setXmlName(new javax.xml.namespace.QName("http://tempuri.org/", "strORG_NAME"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://www.w3.org/2001/XMLSchema", "string"));
        elemField.setMinOccurs(0);
        elemField.setNillable(true);
        typeDesc.addFieldDesc(elemField);
        elemField = new org.apache.axis.description.ElementDesc();
        elemField.setFieldName("MType");
        elemField.setXmlName(new javax.xml.namespace.QName("http://tempuri.org/", "mType"));
        elemField.setXmlType(new javax.xml.namespace.QName("http://schemas.datacontract.org/2004/07/FS.ServerSolution.Data", "PlatformMessageType"));
        elemField.setMinOccurs(0);
        elemField.setNillable(false);
        typeDesc.addFieldDesc(elemField);
    }

    /**
     * Return type metadata object
     */
    public static org.apache.axis.description.TypeDesc getTypeDesc() {
        return typeDesc;
    }

    /**
     * Get Custom Serializer
     */
    public static org.apache.axis.encoding.Serializer getSerializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanSerializer(
            _javaType, _xmlType, typeDesc);
    }

    /**
     * Get Custom Deserializer
     */
    public static org.apache.axis.encoding.Deserializer getDeserializer(
           String mechType,
           Class _javaType,
           javax.xml.namespace.QName _xmlType) {
        return 
          new  org.apache.axis.encoding.ser.BeanDeserializer(
            _javaType, _xmlType, typeDesc);
    }

}
