package com.caict.bsm.project.api.sync.repository.impl.stander;

import com.caict.bsm.project.api.sync.repository.BasicRepository;
import com.caict.bsm.project.api.sync.repository.impl.BasicRepositoryI;
import com.caict.bsm.project.api.sync.repository.stander.ApplyJobInSyncRepository;
import com.caict.bsm.project.system.model.entity.business.transfer_in.ApplyJobIn;
import org.springframework.beans.BeanWrapper;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class ApplyJobInSyncRepositoryI extends BasicRepositoryI<ApplyJobIn, JdbcTemplate> implements ApplyJobInSyncRepository {
    @Override
    public int updateByAppCode(String appGuid,String appCode,String status, JdbcTemplate jdbcTemplate) {
        try{
            String sql = "UPDATE APPLY_JOB_IN set IS_COMPARE = ? " +
                    "where APP_GUID = ? " +
                    "and APP_CODE = ? ";
            Object[] objects = {status,appGuid,appCode};
            return jdbcTemplate.update(sql,objects);
        }catch (Exception e){
            return 0;
        }
    }

    @Override
    public List<ApplyJobIn> findByNewAppCode(String appCode, JdbcTemplate jdbcTemplate) {
        try{
            String sql = "select * from APPLY_JOB_IN " +
                    "where APP_CODE = ?  ";
            Object[] objects = {appCode};
            return jdbcTemplate.query(sql,objects,new BeanPropertyRowMapper<ApplyJobIn>(ApplyJobIn.class) {
                @Override
                protected void initBeanWrapper(BeanWrapper bw) {
                    super.initBeanWrapper(bw);
                }
            });
        }catch (Exception e){
            return null;
        }
    }
}
