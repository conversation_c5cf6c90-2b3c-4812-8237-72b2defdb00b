package com.caict.bsm.project.api.sync.repository.stander;

import com.caict.bsm.project.api.sync.repository.BasicRepository;
import com.caict.bsm.project.system.model.entity.business.transfer_in.ApplyJobIn;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.List;

public interface ApplyJobInSyncRepository extends BasicRepository<ApplyJobIn, JdbcTemplate> {

    int updateByAppCode(String appGuid,String appCode,String status,JdbcTemplate jdbcTemplate);

    List<ApplyJobIn> findByNewAppCode(String appCode, JdbcTemplate jdbcTemplate);
}
