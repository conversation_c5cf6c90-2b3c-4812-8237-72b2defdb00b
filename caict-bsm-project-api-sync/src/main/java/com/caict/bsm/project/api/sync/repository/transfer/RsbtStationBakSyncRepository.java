package com.caict.bsm.project.api.sync.repository.transfer;

import com.caict.bsm.project.api.sync.repository.BasicRepository;
import com.caict.bsm.project.system.model.dto.business.transfer_in.ApplyJobInDTO;
import com.caict.bsm.project.system.model.entity.business.stationbak.RsbtStationBak;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.List;

public interface RsbtStationBakSyncRepository extends BasicRepository<RsbtStationBak, JdbcTemplate> {

    List<RsbtStationBak> findCountByAppGuid(String appGuid, JdbcTemplate jdbcTemplate);

    int updateByAppGuid(String appGuid,String status,JdbcTemplate jdbcTemplate);
}
