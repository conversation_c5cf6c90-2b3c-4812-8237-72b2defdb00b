package com.caict.bsm.project.api.sync.service.stander;

import com.caict.bsm.project.api.sync.repository.stander.ApprovalTransportJobSyncRepository;
import com.caict.bsm.project.system.model.entity.business.transfer.ApprovalTransportJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ApprovalTransportJobSyncService {
    @Autowired
    @Qualifier("oracleJdbcTemplate")
    protected JdbcTemplate oracleJdbcTemplate;

    @Autowired
    private ApprovalTransportJobSyncRepository approvalTransportJobSyncRepository;

    public List<ApprovalTransportJob> findByAppGuid(String appGuid){
        return approvalTransportJobSyncRepository.findByGuid(appGuid,oracleJdbcTemplate);
    }

    public List<ApprovalTransportJob> findByJobId(String jobId){
        return approvalTransportJobSyncRepository.findByJobId(jobId,oracleJdbcTemplate);
    }
}
