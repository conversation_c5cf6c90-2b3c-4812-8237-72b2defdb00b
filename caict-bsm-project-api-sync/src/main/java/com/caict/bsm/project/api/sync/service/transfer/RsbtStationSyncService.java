package com.caict.bsm.project.api.sync.service.transfer;

import com.caict.bsm.project.api.sync.repository.transfer.RsbtStationSyncRepository;
import com.caict.bsm.project.system.model.entity.business.station.RsbtStation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by yanchengpeng on 2020/12/3.
 */
@Service
public class RsbtStationSyncService {

    @Autowired
    @Qualifier("oracleJdbcTemplate")
    protected JdbcTemplate oracleJdbcTemplate;
    @Autowired
    @Qualifier("oracleStandardJdbcTemplate")
    protected JdbcTemplate oracleStandardJdbcTemplate;

    @Autowired
    private RsbtStationSyncRepository rsbtStationSyncRepository;

    public List<RsbtStation> findByAppCode(String appCode){
        return rsbtStationSyncRepository.findByAppCode(appCode,oracleJdbcTemplate);
    }

    public List<RsbtStation> findStationAllPage(int rowStart, int rowEnd,String appCode){
        return rsbtStationSyncRepository.findStationAllPage(rowStart,rowEnd,appCode,oracleJdbcTemplate);
    }

    public int batchInsert(List<RsbtStation> stations){
        return rsbtStationSyncRepository.batchInsert(stations,oracleStandardJdbcTemplate);
    }

    public int batchUpdateByGuid(List<RsbtStation> stations){
        return rsbtStationSyncRepository.batchUpdateByGuid(stations,oracleStandardJdbcTemplate);
    }

    public RsbtStation findOneByStatName(String statName){
        return rsbtStationSyncRepository.findOneByStatName(statName,oracleStandardJdbcTemplate);
    }

    public int findCountByAppCode(String appCode){
        return rsbtStationSyncRepository.findCountByAppCode(appCode,oracleJdbcTemplate);
    }


}
