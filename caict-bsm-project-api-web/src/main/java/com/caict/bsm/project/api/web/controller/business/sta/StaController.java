package com.caict.bsm.project.api.web.controller.business.sta;

import com.alibaba.fastjson.JSONObject;

import com.caict.bsm.project.api.web.service.sta.StaStationWebService;
import com.caict.bsm.project.system.model.dto.business.sta.StaDTO;
import com.caict.bsm.project.system.model.dto.business.sta.StaListDTO;
import com.caict.bsm.project.system.model.dto.business.sta.StaUpdateDTO;
import com.caict.bsm.project.system.utils.util.JSONResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/apiSta/sta/sta")
@Api(value = "台站管理模块", tags = "台站管理模块")
public class StaController {
    @Autowired
    private StaStationWebService staService;

    //台站列表
    @ApiOperation(value = "分页查看台站")
    @PostMapping(value = "/list")
    public JSONObject getList(
            @RequestBody StaDTO staDTO) {
        return staService.getListVOByPage(staDTO);
    }


    //台站详情
    @ApiOperation(value = "台站详情")
    @PostMapping(value = "/sta")
    public JSONObject get(
            @RequestBody Map<String,String> stationGuid) {

        StaListDTO staListDTO = staService.getList(stationGuid.get("stationGuid"));
        if (null == staListDTO) {
            return JSONResult.getFailureJson("参数错误");
        }
        return JSONResult.getSuccessJson(staListDTO);
    }


    //删除台站
    @ApiOperation(value = "删除台站")
    @PostMapping(value = "/deleteSta")
    public JSONObject deleteSta(
            @RequestParam String stationGuid) {
        return staService.deleteSta(stationGuid);
    }

    //增加台站
    @ApiOperation(value = "增加台站")
    @PostMapping(value = "/addSta")
    public JSONObject addSta(
            @RequestBody StaUpdateDTO staUpdateDTO) {
        return staService.add(staUpdateDTO);

    }

    //判断执照编号，无线识别码唯一性
    @ApiOperation(value = "判断执照编号，无线识别码唯一性")
    @PostMapping(value = "/isOnly")
    public JSONObject isOnly(
            @RequestBody Map<String,String> map,
            HttpSession session) {
        return staService.selectIdenCode(map.get("linceseCode"), map.get("idenCode"));
    }
    //修改台站
    @ApiOperation(value = "修改台站")
    @PostMapping(value = "/updateSta")
    public JSONObject updateSta(
            @RequestBody StaUpdateDTO staUpdateDTO) {
        return staService.update(staUpdateDTO);
    }

    @ApiOperation(value = "电子执照打印", notes = "电子执照打印接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stationGuid", value = "基站guid", required = true,paramType = "path", dataType = "String")
    })
    @RequestMapping(value = "/printLicense/{stationGuid}", method = RequestMethod.GET)
    public void printLicense(@PathVariable("stationGuid")String stationGuid, HttpServletResponse response){
        staService.print(stationGuid,response);
    }

    //执照下载
    @ApiOperation(value = "执照下载")
    @RequestMapping(value = "/downloadLicense/{stationId}", method = RequestMethod.GET)
    public JSONObject downloadLicense(@PathVariable("stationId") String stationId) {
        if (stationId!=null){
            String filePath = staService.savePdf(stationId, "template");
            if (filePath==null){
                return JSONResult.getFailureJson("参数错误");
            }
            return JSONResult.getSuccessJson(filePath, "操作成功");
        }
        return JSONResult.getFailureJson("参数错误");
    }

    //执照批量下载
    @ApiOperation(value = "执照批量下载")
    @PostMapping(value = "/downloadLicenseZip")
    public JSONObject downloadLicenseZip(
            @RequestBody List<String> stationIdlist) {
        return staService.savePdfZip(stationIdlist);
    }
}
