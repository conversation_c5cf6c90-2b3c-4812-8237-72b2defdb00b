package com.caict.bsm.project.api.web.controller.security;

import com.alibaba.fastjson.JSONObject;
import com.caict.bsm.project.api.web.service.security.AuthWebService;
import com.caict.bsm.project.system.model.dto.security.AspxDTO;
import com.caict.bsm.project.system.model.dto.security.UsersDTO;
import com.caict.bsm.project.system.utils.util.JSONResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.jasig.cas.client.util.AbstractCasFilter;
import org.jasig.cas.client.validation.Assertion;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.util.Map;

/**
 * Created by dengsy on 2019-10-24.
 */
@RestController
@RequestMapping(value = "/apiWeb/security/auth")
@Api(value = "web端权限接口", tags = "web端权限接口")
public class AuthController {

    private static final Logger LOG = LoggerFactory.getLogger(AuthController.class);

    @Autowired
    private AuthWebService authWebService;

    @ApiOperation(value = "登录", notes = "登录接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "usersDTO",value = "用户对象",required = true,paramType = "body",dataType = "usersDTO")
    })
    @PostMapping(value = "/loginOn")
    public JSONObject loginOn(@RequestBody UsersDTO usersDTO){
        if ("2".equals(authWebService.isLoginOnly())) return JSONResult.getFailureJson("其他运营商正在提交数据，为了保证您的数据安全请稍后重试");
        if(usersDTO != null && usersDTO.getLoginName() != null && !"".equals(usersDTO.getLoginName()) && usersDTO.getPassword() != null && !"".equals(usersDTO.getPassword())){
            Map<String,String> map = authWebService.loginOn(usersDTO.getLoginName(),usersDTO.getPassword());
            if (map != null) return JSONResult.getSuccessJson(map,"登录成功");
            return JSONResult.getFailureJson("登录失败");
        }
        return JSONResult.getFailureJson("请确认数据完整性");
    }

    @ApiOperation(value = "主键登录", notes = "主键登录接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId",value = "用户主键",required = true,paramType = "body",dataType = "String")
    })
    @GetMapping(value = "/loginOn/{userId}")
    public JSONObject loginOnByUserId(@PathVariable("userId")String userId){
        Map<String,String> map = authWebService.loginOn(userId);
        if (map != null) return JSONResult.getSuccessJson(map,"登录成功");
        return JSONResult.getFailureJson("登录失败");
    }

    @ApiOperation(value = "主键登录", notes = "主键登录接口")
    @GetMapping(value = "/loginOn/test")
    public JSONObject test(){
        return JSONResult.getFailureJson("登录失败");
    }

    @ApiOperation(value = "登出", notes = "登出接口")
    @GetMapping(value = "/loginOff")
    public JSONObject loginOff(@RequestHeader String token){
        authWebService.loginOff(token);
        return JSONResult.getSuccessJson("登出成功");
    }

    @ApiOperation(value = "根据token获取用户登陆信息", notes = "根据token获取用户登陆信息口")
    @GetMapping(value = "/getLoginUsersDTO")
    public JSONObject getLoginUsersDTO(@RequestHeader String token){
        return authWebService.getLoginUsersDTO(token);
    }

    @ApiOperation(value = "重置密码", notes = "重置密码接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "usersDTO",value = "用户对象",required = true,paramType = "body",dataType = "usersDTO")
    })
    @PostMapping(value = "/resetPassword")
    public JSONObject resetPassword(@RequestBody UsersDTO usersDTO){
        if (authWebService.resetPassword(usersDTO.getLoginName(),usersDTO.getPassword()) > 0) return JSONResult.getSuccessJson("重置密码成功");
        return JSONResult.getFailureJson("重置失败");
    }

    @ApiOperation(value = "修改密码", notes = "修改密码接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "usersDTO",value = "用户对象",required = true,paramType = "body",dataType = "usersDTO")
    })
    @PostMapping(value = "/updatePassword")
    public JSONObject updatePassword(@RequestHeader String token, @RequestBody UsersDTO usersDTO){
        if(token != null){
            UsersDTO usersDTOToken = authWebService.findLoginUsersDTO(token);
            if(usersDTOToken != null){
                Map<String,String> map = authWebService.updatePassword(usersDTOToken.getUserId(),usersDTO.getPasswordOld(),usersDTO.getPassword());
                if (map != null) return JSONResult.getSuccessJson(map,"修改成功");
                return JSONResult.getFailureJson("修改失败");
            }else {
                return JSONResult.getFailureJson("无token的用户信息");
            }
        }
        return JSONResult.getFailureJson("未识别token");
    }

    @ApiOperation(value = "主键登录", notes = "主键登录接口")
    @GetMapping(value = "/loginBySRRC")
    public JSONObject test(HttpServletRequest request, HttpServletResponse response){
        try {
            HttpSession session = request.getSession();
            Assertion assertion = (Assertion) session.getAttribute(AbstractCasFilter.CONST_CAS_ASSERTION);
            if (assertion != null) {
                //获取登录用户名
                String username = assertion.getPrincipal().getName();
                Object name = assertion.getPrincipal().getAttributes().get("userName");
                String jsessionid = session.getId();
                Cookie cookie = new Cookie("JSESSIONID", jsessionid);
                response.addCookie(cookie);
                Map<String, String> map = authWebService.loginOn(username);
                if (map != null) return JSONResult.getSuccessJson(map, "登录成功");
                return JSONResult.getFailureJson("登录失败");
            }
        } catch (Exception ex) {
//            LOG.error(ex.getMessage());
        }
        return null;
    }
}
