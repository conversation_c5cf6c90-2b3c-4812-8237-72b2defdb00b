package com.caict.bsm.project.api.web.service.business.freqevaluation;

import com.alibaba.fastjson.JSONObject;
import com.caict.bsm.project.domain.business.service.freqevaluation.FreqEvaluationJobService;
import com.caict.bsm.project.domain.business.service.transfer.TransportFileService;
import com.caict.bsm.project.domain.business.service.transfer.TransportJobService;
import com.caict.bsm.project.domain.security.service.UsersService;
import com.caict.bsm.project.system.model.dto.business.freqevaluation.FreqEvaluationJobDTO;
import com.caict.bsm.project.system.model.entity.business.freqevaluation.FreqEvaluationJob;
import com.caict.bsm.project.system.model.entity.business.transfer.TransportFile;
import com.caict.bsm.project.system.model.entity.business.transfer.TransportJob;
import com.caict.bsm.project.system.model.entity.message.Message;
import com.caict.bsm.project.system.model.entity.security.Users;
import com.caict.bsm.project.system.model.vo.freqevaluation.FreqEvaluationJobVO;
import com.caict.bsm.project.system.utils.repository.MyRestRepository;
import com.caict.bsm.project.system.utils.util.JSONResult;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 省（自治区、直辖市）公众移动通信系统无线电频率使用率评价job
 * @date 2023年8月24日 11点13分
 */
@Service
public class FreqEvaluationJobWebService {

    @Value("${caict.myFilePath}")
    private String myFilePath;

    @Autowired
    private FreqEvaluationJobService service;

    @Autowired
    private TransportJobService jobService;

    @Autowired
    private UsersService usersService;

    @Autowired
    private TransportFileService fileService;

    @Autowired
    private MyRestRepository<Message> myRestRepository;

    /**
     * 添加、修改组织机构
     */
    public JSONObject save(FreqEvaluationJobVO freqEvaluationJobVO) {
        // fileType TransportFileStateConst.FREQ = 5
        FreqEvaluationJob freqEvaluationJob = new FreqEvaluationJob();
        BeanUtils.copyProperties(freqEvaluationJobVO, freqEvaluationJob);
        if (StringUtils.isNotEmpty(freqEvaluationJobVO.getFileGuid())) {
            TransportFile file = fileService.findOne(freqEvaluationJobVO.getFileGuid());
            freqEvaluationJob.setFileName(myFilePath + file.getFilePath());
        }
        if ("1".equals(freqEvaluationJobVO.getType())) {
            TransportJob job = jobService.findOne(freqEvaluationJobVO.getJobId());
            Users users = usersService.getUsersById(freqEvaluationJobVO.getUserId());
            freqEvaluationJob.setOrgName(users.getName());
            freqEvaluationJob.setOrgType(users.getType());
            freqEvaluationJob.setJobName(users.getName() + "_系统提交_" + job.getJobName());
        }
        freqEvaluationJob.setCreateDate(new Date());
        String id = service.save(freqEvaluationJob);
        if (id != null) {
            // 频率评价
            myRestRepository.getForStringNpList("http://127.0.0.1:8089/apiweb/basestation/dataImport?jobGuid=" + id);
        }
        return JSONResult.getSuccessJson(id);
    }

    /**
     * 删除组织机构（根据guid）
     */
    public JSONObject delete(String guid) {
        //删除用户关联
        int num = service.delete(guid);
        return JSONResult.getSuccessJson(num);
    }

    /**
     * 分页条件查询
     */
    public JSONObject findAllByWhere(FreqEvaluationJobVO vo) {
        List<FreqEvaluationJobDTO> voList = service.findAllByWhere(((vo.getPage() - 1) * vo.getRows()) + 1, vo.getPage() * vo.getRows(), vo);
        if (voList != null) {
            int total = service.selectCountWhere(vo);
            return JSONResult.getPageSuccessJson(vo.getPage(), vo.getPage() + 1, vo.getRows(), total, voList);
        }
        return JSONResult.getPageFailureJson(vo.getPage(), vo.getPage() + 1, vo.getRows(), 0, "操作失败");

    }

    /**
     * 根据id查询详情
     */
    public JSONObject findOne(String guid) {
        FreqEvaluationJob freqEvaluationJob = service.findById(guid);
        if (freqEvaluationJob != null) {
            FreqEvaluationJobDTO dto = new FreqEvaluationJobDTO();
            BeanUtils.copyProperties(freqEvaluationJob, dto);
            return JSONResult.getSuccessJson(dto);
        }
        return JSONResult.getFailureJson("未查询到数据");
    }

}
