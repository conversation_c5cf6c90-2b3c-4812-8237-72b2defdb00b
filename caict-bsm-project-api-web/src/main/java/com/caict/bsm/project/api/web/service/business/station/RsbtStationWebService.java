package com.caict.bsm.project.api.web.service.business.station;

import com.alibaba.fastjson.JSONObject;
import com.caict.bsm.project.api.web.service.rule.ExcelWriter;
import com.caict.bsm.project.api.web.service.security.AuthWebService;
import com.caict.bsm.project.api.web.service.security.RegionWebService;
import com.caict.bsm.project.domain.business.service.applytable.RsbtApplyService;
import com.caict.bsm.project.domain.business.service.dataconver.AsyncRawBtsService;
import com.caict.bsm.project.domain.business.service.es.RsbtStationMainWebService;
import com.caict.bsm.project.domain.business.service.station.*;
import com.caict.bsm.project.domain.business.service.stationbak.RsbtStationBakService;
import com.caict.bsm.project.domain.business.service.transfer_in.ApplyJobInService;
import com.caict.bsm.project.domain.business.service.transfer_in.ApplyLinkService;
import com.caict.bsm.project.domain.security.service.RsbtOrgService;
import com.caict.bsm.project.system.data.util.PageHandle;
import com.caict.bsm.project.system.model.contrust.DBBoolConst;
import com.caict.bsm.project.system.model.contrust.transfer_in.ApplyLinkStateConst;
import com.caict.bsm.project.system.model.dto.business.common.EchartsResDTO;
import com.caict.bsm.project.system.model.dto.business.common.PointDTO;
import com.caict.bsm.project.system.model.dto.business.station.*;
import com.caict.bsm.project.system.model.dto.security.UsersDTO;
import com.caict.bsm.project.system.model.entity.boer.addorup.*;
import com.caict.bsm.project.system.model.entity.boer.del.StationCodeList;
import com.caict.bsm.project.system.model.entity.boer.del.StationDel;
import com.caict.bsm.project.system.model.entity.business.applytable.RsbtApply;
import com.caict.bsm.project.system.model.entity.business.dataconver.AsyncRawBts;
import com.caict.bsm.project.system.model.entity.business.station.*;
import com.caict.bsm.project.system.model.entity.business.stationbak.RsbtStationBak;
import com.caict.bsm.project.system.model.entity.business.transfer_in.ApplyJobIn;
import com.caict.bsm.project.system.model.entity.business.transfer_in.ApplyLink;
import com.caict.bsm.project.system.model.es.RsbtStationMainES;
import com.caict.bsm.project.system.utils.util.JSONResult;
import com.caict.bsm.project.system.utils.util.VerificationCode;
import com.github.pagehelper.PageInfo;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.elasticsearch.index.query.QueryBuilders;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2020/4/24
 */
@Service
public class RsbtStationWebService {

    private static final Logger LOG = LoggerFactory.getLogger(RsbtStationWebService.class);

    @Value("${caict.myFilePath}")
    private String STATION_DIR;
    @Value("${caict.myFilePath}")
    private String filePath;

    @Autowired
    private RsbtStationService rsbtStationService;

    @Autowired
    private RsbtStationTService rsbtStationTService;

    @Autowired
    private RsbtStationAppendixService rsbtStationAppendixService;

    @Autowired
    private RsbtErrorService rsbtErrorService;

    @Autowired
    private RsbtApplyService bsmApplyTableService;

    @Autowired
    private RsbtOrgService rsbtOrgService;

    @Autowired
    private AuthWebService authWebService;

    @Autowired
    private RegionWebService regionWebService;

    @Autowired
    private RsbtFreqService rsbtFreqService;

    @Autowired
    private RsbtFreqTService rsbtFreqTService;

    @Autowired
    private RsbtFreqAppendixService rsbtFreqAppendixService;

    @Autowired
    private RsbtEquService rsbtEquService;

    @Autowired
    private RsbtEquTService rsbtEquTService;

    @Autowired
    private RsbtEquAppendixService rsbtEquAppendixService;

    @Autowired
    private RsbtAntfeedService rsbtAntfeedService;

    @Autowired
    private RsbtAntfeedTService rsbtAntfeedTService;

    @Autowired
    private RsbtAntfeedAppendixService rsbtAntfeedAppendixService;

    @Autowired
    private AsyncRawBtsService asyncRawBtsService;

    @Autowired
    private RsbtStationBakService rsbtStationBakService;

    @Autowired
    private RsbtEafService rsbtEafService;

    @Autowired
    private ApplyJobInService applyJobInService;

    @Autowired
    private ApplyLinkService applyLinkService;

    @Autowired
    private RsbtStationMainWebService rsbtStationMainWebService;

    /**
     * 分页条件查询
     * */
    public JSONObject findAllByWhere(StationDTO stationDTO,String token){
        /*UsersDTO usersDTO = authWebService.findLoginUsersDTO(token);
        String userType = usersDTO.getType();
        if (userType.equals(UserRoleConst.USER_TYPE_MOBILE) || userType.equals(UserRoleConst.USER_TYPE_UNICOM) || userType.equals(UserRoleConst.USER_TYPE_TELECOM)){
            stationDTO.setUserGuid(usersDTO.getUserId());
        }*/
        UsersDTO usersDTO = authWebService.findLoginUsersDTO(token);
        if ("wuwei".equals(usersDTO.getType()) && !"25".equals(usersDTO.getRegionId())){
            //地市无委
            List<String> code = regionWebService.findCodeArea(usersDTO.getRegionId());
            stationDTO.setCodeList(code);
        }
        //计算半径
        if (stationDTO.getLatitude() != null && stationDTO.getLongitude() != null && stationDTO.getRadius() != null){
            double latitude = Double.valueOf(stationDTO.getLatitude());
            double longitude = Double.valueOf(stationDTO.getLongitude());
            //先计算查询点的经纬度范围
            double r = 6371;//地球半径千米
            double dis = stationDTO.getRadius();//0.5千米距离
            double dlng = 2*Math.asin(Math.sin(dis/(2*r))/Math.cos(latitude*Math.PI/180));
            dlng = dlng*180/Math.PI;//角度转为弧度
            double dlat = dis/r;
            dlat = dlat*180/Math.PI;
            stationDTO.setMinlat(latitude-dlat);
            stationDTO.setMaxlat(latitude+dlat);
            stationDTO.setMinlng(longitude -dlng);
            stationDTO.setMaxlng(longitude + dlng);
        }

        PageInfo<RsbtStationMainES> page = rsbtStationMainWebService.findPage(stationDTO, usersDTO);

//        PageInfo<StationDTO> stationDTOPageInfo = new PageHandle(stationDTO).buildPage(rsbtStationService.findAllByWhere(stationDTO,usersDTO));
        if(page.getList() != null){
            return JSONResult.getSuccessJson(page);
        }
        return JSONResult.getPageFailureJson(stationDTO.getPage(),stationDTO.getPage() + 1,stationDTO.getRows(),0,"操作失败");
    }

    /**
     * 查询fast30公里以内的基站
     * @param stationFastDTO
     * @return
     */
    public JSONObject findAllByFast(StationFastDTO stationFastDTO,String token){
        UsersDTO usersDTO = authWebService.findLoginUsersDTO(token);
        PageInfo<StationFastDTO> stationDTOPageInfo = new PageHandle(stationFastDTO).buildPage(rsbtStationService.findAllByFast(usersDTO));
        if(stationDTOPageInfo.getList() != null){
            return JSONResult.getSuccessJson(stationDTOPageInfo);
        }
        return JSONResult.getPageFailureJson(stationFastDTO.getPage(),stationFastDTO.getPage() + 1,stationFastDTO.getRows(),0,"操作失败");
    }

    /**
     * 查询详情DTO
     * */
    public JSONObject findOneByGuid(String guid){
        return JSONResult.getSuccessJson(rsbtStationService.findOneByGuid(guid),"查询成功");
    }

    /**
     * 统计个数
     * @return
     */
    public JSONObject countTotal(){
        Map<String, Integer> map = new HashMap<>();
        map.put("stationCount",rsbtStationService.queryStationCount());
        map.put("applyCount",bsmApplyTableService.queryApplyTableCount());
        map.put("orgCount", rsbtOrgService.selectOrgCount());
        return JSONResult.getSuccessJson(map);
    }


    public JSONObject countyCount(){
        return JSONResult.getSuccessJson(rsbtStationService.queryStationCountyNumber());
    }

    /**
     * 获取新增台站
     * @return
     */
    public JSONObject queryNewInsertStation(){
        List<StaInfoDTO> list = rsbtStationService.queryNewInsertStation();
        return JSONResult.getSuccessJson(list,"新增台站信息");
    }

    /**
     * 台站区域统计
     * @return
     */
    public JSONObject stationCountyStatistics(){
        List<EchartsResDTO> countyStat = rsbtStationService.queryStationCountyNumber();
        int staNum = rsbtStationService.queryStationCount();
        int newSta = rsbtStationService.queryNewStaByThisMonth();
        Map<String, Object> map = new HashMap<>();
        map.put("countyStat", countyStat);
        map.put("staNum", staNum);
        map.put("newSta", newSta);
        return JSONResult.getSuccessJson(map,"台站区域统计");
    }

    /**
     * 查询区域排名（根据基站数排名),按照基站数量降序
     * @return
     */
    public JSONObject selectRegionSortDTO(){
        List<RegionSortDTO> list = rsbtStationService.selectRegionSortDTO();
        return JSONResult.getSuccessJson(rsbtStationService.searchTwo(list),"查询成功");
    }

    /**
     * 查询台站分布
     * @return
     */
    public JSONObject getDistributionStatistics(){
        return JSONResult.getSuccessJson(rsbtStationService.getDistributionStatistics(),"查询成功");
    }

    /**
     * 查询运营商对应的基站数排名,按照基站数量降序
     * @return
     */
    public JSONObject selectOrgStationNum(){
        List<OrgStationNumSortDTO> list = rsbtStationService.selectOrgStationNum();
        return JSONResult.getSuccessJson(rsbtStationService.search(list),"查询成功");
    }


    /**
     * 查询经纬度范围内的台站
     * @param pointList  范围点
     * @return  台站集合
     */
    public JSONObject queryStationByRange(List<PointDTO> pointList){

        double lowLng = Double.MAX_VALUE, lowLat = Double.MAX_VALUE,
                highLng = Double.MIN_VALUE, highLat = Double.MIN_VALUE;

        for(PointDTO point : pointList){
            if(point.getLongitude() < lowLng) lowLng = point.getLongitude();

            if(point.getLongitude() > highLng) highLng = point.getLongitude();

            if(point.getLatitude() < lowLat) lowLat = point.getLatitude();

            if(point.getLatitude() > highLat) highLat = point.getLatitude();
        }
        List<StationDTO> list = rsbtStationService.queryStationByRange(lowLng,lowLat,highLng,highLat);
        return JSONResult.getSuccessJson(list,"经纬度范围内的台站");
    }

    /**
     * 区域预警查询
     */
    public JSONObject queryAreaRange(StationDTO stationDTO,String token){
        UsersDTO usersDTO = authWebService.findLoginUsersDTO(token);
        PageInfo<StationDTO> stationDTOPageInfo = new PageHandle(stationDTO).buildPage(rsbtStationService.queryAreaRange(stationDTO,usersDTO));
        if(stationDTOPageInfo.getList() != null){
            return JSONResult.getSuccessJson(stationDTOPageInfo);
        }
        return JSONResult.getPageFailureJson(stationDTO.getPage(),stationDTO.getPage() + 1,stationDTO.getRows(),0,"操作失败");
    }

    /*public JSONObject queryAreaRange(StationDTO stationDTO,String token){
        UsersDTO usersDTO = authWebService.findLoginUsersDTO(token);
        List<StationDTO> stationDTOS = rsbtStationService.queryAreaRange(stationDTO, usersDTO);
        stationDTOS.parallelStream().forEach(stationDTO1 -> {
            String latitude = stationDTO1.getLatitude();
            String longitude = stationDTO1.getLongitude();
            Double.valueOf(latitude);
            Double.valueOf(longitude);
            //GPSTransform.isInPolygon()
        });
        return null;
    }*/




    /**
     * 保存list到RsbtAll
     * @param
     * @return
     */

   /* public JSONObject importExcellAll(MultipartFile file) throws IOException {
        String filename = file.getOriginalFilename();
        List<RsbtAll> list=new ArrayList<RsbtAll>();
        InputStream inputStream = file.getInputStream();
        BufferedReader reader=new BufferedReader(new InputStreamReader(inputStream,"GBK"));
        String line=null;
        int num=0;
        String title=reader.readLine();
        while ((line=reader.readLine())!=null){
            num++;
            //line.replace("\t","");
            String item[] = line.split(",",-1);//csv以逗号为分隔符，这里以逗号分开
            RsbtAll rsbtAll = setRsbtAllObject(item);
            //校验通信网络名称（直放站）
            if (rsbtAll.getComNetName().contains("直放站")){
                //continue;
                //插入错误表
                RsbtError rsbtError = new RsbtError();
                String id = VerificationCode.myUUID();
                rsbtError.setId(id);
                rsbtError.setFileName(filename);
                rsbtError.setErrorMessage("直放站不符合要求");
                rsbtError.setStCCode(rsbtAll.getStCCode());
                rsbtError.setSectionCode(rsbtAll.getSectionCode());
                rsbtErrorService.svaeOne(rsbtError);
                continue;
            }
            //校验行政区
            MyBtsDataUtil.checkoutCounty(rsbtAll.getRegionName(), rsbtAll);
            //校验技术体制
            MyBtsDataUtil.checkoutNetTs(rsbtAll.getNetTs(),rsbtAll);
            list.add(rsbtAll);
        }
        //保存
        int i = rsbtAllService.saveList(list);
        if(i<0) return JSONResult.getFailureJson("导入失败");
        return JSONResult.getSuccessJson("导入成功");
    }

    public RsbtAll setRsbtAllObject(String[] item){
        RsbtAll rsbtAll=new RsbtAll();
        String id= VerificationCode.myUUID();
        rsbtAll.setId(id);
        String gOrgName=dealWithString(item[0]);
        rsbtAll.setgOrgName(gOrgName);

        String comNetName=dealWithString(item[4]);
        rsbtAll.setComNetName(comNetName);

        String applyNumber=dealWithString(item[6]);
        rsbtAll.setApplyNumber(applyNumber);

        String statName=dealWithString(item[9]);
        rsbtAll.setStatName(statName);

        String statAddr=dealWithString(item[10]);
        rsbtAll.setStatAddr(statAddr);
        //技术制式
        String netTs=dealWithString(item[12]);
        String subnetTs = netTs.substring(0, 8);
        rsbtAll.setNetTs(subnetTs);

        String regionName=dealWithString(item[9]+item[10]);
        rsbtAll.setRegionName(regionName);
        //经度
        Double lg= DuFenMiaoToLonLat.duFenMiaoToLongLat(item[18]);
        rsbtAll.setStatLg(lg);
        //纬度
        Double lat= DuFenMiaoToLonLat.duFenMiaoToLongLat(item[19]);
        rsbtAll.setStatLa(lat);

        Double statAt = dealWithDouble(item[20]);
        rsbtAll.setStatAt(statAt);


        String stCCode=dealWithString(item[64]);
        rsbtAll.setStCCode(stCCode);

        String freqEfe=dealWithString(item[85]);
        rsbtAll.setFreqEfe(freqEfe);

        String freqEfb=dealWithString(item[86]);
        rsbtAll.setFreqEfb(freqEfb);

        String freqRfb=dealWithString(item[88]);
        rsbtAll.setFreqRfb(freqRfb);

        String freqRfe=dealWithString(item[89]);
        rsbtAll.setFreqRfe(freqRfe);

        String sectionCode=dealWithString(item[106]);
        rsbtAll.setSectionCode(sectionCode);

        String equModel=dealWithString(item[117]);
        rsbtAll.setEquModel(equModel);

        String equAuth=dealWithString(item[118]);
        rsbtAll.setEquAuth(equAuth);

        String equMenu=dealWithString(item[119]);
        rsbtAll.setEquMenu(equMenu);

        String maxPower=dealWithString(item[122]);
        rsbtAll.setMaxPower(maxPower);

        //极化方式
        String antPole=dealWithString(item[165]);
        GetEnumType getEnumType=new GetEnumType();
        Map<String, String> antPoleName = getEnumType.getAntPoleName();
        String s = antPoleName.get(antPole);
        rsbtAll.setAntPole(s);
        //天线类型
        String antType=dealWithString(item[168]);
        Map<String, String> antTypeName = getEnumType.getAntTypeName();
        String s1 = antTypeName.get(antType);
        rsbtAll.setAntType(s1);

        String antMenu=dealWithString(item[170]);
        rsbtAll.setAntMenu(antMenu);

        Double antHight=dealWithDouble(item[171]);
        rsbtAll.setAntHight(antHight);

        Double antGain=dealWithDouble(item[172]);
        rsbtAll.setAntGain(antGain);

        Double atAngle=dealWithDouble(item[175]);
        rsbtAll.setAntAngle(atAngle);

        Double feedLose=dealWithDouble(item[180]);
        rsbtAll.setFeedLose(feedLose);

        String sectionName=dealWithString(item[191]);
        rsbtAll.setSectionName(sectionName);

        return rsbtAll;

    }


    public Double dealWithDouble(String s){
        String trim = s.trim();
        String r = trim.replace("\t", "");
        String replace = r.replace("\"", "");
        if (replace.isEmpty()){
            Double d=0.0;
            return d;
        }
        Double d=Double.parseDouble(replace);
        return d;
    }

    public String dealWithString(String s){
        String trim = s.trim();
        String r = trim.replace("\t", "");
        String replace = r.replace("\"", "");
        if (s.isEmpty()){
            return "null";
        }
        return replace;
    }



    public JSONObject exportExcelForRsbtError(HttpServletRequest request, HttpServletResponse response) throws IOException {
        Workbook workbook = null;
        OutputStream out = null;
        String fileName=null;
        Date date = new Date();
        List<RsbtError> list=findAll();
        try {
            // 生成Excel工作簿对象并写入数据
            workbook = ExcelWriterForRsbtError.exportData(list);
            if (workbook!=null){
                String excelName = "导出错误信息";
                fileName =  excelName +date.getTime()+ ".xlsx";
                out= new FileOutputStream(filePath+fileName);
                workbook.write(out);
                out.flush();
            }
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (null != workbook) {
                workbook.close();
            }
            if (null != out) {
                out.close();
            }
        }

        return JSONResult.getSuccessJson(fileName,"导出成功");
    }

    //查询所有错误信息
    public List<RsbtError> findAll(){
        return rsbtErrorService.findAll();
    }*/

   public int updateAppCode(String oldAppCode,String newAppCode){
       return rsbtStationService.updateAppCode(oldAppCode,newAppCode);
   }

    public JSONObject stationDelBE(StationDel stationDel){
        List<StationCodeList> stationCodeList = stationDel.getStationCodeList();
        List<RsbtStationBak> stationBaks = new ArrayList<>();
        for (StationCodeList codeList:stationCodeList){
            //查询基站编号下的基站
            RsbtStation station = rsbtStationService.findByStCode(codeList.getStCCode());
            if (station!=null){
                rsbtFreqService.updateByStationGuid(station.getGuid(),codeList.getFtFreqCCode());

                rsbtEquService.updateByStationGuid(station.getGuid(),codeList.getFtFreqCCode());

                rsbtAntfeedService.updateByStationGuid(station.getGuid(),codeList.getFtFreqCCode());

                asyncRawBtsService.deleteByBtsIdCellCode(codeList.getStCCode(),codeList.getFtFreqCCode());

                RsbtStationBak rsbtStationBak = new RsbtStationBak();
                BeanUtils.copyProperties(station,rsbtStationBak);
                rsbtStationBak.setDataType("3");

                stationBaks.add(rsbtStationBak);

                //判断一个基站下扇区是否全删除
                if (rsbtStationService.judgeDel(station.getGuid())==0){
                    RsbtStationAppendix stationAppendix = new RsbtStationAppendix();
                    stationAppendix.setGuid(station.getGuid());
                    stationAppendix.setIsDeleted(DBBoolConst.TURE);

                    rsbtStationAppendixService.update(stationAppendix);
                }
            }

            rsbtStationBakService.insertBatch(stationBaks);
        }
        return JSONResult.getSuccessJson("注销成功！");
    }

    public JSONObject stationAddorUpBE(StatAddOrUp statAddOrUp){
        String stationGuid = "";
        //新增
        //基站表
        RsbtStation rsbtStation = new RsbtStation();
        //基站副表
        RsbtStationT rsbtStationT = new RsbtStationT();
        RsbtStationBak rsbtStationBak = new RsbtStationBak();
        RsbtStationAppendix rsbtStationAppendix = new RsbtStationAppendix();
        if ("1".equals(statAddOrUp.getIsNew())){
            stationGuid = statAddOrUp.getStationGuid();
        }else {
            //获得需要变更的基站信息
            RsbtStation station = rsbtStationService.findByStCode(statAddOrUp.getStCCode());
            stationGuid = station.getGuid();
        }

        rsbtStation.setGuid(stationGuid);
        rsbtStationT.setGuid(stationGuid);
        rsbtStationAppendix.setGuid(stationGuid);
        rsbtStationBak.setGuid(stationGuid);

        rsbtStation.setAppCode(statAddOrUp.getAppCode());
        rsbtStation.setStatName(statAddOrUp.getStatName());
        rsbtStation.setStatTdi(statAddOrUp.getStatTdi());
        rsbtStation.setStatAddr(statAddOrUp.getStatAddr());
        rsbtStation.setStatLg(Double.valueOf(statAddOrUp.getStatLg()));
        rsbtStation.setStatLa(Double.valueOf(statAddOrUp.getStatLa()));
        rsbtStation.setStatAt(Double.valueOf(statAddOrUp.getStatAt()));
        rsbtStation.setStatAppType(statAddOrUp.getStatAppType());

        rsbtStationT.setStCCode(statAddOrUp.getStCCode());
        rsbtStationT.setStServR(Double.valueOf(statAddOrUp.getStServR()));

        rsbtStationAppendix.setIsDeleted(0L);
        rsbtStationAppendix.setIsSync("1");

        BeanUtils.copyProperties(rsbtStationBak,rsbtStation);
        rsbtStationBak.setDataType(statAddOrUp.getIsNew());
        rsbtStationBak.setIsValid("1");
        rsbtStationBak.setIsSync("1");

        addOrUpdateFreq(statAddOrUp,stationGuid);
        addOrUpdateEqu(statAddOrUp,stationGuid);
        addOrUpdateAntfeed(statAddOrUp,stationGuid);
        addOrUpdateAsync(statAddOrUp,stationGuid);
        addOrUpdateApply(statAddOrUp,stationGuid);

        if ("1".equals(statAddOrUp.getIsNew())){
            rsbtStationService.insert(rsbtStation);
            rsbtStationTService.insert(rsbtStationT);
            rsbtStationAppendixService.insert(rsbtStationAppendix);
            rsbtStationBakService.insert(rsbtStationBak);
        }else {
            rsbtStationService.update(rsbtStation);
            rsbtStationTService.update(rsbtStationT);
            rsbtStationAppendixService.update(rsbtStationAppendix);
            rsbtStationBakService.update(rsbtStationBak);
        }


        return JSONResult.getSuccessJson("操作成功！");
    }

    public void addOrUpdateApply(StatAddOrUp statAddOrUp, String stationGuid) {
        RsbtApply rsbtApply = bsmApplyTableService.findOneByCode(statAddOrUp.getAppCode());
        if (rsbtApply==null){
            RsbtApply apply = new RsbtApply();
            apply.setGuid(statAddOrUp.getAppGuid());
            apply.setAppCode(statAddOrUp.getAppCode());
            apply.setAppType("T");
            apply.setAppSubType("1".equals(statAddOrUp.getIsNew())?"0":"1");
            apply.setAppObjectType("1");
            apply.setAppFtlb(new Date());
            apply.setAppFtle(new Date());

            ApplyJobIn applyJobIn = new ApplyJobIn();
            applyJobIn.setGuid(VerificationCode.myUUID());
            applyJobIn.setRegionCode(statAddOrUp.getOrgAreaCode());
            applyJobIn.setDataType(statAddOrUp.getIsNew());
            applyJobIn.setUserGuid("boer");
            applyJobIn.setIsCompare(ApplyLinkStateConst.APPLY_SYNC_SUCCESS);

            ApplyLink applyLink = new ApplyLink();
            applyLink.setGuid(VerificationCode.myUUID());
            applyLink.setAppCodeIn(statAddOrUp.getAppCode());
            applyLink.setApplyStatus("1");
            applyLink.setSyncDate(new Date());
            applyLink.setSyncStatus("1");

            bsmApplyTableService.insert(apply);
            applyJobInService.insert(applyJobIn);
            applyLinkService.insert(applyLink);
        }
    }

    public void addOrUpdateFreq(StatAddOrUp statAddOrUp,String stationGuid){
        List<Sections> sections = statAddOrUp.getSection();
        List<RsbtFreq> freqList = new ArrayList<>();
        List<RsbtFreqAppendix> freqAppendixList = new ArrayList<>();
        List<RsbtFreqT> freqTList = new ArrayList<>();
        for (Sections section:sections){
            RsbtFreq rsbtFreq = new RsbtFreq();
            RsbtFreqT rsbtFreqT = new RsbtFreqT();
            RsbtFreqAppendix rsbtFreqAppendix = new RsbtFreqAppendix();
            if ("1".equals(statAddOrUp.getIsNew())){
                String freqGuid = VerificationCode.myUUID();
                rsbtFreq.setGuid(freqGuid);
                rsbtFreqT.setGuid(freqGuid);
                rsbtFreqAppendix.setGuid(freqGuid);
            }else {
                RsbtFreq freq = rsbtFreqService.findOneByStationCell(stationGuid, section.getFreqDatas().getFtFreqCcode());
                rsbtFreq.setGuid(freq.getGuid());
                rsbtFreqT.setGuid(freq.getGuid());
                rsbtFreqAppendix.setGuid(freq.getGuid());
            }

            //频率
            FreqDatas freqDatas = section.getFreqDatas();
            rsbtFreq.setStationGuid(statAddOrUp.getStationGuid());
            rsbtFreq.setFreqType(freqDatas.getFreqType());
            rsbtFreq.setFreqEfb(freqDatas.getFreqEfb());
            rsbtFreq.setFreqEfe(freqDatas.getFreqEfe());
            rsbtFreq.setFreqRfb(freqDatas.getFreqRfb());
            rsbtFreq.setFreqRfe(freqDatas.getFreqRfe());

            rsbtFreqT.setFtFreqCcode(freqDatas.getFtFreqCcode());
            rsbtFreqT.setFtFreqCsgn(freqDatas.getFtFreqCsgn());

            rsbtFreqAppendix.setIsDeleted(0L);

            freqList.add(rsbtFreq);
            freqTList.add(rsbtFreqT);
            freqAppendixList.add(rsbtFreqAppendix);
        }
        if ("1".equals(statAddOrUp.getIsNew())){
            rsbtFreqService.insertBatch(freqList);
            rsbtFreqTService.insertBatch(freqTList);
            rsbtFreqAppendixService.insertBatch(freqAppendixList);
        }else {
            rsbtFreqService.updateBatch(freqList);
            rsbtFreqTService.updateBatch(freqTList);
            rsbtFreqAppendixService.updateBatch(freqAppendixList);
        }

    }

    public void addOrUpdateEqu(StatAddOrUp statAddOrUp,String stationGuid){
        List<Sections> sections = statAddOrUp.getSection();
        List<RsbtEqu> equList = new ArrayList<>();
        List<RsbtEquT> equTList = new ArrayList<>();
        List<RsbtEquAppendix> equAppendixList = new ArrayList<>();
        for (Sections section:sections){
            RsbtEqu rsbtEqu = new RsbtEqu();
            RsbtEquT rsbtEquT = new RsbtEquT();
            RsbtEquAppendix rsbtEquAppendix = new RsbtEquAppendix();
            if ("1".equals(statAddOrUp.getIsNew())){
                String equGuid = VerificationCode.myUUID();
                rsbtEqu.setGuid(equGuid);
                rsbtEquT.setGuid(equGuid);
                rsbtEquAppendix.setGuid(equGuid);
            }else {
                RsbtEqu equ = rsbtEquService.findOneByStationSection(stationGuid, section.getEquDatas().getEtEquCcode());
                rsbtEqu.setGuid(equ.getGuid());
                rsbtEquT.setGuid(equ.getGuid());
                rsbtEquAppendix.setGuid(equ.getGuid());
            }

            //设备
            EquDatas equDatas = section.getEquDatas();
            rsbtEqu.setStationGuid(statAddOrUp.getStationGuid());
            rsbtEqu.setEquPf(equDatas.getEquPf());
            rsbtEqu.setEquModel(equDatas.getEquModel());
            rsbtEqu.setEquAuth(equDatas.getEquAuth());
            rsbtEqu.setEquMenu(equDatas.getEquMenu());
            rsbtEqu.setEquPow(equDatas.getEquPow());

            rsbtEquT.setEtEquCode(equDatas.getEtEquCcode());
            rsbtEquT.setEtPowU(equDatas.getEtPowU());
            rsbtEquT.setEtEquSum(Integer.parseInt(equDatas.getEtEquSum()));
            rsbtEquT.setEtEquUpow(equDatas.getEtEquUpow());
            rsbtEquT.setEtEquDpow(equDatas.getEtEquDpow());

            rsbtEquAppendix.setIsDeleted(0L);

            equList.add(rsbtEqu);
            equTList.add(rsbtEquT);
            equAppendixList.add(rsbtEquAppendix);
        }

        if ("1".equals(statAddOrUp.getIsNew())){
            rsbtEquService.insertBatch(equList);
            rsbtEquTService.insertBatch(equTList);
            rsbtEquAppendixService.insertBatch(equAppendixList);
        }else {
            rsbtEquService.updateBatch(equList);
            rsbtEquTService.updateBatch(equTList);
            rsbtEquAppendixService.updateBatch(equAppendixList);
        }

    }

    public void addOrUpdateAntfeed(StatAddOrUp statAddOrUp,String stationGuid){
        List<Sections> sections = statAddOrUp.getSection();
        List<RsbtAntfeed> antfeedList = new ArrayList<>();
        List<RsbtAntfeedT> antfeedTList = new ArrayList<>();
        List<RsbtAntfeedAppendix> antfeedAppendixList = new ArrayList<>();
        for (Sections section:sections){
            RsbtAntfeed rsbtAntfeed = new RsbtAntfeed();
            RsbtAntfeedT rsbtAntfeedT = new RsbtAntfeedT();
            RsbtAntfeedAppendix rsbtAntfeedAppendix = new RsbtAntfeedAppendix();
            if ("1".equals(statAddOrUp.getIsNew())){
                String antGuid = VerificationCode.myUUID();
                rsbtAntfeed.setGuid(antGuid);
                rsbtAntfeedT.setGuid(antGuid);
                rsbtAntfeedAppendix.setGuid(antGuid);
            }else {
                RsbtAntfeed antfeed = rsbtAntfeedService.findOneByStationCell(stationGuid, section.getFreqDatas().getFtFreqCcode());
                rsbtAntfeed.setGuid(antfeed.getGuid());
                rsbtAntfeedT.setGuid(antfeed.getGuid());
                rsbtAntfeedAppendix.setGuid(antfeed.getGuid());
            }

            //天线
            AntDatas antDatas = section.getAntDatas();

            rsbtAntfeed.setStationGuid(statAddOrUp.getStationGuid());
            rsbtAntfeed.setAntAngle(antDatas.getAntAngle());
            rsbtAntfeed.setAntType(antDatas.getAntType());
            rsbtAntfeed.setAntModel(antDatas.getAntModel());
            rsbtAntfeed.setAntMenu(antDatas.getAntManu());
            rsbtAntfeed.setAntHight(antDatas.getAntHight());
            rsbtAntfeed.setAntRgain(antDatas.getAntRgain());
            rsbtAntfeed.setAntEgain(antDatas.getAntEgain());
            rsbtAntfeed.setFeedLose(Double.valueOf(antDatas.getFeedLose()));
            rsbtAntfeed.setAntPole(antDatas.getAntPole());

            rsbtAntfeedT.setAtCcode(antDatas.getAtCcode());
            rsbtAntfeedT.setAtCsgn(antDatas.getAtCsgn());
            rsbtAntfeedT.setAtRang(antDatas.getAtRang());
            rsbtAntfeedT.setAtEang(antDatas.getAtEang());

            rsbtAntfeedAppendix.setIsDeleted(0L);

            antfeedList.add(rsbtAntfeed);
            antfeedTList.add(rsbtAntfeedT);
            antfeedAppendixList.add(rsbtAntfeedAppendix);
        }

        if ("1".equals(statAddOrUp.getIsNew())){
            rsbtAntfeedService.insertBatch(antfeedList);
            rsbtAntfeedTService.insertBatch(antfeedTList);
            rsbtAntfeedAppendixService.insertBatch(antfeedAppendixList);
        }else {
            rsbtAntfeedService.updateBatch(antfeedList);
            rsbtAntfeedTService.updateBatch(antfeedTList);
            rsbtAntfeedAppendixService.updateBatch(antfeedAppendixList);
        }
    }

    public void addOrUpdateAsync(StatAddOrUp statAddOrUp,String stationGuid){
        //总表
        List<AsyncRawBts> asyncRawBtsList = new ArrayList<>();
        List<Sections> sections = statAddOrUp.getSection();
        for (Sections section:sections){
            FreqDatas freqDatas = section.getFreqDatas();
            EquDatas equDatas = section.getEquDatas();
            AntDatas antDatas = section.getAntDatas();
            //总表
            AsyncRawBts asyncRawBts = new AsyncRawBts();
            if ("1".equals(statAddOrUp.getIsNew())){
                asyncRawBts.setGuid(VerificationCode.myUUID());
            }else {
                AsyncRawBts async = asyncRawBtsService.selectOneByBtsIdCellId(statAddOrUp.getStCCode(), freqDatas.getFtFreqCcode(), statAddOrUp.getNetTs());
                asyncRawBts.setGuid(async.getGuid());
            }

            asyncRawBts.setCellId(freqDatas.getFtFreqCcode());
            asyncRawBts.setBtsName(statAddOrUp.getStatName());
            asyncRawBts.setBtsId(statAddOrUp.getStCCode());
            asyncRawBts.setTechType(statAddOrUp.getNetTs());
            asyncRawBts.setLocation(statAddOrUp.getStatAddr());
            asyncRawBts.setLatitude(statAddOrUp.getStatLa());
            asyncRawBts.setLongitude(statAddOrUp.getStatLg());
            asyncRawBts.setAccStartFreq(String.valueOf(freqDatas.getFreqRfb()));
            asyncRawBts.setAccEndFreq(String.valueOf(freqDatas.getFreqRfe()));
            asyncRawBts.setSendStartFreq(String.valueOf(freqDatas.getFreqEfb()));
            asyncRawBts.setSendEndFreq(String.valueOf(freqDatas.getFreqEfe()));
            asyncRawBts.setMaxEmissivePower(String.valueOf(equDatas.getEquPow()));
            asyncRawBts.setHeight(statAddOrUp.getStatAt());
            asyncRawBts.setVendorName(equDatas.getEquMenu());
            asyncRawBts.setDeviceModel(equDatas.getEquModel());
            asyncRawBts.setModelCode(equDatas.getEquAuth());
            asyncRawBts.setAntennaModel(antDatas.getAntModel());
            asyncRawBts.setAntennaFactory(antDatas.getAntManu());
            asyncRawBts.setPolarizationMode(antDatas.getAntPole());
            asyncRawBts.setAntennaAzimuth(String.valueOf(antDatas.getAntAngle()));
            asyncRawBts.setFeederLoss(String.valueOf(antDatas.getFeedLose()));
            asyncRawBts.setAntennaGain(String.valueOf(antDatas.getAntRgain()));
            asyncRawBts.setAltitude(statAddOrUp.getStatAt());

            asyncRawBtsList.add(asyncRawBts);
        }

        if ("1".equals(statAddOrUp.getIsNew())){
            asyncRawBtsService.insertBatch(asyncRawBtsList);
        }else {
            asyncRawBtsService.updateBatch(asyncRawBtsList);
        }

    }

    public JSONObject downloadStation(StationDTO stationDTO,String token){
       try {
//           JSONObject stationListJson = findAllByWhere(stationDTO, token);
//           List<StationDTO> data = (List<StationDTO>) stationListJson.get("data");
           UsersDTO usersDTO = authWebService.findLoginUsersDTO(token);
           if ("wuwei".equals(usersDTO.getType())){
               stationDTO.setIsSync("1");
           }
           //计算半径
           if (stationDTO.getLatitude() != null && stationDTO.getLongitude() != null && stationDTO.getRadius() != null){
               double latitude = Double.valueOf(stationDTO.getLatitude());
               double longitude = Double.valueOf(stationDTO.getLongitude());
               //先计算查询点的经纬度范围
               double r = 6371;//地球半径千米
               double dis = stationDTO.getRadius();//0.5千米距离
               double dlng = 2*Math.asin(Math.sin(dis/(2*r))/Math.cos(latitude*Math.PI/180));
               dlng = dlng*180/Math.PI;//角度转为弧度
               double dlat = dis/r;
               dlat = dlat*180/Math.PI;
               stationDTO.setMinlat(latitude-dlat);
               stationDTO.setMaxlat(latitude+dlat);
               stationDTO.setMinlng(longitude -dlng);
               stationDTO.setMaxlng(longitude + dlng);
           }
           List<StationDTO> data = rsbtStationService.findAllByWhere(stationDTO, usersDTO);

           if (data.size()!=0){
               List<List<String>> rows = Collections.synchronizedList(new ArrayList<>());

               //拼装表头
               List<String> headLine = new ArrayList<>();
               headLine.add("基站名称");
               headLine.add("基站编号");
               headLine.add("技术体制");
               headLine.add("代数");
               headLine.add("地区");
               headLine.add("经度");
               headLine.add("纬度");
               headLine.add("地址");
               //组装行数据
               data.parallelStream().forEach(t->{
                   List<String> row = new ArrayList<>();
                   row.add(t.getStationName());
                   row.add(t.getStationCode());
                   row.add(t.getNetType());
                   row.add(t.getGenNum()+"G");
                   row.add(t.getCounty());
                   row.add(t.getLongitude());
                   row.add(t.getLatitude());
                   row.add(t.getLocation());

                   rows.add(row);
               });

               return exportExcelForRsbtError(rows,headLine, "导出基站信息_"+new Date().getTime()+ ".xlsx");
           }


       }catch (IOException e){
           e.printStackTrace();
       }
       return null;
    }

    public JSONObject exportExcelForRsbtError(List<List<String>> data,List<String> head,String fileName) throws IOException {
        SXSSFWorkbook workbook = null;
        OutputStream out = null;
        try {
            // 生成Excel工作簿对象并写入数据
            workbook = exportData(head,data);
            if (workbook!=null){
//                fileName = "导出基站信息_"+new Date().getTime()+ ".xlsx";
                out= new FileOutputStream(filePath+fileName);
                workbook.write(out);
                out.close();
                out.flush();
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (null != workbook) {
                workbook.dispose();
            }
            if (null != out) {
                out.close();
            }
        }

        return JSONResult.getSuccessJson(fileName,"导出成功");
    }

    public static SXSSFWorkbook exportData(List<String> head,List<List<String>> data){
        // 生成xlsx的Excel
        SXSSFWorkbook workbook = new SXSSFWorkbook();
        workbook.setCompressTempFiles(false);
        // 如需生成xls的Excel，请使用下面的工作簿对象，注意后续输出时文件后缀名也需更改为xls
        //Workbook workbook = new HSSFWorkbook();

        // 生成Sheet表，写入第一行的列头
        Sheet sheet = buildDataSheet(workbook,head);
        //构建每行的数据内容
        int rowNum = 1;
        for (Iterator<List<String>> it = data.iterator(); it.hasNext(); ) {
            List<String> list = it.next();
            if (list == null) {
                continue;
            }
            //输出行数据
            Row row = sheet.createRow(rowNum++);
            convertDataToRow(list, row);
        }
        return workbook;
    }

    private static Sheet buildDataSheet(Workbook workbook,List<String> head) {
        Sheet sheet = workbook.createSheet();
        // 设置列头宽度
        for (int i=0; i<head.size(); i++) {
            sheet.setColumnWidth(i, 4000);
        }
        // 设置默认行高
        sheet.setDefaultRowHeight((short) 400);
        // 构建头单元格样式
        CellStyle cellStyle = ExcelWriter.buildHeadCellStyle(sheet.getWorkbook());
        // 写入第一行各列的数据
        Row headline = sheet.createRow(0);
        for (int i = 0; i < head.size(); i++) {
            Cell cell = headline.createCell(i);
            cell.setCellValue(head.get(i));
            cell.setCellStyle(cellStyle);
        }
        return sheet;
    }

    private static void convertDataToRow(List<String> data, Row row){
        for (int i = 0; i < data.size(); i++) {
            Cell cell;
            cell = row.createCell(i);
            cell.setCellValue(data.get(i));
        }
    }

    /**
     * 下载fast30公里以内的基站
     * @param stationFastDTO
     * @return
     */
    public JSONObject fastStationDownload(StationFastDTO stationFastDTO,String token){
        try {
            UsersDTO usersDTO = authWebService.findLoginUsersDTO(token);
            List<StationFastDTO> fastStations = rsbtStationService.findAllByFast(usersDTO);

            if (fastStations.size()!=0){
                //拼装表头
                List<String> headLine = new ArrayList<>();
                headLine.add("基站名称");
                headLine.add("基站编号");
                headLine.add("技术体制");
                headLine.add("代数");
                headLine.add("地区");
                headLine.add("经度");
                headLine.add("纬度");
                headLine.add("地址");
                headLine.add("距fast距离(km)");

                List<List<String>> rows = Collections.synchronizedList(new ArrayList<>());
                fastStations.parallelStream().forEach(t->{
                    List<String> list = new ArrayList<>();
                    list.add(t.getStationName());
                    list.add(t.getStationCode());
                    list.add(t.getNetType());
                    list.add(t.getGenNum()+"G");
                    list.add(t.getCounty());
                    list.add(t.getLongitude());
                    list.add(t.getLatitude());
                    list.add(t.getLocation());
                    list.add(t.getDistance());

                    rows.add(list);
                });
                if (fastStations.size()!=0){

                }
                String fileName = "导出fast区域内基站信息_" + new Date().getTime() + ".xlsx";
                return exportExcelForRsbtError(rows,headLine, fileName);

//                return JSONResult.getSuccessJson(fileName,"文件生成成功！");
            }

        }catch (Exception e){
            e.printStackTrace();
        }

        return JSONResult.getFailureJson("操作失败");
    }

}

