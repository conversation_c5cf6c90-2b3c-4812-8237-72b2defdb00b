package com.caict.bsm.project.api.web.service.business.transfer;

import com.alibaba.fastjson.JSONObject;

import com.caict.bsm.project.domain.business.service.transfer.ApprovalRawBtsService;
import com.caict.bsm.project.system.utils.util.JSONResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ApprovalRawBtsWebService {

    @Autowired
    private ApprovalRawBtsService approvalRawBtsService;

    //根据fileId删除所有数据
    public JSONObject deleteAllByFileId(String fileId){
        if (approvalRawBtsService.deleteAllByFileId(fileId) > 0){
            return JSONResult.getSuccessJson("删除成功");
        }
        return JSONResult.getFailureJson("删除失败，无数据");
    }

    /**
     * 根据appGuid查询总数
     * */
    public int selectCountAllByAppGuid(String appGuid){
        return approvalRawBtsService.selectCountAllByAppGuid(appGuid);
    }

    /**
     * 3.0自动生成待审核数据
     * */
    public int insertBySchedule(String jobGuid,String userGuid,String dataType,String genNum,String appGuid,String regionCode,String techType){
        return approvalRawBtsService.insertBySchedule(jobGuid,userGuid,dataType,genNum,appGuid,regionCode,techType);
    }
}
