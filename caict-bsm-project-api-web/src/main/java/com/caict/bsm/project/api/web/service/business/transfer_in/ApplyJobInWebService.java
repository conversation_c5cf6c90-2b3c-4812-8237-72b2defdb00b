package com.caict.bsm.project.api.web.service.business.transfer_in;

import com.alibaba.fastjson.JSONObject;
import com.caict.bsm.project.domain.business.service.stationbak.RsbtStationBakService;
import com.caict.bsm.project.domain.business.service.transfer_in.ApplyJobInService;
import com.caict.bsm.project.system.data.util.PageHandle;
import com.caict.bsm.project.system.model.dto.business.station.StationBakDTO;
import com.caict.bsm.project.system.model.dto.business.transfer.ApprovalScheduleLogDTO;
import com.caict.bsm.project.system.model.dto.business.transfer.ApprovalTransportJobDTO;
import com.caict.bsm.project.system.model.dto.business.transfer_in.ApplyJobInDTO;
import com.caict.bsm.project.system.model.entity.business.transfer_in.ApplyJobIn;
import com.caict.bsm.project.system.utils.util.JSONResult;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class ApplyJobInWebService {

    @Value("${caict.myFilePath}")
    private String BASIC_DIR;
    @Autowired
    private ApplyJobInService applyJobInService;
    @Autowired
    private RsbtStationBakService stationBakService;

    public int updateByAppCode(String appGuid,String appCode,String status){
        return applyJobInService.updateByAppCode(appGuid,appCode,status);
    }

    public JSONObject findDetailByPage(ApprovalTransportJobDTO approvalTransportJobDTO){
        PageInfo<ApplyJobInDTO> applyJobInDTOPageInfo = new PageHandle(approvalTransportJobDTO).buildPage(applyJobInService.findDetailByPage(approvalTransportJobDTO));
        if (applyJobInDTOPageInfo.getList()!=null){
            return JSONResult.getSuccessJson(applyJobInDTOPageInfo);
        }
        return JSONResult.getPageFailureJson(approvalTransportJobDTO.getPage(),approvalTransportJobDTO.getPage() + 1,approvalTransportJobDTO.getRows(),0,"操作失败");
    }

    public ApplyJobInDTO selectByAppCode(String appCode){
        return applyJobInService.selectByAppCode(appCode);
    }

    public int updateAppCode(String oldAppCode,String newAppCode){
        return applyJobInService.updateAppCode(oldAppCode,newAppCode);
    }

    public int insertOne(ApplyJobIn applyJobIn){
        return applyJobInService.insert(applyJobIn);
    }

    public List<ApplyJobInDTO> findByAppGuid(String appGuid){
        return applyJobInService.findByAppGuid(appGuid);
    }

    public JSONObject downloadGiveUpData(String appGuid){
        List<ApprovalScheduleLogDTO> approvalScheduleLogDTOs = stationBakService.findGiveUp(appGuid);
        BufferedWriter csvWrite = null;
        String fileName = new Date().getTime()+".csv";
        try
        {
            //定义文件类型
            File csvFile = new File(BASIC_DIR+"/GIVE_UP_DATA/"+fileName);

            //获取文件目录
            File parent = csvFile.getParentFile();
            if (!parent.exists())
            {
                parent.mkdirs();
            }

            //创建文件
            csvFile.createNewFile();
            csvWrite = new BufferedWriter(
                    new OutputStreamWriter(new FileOutputStream(csvFile), "GBK"), 1024);
            //写入表头
            writeHead(csvWrite);

            //写入数据
            write(approvalScheduleLogDTOs, csvWrite);

            csvWrite.flush();


        }
        catch (IOException e)
        {
            JSONResult.getFailureJson(e.getMessage());
        }
        finally
        {

            try
            {
                if (null != csvWrite)
                {
                    csvWrite.close();
                }
            }
            catch (IOException e) {
                JSONResult.getFailureJson(e.getMessage());
            }
        }
        return JSONResult.getSuccessJson("/GIVE_UP_DATA/"+fileName,"下载成功！");
    }

    private static void writeHead(BufferedWriter csvWrite)
            throws IOException
    {
        List<String> dataList = new ArrayList<>();
        dataList.add("CELL_NAME");
        dataList.add("CELL_ID");
        dataList.add("BTS_NAME");
        dataList.add("BTS_ID");
        dataList.add("TECH_TYPE");
        dataList.add("LOCATION");
        dataList.add("COUNTY");
        dataList.add("LONGITUDE");
        dataList.add("LATITUDE");
        dataList.add("SEND_START_FREQ");
        dataList.add("SEND_END_FREQ");
        dataList.add("ACC_START_FREQ");
        dataList.add("ACC_END_FREQ");
        dataList.add("MAX_EMISSIVE_POWER");
        dataList.add("HEIGHT");
        dataList.add("DEVICE_FACTORY");
        dataList.add("DEVICE_MODEL");
        dataList.add("MODEL_CODE");
        dataList.add("ANTENNA_MODEL");
        dataList.add("ANTENNA_FACTORY");
        dataList.add("POLARIZATION_MODE");
        dataList.add("ANTENNA_AZIMUTH");
        dataList.add("AT_RANG");
        dataList.add("AT_EANG");
        dataList.add("FEEDER_LOSS");
        dataList.add("ANTENNA_GAIN");
        dataList.add("ALTITUDE");
        dataList.add("SET_YEAR");
        dataList.add("SET_MONTH");
        dataList.add("EXPAND_STATION");
        dataList.add("ATTRIBUTE_STATION");
        dataList.add("ST_SERV_R");
        for (String data : dataList)
        {
            StringBuffer buffer = new StringBuffer();
            String rowStr = buffer.append("\"").append(data).append("\",").toString();
            csvWrite.write(rowStr);
        }
        csvWrite.newLine();
    }

    private static void write(List<ApprovalScheduleLogDTO> approvalScheduleLogDTOs, BufferedWriter csvWrite)
            throws IOException
    {
        for (ApprovalScheduleLogDTO data : approvalScheduleLogDTOs)
        {
            StringBuffer buffer = new StringBuffer();
            buffer.append("\"").append(data.getCellName()).append("\",");
            buffer.append("\"").append(data.getCellId()).append("\",");
            buffer.append("\"").append(data.getBtsName()).append("\",");
            buffer.append("\"").append(data.getBtsId()).append("\",");
            buffer.append("\"").append(data.getTechType()).append("\",");
            buffer.append("\"").append(data.getLocation()).append("\",");
            buffer.append("\"").append(data.getCounty()).append("\",");
            buffer.append("\"").append(data.getLongitude()).append("\",");
            buffer.append("\"").append(data.getLatitude()).append("\",");
            buffer.append("\"").append(data.getSendStartFreq()).append("\",");
            buffer.append("\"").append(data.getSendEndFreq()).append("\",");
            buffer.append("\"").append(data.getAccStartFreq()).append("\",");
            buffer.append("\"").append(data.getAccEndFreq()).append("\",");
            buffer.append("\"").append(data.getMaxEmissivePower()).append("\",");
            buffer.append("\"").append(data.getHeight()).append("\",");
            buffer.append("\"").append(data.getVendorName()).append("\",");
            buffer.append("\"").append(data.getDeviceModel()).append("\",");
            buffer.append("\"").append(data.getModelCode()==null?"":data.getModelCode()).append("\",");
            buffer.append("\"").append(data.getAntennaModel()==null?"":data.getAntennaModel()).append("\",");
            buffer.append("\"").append(data.getAntennaFactory()==null?"":data.getAntennaFactory()).append("\",");
            buffer.append("\"").append(data.getPolarizationMode()==null?"":data.getPolarizationMode()).append("\",");
            buffer.append("\"").append(data.getAntennaAzimuth()==null?"":data.getAntennaAzimuth()).append("\",");
            buffer.append("\"").append(data.getAtRang()==null?"":data.getAtRang()).append("\",");
            buffer.append("\"").append(data.getAtEang()==null?"":data.getAtEang()).append("\",");
            buffer.append("\"").append(data.getFeederLoss()==null?"":data.getFeederLoss()).append("\",");
            buffer.append("\"").append(data.getAntennaGain()==null?"":data.getAntennaGain()).append("\",");
            buffer.append("\"").append(data.getAltitude()==null?"":data.getAltitude()).append("\",");
            buffer.append("\"").append(data.getSetYear()).append("\",");
            buffer.append("\"").append(data.getSetMonth()).append("\",");
            buffer.append("\"").append(data.getExpandStation()).append("\",");
            buffer.append("\"").append(data.getAttributeStation()).append("\",");
            buffer.append("\"").append(data.getStServR()==null?"":data.getStServR()).append("\",");
            String rowStr = buffer.toString();
            csvWrite.write(rowStr);

            csvWrite.newLine();
        }

    }

    public JSONObject findGiveUpDataList(ApprovalTransportJobDTO approvalTransportJobDTO){
        PageInfo<StationBakDTO> applyJobInDTOPageInfo = new PageHandle(approvalTransportJobDTO).buildPage(stationBakService.findGiveUpDataList(approvalTransportJobDTO));
        if (applyJobInDTOPageInfo.getList()!=null){
            return JSONResult.getSuccessJson(applyJobInDTOPageInfo);
        }
        return JSONResult.getPageFailureJson(approvalTransportJobDTO.getPage(),approvalTransportJobDTO.getPage() + 1,approvalTransportJobDTO.getRows(),0,"操作失败");
    }


}
