package com.caict.bsm.project.api.web.service.business.twotable;

import com.alibaba.fastjson.JSONObject;
import com.caict.bsm.project.domain.business.service.twotable.ReformTableService;
import com.caict.bsm.project.system.model.dto.twotable.ReformTableDTO;
import com.caict.bsm.project.system.model.entity.business.twotable.ReformTable;
import com.caict.bsm.project.system.utils.util.JSONResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/12/28
 */
@Service
public class ReformTableWebService {

    @Autowired
    private ReformTableService reformTableService;

    public int saves(List<ReformTable> reformTables){
        return reformTableService.batchInsert(reformTables);
    }

    public List<ReformTableDTO> findReformByPage(ReformTableDTO reformTableDTO){
        return reformTableService.findReformByPage(reformTableDTO);
    }

    public JSONObject updateReform(ReformTable reformTable){
        if (reformTableService.update(reformTable)>0){
            return JSONResult.getSuccessJson("修改成功！");
        }
        return JSONResult.getFailureJson("修改失败！");
    }

    public int deleteAll(){
        return reformTableService.deleteAll();
    }
}
