package com.caict.bsm.project.api.web.service.datav;

import com.caict.bsm.project.domain.business.service.datav.RsbtStationAreaDataVService;
import com.caict.bsm.project.system.data.util.PageHandle;
import com.caict.bsm.project.system.model.dto.datav.RsbtStationAreaDateVDTO;
import com.caict.bsm.project.system.model.dto.datav.RsbtStationAreaDateVSearchDTO;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class RsbtStationAreaDataVWebService {

    @Autowired
    private RsbtStationAreaDataVService rsbtStationAreaDataVService;

    /**
     * 区域基站数据查询
     * */
    public List<RsbtStationAreaDateVDTO> findAllStatAreaByWhere(RsbtStationAreaDateVSearchDTO rsbtStationAreaDateVSearchDTO){
        return rsbtStationAreaDataVService.findAllStatAreaByWhere(rsbtStationAreaDateVSearchDTO);
    }

    /**
     * 分页查询
     * */
    public PageInfo<RsbtStationAreaDateVDTO> findAllPageWhere(RsbtStationAreaDateVSearchDTO rsbtStationAreaDateVSearchDTO){
        return new PageHandle(rsbtStationAreaDateVSearchDTO).buildPage(findAllStatAreaByWhere(rsbtStationAreaDateVSearchDTO));
    }
}
