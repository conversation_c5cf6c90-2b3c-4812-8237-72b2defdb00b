package com.caict.bsm.project.api.web.service.sta;

import com.alibaba.fastjson.JSONObject;
import com.caict.bsm.project.api.web.service.business.license.LicenseWebService;
import com.caict.bsm.project.domain.business.service.sta.StaStationService;
import com.caict.bsm.project.extension.document.service.PdfExportService;
import com.caict.bsm.project.system.model.dto.business.license.LicensePdfDTO;
import com.caict.bsm.project.system.model.dto.business.sta.*;
import com.caict.bsm.project.system.model.dto.business.station.ISectionDTO;
import com.caict.bsm.project.system.model.entity.business.sta.StaAntenna;
import com.caict.bsm.project.system.model.entity.business.sta.StaEqu;
import com.caict.bsm.project.system.model.entity.business.sta.StaFreq;
import com.caict.bsm.project.system.model.entity.business.sta.StaStation;
import com.caict.bsm.project.system.utils.util.CalculateUtil;
import com.caict.bsm.project.system.utils.util.DateUtils;
import com.caict.bsm.project.system.utils.util.JSONResult;
import com.caict.bsm.project.system.utils.util.VerificationCode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2020/5/8
 */
@Service
public class StaStationWebService {

    private static final Logger LOG = LoggerFactory.getLogger(StaStationWebService.class);

    @Value("${caict.generalTemplate}")
    private String PDF_TEMPLATE;
    @Value("${caict.generalTemplateForm}")
    private String PDF_TEMPLATE_FORM;
    @Value("${caict.myFilePath}")
    private String PDF_DIR;
    @Value("${caict.zipPath}")
    private String ZIP_DIR;
    @Autowired
    private StaStationService staStationService;
    @Autowired
    private PdfExportService pdfExportService;
    @Autowired
    private LicenseWebService licenseWebService;

    public String save(StaStation staStation){
        return staStationService.save(staStation);
    }

    public JSONObject getListVOByPage(StaDTO staDTO) {
        int page = staDTO.getPage();
        int rows = staDTO.getRows();

        List<StaLicenseDTO> list = staStationService.getListVOByPage((page - 1) * rows + 1, page * rows,staDTO.getStationName(),staDTO.getPipeNum(),staDTO.getLinceseCode(),
                staDTO.getDateStart(),staDTO.getDateEnd());
        if (list!=null){
            int staListNum = staStationService.getStaListNum(staDTO.getStationName(),staDTO.getPipeNum(),staDTO.getLinceseCode(),
                    staDTO.getDateStart(),staDTO.getDateEnd());
            return JSONResult.getPageSuccessJson(page,page+1,rows,staListNum,list);
        }
        return JSONResult.getPageFailureJson(page,page+1,rows,0,"查询数据为空！");
    }

    public StaListDTO getList(String stationGuid) {
        StaListTempDTO staListTempDTO = staStationService.getList(stationGuid);
        StaListDTO staListDTO = new StaListDTO();
        BeanUtils.copyProperties(staListTempDTO,staListDTO);
        Map<String, String> longlaMap = CalculateUtil.calLongitude(Double.valueOf(staListTempDTO.getLongitude()), Double.valueOf(staListTempDTO.getLatitude()));
        String[] longi = new String[]{longlaMap.get("longitudeD"),longlaMap.get("longitudeF"),longlaMap.get("longitudeM")};
        String[] lati = new String[]{longlaMap.get("latitudeD"),longlaMap.get("latitudeF"),longlaMap.get("latitudeM")};
        staListDTO.setLongitude(longi);
        staListDTO.setLatitude(lati);
        List<StaAntenna> staAntennaList = staStationService.getAntenna(stationGuid);
        List<StaEqu> staEquList = staStationService.getEqu(stationGuid);
        List<StaFreq> staFreqList = staStationService.getFreq(stationGuid);

        staListDTO.setStaAntennaList(staAntennaList);
        staListDTO.setStaEquList(staEquList);
        staListDTO.setStaFreqList(staFreqList);

        return staListDTO;
    }

    @Transactional
    public JSONObject deleteSta(String stationGuid){
        try {
            List<StaStation> staStations = staStationService.selectStaions(stationGuid);
            if (!(staStations == null || staStations.size()>1)) {
                staStationService.deleteLicense(stationGuid);
                staStationService.deleteFreq(stationGuid);
                staStationService.deleteEqu(stationGuid);
                staStationService.deleteAntenna(stationGuid);
                staStationService.deleteSta(stationGuid);
                staStationService.deleteEaf(stationGuid);
                return JSONResult.getSuccessJson("操作成功！");
            } else {
                return JSONResult.getFailureJson("删除出错！");
            }
        }catch (Exception e){
            return JSONResult.getFailureJson(e.getMessage(),"程序出错");
        }

    }

    @Transactional
    public JSONObject add(StaUpdateDTO staUpdateDTO) {
        String stationId = VerificationCode.idGet("sta",4);
        String staLicenseId = VerificationCode.idGet("license",4);
        String getStationId = staStationService.getStationId(staUpdateDTO.getIdenCode());

        if (getStationId == null) {
            Date gmtCreate = new Date();
            String linceseCode = staUpdateDTO.getLinceseCode();
                /*SimpleDateFormat sdf = new SimpleDateFormat("yyyy");
                Date date = new Date();
                linceseCode.append(sdf.format(date));
                linceseCode.append("S");
                linceseCode.append(5000);
                linceseCode.append(staUpdateDTO.getLinceseCode());*/
            String longitude = String.valueOf(CalculateUtil.duFenMiaoToLongLat(staUpdateDTO.getLongitude()));
            String latitude = String.valueOf(CalculateUtil.duFenMiaoToLongLat(staUpdateDTO.getLatitude()));
            Boolean a1 = staStationService.add1(stationId, staUpdateDTO.getOrgCode(), staUpdateDTO.getDistrCode(), staUpdateDTO.getStaType(), staUpdateDTO.getPipeNum(), staUpdateDTO.getStaName(), staUpdateDTO.getIdenCode(), staUpdateDTO.getUserName(), staUpdateDTO.getLocation(), longitude, latitude, staUpdateDTO.getDetail(), staUpdateDTO.getSpecialCase(), staUpdateDTO.getAppCode(), staUpdateDTO.getStatAppType(), staUpdateDTO.getStatTdi(), staUpdateDTO.getStatWork(), staUpdateDTO.getStatStatus(), staUpdateDTO.getLinceseState());
            Boolean a2 = staStationService.add2(staLicenseId, stationId, linceseCode, gmtCreate, gmtCreate, staUpdateDTO.getLinceseState(), staUpdateDTO.getLinceseStartDate(), staUpdateDTO.getLinceseEndDate(), staUpdateDTO.getLicenseAuth());

            for (int i = 0; i < staUpdateDTO.getStaEquList().size(); i++) {
                String staFreqId = VerificationCode.idGet("freq",4);
                String staEquId = VerificationCode.idGet("equ",4);
                String staAntennaId = VerificationCode.idGet("ant",4);
                String eafGuid = VerificationCode.idGet("eaf",4);
                if (!(staUpdateDTO.getStaFreqList()== null || staUpdateDTO.getStaFreqList().size()==0)){
                    staStationService.add3(staFreqId, stationId, staUpdateDTO.getStaFreqList().get(i).getFreqEf(), staUpdateDTO.getStaFreqList().get(i).getFreqRf(), staUpdateDTO.getStaFreqList().get(i).getFreqBand(), staUpdateDTO.getStaFreqList().get(i).getFtFreqCsgn(), staUpdateDTO.getStaFreqList().get(i).getFreqPass());
                }
                if (!(staUpdateDTO.getStaEquList()== null || staUpdateDTO.getStaEquList().size()==0)) {
                    staStationService.add4(staEquId, stationId, staUpdateDTO.getStaEquList().get(i).getEquModel(), staUpdateDTO.getStaEquList().get(i).getEquAuth(), staUpdateDTO.getStaEquList().get(i).getEquMenu(), staUpdateDTO.getStaEquList().get(i).getEquPow(), staUpdateDTO.getStaEquList().get(i).getEquType(), staUpdateDTO.getStaEquList().get(i).getEquCode(), staUpdateDTO.getStaEquList().get(i).getEquMb());
                }
                if (!(staUpdateDTO.getStaAntennaList()== null || staUpdateDTO.getStaAntennaList().size()==0)) {
                    staStationService.add5(staAntennaId, stationId, staUpdateDTO.getStaAntennaList().get(i).getAntGain(), staUpdateDTO.getStaAntennaList().get(i).getAntEpole(), staUpdateDTO.getStaAntennaList().get(i).getAntHight());
                }
                staStationService.add6(eafGuid, stationId, staEquId, staAntennaId, staFreqId);
            }
            if (a1 & a2) {
                return JSONResult.getSuccessJson("操作成功");
            } else {
                return JSONResult.getFailureJson("操作失败");
            }
        } else {
            staUpdateDTO.setStationId(getStationId);
            return update(staUpdateDTO);
        }

    }

    public JSONObject update(StaUpdateDTO staUpdateDTO) {
        if (staUpdateDTO.getStaFreqList().size() != staUpdateDTO.getStaEquList().size() && staUpdateDTO.getStaFreqList().size() != staUpdateDTO.getStaAntennaList().size()) {
            return JSONResult.getFailureJson("修改失败，设备天线频率数据数量不一致");
        } else {
            Date gmtModified = new Date();
            List<String> FreqIdList = staStationService.getFreqIdlist(staUpdateDTO.getStationId());
//        List<String> EquIdList = staMapper.getEquIdlist(staUpdateDTO.getStationId());
//        List<String> AntennaIdList = staMapper.getAntennaIdlist(staUpdateDTO.getStationId());
            String longitude = String.valueOf(CalculateUtil.duFenMiaoToLongLat(staUpdateDTO.getLongitude()));
            String latitude = String.valueOf(CalculateUtil.duFenMiaoToLongLat(staUpdateDTO.getLatitude()));
            Boolean a1 = staStationService.update1(staUpdateDTO.getStationId(), staUpdateDTO.getOrgCode(), staUpdateDTO.getDistrCode(), staUpdateDTO.getStaType(), staUpdateDTO.getPipeNum(), staUpdateDTO.getStaName(), staUpdateDTO.getIdenCode(), staUpdateDTO.getUserName(), staUpdateDTO.getLocation(), longitude, latitude, staUpdateDTO.getDetail(), staUpdateDTO.getSpecialCase(), staUpdateDTO.getAppCode(), staUpdateDTO.getStatAppType(), staUpdateDTO.getStatTdi(), staUpdateDTO.getStatWork(), staUpdateDTO.getStatStatus(), staUpdateDTO.getLinceseState());
            Boolean a2 = staStationService.update2(staUpdateDTO.getLicenseGuid(), staUpdateDTO.getLinceseCode(), staUpdateDTO.getGmtCreate(), gmtModified, staUpdateDTO.getLinceseState(), staUpdateDTO.getLinceseStartDate(), staUpdateDTO.getLinceseEndDate(), staUpdateDTO.getLicenseAuth());


            staStationService.deleteFreq(staUpdateDTO.getStationId());
            staStationService.deleteEqu(staUpdateDTO.getStationId());
            staStationService.deleteAntenna(staUpdateDTO.getStationId());
            staStationService.deleteEaf(staUpdateDTO.getStationId());
            for (int i = 0; i < staUpdateDTO.getStaFreqList().size(); i++) {
                String staFreqId = VerificationCode.idGet("freq",4);
                String staEquId = VerificationCode.idGet("equ",4);
                String staAntennaId = VerificationCode.idGet("ant",4);
                String eafGuid = VerificationCode.idGet("eaf",4);
                staStationService.add3(staFreqId, staUpdateDTO.getStationId(), staUpdateDTO.getStaFreqList().get(i).getFreqEf(), staUpdateDTO.getStaFreqList().get(i).getFreqRf(), staUpdateDTO.getStaFreqList().get(i).getFreqBand(), staUpdateDTO.getStaFreqList().get(i).getFtFreqCsgn(), staUpdateDTO.getStaFreqList().get(i).getFreqPass());
                staStationService.add4(staEquId, staUpdateDTO.getStationId(), staUpdateDTO.getStaEquList().get(i).getEquModel(), staUpdateDTO.getStaEquList().get(i).getEquAuth(), staUpdateDTO.getStaEquList().get(i).getEquMenu(), staUpdateDTO.getStaEquList().get(i).getEquPow(), staUpdateDTO.getStaEquList().get(i).getEquType(), staUpdateDTO.getStaEquList().get(i).getEquCode(), staUpdateDTO.getStaEquList().get(i).getEquMb());
                staStationService.add5(staAntennaId, staUpdateDTO.getStationId(), staUpdateDTO.getStaAntennaList().get(i).getAntGain(), staUpdateDTO.getStaAntennaList().get(i).getAntEpole(), staUpdateDTO.getStaAntennaList().get(i).getAntHight());
                staStationService.add6(eafGuid, staUpdateDTO.getStationId(), staEquId, staAntennaId, staFreqId);
            }
            if (a1 & a2) {
                return JSONResult.getSuccessJson("操作成功");
            }
            return JSONResult.getFailureJson("修改失败");
        }
    }

    public JSONObject selectIdenCode(String linceseCode,String idenCode){
        if (idenCode == null) {
            return JSONResult.getFailureJson("台站识别码不能为空");
        }
        if (linceseCode == null) {
            return JSONResult.getFailureJson("执照编号不能为空");
        }
        String errorMessage1 = staStationService.selectIdenCode(idenCode);
        String errorMessage2 = staStationService.selectLinceseCode(linceseCode);
        if (errorMessage1 != null) {
            return JSONResult.getFailureJson("台站识别码已经存在");
        }
        if (errorMessage2 != null) {
            return JSONResult.getFailureJson("执照编号已经存在");
        }
        return JSONResult.getSuccessJson("唯一！");
    }

    public LicensePdfDTO getLicense(String stationId){
        return staStationService.getLicense(stationId);
    }


    //使用台站名称作为名称
    private String getLicensePdfName(LicensePdfDTO pdfDTO,String type) {
        String pdfName = pdfDTO.getStationGuid();
        if ("print".equals(type)){
            pdfName = pdfDTO.getStationGuid()+"print";
        }
        return pdfName;
    }

    public String savePdf(String stationId, String type){
        LicensePdfDTO pdfDTO = getLicense(stationId);
        List<ISectionDTO> iSectionDTOList = staStationService.getISectionDTOList(stationId);
        if (iSectionDTOList!=null && iSectionDTOList.size()!=0){
            pdfDTO.setiSectionDTOList(iSectionDTOList);
        }
        //表单数据
        Map<String, String> map = new LinkedHashMap<>();
        //二维码数据
        Map<String,String> codeMap = new HashMap<String,String>();
        licenseWebService.setLicenseData(map,codeMap,pdfDTO);
        if ("template".equals(type)){
            return pdfExportService.savePdf(PDF_DIR,PDF_TEMPLATE,stationId,type,map,codeMap);
        }else if ("print".equals(type)) {
            return pdfExportService.savePdf(PDF_DIR,PDF_TEMPLATE_FORM,stationId,type,map,codeMap);
        }
        return null;
    }

    public JSONObject savePdfZip(List<String> stationIdlist){
        List<LicensePdfDTO> licensePdfDTOList = staStationService.findOneFDFByStationGuids(stationIdlist);
        if (licensePdfDTOList != null && licensePdfDTOList.size() > 0){
            List<String> filePathList = new ArrayList<>();
            for (LicensePdfDTO licensePdfDTO : licensePdfDTOList){
                List<ISectionDTO> iSectionDTOList = staStationService.getISectionDTOList(licensePdfDTO.getStationGuid());
                if (iSectionDTOList!=null && iSectionDTOList.size()!=0){
                    licensePdfDTO.setiSectionDTOList(iSectionDTOList);
                }

                //表单数据
                Map<String, String> map = new LinkedHashMap<>();
                //二维码数据
                Map<String,String> codeMap = new HashMap<String,String>();
                licenseWebService.setLicenseData(map,codeMap,licensePdfDTO);

                String filePath = PDF_DIR + pdfExportService.savePdf(PDF_DIR ,PDF_TEMPLATE,licensePdfDTO.getStationGuid(),"template",map,codeMap);
                filePathList.add(filePath);
            }
            return JSONResult.getSuccessJson(pdfExportService.savePdfZip(PDF_DIR ,filePathList),"勾选下载成功");
        }
        return JSONResult.getFailureJson("未检测到执照信息");
    }

    public void print(String stationGuid, HttpServletResponse response){
        String path = savePdf(stationGuid, "print");
        response.setContentType("application/pdf");
        FileInputStream in;
        OutputStream out;
        try {
            in = new FileInputStream(new File(PDF_DIR+path));
            out = response.getOutputStream();
            byte[] b = new byte[512];
            while ((in.read(b)) != -1) {
                out.write(b);
            }
            out.flush();
            in.close();
            out.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

}
