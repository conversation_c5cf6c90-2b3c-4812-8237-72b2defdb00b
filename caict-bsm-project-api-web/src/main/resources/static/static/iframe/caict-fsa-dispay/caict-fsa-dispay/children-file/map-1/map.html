<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>频率台站可视化</title>

  <link rel="stylesheet" href="../../fonts/icomoon.css">
  <link rel="stylesheet" href="./css/map.css">
</head>

<body>
  <div class="main">
    <div class="map">
      <h3>
        <span class="icon-cube"></span>
        <span class="title">台站分布统计</span>
      </h3>
      <div class="chart">
        <div class="geo"></div>
      </div>
    </div>
    <div class="body">
      <div class="tab_box">
        <p class="active" data-id="1">频率许可管理</p>
        <p data-id="2">申请表管理</p>
        <p data-id="3">台站管理</p>
        <p data-id="4">执照管理</p>
        <p data-id="5">缴费管理</p>
        <p data-id="6">台站备案</p>
      </div>
      <div class="query_box">
        <div>
          <label>许可证编号</label>
          <input type="text" name="" id="">
        </div>
        <div>
          <label>业务系统</label>
          <input type="text" name="" id="">
        </div>
        <div>
          <label>签发时间</label>
          <input type="text" name="" id="">
        </div>
        <div>
          <label>使用频率</label>
          <input type="text" name="" id="">
        </div>
        <button class="query">搜索</button>
        <button class="clear">清空</button>
      </div>
      <div class="list">
        <div class="header">
          <div>许可证编号</div>
          <div>业务系统</div>
          <div>签发时间</div>
          <div>使用频率</div>
        </div>
        <div class="scroll_body">
          <div class="item">
            <div class="item_main">
              <div>aaa</div>
              <div>1号系统</div>
              <div>2020-05-06</div>
              <div>30MHz</div>
            </div>
            <div class="detail">
              <p>申请表编号:'aaaa'</p>
              <p>技术资料表个数:100个</p>
            </div>
          </div>       
        </div>
      </div>
    </div>
  </div>

</body>
<script src="../../lib/jquery.min.js"></script>
<script src="../../lib/echarts.min.js"></script>
<script src="./js/map.js"></script>
<script>
  function getData(){
    var html = '';
    for(var i = 0; i < 10; i++){
      html += `
        <div class="item">
          <div class="item_main">
            <div>aaa${Math.round(Math.random() * 1000)}</div>
            <div>1号系统</div>
            <div>2020-05-06</div>
            <div>30MHz</div>
          </div>
          <div class="detail">
            <p>申请表编号:${Math.round(Math.random() * 1000)}</p>
            <p>技术资料表个数:${Math.round(Math.random() * 50)}个</p>
          </div>
        </div>       
      `
    }
    html = $(html).click(bindItem)
    $('.scroll_body').html(html)
  }
  function bindItem() {
    if (/show/gi.test($(this).find('.detail').attr('class'))) {
      $('.list .item .detail').removeClass('show')
    } else {
      $('.list .item .detail').removeClass('show')
      $(this).find('.detail').addClass('show')
    }
    $('.list .item .detail').click(function (e) {
      e.stopPropagation();
    })
  }
  getData();
  $('.query').click(function(){
    getData();
  })
  $('.clear').click(function () {
    $('.query_box input').val('')
    getData();
  })
  $('.tab_box p').click(function(){
    window.location = '../map-' + $(this).data('id') + '/map.html'
  })

</script>

</html>
