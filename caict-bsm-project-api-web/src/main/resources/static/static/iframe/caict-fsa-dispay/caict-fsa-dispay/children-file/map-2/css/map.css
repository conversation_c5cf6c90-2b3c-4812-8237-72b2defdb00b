body {
  line-height: 1.15;
  font-size: 0.5rem;
  margin: 0;
  padding: 0;
  background-repeat: no-repeat;
  background-position: 0 0 / cover;
  background-color: #101129;
}

html,
body {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

* {
  margin: 0;
  padding: 0;
  font-weight: normal;
}

.main {
  width: 100%;
  height: 100%;
  display: flex;
}

.main>div {
  flex: 1;
  height: 100%;
}

.map {
  display: flex;
  flex-direction: column;
}

.map h3 {
  line-height: 1;
  padding: 0.667rem 0;
  margin: 0;
  font-size: 0.833rem;
  color: #fff;
  cursor: pointer;
}

.map .icon-cube {
  color: #68d8fe;
}

.map .chart {
  flex: 1;
  background-color: rgba(255, 255, 255, 0.05);
}

.map .geo {
  width: 100%;
  height: 100%;
}

.body {
  color: #fff;
  font-size: 16px;
  padding: 30px;
  box-sizing: border-box;
}
.tab_box{
    display: flex;
    width: 100%;
}
.tab_box p{
    flex: 1;
    padding: 10px;
    background: #fff;
    color: #101129;
    cursor: pointer;
    text-align: center;
}
.tab_box p.active{
    background: #2f54eb;
    color: #fff;
}
.query_box {
  border: 1px dashed rgba(255, 255, 255, 0.4);
  border-top: none;
  border-radius: 5px;
  padding: 15px;
}

.query_box>div {
  width: 50%;
  float: left;
  margin-bottom: 10px;
}

.query_box>div label {
  display: inline-block;
  width: 5em;
}

.query_box>div input {
  line-height: 28px;
  font-size: 16px;
  padding: 0 5px;
}

.query_box button {
  width: 100px;
  height: 30px;
  font-size: 16px;
  border: none;
  margin-top: 20px;
}

.list {
  margin-top: 20px;
  border: 1px solid #555;

}

.list .scroll_body {
  max-height: 550px;
  overflow: auto;
}

.list .header {
  display: flex;
}

.list .header>div {
  flex: 1;
  margin-right: 1px;
  background: rgba(255, 255, 255, 0.2);
  padding: 10px 20px;
}

.list .item_main {
  display: flex;
  cursor: pointer;

}

.list .item>.item_main>div {
  flex: 1;
  margin-right: 1px;
  background: rgba(255, 255, 255, 0.07);
  padding: 10px 20px;
}

.list .item>div.detail.show{
    display: block;
}
.list .item>div.detail {
    border-top: 1px dashed #555;
  background: rgba(255, 255, 255, 0.07);
  padding: 20px;
  display: none;
}
.list .item>div.detail p{
    margin-bottom: 10px;
}

.list .item:nth-child(2n)>.item_main>div {
  flex: 1;
  margin-right: 1px;
  background: rgba(255, 255, 255, 0);
  padding: 10px 20px;
}

.list .item:nth-child(2n)>div.detail {
  background: rgba(255, 255, 255, 0);
}
