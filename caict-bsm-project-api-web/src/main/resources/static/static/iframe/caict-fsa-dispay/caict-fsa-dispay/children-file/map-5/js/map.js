function randomData() {
  return Math.round(Math.random() * 10000);
}

var series = [{
  name: '台站分布统计',
  type: 'map',
  mapType: 'chongqing', // 自定义扩展图表类型
  zoom: 1.2,
  itemStyle: {
    normal: {
      label: {
        show: true
      }
    },
    emphasis: {
      label: {
        show: true
      }
    }
  },
  data: [{
      name: '开州区',
      value: randomData()
    },
    {
      name: '万州区',
      value: randomData(),
    },
    {
      name: '江津区',
      value: randomData()
    },
    {
      name: '涪陵区',
      value: randomData()
    },
    {
      name: '武隆区',
      value: randomData()
    },
    {
      name: '南川区',
      value: randomData()
    },
    {
      name: '黔江区',
      value: randomData()
    },
    {
      name: '合川区',
      value: randomData()
    },
    {
      name: '綦江区',
      value: randomData()
    },
    {
      name: '巴南区',
      value: randomData()
    },
    {
      name: '潼南区',
      value: randomData()
    },
    {
      name: '永川区',
      value: randomData()
    },
    {
      name: '渝北区',
      value: randomData()
    },
    {
      name: '长寿区',
      value: randomData()
    },
    {
      name: '大足区',
      value: randomData()
    },
    {
      name: '铜梁区',
      value: randomData()
    },
    {
      name: '荣昌区',
      value: randomData()
    },
    {
      name: '璧山区',
      value: randomData()
    },
    {
      name: '北碚区',
      value: randomData()
    },
    {
      name: '万盛区',
      value: randomData()
    },
    {
      name: '九龙坡区',
      value: randomData()
    },
    {
      name: '沙坪坝区',
      value: randomData()
    },
    {
      name: '南岸区',
      value: randomData()
    },
    {
      name: '江北区',
      value: randomData()
    },
    {
      name: '大渡口区',
      value: randomData()
    },
    {
      name: '渝中区',
      value: randomData()
    },
    {
      name: '奉节县',
      value: randomData()
    },
    {
      name: '巫溪县',
      value: randomData()
    },
    {
      name: '酉阳土家族苗族自治县',
      value: randomData()
    },
    {
      name: '彭水苗族土家族自治县',
      value: randomData()
    },
    {
      name: '云阳县',
      value: randomData()
    },
    {
      name: '城口县',
      value: randomData()
    },
    {
      name: '石柱土家族自治县',
      value: randomData()
    },
    {
      name: '巫山县',
      value: randomData()
    },
    {
      name: '丰都县',
      value: randomData()
    },
    {
      name: '秀山土家族苗族自治县',
      value: randomData()
    },
    {
      name: '忠县',
      value: randomData()
    },
    {
      name: '梁平区',
      value: randomData()
    },
    {
      name: '垫江县',
      value: randomData()
    },
  ]
}];

option = {
  backgroundColor: '#080a20',
  title: {
    left: 'left',
    textStyle: {
      color: '#fff'
    }
  },
  tooltip: {
    trigger: 'item'
  },

  visualMap: {
    min: 0,
    max: 10000,
    text: ['High', 'Low'],
    realtime: false,
    calculable: true,
    inRange: {
      color: ['#e0ffff', '#006edd']
    }
  },
  // geo: {
  //     map: 'chongqing',
  //     zoom: 1.2,
  //     label: {
  //         emphasis: {
  //             show: false
  //         }
  //     },
  //     roam: true,
  //     itemStyle: {
  //         normal: {
  //             areaColor: '#142957',
  //             borderColor: '#0692a4'
  //         },
  //         emphasis: {
  //             areaColor: '#0b1c2d'
  //         }
  //     }
  // },
  series: series
};

console.log($('.map .geo')[0])
var myEcharts = echarts.init($('.map .geo')[0])

myEcharts.showLoading();
$.get('../../lib/map/chongqing.json', function (geoJson) {

  myEcharts.hideLoading();

  echarts.registerMap('chongqing', geoJson);

  myEcharts.setOption(option)
  // 处理点击事件并且跳转到相应的百度搜索页面
  myEcharts.on('click', function (params) {
    console.log(params)
    getData();
    for (var i = 0; i < series[0].data.length; i++) {
      series[0].data[i].selected = false;
      if (params.name == series[0].data[i].name) {
        series[0].data[i].selected = true;
      }
    }
    myEcharts.setOption(option)

    $('.title').text(params.name + '台站分布统计')
  });
});
