webpackJsonp([10],{Jqt7:function(e,t){},"Nko+":function(e,t,s){e.exports=s.p+"static/img/logo-2.7108036.png"},W2Q3:function(e,t,s){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r,a,n,i,o,u=s("Xxa5"),p=s.n(u),c=s("exGp"),l=s.n(c),g=s("L6bb"),d=s.n(g),m=new(s("oFuF").a);r=l()(p.a.mark(function e(t){var s;return p.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return s={type:"post",url:"/api/login/ajax",data:t},e.abrupt("return",m.requestFun(s));case 2:case"end":return e.stop()}},e,this)})),a=l()(p.a.mark(function e(t){var s;return p.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return s={type:"get",url:"/api/login/checkLogin",data:t},e.abrupt("return",m.requestFun(s));case 2:case"end":return e.stop()}},e,this)})),n=l()(p.a.mark(function e(t){var s;return p.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return s={type:"post",url:"/api/logout",data:t},e.abrupt("return",m.requestFun(s));case 2:case"end":return e.stop()}},e,this)})),i=l()(p.a.mark(function e(t){var s;return p.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return s={type:"post",url:"/api/apiWeb/security/auth/updatePassword",data:t},e.abrupt("return",m.requestFun(s));case 2:case"end":return e.stop()}},e,this)})),o=l()(p.a.mark(function e(t){var s;return p.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return s={type:"post",url:"/api/transportJob/count",data:t},e.abrupt("return",m.requestFun(s));case 2:case"end":return e.stop()}},e,this)}));function f(e){if(""==e||!e)return{flag:!1,msg:"请输入用户名！"};return/^[a-zA-Z\d]\w{3,11}[a-zA-Z\d]$/.test(e)?{flag:!0,msg:"验证成功！"}:{falg:!1,msg:"用户名由2-20个英文和数字构成！"}}function h(e){if(""==e||!e)return{flag:!1,msg:"请输入密码！"};return/^(\w){6,12}$/.test(e)?{flag:!0,msg:"验证成功！"}:{flag:!1,msg:"密码由6-12数字、英文构成！"}}var v={data:function(){return{username:"",password:"",isLogin:!1,passwordTips:"",usernameTips:"",loginTips:""}},mounted:function(){},methods:{login:function(){var e=this;return l()(p.a.mark(function t(){var s,r,a;return p.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(e.$message.closeAll(),sessionStorage.clear(),(s=f(e.username)).flag){t.next=6;break}return e.$message.error(s.msg),t.abrupt("return");case 6:if((r=h(e.password)).flag){t.next=10;break}return e.$message.error(r.msg),t.abrupt("return");case 10:e.isLogin=!0,a={loginName:e.username,password:d()(e.password)},e.$ajax.post({url:"/api/apiWeb/security/auth/loginOn",data:a}).then(function(t){try{if(t.success){var s=t.data,r="";switch(parseInt(s.roleType)){case 0:r="admin";break;case 1:r=s.userType+"Provincial";break;case 2:r=s.userType+"City"}sessionStorage.setItem("token",s.token),sessionStorage.setItem("regionId",s.regionId),sessionStorage.setItem("roleType",r),"wuweiProvincial"==r||"admin"==r?e.$router.push({path:"/home"}):"wuweiCity"==r?e.$router.push({path:"/apply-table-management"}):e.$router.push({path:"/report/me"})}else e.loginTips=t.message,e.$message.error(t.message)}catch(e){}e.isLogin=!1},function(t){e.isLogin=!1,e.loginTips="登录失败，请重试！"});case 13:case"end":return t.stop()}},t,e)}))()}},watch:{password:function(e){this.loginTips="";var t=h(e);t.flag?this.passwordTips="":this.passwordTips=t.msg},username:function(e){this.loginTips="";var t=f(e);t.flag?this.usernameTips="":this.usernameTips=t.msg},isLogin:function(e){!0===e&&(this.loginTips="")}}},w={render:function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"login-wrap"},[r("img",{staticClass:"logo_1",attrs:{src:s("xReq")}}),e._v(" "),r("div",{staticClass:"input-wrap"},[r("img",{staticClass:"logo_2",attrs:{src:s("Nko+")}}),e._v(" "),r("div",{staticClass:"login_box"},[r("input",{staticStyle:{width:"0",height:"0",border:"none","line-height":"0"},attrs:{type:"text"}}),e._v(" "),r("input",{staticStyle:{width:"0",height:"0",border:"none","line-height":"0"},attrs:{type:"password"}}),e._v(" "),r("div",{staticClass:"username input-box"},[r("label",[e._v("用户名：")]),e._v(" "),r("input",{directives:[{name:"model",rawName:"v-model",value:e.username,expression:"username"}],attrs:{type:"text",placeholder:"请输入用户名",autocomplete:"off"},domProps:{value:e.username},on:{input:function(t){t.target.composing||(e.username=t.target.value)}}})]),e._v(" "),r("div",{staticClass:"password input-box"},[r("label",[e._v("密码：")]),e._v(" "),r("input",{directives:[{name:"model",rawName:"v-model",value:e.password,expression:"password"}],attrs:{type:"password",placeholder:"请输入密码",autocomplete:"new-password"},domProps:{value:e.password},on:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.login(t)},input:function(t){t.target.composing||(e.password=t.target.value)}}})]),e._v(" "),r("div",{staticClass:"login-box"},[r("el-button",{staticClass:"login-btn",attrs:{loading:e.isLogin},on:{click:e.login}},[e._v("登录")])],1)])])])},staticRenderFns:[]};var y={mounted:function(){this.loginSync()},methods:{loginSync:function(){var e=this,t=this.$loading({lock:!0,text:"正在同步用户信息，请稍候...",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.3)"});this.$ajax.get({url:"/api/apiWeb/security/auth/loginBySRRC"}).then(function(s){if(s.success){var r=s.data,a="";switch(parseInt(r.roleType)){case 0:a="admin";break;case 1:a=r.userType+"Provincial";break;case 2:a=r.userType+"City"}sessionStorage.setItem("token",r.token),sessionStorage.setItem("regionId",r.regionId),sessionStorage.setItem("roleType",a),"wuweiProvincial"==a||"admin"==a?e.$router.push({path:"/home"}):"wuweiCity"==a?e.$router.push({path:"/apply-table-management"}):e.$router.push({path:"/report/me"})}else e.loginTips=s.message,e.$message.error(s.message);t.close()})}}},b={render:function(){var e=this.$createElement;return(this._self._c||e)("div",{staticClass:"login-wrap"})},staticRenderFns:[]},x={data:function(){return{isWuwei:!0}},created:function(){var e="false";try{alert(this.$route.query.UserID),e=this.$route.query.UserID?"true":"false",alert(e)}catch(t){e="false"}alert(e),sessionStorage.setItem("isFromPlatform",e),this.isWuwei="true"==e},components:{LoginYYS:s("VU/8")(v,w,!1,function(e){s("xMLd")},"data-v-470ca81f",null).exports,LoginWuWei:s("VU/8")(y,b,!1,null,null,null).exports}},k={render:function(){var e=this.$createElement,t=this._self._c||e;return t("div",{staticClass:"main"},[this.isWuwei?t("LoginWuWei"):t("LoginYYS")],1)},staticRenderFns:[]};var _=s("VU/8")(x,k,!1,function(e){s("Jqt7")},"data-v-afee2246",null);t.default=_.exports},xMLd:function(e,t){},xReq:function(e,t,s){e.exports=s.p+"static/img/logo-1.2f2ee4f.png"}});