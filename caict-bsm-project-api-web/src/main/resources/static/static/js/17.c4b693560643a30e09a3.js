webpackJsonp([17],{"EK/4":function(e,t,a){"use strict";var s=a("Dd8w"),i=a.n(s),o={props:["activeName","data","value"],data:function(){return{form:i()({},this.data),formRules:this.$formCheck,chinese_3:{indexNum:"5G基站索引号",userName:"台站设置使用人",linkPerson:"联系人",phone:"联系电话",statAddr:"台址",longitude:"经度",latitude:"纬度",freqBandB:"频段下限",freqBandE:"频段上限",isDoor:"室内室外",cellId:"全球小区识别码",statStatus:"基站状态",createDate:"建站时间",licenseNo:"电台执照编号",isReform:"是否需要协调",reformStatName:"需要协调的台站名称",reformStatus:"是否完成协调"},chinese_4:{indexNum:"保护清单识别号",compnayName:"用户单位名称",longitude:"经度",latitude:"纬度",isReform:"是否需要协调",isInitiate:"是否已发起协调请求",reformConfirm:"是否已确认协调请求",reformStatus:"是否已完成协调",isRevoke:"是否撤站",revokeDate:"撤站时间",isRelocation:"是否迁址",relocationAddr:"迁址后地址",relocationLongitude:"迁址后经度",relocationLatitude:"迁址后纬度",relocationDate:"迁址时间",isAdjust:"是否调整频率",adjustFreqRfb:"调整后接收频率起",adjustFreqRfe:"调整后接收频率止",adjustDate:"调整时间"}}},methods:{formCheck:function(e){var t=!0;return this.$refs[e].validate(function(e){e?console.log("submit!!"):t=!1}),t},dialogClose:function(){this.$parent.selectTableItem={},this.$emit("input",!1)},submit:function(e){var t=this;if(this.formCheck(e))if(this.form.isRead)this.dialogClose();else{var a="";3==this.activeName?a="/api/apiWeb/station/twoTable/updateProcess":4==this.activeName&&(a="/api/apiWeb/station/twoTable/updateReform"),this.$ajax.post({url:a,data:this.form}).then(function(e){e.success&&(t.$message({message:e.message,type:"success"}),t.$emit("addComplate"),t.dialogClose())})}}}},n={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:"编辑详情",visible:e.value,"before-close":e.dialogClose,width:"70%"},on:{"update:visible":function(t){e.value=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.formRules,"label-width":"150px",disabled:e.form.isRead}},[3==e.activeName?e._l(e.chinese_3,function(t,s){return a("el-form-item",{key:s,attrs:{label:t+"：",prop:s,rules:e.formRules[s]}},[a("el-input",{attrs:{placeholder:"配置名称"},model:{value:e.form[s],callback:function(t){e.$set(e.form,s,t)},expression:"form[key]"}})],1)}):e._e(),e._v(" "),4==e.activeName?e._l(e.chinese_4,function(t,s){return a("el-form-item",{key:s,attrs:{label:t+"：",prop:s,rules:e.formRules[s]}},[a("el-input",{attrs:{placeholder:"配置名称"},model:{value:e.form[s],callback:function(t){e.$set(e.form,s,t)},expression:"form[key]"}})],1)}):e._e()],2),e._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:e.dialogClose}},[e._v("取 消")]),e._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.submit("form")}}},[e._v("确 定")])],1)],1)},staticRenderFns:[]};var r=a("VU/8")(o,n,!1,function(e){a("HABL")},"data-v-0722c4c8",null);t.a=r.exports},HABL:function(e,t){},TRA0:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var s=a("Dd8w"),i=a.n(s),o=a("2F/+"),n=a("99Ht"),r=a("EK/4"),l=new(a("oFuF").a),c={data:function(){return{uploadHeaders:{token:sessionStorage.getItem("token")},ajaxDomain:n.a.ajaxDomain,activeName:"1",form:{tableType:"A",page:1,rows:20,total:0},tableReload:!0,tableData:[],toChinese:{},loading:!0,isOpenDialog:!1,selectTableItem:{}}},watch:{activeName:function(e){var t=this;this.form=i()({},this.form,{page:1}),this.tableReload=!1,window.setTimeout(function(){t.tableReload=!0},0),this.getData()}},mounted:function(){this.getData()},methods:{setToChinese:function(){1==this.activeName||2==this.activeName?this.toChinese={idCode:"保护清单识别号",compName:"用户单位名称",linkPerson:"联系人",phone:"联系电话",statType:"地球站类型",statDetail:"地球站有效证",freqEfb:"发射频率下限",freqEfe:"发射频率上限",freqRfb:"接收频率下限",freqRfe:"接收频率上限",spaceName:"对应的空间无线电台名称",location:"位置",longitude:"经度",latitude:"纬度",statConfirm:"台站信息确认",confirmDate:"确认时间",verifier:"核验人",demo:"备注",tableType:"表格类型"}:3==this.activeName?this.toChinese={indexNum:"5G基站索引号",userName:"台站设置使用人",linkPerson:"联系人",phone:"联系电话",statAddr:"台址",longitude:"经度",latitude:"纬度",freqBandB:"频段下限",freqBandE:"频段上限",isDoor:"室内室外",cellId:"全球小区识别码",statStatus:"基站状态",createDate:"建站时间",licenseNo:"电台执照编号",isReform:"是否需要协调",reformStatName:"需要协调的台站名称",reformStatus:"是否完成协调"}:4==this.activeName&&(this.toChinese={indexNum:"保护清单识别号",compnayName:"用户单位名称",longitude:"经度",latitude:"纬度",isReform:"是否需要协调",isInitiate:"是否已发起协调请求",reformConfirm:"是否已确认协调请求",reformStatus:"是否已完成协调",isRevoke:"是否撤站",revokeDate:"撤站时间",isRelocation:"是否迁址",relocationAddr:"迁址后地址",relocationLongitude:"迁址后经度",relocationLatitude:"迁址后纬度",relocationDate:"迁址时间",isAdjust:"是否调整频率",adjustFreqRfb:"调整后接收频率起",adjustFreqRfe:"调整后接收频率止",adjustDate:"调整时间"})},getData:function(){var e=this;this.loading||(this.loading=!0),this.setToChinese();var t=this.activeName,a="";switch(parseInt(t)){case 1:this.form.tableType="A",a="/api/apiWeb/station/twoTable/findEarthByPage";break;case 2:this.form.tableType="B",a="/api/apiWeb/station/twoTable/findEarthByPage";break;case 3:a="/api/apiWeb/station/twoTable/findProcessByPage",this.form.tableType="";break;case 4:this.form.tableType="",a="/api/apiWeb/station/twoTable/findReformByPage"}this.$ajax.post({url:a,data:this.form}).then(function(t){if(e.loading=!1,t.success)return e.tableData=t.data.list,void(e.form.total=t.data.total);t.success||e.$message.error(t.message)},function(){return e.loading=!1})},currentRowView:function(e){this.$router.push({path:"/work/offices/detail",query:{appGuid:e.guid}})},editItem:function(e){this.selectTableItem=e,this.isOpenDialog=!0},handleCurrentChange:function(e){this.form.page=e,this.getData()},handleSizeChange:function(e){this.form.rows=e,this.getData()},uploadSuccess:function(e,t,a){e.success?(this.$message.success(e.message),this.getData()):this.$message.error(e.message)},exportModel:function(){var e=this;this.$ajax.get({url:"/api/apiWeb/station/twoTable/downLoadTemplate"}).then(function(t){t.success&&(l.funDownload(t.message),e.$message.success("下载完成，请注意查收！"))})}},components:{StationDetail:o.a,EditBox:r.a},filters:{statTypeFilter:function(e){switch(parseInt(e)){case 1:return"双向通信地球站";case 2:return"单收地球站";case 3:return"广播电视地球站";case 4:return"微波站";case 5:return"射电天文台";case 6:return"其他"}}}},u={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"content-template"},[a("div",{staticClass:"btn_box"},[a("el-upload",{staticClass:"import_data",attrs:{action:e.ajaxDomain+"/apiWeb/station/twoTable/uploadFile",headers:e.uploadHeaders,"on-success":e.uploadSuccess,"show-file-list":!1}},[a("el-button",{attrs:{size:"small",type:"primary"}},[e._v("导入两表一单数据")])],1),e._v(" "),a("el-button",{staticClass:"exportModel",attrs:{size:"small",type:"success"},on:{click:e.exportModel}},[e._v("导出两表一单模版")])],1),e._v(" "),a("el-tabs",{attrs:{"tab-position":"top"},model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[a("el-tab-pane",{attrs:{label:"查看单A数据",name:"1"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"查看单B数据",name:"2"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"查看表1 5G基站设置、使用进度",name:"3"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"查看表2 5G干扰协调完成情况进度",name:"4"}})],1),e._v(" "),a("div",{staticClass:"table-box"},[e.tableReload?a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"table-content",attrs:{height:"100%",data:e.tableData,stripe:""}},[e._l(e.toChinese,function(t,s){return a("el-table-column",{key:s,attrs:{prop:"gmtCreate",align:"center",label:t},scopedSlots:e._u([{key:"default",fn:function(t){return[/statType/.test(s)?[e._v(e._s(e._f("statTypeFilter")(t.row[s])))]:[e._v("\n            "+e._s(t.row[s])+"\n          ")]]}}],null,!0)})}),e._v(" "),/[34]/.test(e.activeName)?a("el-table-column",{attrs:{label:"操作",width:"100",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.editItem(t.row)}}},[e._v("編辑")])]}}],null,!1,3615442337)}):e._e()],2):e._e(),e._v(" "),a("div",{staticClass:"table-pagin"},[a("el-pagination",{attrs:{layout:"sizes, total, prev, pager, next, jumper","page-size":e.form.rows,"current-page":e.form.page,total:e.form.total},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1),e._v(" "),e.isOpenDialog?a("EditBox",{attrs:{activeName:e.activeName,data:e.selectTableItem},on:{addComplate:e.getData},model:{value:e.isOpenDialog,callback:function(t){e.isOpenDialog=t},expression:"isOpenDialog"}}):e._e()],1)],1)},staticRenderFns:[]};var d=a("VU/8")(c,u,!1,function(e){a("Zn/i")},"data-v-ed7fddfe",null);t.default=d.exports},"Zn/i":function(e,t){}});