webpackJsonp([24],{Rljh:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=a("Dd8w"),i=a.n(n),s=a("qNvg"),l=new(a("oFuF").a),o={data:function(){return{roleType:sessionStorage.getItem("roleType"),licenseData:[],licenseStates:[{label:"已发",value:1},{label:"停用",value:3},{label:"即将过期",value:5},{label:"过期",value:2},{label:"注销",value:4}],stationId:"",loading:!0,dialogVisible:!1,selectionList:[],disabledBtn1:!1,disabledBtn2:!1,pageTotal:0,isAdvancedSearch:!1,licensePageDTO:{applytableCode:"",currentUserGuid:"",expirationDate:"",licenseCode:"",licenseState:"",stationCode:"",stationName:"",page:1,rows:50},licenseDTO:{applytableCode:"",currentUserGuid:"",expirationDate:0,licenseCode:"",licenseState:0,stationCode:"",stationGuIdList:[],stationName:"string"}}},created:function(){this.getData()},methods:{getData:function(){var e=this,t=this.$route.query.state;t&&(this.licensePageDTO.licenseState=Number(t)),this.loading||(this.loading=!0),this.$ajax.post({url:"/api/apiWeb/license/license/findAllByWhere",data:this.licensePageDTO}).then(function(t){e.loading=!1;try{if(t.success){var a=[t.data.list,t.data.total];e.licenseData=a[0],e.pageTotal=a[1]}else e.$message.error(t.message)}catch(e){}},function(){return e.loading=!1})},getApplicationCode:function(e){if(""===e||null===e)return"";var t=e.indexOf("/");return e.substring(0,t)},selectChange:function(e){this.selectionList=e},selectAll:function(e){this.selectionList=e},uploadAll:function(){var e=this;0!==this.pageTotal?this.$confirm("您将下载"+this.pageTotal+"个文件, 点击确定后即可下载并且不可取消， 是否继续?","友情提示：",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){e.disabledBtn1=!0;var t=e.$notify.warning({title:"温馨提示：",message:"由于本次过程比较漫长请您耐心等待，您也可以浏览其他。正在打包中，请稍后......",duration:0}),a={applytableCode:e.licensePageDTO.applytableCode,licenseCode:e.licensePageDTO.licenseCode,stationCode:e.licensePageDTO.stationCode,stationGuIdList:[],stationName:e.licensePageDTO.stationName};e.$ajax.post({url:"/api/license/download/all",isDownload:!0,data:a}).then(function(a){if(e.disabledBtn1=!1,t.close(),a)if(!0===a.status){var n=a.detail.value;l.funDownload(n),e.$message.success("下载完成，请注意查收！")}else e.$message.success(a.reason)},function(a){t.close(),e.disabledBtn1=!1})}):this.$message.warning("无可下载执照")},uploadCheck:function(){var e=this,t=this.selectionList.length;return 0===t?this.$message.warning("请勾选您要下载的文件"):t>200?this.$message.warning("非常抱歉，下载文件不能超过200条！"):void this.$confirm("您将下载"+t+"个文件,点击确定后即可下载并且不可取消，是否继续?","友情提示：",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){e.disabledBtn2=!0;var t=e.$notify.warning({title:"温馨提示：",message:"由于本次过程比较漫长请您耐心等待，您也可以浏览其他。正在打包中，请稍后......",duration:0}),a=e.selectionList.map(function(e){return e.stationGuid});e.$ajax.post({url:"/api/apiWeb/license/license/downloadLicenses",isDownload:!0,data:a}).then(function(a){e.disabledBtn2=!1,t.close();try{if(a.success){var n=a.data;l.funDownload(n),e.$message.success("打包完成，请注意查收！")}else e.$message.error(a.message)}catch(e){}},function(a){t.close(),e.disabledBtn2=!1})})},searchFun:function(){this.licensePageDTO.page=1,this.getData()},selectChangeHandler:function(){this.licensePageDTO.page=1,this.getData()},toViewDetailHandler:function(e){e.stationGuid?(this.stationId=e.stationGuid,this.dialogVisible=!0):this.$message.error("当前数据有误，请重新选择！")},handleCurrentChange:function(e){this.licensePageDTO.page=e,this.getData()},handleSizeChange:function(e){this.licensePageDTO.page=1,this.licensePageDTO.rows=e,this.getData()},openAdvancedSearch:function(){this.isAdvancedSearch=!this.isAdvancedSearch,this.licensePageDTO=i()({},this.licensePageDTO,{applytableCode:"",licenseState:"",stationCode:""})}},components:{ElicenseDetail:s.a},filters:{licenseStateFilter:function(e){switch(e){case 1:return"已发";case 3:return"停用";case 2:return"过期";case 4:return"注销";case 5:return"即将过期";default:return"异常"}}}},c={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"content-template"},[a("div",{staticClass:"search-box"},[a("el-form",{staticClass:"search-form",attrs:{inline:!0,"label-position":"left"}},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{xs:16,sm:12,lg:8}},[a("el-form-item",{attrs:{label:"执照编号：","label-width":"120px"}},[a("el-input",{attrs:{clearable:""},model:{value:e.licensePageDTO.licenseCode,callback:function(t){e.$set(e.licensePageDTO,"licenseCode",t)},expression:"licensePageDTO.licenseCode"}})],1)],1),e._v(" "),a("el-col",{attrs:{xs:16,sm:12,lg:8}},[a("el-form-item",{attrs:{label:"基站名称："}},[a("el-input",{attrs:{clearable:""},model:{value:e.licensePageDTO.stationName,callback:function(t){e.$set(e.licensePageDTO,"stationName",t)},expression:"licensePageDTO.stationName"}})],1)],1),e._v(" "),a("el-col",{attrs:{xs:16,sm:12,lg:8}},[a("el-form-item",{directives:[{name:"show",rawName:"v-show",value:e.isAdvancedSearch,expression:"isAdvancedSearch"}],attrs:{label:"申请表编号：","label-width":"120px"}},[a("el-input",{attrs:{clearable:""},model:{value:e.licensePageDTO.applytableCode,callback:function(t){e.$set(e.licensePageDTO,"applytableCode",t)},expression:"licensePageDTO.applytableCode"}})],1),e._v(" "),a("el-form-item",{directives:[{name:"show",rawName:"v-show",value:!e.isAdvancedSearch,expression:"!isAdvancedSearch"}]},[a("el-button",{staticClass:"default-button",attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.searchFun}},[e._v("查询")]),e._v(" "),a("el-button",{attrs:{type:"text"},on:{click:e.openAdvancedSearch}},[e._v("\n              展开高级搜索 \n              "),a("span",{staticClass:"el-icon-arrow-down form-item-icon"})])],1)],1)],1),e._v(" "),a("el-row",{directives:[{name:"show",rawName:"v-show",value:e.isAdvancedSearch,expression:"isAdvancedSearch"}],attrs:{gutter:20}},[a("el-col",{attrs:{xs:16,sm:12,lg:8}},[a("el-form-item",{attrs:{label:"基站识别码：","label-width":"120px"}},[a("el-input",{attrs:{clearable:""},model:{value:e.licensePageDTO.stationCode,callback:function(t){e.$set(e.licensePageDTO,"stationCode",t)},expression:"licensePageDTO.stationCode"}})],1)],1),e._v(" "),a("el-col",{attrs:{xs:16,sm:12,lg:8}},[a("el-form-item",{attrs:{label:"执照状态："}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择"},on:{change:e.selectChangeHandler},model:{value:e.licensePageDTO.licenseState,callback:function(t){e.$set(e.licensePageDTO,"licenseState",t)},expression:"licensePageDTO.licenseState"}},e._l(e.licenseStates,function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}),1)],1)],1),e._v(" "),a("el-col",{attrs:{xs:16,sm:12,lg:8}},[a("el-button",{staticClass:"default-button",attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.searchFun}},[e._v("查询")]),e._v(" "),a("el-button",{attrs:{type:"text"},on:{click:e.openAdvancedSearch}},[e._v("\n            隐藏高级搜索 \n            "),a("span",{staticClass:"el-icon-arrow-up form-item-icon"})])],1)],1)],1)],1),e._v(" "),a("div",{staticClass:"operate-box"},[a("span",{staticClass:"operate-table-title"},[e._v("执照库")]),e._v(" "),a("el-button",{attrs:{type:"primary",disabled:e.disabledBtn2,icon:"el-icon-check"},on:{click:e.uploadCheck}},[e._v("下载勾选")])],1),e._v(" "),a("div",{staticClass:"table-box"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"table-content",attrs:{height:"100%",fit:"",stripe:"",data:e.licenseData},on:{"selection-change":e.selectChange,"select-all":e.selectAll}},[a("el-table-column",{attrs:{type:"selection",align:"center"}}),e._v(" "),a("el-table-column",{attrs:{prop:"licenseCode",align:"center",label:"执照编号",width:"180px"}}),e._v(" "),a("el-table-column",{attrs:{prop:"stationName",align:"center",label:"基站名称","show-overflow-tooltip":!0}}),e._v(" "),a("el-table-column",{attrs:{prop:"stationCode",align:"center",label:"基站识别码","show-overflow-tooltip":!0}}),e._v(" "),a("el-table-column",{attrs:{prop:"netType",align:"center",label:"技术体制","show-overflow-tooltip":!0}}),e._v(" "),a("el-table-column",{attrs:{prop:"licenseState",align:"center",label:"执照状态",width:"80px","show-overflow-tooltip":!0},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(e._f("licenseStateFilter")(t.row.licenseState)))]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"location",align:"center",label:"台址","show-overflow-tooltip":!0}}),e._v(" "),a("el-table-column",{attrs:{label:"操作",width:"150",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.toViewDetailHandler(t.row)}}},[e._v("查看")]),e._v(" "),/wuwei/.test(e.roleType)?e._e():a("el-button",{attrs:{type:"text"}},[e._v("执照延期")])]}}])})],1),e._v(" "),a("div",{staticClass:"table-pagin"},[a("el-pagination",{attrs:{layout:"sizes, total, prev, pager, next, jumper","page-sizes":[50,100,200],"current-page":e.licensePageDTO.page,total:e.pageTotal},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1),e._v(" "),a("el-dialog",{attrs:{title:"电子执照详情",visible:e.dialogVisible,width:"980px"},on:{"update:visible":function(t){e.dialogVisible=t}}},[a("elicense-detail",{attrs:{"station-id":e.stationId}})],1)],1)},staticRenderFns:[]},r=a("VU/8")(o,c,!1,null,null,null);t.default=r.exports},qNvg:function(e,t,a){"use strict";var n=a("nKpR"),i=a.n(n),s=a("99Ht"),l=new(a("oFuF").a);i.a.GlobalWorkerOptions.workerSrc="pdfjs-dist/build/pdf.worker";var o=null,c=null,r=!1,d=null,u=null,g=null,p={props:{stationId:{default:"",required:!0}},data:function(){return{roleType:sessionStorage.getItem("roleType"),BaseURL:s.a,licenseUrl:"",totalPage:1,numPage:1,prevBtn:!0,nextBtn:!0,loading:!1,printing:!1}},created:function(){var e=this;this.$nextTick(function(){o=null,c=null,r=!1,d=null,u=null,g=null,e.initData(e.numPage)})},beforeDestroy:function(){document.getElementById("printIframe")&&document.body.removeChild(document.getElementById("printIframe"))},methods:{initData:function(e){var t=this;c=null,r=!1,d=null,u=this.$refs.canvas,(g=u.getContext("2d")).save(),g.setTransform(1,0,0,1,0,0),g.clearRect(0,0,u.width,u.height),g.restore();var a=this.stationId;a&&(this.loading||(this.loading=!0),this.$ajax.get({url:"/api/apiWeb/license/license/printLicense/"+a+"/template"}).then(function(a){try{if(a.success){var n=a.data;t.licenseUrl=n,t.initPage(u,g,n,e,o)}else t.$message.error(a.message),t.loading=!1}catch(e){console.err(e),t.loading=!1}},function(){t.loading=!1}))},initPage:function(e,t,a,n){var l=arguments,r=this;i.a.getDocument({url:s.a.ajaxDomain+"/"+a,cMapUrl:"/static/fonts/cmaps/",cMapPacked:!0}).promise.then(function(a){c=5==l.length?o||(o=a):a,r.loading=!1,a.numPages<=0||(r.totalPage=a.numPages,r.renderPage(e,t,n))})},renderPage:function(e,t,a){var n=this;r=!0,c.getPage(a).then(function(a){var i=a.getViewport({scale:1.5});e.height=i.height,e.width=i.width,e.style.width=i.width,e.style.height=i.height,t.imageSmoothingQuality="high";var s={transform:[1,0,0,1,0,0],canvasContext:t,viewport:i,background:"#fff"};a.render(s).promise.then(function(){r=!1,null!==d&&(n.renderPage(e,t,d),d=null)})})},queueRenderPage:function(e){r?d=e:this.renderPage(u,g,e,1)},onPrevPage:function(){this.numPage<=1||(this.numPage--,this.queueRenderPage(this.numPage))},onNextPage:function(){this.numPage>=this.totalPage||(this.numPage++,this.queueRenderPage(this.numPage))},downLicense:function(){var e=this;""!==this.licenseUrl?this.$ajax.post({url:"/api/apiWeb/license/license/downloadLicenses",isDownload:!0,data:[this.stationId]}).then(function(t){e.disabledBtn2=!1;try{if(t.success){var a=t.data;l.funDownload(a),e.$message.success("正在下载，请注意查收！")}else e.$message.error(t.message)}catch(e){}},function(t){notify.close(),e.disabledBtn2=!1}):this.$message.error("非常抱歉，暂无相关执照！")},printdiv:function(){document.getElementById("printIframe")&&document.body.removeChild(document.getElementById("printIframe")),this.$ajax.post({url:"/api/apiWeb/license/license/printTest",responseType:"blob",data:{page:1,stationGuid:this.stationId}}).then(function(e){var t=new Blob([e],{type:"application/pdf"}),a=document.createElement("iframe");a.width=0,a.height=0,a.id="printIframe",document.body.appendChild(a),a.src=window.URL.createObjectURL(t);var n=setTimeout(function(){window.clearTimeout(n),a.contentWindow.print()},1e3)})}}},h={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"content-template",attrs:{"element-loading-text":"拼命加载中..."}},[a("div",{staticClass:"operate-box"},[a("span",{staticClass:"operate-table-title"},[e._v("执照预览")]),e._v(" "),/wuwei/.test(e.roleType)?[a("el-button",{staticClass:"default-button",attrs:{type:"primary",icon:"el-icon-download"},on:{click:e.downLicense}},[e._v("下载执照")]),e._v(" "),a("el-button",{staticClass:"default-button",attrs:{type:"primary",icon:"el-icon-printer",loading:e.printing},on:{click:e.printdiv}},[e._v("打印执照")])]:e._e(),e._v(" "),a("el-button",{staticClass:"default-button",attrs:{type:"success",icon:"el-icon-caret-left"},on:{click:e.onPrevPage}},[e._v("上一页")]),e._v(" "),a("el-button",{staticClass:"default-button",attrs:{type:"success",icon:"el-icon-caret-right"},on:{click:e.onNextPage}},[e._v("下一页")]),e._v(" "),a("span",{staticClass:"total-page"},[e._v("第 "+e._s(e.numPage)+"/"+e._s(e.totalPage)+" 页")])],2),e._v(" "),a("div",{staticClass:"pdf-container"},[a("div",{staticStyle:{display:"none"},attrs:{id:"printBox"}},[a("canvas",{ref:"printCanvas",staticClass:"print-canvas",attrs:{id:"printCanvas",height:"300",width:"300"}})]),e._v(" "),a("canvas",{ref:"canvas",staticClass:"pdf-canvas",attrs:{id:"canvas",height:"1262",width:"892"}})])])},staticRenderFns:[]};var v=a("VU/8")(p,h,!1,function(e){a("s/IS")},"data-v-bffb684a",null);t.a=v.exports},"s/IS":function(e,t){}});