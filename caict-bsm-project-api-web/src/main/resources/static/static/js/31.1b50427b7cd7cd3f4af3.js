webpackJsonp([31],{"3omw":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=a("Dd8w"),r=a.n(o),s=a("Xlkb"),l=a("L6bb"),i=a.n(l),n={props:["data","value"],data:function(){var e=this;return{form:r()({img:"",name:"",loginName:"",password:"",sex:"男",age:"",state:"0",type:"",mobile:"",qq:"",wechat:"",email:"",regionId:sessionStorage.getItem("regionId")},this.data),formRules:this.$formCheck,organForm:{organList:{lazy:!0,expandTrigger:"click",lazyLoad:function(t,a){t.level;console.log(11,t,t.isLeaf),t.isLeaf?a(t):e.$ajax.post({url:"/api/apiWeb/security/org/findAllPage",data:{page:1,rows:1e3}}).then(function(t){var o=t.data.list.map(function(e){return{value:e.guid,label:e.orgName,leaf:!0}});console.log(213,e),e.organForm.organizationId=e.form.rsbtOrgDTO?e.form.rsbtOrgDTO.guid:"",a(o)})}},organizationId:""},organizationShow:!1,roleForm:{roleList:[],roleId:""},roleShow:!1}},methods:{handleAvatarSuccess:function(e,t){this.imageUrl=URL.createObjectURL(t.raw)},beforeAvatarUpload:function(e){var t="image/jpeg"===e.type,a=e.size/1024/1024<2;return t||this.$message.error("上传头像图片只能是 JPG 格式!"),a||this.$message.error("上传头像图片大小不能超过 2MB!"),t&&a},formCheck:function(e){var t=!0;return this.$refs[e].validate(function(e){e?console.log("submit!!"):t=!1}),t},bindOrgan:function(){var e=this;this.$ajax.get({url:"/api/apiWeb/security/users/setUsersOrg/"+this.organForm.organizationId+"/"+this.form.userId}).then(function(t){e.$message({message:t.message,type:"success"}),e.organizationShow=!1})},organizationOpen:function(){this.organizationShow=!0},getRoleList:function(){var e=this;this.$ajax.get({url:"/api/apiWeb/security/role/findAll"}).then(function(t){e.roleForm.roleList=t.data,e.roleForm.roleId=e.form.roleDTO?e.form.roleDTO.id:""})},bindRole:function(){var e=this;this.$ajax.get({url:"/api/apiWeb/security/users/setUsersRole/"+this.roleForm.roleId+"/"+this.form.userId}).then(function(t){e.$message({message:t.message,type:"success"}),e.roleShow=!1})},roleOpen:function(){this.roleShow=!0,this.getRoleList()},dialogClose:function(){this.$parent.selectTableItem={},this.$emit("input",!1)},submit:function(e){var t=this;if(this.formCheck(e))if(this.form.isRead)this.dialogClose();else{var a="";this.form.userId?(a="/api/apiWeb/security/users/save",this.form.id=this.form.userId):(a="/api/apiWeb/security/users/register",this.form.password=i()(this.form.password));var o=r()({},this.form);this.$ajax.post({url:a,data:o}).then(function(e){e&&(t.$message({message:e.message,type:"success"}),t.$emit("addComplate"),t.dialogClose())})}}}},u={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:"编辑详情",visible:e.value,"before-close":e.dialogClose,width:"70%"},on:{"update:visible":function(t){e.value=t}}},[a("el-form",{ref:"form",staticStyle:{height:"500px",overflow:"auto"},attrs:{model:e.form,rules:e.formRules,"label-width":"150px",disabled:e.form.isRead}},[a("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"头像",prop:"img"}},[a("el-upload",{staticClass:"avatar-uploader",attrs:{action:"https://jsonplaceholder.typicode.com/posts/","show-file-list":!1,"on-success":e.handleAvatarSuccess,"before-upload":e.beforeAvatarUpload}},[e.form.img?a("img",{staticClass:"avatar",attrs:{src:e.form.img}}):a("i",{staticClass:"el-icon-plus avatar-uploader-icon"})])],1),e._v(" "),a("el-form-item",{attrs:{label:"用户姓名",prop:"name",rules:e.formRules.userName}},[a("el-input",{attrs:{placeholder:"用户姓名"},model:{value:e.form.name,callback:function(t){e.$set(e.form,"name",t)},expression:"form.name"}})],1),e._v(" "),!e.form.userId||e.form.isRead?a("el-form-item",{attrs:{label:"登录名",prop:"loginName",rules:e.formRules.loginName}},[a("el-input",{attrs:{placeholder:"登录名"},model:{value:e.form.loginName,callback:function(t){e.$set(e.form,"loginName",t)},expression:"form.loginName"}})],1):e._e(),e._v(" "),e.form.userId?e._e():a("el-form-item",{attrs:{label:"用户密码",prop:"password",rules:e.formRules.loginPassword}},[a("el-input",{attrs:{placeholder:"用户密码"},model:{value:e.form.password,callback:function(t){e.$set(e.form,"password",t)},expression:"form.password"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"用户性别",prop:"sex"}},[a("el-radio-group",{model:{value:e.form.sex,callback:function(t){e.$set(e.form,"sex",t)},expression:"form.sex"}},[a("el-radio",{attrs:{label:"男"}},[e._v("男")]),e._v(" "),a("el-radio",{attrs:{label:"女"}},[e._v("女")])],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"用户年龄",prop:"age"}},[a("el-input",{attrs:{placeholder:"用户年龄"},model:{value:e.form.age,callback:function(t){e.$set(e.form,"age",t)},expression:"form.age"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"状态",prop:"state"}},[a("el-radio-group",{model:{value:e.form.state,callback:function(t){e.$set(e.form,"state",t)},expression:"form.state"}},[a("el-radio-button",{attrs:{label:"0"}},[e._v("启用")]),e._v(" "),a("el-radio-button",{attrs:{label:"1"}},[e._v("禁用")])],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"用户类型",prop:"type",rules:e.formRules.userType}},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:e.form.type,callback:function(t){e.$set(e.form,"type",t)},expression:"form.type"}},[a("el-option",{attrs:{label:"无委",value:"wuwei"}}),e._v(" "),a("el-option",{attrs:{label:"移动",value:"mobile"}}),e._v(" "),a("el-option",{attrs:{label:"电信",value:"telecom"}}),e._v(" "),a("el-option",{attrs:{label:"联通",value:"unicom"}}),e._v(" "),a("el-option",{attrs:{label:"铁路",value:"railway"}}),e._v(" "),a("el-option",{attrs:{label:"广电",value:"guangdian"}})],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"用户电话",prop:"mobile"}},[a("el-input",{attrs:{placeholder:"用户电话"},model:{value:e.form.mobile,callback:function(t){e.$set(e.form,"mobile",t)},expression:"form.mobile"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"QQ号",prop:"qq"}},[a("el-input",{attrs:{placeholder:"QQ号"},model:{value:e.form.qq,callback:function(t){e.$set(e.form,"qq",t)},expression:"form.qq"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"微信",prop:"wechat"}},[a("el-input",{attrs:{placeholder:"微信"},model:{value:e.form.wechat,callback:function(t){e.$set(e.form,"wechat",t)},expression:"form.wechat"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"邮箱",prop:"email"}},[a("el-input",{attrs:{placeholder:"邮箱"},model:{value:e.form.email,callback:function(t){e.$set(e.form,"email",t)},expression:"form.email"}})],1)],1),e._v(" "),a("el-dialog",{attrs:{width:"50%",title:"绑定组织机构",visible:e.organizationShow,"append-to-body":""},on:{"update:visible":function(t){e.organizationShow=t}}},[a("el-form",{ref:"organForm",staticStyle:{height:"100px",overflow:"auto"},attrs:{model:e.organForm,rules:e.formRules,"label-width":"150px"}},[a("el-form-item",{attrs:{label:"组织机构"}},[a("el-cascader",{attrs:{props:Object.assign({},{checkStrictly:!0,emitPath:!1},e.organForm.organList)},model:{value:e.organForm.organizationId,callback:function(t){e.$set(e.organForm,"organizationId",t)},expression:"organForm.organizationId"}})],1)],1),e._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"success"},on:{click:function(t){return e.bindOrgan("organForm")}}},[e._v("绑定")])],1)],1),e._v(" "),a("el-dialog",{attrs:{width:"50%",title:"绑定角色",visible:e.roleShow,"append-to-body":""},on:{"update:visible":function(t){e.roleShow=t}}},[a("el-form",{ref:"roleForm",staticStyle:{height:"100px",overflow:"auto"},attrs:{model:e.roleForm,rules:e.formRules,"label-width":"150px"}},[a("el-form-item",{attrs:{label:"绑定角色",prop:"roleId"}},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:e.roleForm.roleId,callback:function(t){e.$set(e.roleForm,"roleId",t)},expression:"roleForm.roleId"}},e._l(e.roleForm.roleList,function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})}),1)],1)],1),e._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"success"},on:{click:function(t){return e.bindRole("organForm")}}},[e._v("绑定")])],1)],1),e._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:e.dialogClose}},[e._v("取 消")]),e._v(" "),a("el-button",{attrs:{type:"success"},on:{click:e.organizationOpen}},[e._v("绑定组织机构")]),e._v(" "),a("el-button",{attrs:{type:"warning"},on:{click:e.roleOpen}},[e._v("绑定角色")]),e._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.submit("form")}}},[e._v("确 定")])],1)],1)},staticRenderFns:[]};var m=a("VU/8")(n,u,!1,function(e){a("GLKW")},"data-v-5fb73a84",null).exports,c={data:function(){return{form:{companyCode:this.$route.query.companyCode||"",companyName:this.$route.query.companyName||"",companyType:this.$route.query.companyType||""},formRules:this.$formCheck,tableData:[],chinese:{name:"姓名",sex:"性别",age:"年龄",email:"邮箱",qq:"qq号",mobile:"电话",roleName:"角色",orgName:"组织机构"},page:parseInt(this.$route.query.page)||1,sumPage:parseInt(this.$route.query.sumPage)||1,rows:10,selectTableItem:{},delAllSelectItem:[],isOpenDialog:!1,delDialog:!1}},watch:{$route:function(){this.pageEvent()}},mounted:function(){this.pageEvent()},components:{MyTable:s.a,EditBox:m},methods:{pageEvent:function(){this.getData()},getData:function(){var e=this;this.tableData=[],this.$ajax.post({url:"/api/apiWeb/security/users/findAllWherePage",data:r()({rows:this.rows,page:this.page},this.$route.query)}).then(function(t){t.success&&(t=t.data,e.sumPage=Math.ceil(t.total/e.rows),0==t.list.length&&(e.$refs.table.loading=!1),t.list.map(function(t){var a=new Date(t.setDate);t.setDate=a.getFullYear()+"-"+(a.getMonth()+1)+"-"+a.getDate(),e.tableData.push(t)}))})},formCheck:function(e){var t=!0;return this.$refs[e].validate(function(e){e?console.log("submit!!"):t=!1}),t},onQuery:function(e){if(!this.form.spectrumStart&&!this.form.spectrumEnd||this.formCheck(e)){var t=r()({},this.$route.query,this.form,{page:1,sumPage:1,r:parseInt(1e5*Math.random())});this.$router.push({query:t})}},clearQuery:function(e){for(var t in this.form)this.form[t]="";var a=r()({},this.$route.query,this.form,{page:1,sumPage:1,r:parseInt(1e5*Math.random())});this.$router.push({query:a})},add:function(e){this.isOpenDialog=!0},selectPage:function(e){var t=r()({},this.$route.query,{page:e,sumPage:this.sumPage,r:parseInt(1e5*Math.random())});this.$router.push({query:t})},editItem:function(e){var t=this;this.$ajax.get({url:"/api/apiWeb/security/users/findOne/"+e.id}).then(function(e){e.data.isRead=!1,t.selectTableItem=e.data,t.isOpenDialog=!0})},delItem:function(e){var t=this;this.$ajax.delete({url:"/api/apiWeb/security/users/deleteUsers/"+e.id}).then(function(e){if(t.$message({message:e.message,type:"success"}),1==t.tableData.length){1==t.sumPage?t.sumPage:t.sumPage--,t.page>t.sumPage&&(t.page=t.sumPage);var a=r()({},t.$route.query,{page:t.page,sumPage:t.sumPage},t.form,{r:parseInt(1e5*Math.random())});t.$router.push({query:a})}else t.getData()})},readItem:function(e){var t=this;this.$ajax.get({url:"/api/apiWeb/security/users/findOne/"+e.id}).then(function(e){e.data.isRead=!0,t.selectTableItem=e.data,t.isOpenDialog=!0})},allSelect:function(e){this.delAllSelectItem=e},allDel:function(){0!=this.delAllSelectItem.length?this.delDialog=!0:this.$message.error("您还没有选择什么数据")},closeDialog:function(){this.delDialog=!1},allDelSure:function(){var e=this;this.closeDialog();var t=this.delAllSelectItem.map(function(e){return e.id});this.$ajax.post({url:"/api/apiWeb/security/users/deletesUsers",data:t}).then(function(a){if(a)if(e.$message({message:a.message,type:"success"}),e.tableData.length==t.length){1==e.sumPage?e.sumPage:e.sumPage--,e.page>e.sumPage&&(e.page=e.sumPage);var o=r()({},e.$route.query,{page:e.page,sumPage:e.sumPage},e.form,{r:parseInt(1e5*Math.random())});e.$router.push({query:o})}else e.getData()})}}},p={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"division_management"},[a("div",{staticClass:"btn_group"},[a("el-button",{attrs:{type:"primary",icon:"el-icon-document-add"},on:{click:function(t){return e.add("add")}}},[e._v("新增")]),e._v(" "),a("el-button",{attrs:{type:"danger",icon:"el-icon-delete"},on:{click:e.allDel}},[e._v("批量删除")])],1),e._v(" "),a("div",{staticClass:"table_box"},[a("MyTable",{ref:"table",attrs:{data:e.tableData,currentPage:e.page,sumPage:e.sumPage,chinese:e.chinese},on:{pageEvent:e.selectPage,editItem:e.editItem,delItem:e.delItem,readItem:e.readItem,allSelect:e.allSelect}})],1),e._v(" "),e.isOpenDialog?a("EditBox",{attrs:{data:e.selectTableItem},on:{addComplate:e.getData},model:{value:e.isOpenDialog,callback:function(t){e.isOpenDialog=t},expression:"isOpenDialog"}}):e._e(),e._v(" "),a("el-dialog",{attrs:{title:"提示",visible:e.delDialog,width:"30%","before-close":e.closeDialog},on:{"update:visible":function(t){e.delDialog=t}}},[a("span",[e._v("是否要删除所选")]),e._v(" "),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:e.closeDialog}},[e._v("取 消")]),e._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:e.allDelSure}},[e._v("确 定")])],1)])],1)},staticRenderFns:[]};var f=a("VU/8")(c,p,!1,function(e){a("LLvA")},"data-v-48f1fe5f",null);t.default=f.exports},GLKW:function(e,t){},LLvA:function(e,t){}});