webpackJsonp([31],{bstG:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=a("mvHQ"),o=a.n(r),s=a("Dd8w"),i=a.n(s),l=a("Xlkb"),n={props:["data","value"],data:function(){return{form:i()({code:"",name:"",standardCode:"",sort:1,type:this.$route.query.type?this.$route.query.type:"",parentId:this.$route.query.parentId||0,dictionaryLevel:parseInt(this.$route.query.dictionaryLevel)||1,status:0,remark:""},this.data),formRules:this.$formCheck}},methods:{formCheck:function(e){var t=!0;return this.$refs[e].validate(function(e){e?console.log("submit!!"):t=!1}),t},dialogClose:function(){this.$parent.selectTableItem={},this.$emit("input",!1)},submit:function(e){var t=this;this.formCheck(e)&&(this.form.isRead?this.dialogClose():this.$ajax.post({url:"/api/apiWeb/dictionary/dictionary/save",data:this.form}).then(function(e){e&&(t.$message({message:e.message,type:"success"}),t.$emit("addComplate"),t.dialogClose())}))}}},u={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:"编辑详情",visible:e.value,"before-close":e.dialogClose,width:"70%"},on:{"update:visible":function(t){e.value=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.formRules,"label-width":"120px",disabled:e.form.isRead}},[a("el-form-item",{attrs:{label:"编号",prop:"code"}},[a("el-input",{attrs:{placeholder:"编号"},model:{value:e.form.code,callback:function(t){e.$set(e.form,"code",t)},expression:"form.code"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"名称",prop:"name"}},[a("el-input",{attrs:{placeholder:"名称"},model:{value:e.form.name,callback:function(t){e.$set(e.form,"name",t)},expression:"form.name"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"标准编号",prop:"standardCode"}},[a("el-input",{attrs:{placeholder:"标准编号"},model:{value:e.form.standardCode,callback:function(t){e.$set(e.form,"standardCode",t)},expression:"form.standardCode"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"权重",prop:"sort"}},[a("el-input",{attrs:{placeholder:"权重"},model:{value:e.form.sort,callback:function(t){e.$set(e.form,"sort",e._n(t))},expression:"form.sort"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"类型",prop:"type"}},[a("el-radio-group",{model:{value:e.form.type,callback:function(t){e.$set(e.form,"type",t)},expression:"form.type"}},[this.$route.query.type&&1!=this.$route.query.type?e._e():a("el-radio",{attrs:{label:"1"}},[e._v("系统类型")]),e._v(" "),this.$route.query.type&&2!=this.$route.query.type?e._e():a("el-radio",{attrs:{label:"2"}},[e._v("业务类型")])],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"备注"}},[a("el-input",{attrs:{placeholder:"备注"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"状态"}},[a("el-radio-group",{model:{value:e.form.status,callback:function(t){e.$set(e.form,"status",t)},expression:"form.status"}},[a("el-radio-button",{attrs:{label:0}},[e._v("启用")]),e._v(" "),a("el-radio-button",{attrs:{label:1}},[e._v("禁用")])],1)],1)],1),e._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:e.dialogClose}},[e._v("取 消")]),e._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.submit("form")}}},[e._v("确 定")])],1)],1)},staticRenderFns:[]};var c=a("VU/8")(n,u,!1,function(e){a("fBot")},"data-v-4366872e",null).exports,d={data:function(){return{typeList:[{value:"1",label:"主要业务"},{value:"2",label:"次要业务"}],form:{code:this.$route.query.code||"",name:this.$route.query.name||"",parentId:this.$route.query.parentId||"",dictionaryLevel:this.$route.query.dictionaryLevel||""},formRules:this.$formCheck,breadcrumb:[],tableData:[],chinese:{code:"编号",name:"名称",sort:"权重",type:"类型",remark:"备注",updateDate:"保存时间",dictionaryLevel:"数据级别"},page:parseInt(this.$route.query.page)||1,sumPage:parseInt(this.$route.query.sumPage)||1,rows:10,selectTableItem:{},delAllSelectItem:[],isOpenDialog:!1,delDialog:!1}},watch:{$route:function(){this.pageEvent()}},mounted:function(){sessionStorage.getItem("breadcrumb")&&this.$route.parentId?this.breadcrumb=JSON.parse(sessionStorage.getItem("breadcrumb")):(sessionStorage.removeItem("breadcrumb"),this.breadcrumb=[{id:0,name:"首页"}]),this.pageEvent()},components:{MyTable:l.a,EditBox:c},methods:{initForm:function(){this.form={code:"",name:"",parentId:"",dictionaryLevel:""}},pageEvent:function(){this.getData()},getData:function(){var e=this;this.tableData=[],this.$ajax.post({url:"/api/apiWeb/dictionary/dictionary/findAllPage",data:i()({rows:this.rows,page:this.page,parentId:this.$route.query.parentId||0},this.$route.query)}).then(function(t){t&&(t=t.data,e.sumPage=t.pages,0==t.list.length&&(e.$refs.table.loading=!1),t.list.map(function(t){e.tableData.push(t)}))})},formCheck:function(e){var t=!0;return this.$refs[e].validate(function(e){e?console.log("submit!!"):t=!1}),t},onQuery:function(e){if(!this.form.spectrumStart&&!this.form.spectrumEnd||this.formCheck(e)){var t=i()({},this.$route.query,{page:1,sumPage:1},this.form,{r:parseInt(1e5*Math.random())});this.$router.push({query:t})}},add:function(e){this.selectTableItem.add=1,this.isOpenDialog=!0},selectPage:function(e){var t=i()({},this.$route.query,{page:e,sumPage:this.sumPage,r:parseInt(1e5*Math.random())});console.log(333,t),this.$router.push({query:t})},editItem:function(e){var t=this;this.$ajax.get({url:"/apiWeb/dictionary/dictionary/findOne/"+e.id}).then(function(e){e.data.isRead=!1,t.selectTableItem=e.data,t.isOpenDialog=!0})},delItem:function(e){var t=this;this.$ajax.delete({url:"/api/apiWeb/dictionary/dictionary/delete/"+e.id}).then(function(e){if(t.$message({message:e.message,type:"success"}),1==t.tableData.length){1==t.sumPage?t.sumPage:t.sumPage--,t.page>t.sumPage&&(t.page=t.sumPage);var a=i()({},t.$route.query,{page:t.page,sumPage:t.sumPage,r:parseInt(1e5*Math.random())});t.$router.push({query:a})}else t.getData()})},readItem:function(e){var t=this;this.$ajax.get({url:"/apiWeb/dictionary/dictionary/findOne/"+e.id}).then(function(e){e.data.isRead=!0,t.selectTableItem=e.data,t.isOpenDialog=!0})},nextLevel:function(e){this.breadcrumb.push(e),sessionStorage.setItem("breadcrumb",o()(this.breadcrumb)),this.initForm();var t=i()({},this.$route.query,{page:1,sumPage:1,parentId:e.id,dictionaryLevel:parseInt(e.dictionaryLevel)+1,type:e.type,r:parseInt(1e5*Math.random())});this.$router.push({query:t})},goBreadcrumb:function(e,t){console.log(e),this.breadcrumb.splice(t+1,this.breadcrumb.length-t-1),sessionStorage.setItem("breadcrumb",o()(this.breadcrumb)),0==e.id&&delete this.$route.query.type;var a=i()({},this.$route.query,{page:this.page,sumPage:this.sumPage,parentId:e.id||0,dictionaryLevel:e.dictionaryLevel+1||"",r:parseInt(1e5*Math.random())});this.$router.push({query:a})},allSelect:function(e){this.delAllSelectItem=e},allDel:function(){0!=this.delAllSelectItem.length?this.delDialog=!0:this.$message.error("您还没有选择什么数据")},closeDialog:function(){this.delDialog=!1},allDelSure:function(){var e=this;this.closeDialog();var t=this.delAllSelectItem.map(function(e){return e.id});this.$axios.post(this.$api.deletesDictionary,t).then(function(a){if(a)if(e.$message({message:a.message,type:"success"}),e.tableData.length==t.length){1==e.sumPage?e.sumPage:e.sumPage--,e.page>e.sumPage&&(e.page=e.sumPage);var r=i()({},e.$route.query,{page:e.page,sumPage:e.sumPage,r:parseInt(1e5*Math.random())});e.$router.push({query:r})}else e.getData()})}}},m={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"system_data"},[a("div",{staticClass:"query_box"},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.formRules,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"编号",prop:"code"}},[a("el-input",{attrs:{placeholder:"编号"},model:{value:e.form.code,callback:function(t){e.$set(e.form,"code",t)},expression:"form.code"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"名称",prop:"name"}},[a("el-input",{attrs:{placeholder:"名称"},model:{value:e.form.name,callback:function(t){e.$set(e.form,"name",t)},expression:"form.name"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"父级id",prop:"parentId"}},[a("el-input",{attrs:{placeholder:"父级id"},model:{value:e.form.parentId,callback:function(t){e.$set(e.form,"parentId",t)},expression:"form.parentId"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"数据级别",prop:"dictionaryLevel"}},[a("el-input",{attrs:{placeholder:"数据级别"},model:{value:e.form.dictionaryLevel,callback:function(t){e.$set(e.form,"dictionaryLevel",t)},expression:"form.dictionaryLevel"}})],1),e._v(" "),a("el-form-item",[a("div",{staticClass:"wrapper_item"},[a("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:function(t){return e.onQuery("form")}}},[e._v("查询")]),e._v(" "),a("el-button",[e._v("清空")])],1)])],1)],1),e._v(" "),a("div",{staticClass:"btn_group"},[a("el-button",{attrs:{type:"primary",icon:"el-icon-document-add"},on:{click:function(t){return e.add("add")}}},[e._v("新增")]),e._v(" "),a("el-button",{attrs:{type:"primary",icon:"el-icon-s-unfold"}},[e._v("导出")]),e._v(" "),a("el-button",{attrs:{type:"danger",icon:"el-icon-delete"},on:{click:e.allDel}},[e._v("批量删除")])],1),e._v(" "),a("div",{staticClass:"table_box"},[a("el-breadcrumb",{attrs:{"separator-class":"el-icon-arrow-right"}},e._l(e.breadcrumb,function(t,r){return a("el-breadcrumb-item",{key:r},[a("span",{on:{click:function(a){return e.goBreadcrumb(t,r)}}},[e._v(e._s(t.name))])])}),1),e._v(" "),a("MyTable",{ref:"table",attrs:{origin:"systemData",data:e.tableData,currentPage:e.page,sumPage:e.sumPage,chinese:e.chinese},on:{pageEvent:e.selectPage,editItem:e.editItem,delItem:e.delItem,readItem:e.readItem,allSelect:e.allSelect,nextLevel:e.nextLevel}})],1),e._v(" "),e.isOpenDialog?a("EditBox",{attrs:{data:e.selectTableItem},on:{addComplate:e.getData},model:{value:e.isOpenDialog,callback:function(t){e.isOpenDialog=t},expression:"isOpenDialog"}}):e._e(),e._v(" "),a("el-dialog",{attrs:{title:"提示",visible:e.delDialog,width:"30%","before-close":e.closeDialog},on:{"update:visible":function(t){e.delDialog=t}}},[a("span",[e._v("是否要删除所选")]),e._v(" "),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:e.closeDialog}},[e._v("取 消")]),e._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:e.allDelSure}},[e._v("确 定")])],1)])],1)},staticRenderFns:[]};var p=a("VU/8")(d,m,!1,function(e){a("e3j4")},"data-v-4a5b8c34",null);t.default=p.exports},e3j4:function(e,t){},fBot:function(e,t){}});