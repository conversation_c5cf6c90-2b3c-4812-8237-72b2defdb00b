webpackJsonp([38],{"0keS":function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var l={data:function(){var t=this;return{stationData:[],pageTotal:0,loading:!1,selectChildValueArr:[],postData:{page:1,rows:20,county:"",genNum:"",isArea:""},area:{lazy:!0,expandTrigger:"hover",lazyLoad:function(e,a){var l=e.level;e.value?t.$ajax.get({url:"/api/apiWeb/security/region/findAllByParentId/"+(e.value?e.value.split(",")[0]:-1)}).then(function(t){var e=t.data.map(function(t){return{value:t.id+","+t.code+","+t.name,label:t.name,leaf:l>=2}});a(e)}):t.$ajax.get({url:"/api/apiWeb/security/region/findOneById/"+sessionStorage.getItem("regionId")}).then(function(t){var e=[t.data].map(function(t){return{value:t.id+","+t.code+","+t.name,label:t.name,leaf:l>=3}});a(e)})}}}},watch:{selectChildValueArr:{handler:function(t){var e=t[t.length-1].split(",");this.postData.county=e[e.length-1]},deep:!0}},mounted:function(){var t=this;this.$nextTick(function(){t.initRegion()})},methods:{initRegion:function(){var t=this;this.$ajax.get({url:"/api/apiWeb/security/region/findOneById/"+sessionStorage.getItem("regionId")}).then(function(e){e.success&&(t.postData.county="贵州省",t.initTableData("ruleForm"))})},initTableData:function(t){var e=this,a=!1;if(this.$refs[t].validate(function(t){if(!t)return!1;a=!0}),a){this.$refs[t].clearValidate();var l=this.postData.radius,o=this.postData.longitude,i=this.postData.latitude;this.postData.freqEfe,this.postData.freqEfb,this.postData.freqRfe,this.postData.freqRfb;if(""!==l){if(""===o)return void this.$message.error("经度不能为空！");if(""===i)return void this.$message.error("纬度不能为空！")}""===o||""!==i?""===i||""!==o?(this.loading||(this.loading=!0),this.$ajax.post({url:"/api/apiWeb/station/rsbtStation/queryAreaRange",data:this.postData}).then(function(t){if(e.loading=!1,t.success){var a=[t.data.list,t.data.total];e.stationData=a[0],e.pageTotal=a[1]}else e.$message.error(t.message)},function(){return e.loading=!1})):this.$message.error("经度不能为空！"):this.$message.error("纬度不能为空！")}},getBaseStateNum:function(t){vueBus.stationMapData=[t],this.$router.push({path:"/gis-map"})},currentApplayTable:function(t){if(!t.applyTableGuid)return this.$message.error("当前数据有误，请重新选择！");this.$router.push("/station/apply/"+t.applyTableGuid)},searchFun:function(){this.postData.page=1,this.initTableData("ruleForm")},changePage:function(t){this.postData.page=t,this.initTableData("ruleForm")}},filters:{isAreaToChinese:function(t){switch(t=parseInt(t)){case 1:return"否";case 2:return"是"}}}},o={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"content-template"},[a("div",{staticClass:"search-box"},[a("el-form",{ref:"ruleForm",staticClass:"search-form",attrs:{model:t.postData,inline:!0,"label-position":"right","label-width":"80px"}},[a("el-form-item",{attrs:{label:"设置区域："}},[a("el-cascader",{staticStyle:{width:"280px"},attrs:{props:Object.assign({},{checkStrictly:!0},t.area)},model:{value:t.selectChildValueArr,callback:function(e){t.selectChildValueArr=e},expression:"selectChildValueArr"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"代数："}},[a("el-radio-group",{model:{value:t.postData.genNum,callback:function(e){t.$set(t.postData,"genNum",e)},expression:"postData.genNum"}},[a("el-radio-button",{attrs:{label:""}},[t._v("全部")]),t._v(" "),a("el-radio-button",{attrs:{label:"2"}},[t._v("2G")]),t._v(" "),a("el-radio-button",{attrs:{label:"3"}},[t._v("3G")]),t._v(" "),a("el-radio-button",{attrs:{label:"4"}},[t._v("4G")]),t._v(" "),a("el-radio-button",{attrs:{label:"5"}},[t._v("5G")]),t._v(" "),a("el-radio-button",{attrs:{label:"GSM-R"}},[t._v("GSM-R")])],1)],1),t._v(" "),a("el-form-item",{attrs:{label:"基站状态："}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择"},model:{value:t.postData.isArea,callback:function(e){t.$set(t.postData,"isArea",e)},expression:"postData.isArea"}},[a("el-option",{attrs:{label:"全部",value:""}}),t._v(" "),a("el-option",{attrs:{label:"区域内",value:1}}),t._v(" "),a("el-option",{attrs:{label:"区域外",value:2}})],1)],1),t._v(" "),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.searchFun}},[t._v("搜索")])],1)],1)],1),t._v(" "),a("div",{staticClass:"table-box"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"table-content",attrs:{height:"100%",data:t.stationData,stripe:""}},[a("el-table-column",{attrs:{type:"selection",width:"55"}}),t._v(" "),a("el-table-column",{attrs:{prop:"stationName",align:"center",label:"基站名称","show-overflow-tooltip":!0}}),t._v(" "),a("el-table-column",{attrs:{prop:"stationCode",align:"center",label:"基站识别码","show-overflow-tooltip":!0}}),t._v(" "),a("el-table-column",{attrs:{prop:"netType",align:"center",label:"技术体制",width:"120px","show-overflow-tooltip":!0}}),t._v(" "),a("el-table-column",{attrs:{prop:"location",align:"center",label:"台址","show-overflow-tooltip":!0}}),t._v(" "),a("el-table-column",{attrs:{prop:"longitude",align:"center",label:"经度",width:"120px","show-overflow-tooltip":!0}}),t._v(" "),a("el-table-column",{attrs:{prop:"latitude",align:"center",label:"纬度",width:"120px","show-overflow-tooltip":!0}}),t._v(" "),a("el-table-column",{attrs:{prop:"county",align:"center",label:"上报区域","show-overflow-tooltip":!0}}),t._v(" "),a("el-table-column",{attrs:{prop:"isArea",align:"center",label:"是否跨区","show-overflow-tooltip":!0},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(t._f("isAreaToChinese")(e.row.isArea)))])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"操作",width:"120",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return t.getBaseStateNum(e.row)}}},[t._v("详情")])]}}])})],1),t._v(" "),a("div",{staticClass:"table-pagin"},[a("el-pagination",{staticStyle:{float:"right"},attrs:{layout:"total, prev, pager, next, jumper","current-page":t.postData.page,total:t.pageTotal,"page-size":t.postData.rows},on:{"current-change":t.changePage}})],1)],1)])},staticRenderFns:[]};var i=a("VU/8")(l,o,!1,function(t){a("37F8")},"data-v-7a419e6d",null);e.default=i.exports},"37F8":function(t,e){}});