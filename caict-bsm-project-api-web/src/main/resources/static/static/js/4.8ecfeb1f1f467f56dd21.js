webpackJsonp([4],{"2xOt":function(e,t,a){"use strict";a("D4uH").a.register({upload:{width:512,height:512,paths:[{d:"M296 384h-80c-13.3 0-24-10.7-24-24v-168h-87.7c-17.8 0-26.7-21.5-14.1-34.1l152.1-152.2c7.5-7.5 19.8-7.5 27.3 0l152.2 152.2c12.6 12.6 3.7 34.1-14.1 34.1h-87.7v168c0 13.3-10.7 24-24 24zM512 376v112c0 13.3-10.7 24-24 24h-464c-13.3 0-24-10.7-24-24v-112c0-13.3 10.7-24 24-24h136v8c0 30.9 25.1 56 56 56h80c30.9 0 56-25.1 56-56v-8h136c13.3 0 24 10.7 24 24zM388 464c0-11-9-20-20-20s-20 9-20 20 9 20 20 20 20-9 20-20zM452 464c0-11-9-20-20-20s-20 9-20 20 9 20 20 20 20-9 20-20z"}]}})},"80vm":function(e,t){},"b+Ml":function(e,t,a){"use strict";var n=a("Xxa5"),r=a.n(n),s=a("exGp"),i=a.n(s),_=a("bOdI"),o=a.n(_),u=a("lSn7"),c=(new(a("oFuF").a),{name:"vue-upload",props:{msg:o()({default:Object},"default",null),url:{type:String,default:"/api/sync/upload/license"},fileNumLimit:{type:Number,default:10},fileSingleSizeLimit:{type:Number,default:2048e3},formData:{type:Object,default:null},keyGenerator:{type:Function,default:function(e){return(new Date).getTime()+"."+e.name}},multiple:{type:Boolean,default:!1},uploadButton:{type:String,default:"",required:!0}},data:function(){return{uploader:null,isSelect:0,isUpload:!1,accept:"Texts"}},mounted:function(){this.initWebUpload()},methods:{initWebUpload:function(){var e,t=this;this.uploader=WebUploader.create({auto:!1,swf:"../../assets/swf/Uploader.swf",server:this.url,pick:{id:this.uploadButton,multiple:this.multiple,label:""},accept:{title:"上传组件",extensions:"zip",mimeTypes:".zip"},threads:3,fileNumLimit:this.fileNumLimit,chunked:!0,chunkSize:2048e3,duplicate:!0,formData:{jobId:""}}),this.uploader.on("fileQueued",(e=i()(r.a.mark(function e(a){var n,s,i;return r.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(""!==(n=t.uploader.options).formData.jobId){e.next=9;break}return e.next=4,u.a();case 4:s=e.sent,i=s.res.detail.value,s.status&&i?(t.isSelect=1,n.formData.jobId=i,t.jobId=i,t.$emit("fileChange",a,i),t.isUpload=!0):(t.$message.error("创建上传任务失败"),t.isUpload=!1),e.next=10;break;case 9:t.$emit("fileChange",a,t.jobId);case 10:case"end":return e.stop()}},e,t)})),function(t){return e.apply(this,arguments)})),this.uploader.on("uploadStart",function(e){}),this.uploader.on("uploadProgress",function(e,a){t.$emit("progress",e,a)}),this.uploader.on("uploadSuccess",function(e,a){t.$emit("success",e,a,t.jobId)}),this.uploader.on("uploadError",function(e,a){console.error(a),t.$emit("uploadError",e,a)}),this.uploader.on("error",function(e){"F_EXCEED_SIZE"===e?t.$message.error("文件大小不能超过"+t.fileSingleSizeLimit/1024e3+"M"):"Q_EXCEED_NUM_LIMIT"===e?t.$message.error("文件上传已达到最大上限数"):"Q_TYPE_DENIED"===e?t.$message.error("文件类型不符合要求，无法上传"):t.$message.error("上传出错！请检查后重新上传！错误代码"+e),t.$emit("error","")}),this.uploader.on("uploadComplete",function(e,a){t.$emit("complete",e,a)})},upload:function(e){this.uploader.upload(e)},stop:function(e){this.uploader.stop(e)},cancelFile:function(e){this.uploader.cancelFile(e)},removeFile:function(e,t){this.uploader.removeFile(e,t)},getAccept:function(e){switch(e){case"text":return{title:"Texts",exteensions:"xls,xlsx,csv",mimeTypes:".xls,.xlsx,.csv"};case"video":return{title:"Videos",exteensions:"mp4",mimeTypes:".mp4"};case"image":return{title:"Images",exteensions:"gif,jpg,jpeg,bmp,png",mimeTypes:".gif,.jpg,.jpeg,.bmp,.png"};default:return e}}}}),l={render:function(){var e=this.$createElement;return(this._self._c||e)("div",{staticClass:"upload"})},staticRenderFns:[]},p=a("VU/8")(c,l,!1,null,null,null);t.a=p.exports},dxkS:function(e,t){},"ewg/":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=a("yNJm"),r={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"content-template"},[a("div",{staticClass:"operate-box"},[a("div",{attrs:{id:"filePicker"}},[a("Icon",{attrs:{name:"upload"}}),a("span",[e._v("选择同步文件")])],1)]),e._v(" "),a("div",{staticClass:"file-panel"},[e._m(0),e._v(" "),a("div",{staticClass:"file-list"},[e._l(e.fileList,function(t){return a("ul",{key:t.id,class:"file-item file-"+t.id},[a("li",{staticClass:"file-type"},[e._v(e._s(e._f("fileCategory")(t.ext)))]),e._v(" "),a("li",{staticClass:"file-name"},[e._v(e._s(t.name))]),e._v(" "),a("li",{staticClass:"file-size"},[e._v(e._s(e._f("fileSize")(t.size)))]),e._v(" "),a("li",{class:"text-primary"},[e._v(e._s(t.status?t.status:"等待上传..."))]),e._v(" "),a("li",{staticClass:"file-operate"},[1===e.isSelect?a("el-button",{attrs:{type:"success",size:"mini",plain:"",icon:"el-icon-upload"},on:{click:function(a){return e.resume(t)}}},[e._v("上传")]):e._e(),e._v(" "),1===e.isSelect?a("el-button",{attrs:{type:"danger",size:"mini",plain:"",icon:"el-icon-remove"},on:{click:function(a){return e.cancel(t)}}},[e._v("取消")]):e._e(),e._v(" "),2===e.isSelect?a("el-button",{attrs:{type:"warning",size:"mini",plain:"",icon:"el-icon-warning"},on:{click:function(a){return e.stop(t)}}},[e._v("暂停")]):e._e(),e._v(" "),3===e.isSelect?a("el-button",{attrs:{type:"primary",size:"mini",plain:"",icon:"el-icon-refresh"},on:{click:function(a){return e.remove(t)}}},[e._v("重新上传")]):e._e()],1)])}),e._v(" "),e.fileList.length?e._e():a("div",{staticClass:"no-file"},[a("i",{staticClass:"iconfont icon-empty-file"}),e._v(" 暂无待上传文件")])],2),e._v(" "),a("vue-upload",{ref:"uploader",attrs:{"upload-button":"#filePicker",multiple:!1,"file-num-limit":1},on:{fileChange:e.fileChange,progress:e.onProgress,success:e.onSuccess}})],1),e._v(" "),a("div",{staticClass:"table-box"},[e._m(1),e._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"table-content",attrs:{height:"100%",fit:"",stripe:"",data:e.syncData}},[a("el-table-column",{attrs:{prop:"fileLocalName",align:"center",label:"文件名称","show-overflow-tooltip":!0}}),e._v(" "),a("el-table-column",{attrs:{prop:"fileSize",align:"center",label:"文件大小(M)","show-overflow-tooltip":!0},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n          "+e._s(e._f("bytesToSize")(t.row.fileSize))+"\n        ")]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"gmtCreate",align:"center",label:"上传时间",width:"200px"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n          "+e._s(e._f("format")(t.row.gmtCreate))+"\n        ")]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"fileState",align:"center",label:"上传状态","show-overflow-tooltip":!0},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",{class:e._f("syncStatus")(t.row.fileState,"theme")},[e._v("\n            "+e._s(e._f("syncStatus")(t.row.fileState))+"\n          ")])]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"normalCount",align:"center",label:"有效执照数量","show-overflow-tooltip":!0}}),e._v(" "),a("el-table-column",{attrs:{prop:"expireCount",align:"center",label:"过期执照数量","show-overflow-tooltip":!0}}),e._v(" "),a("el-table-column",{attrs:{prop:"pauseCount",align:"center",label:"停用执照数量","show-overflow-tooltip":!0}}),e._v(" "),a("el-table-column",{attrs:{prop:"disableCount",align:"center",label:"注销执照数量","show-overflow-tooltip":!0}}),e._v(" "),a("el-table-column",{attrs:{prop:"exportCount",align:"center",label:"执照总数","show-overflow-tooltip":!0}})],1),e._v(" "),a("div",{staticClass:"table-pagin"},[a("el-pagination",{attrs:{layout:"total, prev, pager, next, jumper","current-page":e.pageDTO.page,total:e.pageTotal},on:{"current-change":e.changePage}})],1)],1),e._v(" "),a("el-dialog",{attrs:{title:"同步详细信息",visible:e.popShow,"custom-class":"license-dialog",width:"998px"},on:{"update:visible":function(t){e.popShow=t}}})],1)},staticRenderFns:[function(){var e=this.$createElement,t=this._self._c||e;return t("div",{staticClass:"operate-box"},[t("span",{staticClass:"operate-table-title"},[this._v("上传列表")])])},function(){var e=this.$createElement,t=this._self._c||e;return t("div",{staticClass:"operate-box"},[t("span",{staticClass:"operate-table-title"},[this._v("同步记录")])])}]};var s=function(e){a("80vm")},i=a("VU/8")(n.a,r,!1,s,"data-v-faedfdb2",null);t.default=i.exports},lSn7:function(e,t,a){"use strict";a.d(t,"d",function(){return y}),a.d(t,"c",function(){return D}),a.d(t,"b",function(){return x}),a.d(t,"a",function(){return C}),a.d(t,"f",function(){return k}),a.d(t,"e",function(){return M});var n,r,s,i,_,o,u,c,l,p,d,v,f,m,h=a("Xxa5"),b=a.n(h),g=a("exGp"),E=a.n(g),w=new(a("oFuF").a),y=(n=E()(b.a.mark(function e(t){var a;return b.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return a={type:"post",url:"/api/transportJob/create",data:t},e.abrupt("return",w.requestFun(a));case 2:case"end":return e.stop()}},e,this)})),function(e){return n.apply(this,arguments)}),D=(r=E()(b.a.mark(function e(t){var a;return b.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return a={type:"post",url:"/api/upload/delete/csv",data:t},e.abrupt("return",w.requestFun(a));case 2:case"end":return e.stop()}},e,this)})),s=E()(b.a.mark(function e(t){var a;return b.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return a={type:"post",url:"/api/transportJob/file/del",data:t},e.abrupt("return",w.requestFun(a));case 2:case"end":return e.stop()}},e,this)})),i=E()(b.a.mark(function e(t){var a;return b.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return a={type:"post",url:"/api/transportJob/file/list",data:t},e.abrupt("return",w.requestFun(a));case 2:case"end":return e.stop()}},e,this)})),_=E()(b.a.mark(function e(t){var a;return b.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return a={type:"post",url:"/api/transportJob/list",data:t},e.abrupt("return",w.requestFun(a));case 2:case"end":return e.stop()}},e,this)})),o=E()(b.a.mark(function e(t){var a;return b.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return a={type:"post",url:"/api/transportJob/detail",data:t},e.abrupt("return",w.requestFun(a));case 2:case"end":return e.stop()}},e,this)})),u=E()(b.a.mark(function e(t){var a;return b.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return a={type:"post",url:"/api/transportJob/commit",data:t},e.abrupt("return",w.requestFun(a));case 2:case"end":return e.stop()}},e,this)})),c=E()(b.a.mark(function e(t){var a;return b.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return a={type:"post",url:"/api/transportJob/start",data:t},e.abrupt("return",w.requestFun(a));case 2:case"end":return e.stop()}},e,this)})),l=E()(b.a.mark(function e(t){var a;return b.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return a={type:"post",url:"/api/upload/csv/notify",data:t},e.abrupt("return",w.requestFun(a));case 2:case"end":return e.stop()}},e,this)})),function(e){return l.apply(this,arguments)}),x=(p=E()(b.a.mark(function e(t){var a;return b.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return a={type:"post",url:"/api/upload/delete/csv",data:t},e.abrupt("return",w.requestFun(a));case 2:case"end":return e.stop()}},e,this)})),function(e){return p.apply(this,arguments)}),C=(d=E()(b.a.mark(function e(t){var a;return b.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return a={type:"post",url:"/api/upload/delete/file",data:t},e.abrupt("return",w.requestFun(a));case 2:case"end":return e.stop()}},e,this)})),v=E()(b.a.mark(function e(t){var a;return b.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return a={type:"post",url:"/api/sync/create",data:t},e.abrupt("return",w.requestFun(a));case 2:case"end":return e.stop()}},e,this)})),function(e){return v.apply(this,arguments)}),k=(f=E()(b.a.mark(function e(t){var a;return b.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return a={type:"post",url:"/api/sync/upload/notify",data:t},e.abrupt("return",w.requestFun(a));case 2:case"end":return e.stop()}},e,this)})),function(e){return f.apply(this,arguments)}),M=(m=E()(b.a.mark(function e(t){var a;return b.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return a={type:"post",url:"/api/sync/list",data:t},e.abrupt("return",w.requestFun(a));case 2:case"end":return e.stop()}},e,this)})),function(e){return m.apply(this,arguments)})},uJMM:function(e,t,a){"use strict";var n=a("Xxa5"),r=a.n(n),s=a("exGp"),i=a.n(s),_=a("SR6C"),o=new(a("oFuF").a),u={props:{stationId:{default:Object}},mounted:function(){var e=this;this.$nextTick(function(){e.getLicenseDetail()})},data:function(){return{imgUrl:"",elicenseData:{}}},watch:{stationId:{handler:function(e,t){this.getLicenseDetail()},deep:!0}},methods:{getBandWidth:function(e){var t=0,a=e.freq_EFB,n=e.freq_EFE;if(a&&n)for(var r=a.split(","),s=n.split(","),i=0;i<r.length;i++){t+=s[i]-r[i]}return 0===t?"":t},returnFreq:function(e,t){var a="";if("EF"===t){for(var n=e.freq_EFB,r=e.freq_EFE,s=n.split(","),i=r.split(","),_=0;_<s.length;_++)a=a+s[_]+"-"+i[_],_===s.length-1||(a+=",");return a}for(var o=e.freq_RFB,u=e.freq_RFE,c=o.split(","),l=u.split(","),p=0;p<c.length;p++)a=a+c[p]+"-"+l[p],p===c.length-1||(a+=",");return a},getDate:function(e){return o.format(e,"yyyy.mm.dd")},getLicenseDetail:function(){var e=this;return i()(r.a.mark(function t(){var a,n,s;return r.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return a={stationGuId:e.stationId},t.next=3,_.b(a);case 3:if((n=t.sent).status&&n.res.status)for(e.elicenseData=n.res.detail,(s=n.res.detail.sectionList.length)>12&&(s=12);s<12;s++)e.elicenseData.sectionList[s]={};case 5:case"end":return t.stop()}},t,e)}))()},init:function(){var e=this;return i()(r.a.mark(function t(){var a,n;return r.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return a={stationGuId:e.stationId},t.next=3,_.a(a);case 3:(n=t.sent).status&&(e.imgUrl="/api/"+n.res.detail.value);case 5:case"end":return t.stop()}},t,e)}))()},_downLicense:function(e){var t=this;return i()(r.a.mark(function e(){var a,n,s;return r.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return a={stationGuId:t.stationId},e.next=3,_.a(a);case 3:(n=e.sent).status?(s=n.res.detail.value,o.funDownload(s)):t.$message.error("文件下载失败");case 5:case"end":return e.stop()}},e,t)}))()},cancelFun:function(){this.$emit("cancel",!0)}}},c={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"form"},[a("div",{staticClass:"upload-btn"},[a("el-button",{attrs:{type:"primary"},on:{click:e._downLicense}},[e._v("下载执照")])],1),e._v(" "),a("div",{staticClass:"word-box-front"},[a("h2",[e._v("中华人民共和国无线电台执照")]),e._v(" "),a("div",{staticClass:"elicense-number",staticStyle:{"margin-left":"33%"}},[e._v("执照编号：\n        "),a("span",[e._v(e._s(e.elicenseData.licenseCode))])]),e._v(" "),a("div",{staticClass:"elicense-rule"},[e._v("根据《中华人民共和国无线电管理条列》和国家相关规定，颁发本执照，准予设置和使用下述无线电台(站):")]),e._v(" "),a("div",{staticClass:"station-info"},[e._v("台(站)基本信息")]),e._v(" "),a("table",{staticClass:"table-station"},[a("tbody",[a("tr",[a("td",{staticStyle:{width:"120px"}},[e._v("台站名称")]),e._v(" "),a("td",{attrs:{colspan:"3"}},[e._v(e._s(e.elicenseData.stationName))])]),e._v(" "),a("tr",[a("td",[e._v("台站设置使用人")]),e._v(" "),a("td",{attrs:{colspan:"3"}},[e._v(e._s(e.elicenseData.org?e.elicenseData.org.orgName:e.elicenseData.orgName))])]),e._v(" "),a("tr",[a("td",[e._v("台址(编号)")]),e._v(" "),a("td",{attrs:{colspan:"3"}},[a("span",{staticStyle:{float:"left"}},[e._v("  经度："+e._s(e.elicenseData.longitude))]),e._v(" "),a("span",{staticStyle:{float:"left"}},[e._v("   纬度："+e._s(e.elicenseData.latitude)+" ")]),e._v(" "),a("span",[e._v(e._s(e.elicenseData.location))])])]),e._v(" "),a("tr",[a("td",[e._v("无线电台识别码")]),e._v(" "),a("td",[e._v(e._s(e.elicenseData.stationCode))]),e._v(" "),a("td",[e._v("台站类别")]),e._v(" "),a("td",[e._v(e._s(e.elicenseData.netType))])]),e._v(" "),a("tr",[a("td",[e._v("对应频率许可证编号")]),e._v(" "),a("td"),e._v(" "),a("td",[e._v("起止日期")]),e._v(" "),a("td",[e._v(e._s(e.getDate(e.elicenseData.stationStartDate))+"-"+e._s(e.getDate(e.elicenseData.licenseEndDate)))])])])]),e._v(" "),a("div",{staticClass:"section-info"},[e._v("发射参数信息")]),e._v(" "),a("table",{staticClass:"table-section"},[e._m(0),e._v(" "),a("tbody",e._l(e.elicenseData.sectionList,function(t,n){return a("tr",{key:n},[a("td",[e._v(e._s(n+1))]),e._v(" "),t.freq_EFB?a("td",[e._v(e._s(e.returnFreq(t,"EF")))]):e._e(),e._v(" "),t.freq_EFB?e._e():a("td"),e._v(" "),t.freq_RFB?a("td",[e._v(e._s(e.returnFreq(t,"RF")))]):e._e(),e._v(" "),t.freq_RFB?e._e():a("td"),e._v(" "),t.max_POWER?a("td",[e._v(e._s(t.max_POWER))]):e._e(),e._v(" "),t.max_POWER?e._e():a("td"),e._v(" "),a("td",[e._v(e._s(e.getBandWidth(t)))]),e._v(" "),t.sectionCode?a("td",[e._v(e._s(e.elicenseData.stationCode)+"/"+e._s(t.sectionCode))]):e._e(),e._v(" "),t.sectionCode?e._e():a("td")])}),0)]),e._v(" "),a("div",{staticClass:"other-info"},[e._v("其它事项载于背面：")]),e._v(" "),e._m(1)]),e._v(" "),e._m(2)])},staticRenderFns:[function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("thead",[a("tr",[a("td",[e._v("序号")]),e._v(" "),a("td",[e._v("发射频率/频段")]),e._v(" "),a("td",[e._v("接收频率/频段")]),e._v(" "),a("td",[e._v("发射功率")]),e._v(" "),a("td",[e._v("占用带宽/波道间隔")]),e._v(" "),a("td",[e._v("其他必要信息")])])])},function(){var e=this.$createElement,t=this._self._c||e;return t("div",{staticClass:"elicense-footer"},[t("div",{staticClass:"QR-code"},[this._v("二维码")]),this._v(" "),t("div",{staticClass:"elicense-date"},[this._v("\n          发证机关(签章):"),t("br"),this._v("\n          颁发日期：         年     月     日\n        ")])])},function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"word-box-back"},[a("div",{staticStyle:{"text-align":"center"}},[e._v("发射设备及天线信息")]),e._v(" "),a("table",{staticClass:"table-antenna"},[a("thead",[a("tr",[a("td",[e._v("序号")]),e._v(" "),a("td",[e._v("设备型号")]),e._v(" "),a("td",[e._v("天线类型（尺寸）")]),e._v(" "),a("td",[e._v("天线增益")]),e._v(" "),a("td",[e._v("极化方式")]),e._v(" "),a("td",[e._v("天线距地高度")])])]),e._v(" "),a("tbody",[a("tr",[a("td",[e._v("1")]),e._v(" "),a("td"),e._v(" "),a("td"),e._v(" "),a("td"),e._v(" "),a("td"),e._v(" "),a("td")]),e._v(" "),a("tr",[a("td",[e._v("2")]),e._v(" "),a("td"),e._v(" "),a("td"),e._v(" "),a("td"),e._v(" "),a("td"),e._v(" "),a("td")]),e._v(" "),a("tr",[a("td",[e._v("3")]),e._v(" "),a("td"),e._v(" "),a("td"),e._v(" "),a("td"),e._v(" "),a("td"),e._v(" "),a("td")]),e._v(" "),a("tr",[a("td",[e._v("4")]),e._v(" "),a("td"),e._v(" "),a("td"),e._v(" "),a("td"),e._v(" "),a("td"),e._v(" "),a("td")]),e._v(" "),a("tr",[a("td",[e._v("5")]),e._v(" "),a("td"),e._v(" "),a("td"),e._v(" "),a("td"),e._v(" "),a("td"),e._v(" "),a("td")]),e._v(" "),a("tr",[a("td",[e._v("6")]),e._v(" "),a("td"),e._v(" "),a("td"),e._v(" "),a("td"),e._v(" "),a("td"),e._v(" "),a("td")]),e._v(" "),a("tr",[a("td",[e._v("7")]),e._v(" "),a("td"),e._v(" "),a("td"),e._v(" "),a("td"),e._v(" "),a("td"),e._v(" "),a("td")])])]),e._v(" "),a("div",{staticStyle:{"text-align":"center"}},[e._v("使用要求")]),e._v(" "),a("div",{staticClass:"table-require"},[a("div",[e._v("     1、  设置、使用无线电台站、应当接收无线电管理机构的监督检查。依法设置、使用的无线电台（站）受到有\n          害干扰的，可以向无线电管理机构投诉。\n        ")]),e._v(" "),a("div",[e._v("     2、  使用无线电台（站）的单位或者个人应当对无线电台（站）进行定期维护，保证其性能指标符合国家标准\n          和国家无线电管理的有关规定，避免对其他依法设置、使用的无线电台（站）产生有害干扰。\n        ")]),e._v(" "),a("div",[e._v("     3、  使用无线电台（站）的单位或者个人应当遵守国家环境保护的规定，采取必要措施防止无线电波发射产生\n          的电磁辐射污染环境。\n        ")]),e._v(" "),a("div",[e._v("     4、  使用无线电台（站）的单位或者个人不得故意收发无线电台执照许可事项之外的无线电信号，不得传播、\n          公布或者利用无意接收的信息\n        ")]),e._v(" "),a("div",[e._v("     5、  无线电台（站）应当按照无线电台执照规定的许可事项和条件设置、使用；变更许可事项的，应当向作\n          出许可决定的无线电管理机构办理变更手续；无线电台执照有效期届满后需要继续使用无线电台（站）的，应当在期\n          限届满30个工作日前向作出许可决定的无线电管理机构申请更换无线电台执照。\n        ")]),e._v(" "),a("div",[e._v("     6、  无线电台（站）终止使用的，应当及时向作出许可决定的无线电管理机构办理注销手续，交回无线电台执\n          照，拆除无线电台（站）及天线等附属设备。\n        ")]),e._v(" "),a("div",[e._v("     7、  其他特别的约定事项：")])])])}]};var l=a("VU/8")(u,c,!1,function(e){a("dxkS")},"data-v-711e30ae",null);t.a=l.exports},yNJm:function(module,__webpack_exports__,__webpack_require__){"use strict";var __WEBPACK_IMPORTED_MODULE_0_babel_runtime_core_js_object_keys__=__webpack_require__("fZjL"),__WEBPACK_IMPORTED_MODULE_0_babel_runtime_core_js_object_keys___default=__webpack_require__.n(__WEBPACK_IMPORTED_MODULE_0_babel_runtime_core_js_object_keys__),__WEBPACK_IMPORTED_MODULE_1_babel_runtime_regenerator__=__webpack_require__("Xxa5"),__WEBPACK_IMPORTED_MODULE_1_babel_runtime_regenerator___default=__webpack_require__.n(__WEBPACK_IMPORTED_MODULE_1_babel_runtime_regenerator__),__WEBPACK_IMPORTED_MODULE_2_babel_runtime_helpers_asyncToGenerator__=__webpack_require__("exGp"),__WEBPACK_IMPORTED_MODULE_2_babel_runtime_helpers_asyncToGenerator___default=__webpack_require__.n(__WEBPACK_IMPORTED_MODULE_2_babel_runtime_helpers_asyncToGenerator__),__WEBPACK_IMPORTED_MODULE_3_service_datasync__=__webpack_require__("lSn7"),__WEBPACK_IMPORTED_MODULE_4_components_license_pop_pop__=__webpack_require__("uJMM"),__WEBPACK_IMPORTED_MODULE_5_components_upload_uploadSync__=__webpack_require__("b+Ml"),__WEBPACK_IMPORTED_MODULE_6_vue_awesome_icons_upload__=__webpack_require__("2xOt"),__WEBPACK_IMPORTED_MODULE_7_vue_awesome_components_Icon__=__webpack_require__("D4uH"),__WEBPACK_IMPORTED_MODULE_8__utils_type__=__webpack_require__("nR8Q");__webpack_exports__.a={data:function(){return{fileList:[],syncData:[],pageTotal:0,loading:!1,isActiveNum:-1,isSelect:0,popShow:!1,licenseDetail:"",pageDTO:{page:1,rows:10}}},computed:{uploader:function(){return this.$refs.uploader}},mounted:function(){var e=this;this.$nextTick(function(){e._getSynList()})},methods:{_getSynList:function(){var e=this;return __WEBPACK_IMPORTED_MODULE_2_babel_runtime_helpers_asyncToGenerator___default()(__WEBPACK_IMPORTED_MODULE_1_babel_runtime_regenerator___default.a.mark(function t(){var a,n;return __WEBPACK_IMPORTED_MODULE_1_babel_runtime_regenerator___default.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e.loading||(e.loading=!0),t.next=3,__WEBPACK_IMPORTED_MODULE_3_service_datasync__.e(e.pageDTO);case 3:(a=t.sent).status&&a.res.status&&(n=[a.res.records,a.res.total],e.syncData=n[0],e.pageTotal=n[1]),e.loading=!1;case 6:case"end":return t.stop()}},t,e)}))()},getLogDetail:function(e,t){var a=this;return __WEBPACK_IMPORTED_MODULE_2_babel_runtime_helpers_asyncToGenerator___default()(__WEBPACK_IMPORTED_MODULE_1_babel_runtime_regenerator___default.a.mark(function n(){return __WEBPACK_IMPORTED_MODULE_1_babel_runtime_regenerator___default.a.wrap(function(n){for(;;)switch(n.prev=n.next){case 0:a.isActiveNum!=e&&(a.isActiveNum=e),a.licenseDetail=t,a.popShow=!0;case 3:case"end":return n.stop()}},n,a)}))()},changePage:function(e){this.pageDTO.page=e,this.isActiveNum=-1,this._getSynList()},closeDialog:function(e){e&&(this.popShow=!1)},fileChange:function(e){e.size&&(this.fileList.push(e),this.isSelect=1)},onProgress:function(e,t){this.isSelect=2,this.fileList.forEach(function(a){a.id===e.id&&(a.status=(100*t).toFixed(2)+"%",a.theme="text-primary")})},onSuccess:function(e,t,a){var n=this;if(t.status){var r={jobId:a};__WEBPACK_IMPORTED_MODULE_3_service_datasync__.f(r).then(function(t){if(n.isSelect=3,!t||!t.status)return n.fileList.forEach(function(t){t.id===e.id&&(t.status="非常抱歉，上传失败！",t.theme="text-error")}),void n.$message.error("上传失败");n._getSynList(),n.fileList.forEach(function(t){t.id===e.id&&(t.status="恭喜您，上传成功！",t.theme="text-success")}),n.$message.success("上传成功！")})}},cancel:function(e){this.uploader.cancelFile(e),this.uploader.removeFile(e,!0);var t=this.fileList.findIndex(function(t){return t.id===e.id});this.fileList.splice(t,1),this.$message({message:"已取消",center:!0})},resume:function(e){this.uploader.upload(e)},stop:function(e){this.uploader.stop(e)},remove:function(e){this.uploader.cancelFile(e),this.uploader.removeFile(e,!0);var t=this.fileList.findIndex(function(t){return t.id===e.id});this.fileList.splice(t,1)}},filters:{syncStatus:function(e,t){var a=null;switch(e){case 0:case 1:a={tips:"文件上传异常",theme:"text-warning"};break;case 2:a={tips:"处理中...",theme:"text-primary"};break;case 3:a={tips:"导入成功",theme:"text-success"};break;case 4:a={tips:"导入失败",theme:"text-danger"};break;default:a={tips:"暂无相关状态值"+e,theme:"text-info"}}return t&&"tips"!==t?"theme"===t?a.theme:void 0:a.tips},fileCategory:function(e){var t="",a={image:["gif","jpg","jpeg","png","bmp","webp"],video:["mp4","m3u8","rmvb","avi","swf","3gp","mkv","flv"],text:["doc","txt","docx","pages","epub","pdf","numbers","csv","xls","xlsx","keynote","ppt","pptx"],"压缩文件":["zip","zipx","rar","tar","gz","jar","war","iso","img"]};return __WEBPACK_IMPORTED_MODULE_0_babel_runtime_core_js_object_keys___default()(a).forEach(function(n){-1!==a[n].indexOf(e)&&(t=n)}),t},fileSize:function(e){return WebUploader.Base.formatSize(e)},bytesToSize:function bytesToSize(bytes){return bytes<0?0:eval(bytes/1024).toFixed(2)}},components:{LicenseDetail:__WEBPACK_IMPORTED_MODULE_4_components_license_pop_pop__.a,vueUpload:__WEBPACK_IMPORTED_MODULE_5_components_upload_uploadSync__.a,Icon:__WEBPACK_IMPORTED_MODULE_7_vue_awesome_components_Icon__.a}}}});