webpackJsonp([44],{RzGg:function(t,e){},SisZ:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var s=a("H+Cu"),l=new(a("oFuF").a),o={components:{CompareDetail:s.a},data:function(){return{dialogVisible:!1,textarea:"",fileRadio:!0,tableData:[],fileData:[],transportRawBts:[],transportSchedule:[],compareResult:"",pageTotal:0,pageDTO:{jobId:"",appGuid:"",page:1,rows:10,resultType:"1"},add:"",del:"",edit:"",nor:""}},computed:{jobId:function(){return this.$route.params.job},appGuid:function(){return this.$route.params.guid}},mounted:function(){var t=this;this.$nextTick(function(){t.getCount(),t.initTableData(),t.initFileDetail()})},methods:{initTableData:function(){var t=this;this.pageDTO.jobId=this.jobId,this.pageDTO.appGuid=this.appGuid,this.$ajax.post({url:"/api/apiWeb/transfer/transportCompareResult/getCompareVOListByPage",data:this.pageDTO}).then(function(e){if(e.success)return t.tableData=e.data.list||[],void(t.pageTotal=e.data.total);e.success||(t.$message.error(e.message),t.tableData=[],t.pageTotal=0)},function(){t.tableData=[],t.pageTotal=0})},initFileDetail:function(){var t=this;this.$ajax.get({url:"/api/apiWeb/transfer/transportFile/findFileListByJob/"+this.appGuid}).then(function(e){e.success?t.fileData=e.data:e.success||t.$message.error(e.message)})},downloadFile:function(t){l.funDownload(t)},getCount:function(){var t=this;this.$ajax.get({url:"/api/apiWeb/transfer/transportCompareResult/getCount/"+this.appGuid}).then(function(e){if(e.success){var a=[e.data.add,e.data.edit,e.data.del,e.data.nor];return t.add=a[0],t.edit=a[1],t.del=a[2],void(t.nor=a[3])}e.success||t.$message.error(e.message)})},currentRowView:function(t){var e=this;this.compareResult=t.resultType,this.$ajax.get({url:"/api/apiWeb/transfer/transportCompareResult/findOneDetail/"+t.guid}).then(function(t){if(t.success)return e.dialogVisible=!0,e.transportRawBts=[t.data.transportRawBtsDTO],void(e.transportSchedule=[t.data.approvalScheduleLogDTO]);t.success||e.$message.error(t.message)})},handleChangePage:function(t){this.pageDTO.page=t,this.initTableData()},commitReview:function(){var t=this,e={detailResult:this.textarea,jobId:this.jobId,appGuid:this.appGuid,finalResult:this.fileRadio};this.$ajax.post({url:"/api/transportJob/approve",data:e}).then(function(e){if(e.success)return t.$message.success("恭喜您，操作成功！"),void t.$router.push("/compare/records");e.success||t.$message.error(e.reason)})}},watch:{"pageDTO.resultType":function(){this.initTableData()}}},r={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"content-template"},[a("div",{staticClass:"top-nav"},[a("el-breadcrumb",{attrs:{"separator-class":"el-icon-arrow-right"}},[a("el-breadcrumb-item",{attrs:{to:"/compare/records"}},[t._v("数据比对")]),t._v(" "),a("el-breadcrumb-item",[t._v("比对详情")])],1)],1),t._v(" "),t._m(0),t._v(" "),a("div",{staticClass:"table-box"},[[a("el-tabs",{model:{value:t.pageDTO.resultType,callback:function(e){t.$set(t.pageDTO,"resultType",e)},expression:"pageDTO.resultType"}},[a("el-tab-pane",{attrs:{name:"1"}},[a("span",{attrs:{slot:"label"},slot:"label"},[t._v("\n            新增异常\n            "),t.add>0?a("el-badge",{attrs:{value:t.add,size:"mini"}}):t._e()],1)]),t._v(" "),a("el-tab-pane",{attrs:{name:"2"}},[a("span",{attrs:{slot:"label"},slot:"label"},[t._v("\n            变更异常\n            "),t.edit>0?a("el-badge",{attrs:{value:t.edit,size:"mini"}}):t._e()],1)]),t._v(" "),a("el-tab-pane",{attrs:{name:"3"}},[a("span",{attrs:{slot:"label"},slot:"label"},[t._v("\n            注销异常\n            "),t.del>0?a("el-badge",{attrs:{value:t.del,size:"mini"}}):t._e()],1)]),t._v(" "),a("el-tab-pane",{attrs:{name:"4"}},[a("span",{attrs:{slot:"label"},slot:"label"},[t._v("\n            比对通过\n            "),t.nor>0?a("el-badge",{attrs:{value:t.nor,size:"mini"}}):t._e()],1)])],1)],t._v(" "),a("el-table",{staticClass:"table-content",attrs:{height:"300px",fit:"",stripe:"",data:t.tableData}},[a("el-table-column",{attrs:{prop:"btsName",align:"left",label:"基站名称","show-overflow-tooltip":!0}}),t._v(" "),a("el-table-column",{attrs:{prop:"btsId",align:"left",label:"基站识别码","show-overflow-tooltip":!0}}),t._v(" "),a("el-table-column",{attrs:{prop:"cellName",align:"center",label:"小区名称"}}),t._v(" "),a("el-table-column",{attrs:{prop:"cellId",align:"center",label:"小区识别码","show-overflow-tooltip":!0}}),t._v(" "),a("el-table-column",{attrs:{prop:"result",align:"left",label:"比对结果","show-overflow-tooltip":!0},scopedSlots:t._u([{key:"default",fn:function(e){return["比对信息一致！"==e.row.compareResult?a("span",{staticClass:"text-success"},[t._v(t._s(e.row.compareResult))]):a("span",{staticClass:"text-danger"},[t._v(t._s(e.row.compareResult))])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"操作",width:"100px",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return t.currentRowView(e.row)}}},[t._v("查看")])]}}])})],1),t._v(" "),a("div",{staticClass:"table-pagin"},[a("el-pagination",{attrs:{layout:"total, prev, pager, next, jumper","current-page":t.pageDTO.page,total:t.pageTotal},on:{"current-change":t.handleChangePage}})],1)],2),t._v(" "),a("el-card",{staticStyle:{margin:"20px 0"},attrs:{shadow:"always"}},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("附件列表")])]),t._v(" "),a("el-table",{staticClass:"table-content",attrs:{height:"300px",fit:"",stripe:"",data:t.fileData}},[a("el-table-column",{attrs:{prop:"fileLocalName",align:"left",label:"文件名称","show-overflow-tooltip":!0}}),t._v(" "),a("el-table-column",{attrs:{prop:"gmtCreate",align:"left",label:"创建时间","show-overflow-tooltip":!0},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(t._f("format")(e.row.gmtCreate)))]}}])}),t._v(" "),a("el-table-column",{attrs:{align:"left",label:"操作","show-overflow-tooltip":!0},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return t.downloadFile(e.row.filePath)}}},[t._v("下载")])]}}])})],1)],1),t._v(" "),a("el-dialog",{attrs:{title:"比对详情","append-to-body":"",visible:t.dialogVisible,width:"980px"},on:{"update:visible":function(e){t.dialogVisible=e}}},[a("compare-detail",{attrs:{"table-data1":t.transportSchedule,"table-data2":t.transportRawBts,"compare-result":t.compareResult}}),t._v(" "),a("span",{attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:function(e){t.dialogVisible=!1}}},[t._v("关闭")])],1)],1)],1)},staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"operate-box"},[e("span",{staticClass:"operate-table-title"},[this._v("比对详情列表")])])}]};var i=a("VU/8")(o,r,!1,function(t){a("RzGg")},"data-v-738ce023",null);e.default=i.exports}});