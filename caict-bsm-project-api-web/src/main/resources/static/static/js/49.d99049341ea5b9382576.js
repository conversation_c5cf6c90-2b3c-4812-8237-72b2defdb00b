webpackJsonp([49],{AP0Z:function(t,a){},PKyv:function(t,a,e){"use strict";Object.defineProperty(a,"__esModule",{value:!0});var n=e("Xxa5"),i=e.n(n),r=e("exGp"),s=e.n(r),o=e("sE1n"),l=(e("GbHy"),e("Vb+l"),e("Oq2I"),e("80cc"),e("nR8Q"),["2G","3G","4G","5G","GSM-R"]),d={data:function(){return{barOptions:{},barOptions02:{},pieOptions:{},checkAll:!0,dateTypes:["2G","3G","4G","5G","GSM-R"],signalOptions:[{label:"2G",value:"1"},{label:"3G",value:"2"},{label:"4G",value:"3"},{label:"5G",value:"4"}],isIndeterminate:!1}},created:function(){this.initData()},mounted:function(){window.addEventListener("resize",this.debounce())},beforeDestroy:function(){window.removeEventListener("resize",this.debounce())},methods:{debounce:function(){var t=this;if("/statistic/interact"===this.$route.path){var a=null;return function(){clearTimeout(a),a=setTimeout(function(){t.$refs.echarts1&&t.$refs.echarts1.resize(),t.$refs.echarts2&&t.$refs.echarts2.resize(),t.$refs.echarts3&&t.$refs.echarts3.resize()},500)}}},initData:function(){var t=this;return s()(i.a.mark(function a(){return i.a.wrap(function(a){for(;;)switch(a.prev=a.next){case 0:return t.getSystemData(),a.next=3,t.initChartData();case 3:case"end":return a.stop()}},a,t)}))()},defaultGenData:function(){return[{total:0,genNum:"2G"},{total:0,genNum:"3G"},{total:0,genNum:"4G"},{total:0,genNum:"5G"},{total:0,genNum:"GSM-R"}]},defaultNetData:function(){return[{total:0,genNum:"GSM"},{total:0,genNum:"CDMA"},{total:0,genNum:"CDMA2000"},{total:0,genNum:"TD-SCDMA"},{total:0,genNum:"WCDMA"},{total:0,genNum:"TD-LTE"},{total:0,genNum:"LTE FDD"},{total:0,genNum:"5G"},{total:0,genNum:"GSM-R"}]},initChartData:function(){var t=this;return this.$ajax.post({url:"/api/apiWeb/datav/approvedDataV/approvedGenNumDataV",data:{dateTypes:l}}).then(function(a){if(a.success){var e=[a.deal.mobile||t.defaultGenData(),a.deal.unicom||t.defaultGenData(),a.deal.telecom||t.defaultGenData(),a.deal.railway||t.defaultGenData(),a.deal.guangdian||t.defaultGenData()],n=[a.undeal.mobile||t.defaultGenData(),a.undeal.unicom||t.defaultGenData(),a.undeal.telecom||t.defaultGenData(),a.undeal.railway||t.defaultGenData(),a.undeal.guangdian||t.defaultGenData()];return t.barOptions=u(t.setSeries(e,n,1)),void(t.pieOptions=(i=t.setSeries(e,n,2),r=i.map(function(t){var a={name:"",value:0};return a.name=t.name,t.data.map(function(t){a.value+=parseInt(t)}),a}),{title:{show:!1},tooltip:{trigger:"item",formatter:"{a} <br/>{b} : {c}个 ({d}%)"},legend:{top:10,left:"center",textStyle:{fontSize:16},data:i.map(function(t){return t.name})},series:[{name:"基站统计",type:"pie",radius:"55%",center:["50%","50%"],data:r,label:{fontSize:16,normal:{formatter:"{a|{a}}{abg|}\n{hr|}\n  {b|{b}：}{c}个  \n  所占比例：{per|{d}%}  ",backgroundColor:"#EBEEF5",borderColor:"#255AE8",borderWidth:1,borderRadius:4,rich:{a:{color:"#fff",lineHeight:22,align:"center"},abg:{backgroundColor:"#255AE8",width:"100%",align:"right",height:22,borderRadius:[4,4,0,0]},hr:{borderColor:"#255AE8",width:"100%",borderWidth:.5,height:0},b:{fontSize:16,lineHeight:33},c:{fontSize:20},per:{color:"#eee",backgroundColor:"#334455",padding:[2,4],borderRadius:2,lineHeight:22}}}},itemStyle:{emphasis:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]}))}var i,r;t.barOptions="",t.pieOptions="",a.status||t.$message.error(a.reason)})},getSystemData:function(){var t=this;this.$ajax.post({url:"/api/apiWeb/datav/approvedDataV/approvedNetNumDataV",data:{dateTypes:"GSM,CDMA,TD-SCDMA,CDMA2000,WCDMA,LTE FDD,TD-LTE,5G,GSM-R".split(",")}}).then(function(a){if(a.success){var e=[a.deal.mobile||t.defaultNetData(),a.deal.unicom||t.defaultNetData(),a.deal.telecom||t.defaultNetData(),a.deal.railway||t.defaultNetData(),a.deal.guangdian||t.defaultNetData()],n=[a.undeal.mobile||t.defaultNetData(),a.undeal.unicom||t.defaultNetData(),a.undeal.telecom||t.defaultNetData(),a.undeal.railway||t.defaultNetData(),a.undeal.guangdian||t.defaultNetData()];t.barOptions02=u(t.setSeries(e,n,3))}else t.barOptions02=""})},setSeries:function(t,a,e){var n={name:"许可基站数",data:[]},i={name:"未许可基站数",data:[]},r={name:"基站总数",data:[]},s="",o={};1==e?s="genNum":2==e?s="genNum":3==e&&(s="genNum"),n.data=t.map(function(t){var a=0;return t.map(function(t){a+=parseInt(t.total)}),a}),i.data=a.map(function(t){var a=0;return t.map(function(t){a+=parseInt(t.total)}),a}),r.data=n.data.map(function(t,a){return parseInt(t)+parseInt(i.data[a])}),t.map(function(t){t.map(function(t){var a=t[s]+n.name;o[a]||(o[a]={},o[a].name=t[s],o[a].stack=n.name,o[a].data=[]),o[a].data.push(t.total)})}),a.map(function(t){t.map(function(t){var a=t[s]+i.name;o[a]||(o[a]={},o[a].name=t[s],o[a].stack=i.name,o[a].data=[]),o[a].data.push(t.total)})}),a.map(function(a,e){a.map(function(a,n){var i=a[s]+r.name;o[i]||(o[i]={},o[i].name=a[s],o[i].stack=r.name,o[i].data=[]),o[i].data.push(parseInt(t[e][n].total)+parseInt(a.total))})});var l=[];if(1==e||3==e)for(var d in o)l.push(o[d]);else l=[n,i];return l},handleCheckAllChange:function(t){this.checkedOptions=t?l:[],this.isIndeterminate=!1,this.initChartData()},handleCheckedChange:function(t){var a=t.length;this.checkedOptions=t,this.checkAll=a===l.length,this.isIndeterminate=a>0&&a<l.length,this.initChartData()}},components:{VEchart:o.a}};function u(t){return{title:{show:!1},grid:{left:"5%",right:"2%",top:"100"},tooltip:{trigger:"axis",axisPointer:{type:"shadow"},formatter:function(a,e,n){var i="",r="<div>",s=0;return a.map(function(a,e){t[e].stack!=i&&(1==s&&(s=0,r+="</div>"),s=1,r+="<div style='float:left; margin-right:20px;'>",i=t[e].stack,r+=t[e].stack+"<br/>"),r+=a.marker+a.seriesName+":"+a.value+" <br/>"}),r+="</div></div>"}},legend:{data:t.map(function(t){return t.name}),textStyle:{fontSize:16}},toolbox:{show:!0,feature:{dataView:{show:!0,readOnly:!1},magicType:{show:!0,type:["line","bar"]},restore:{show:!0},saveAsImage:{show:!0}}},calculable:!0,xAxis:{type:"category",axisLabel:{show:!0,fontSize:16},data:["许可-未许可-总数\n贵州移动","许可-未许可-总数\n贵州联通","许可-未许可-总数\n贵州电信","许可-未许可-总数\n贵州铁路","许可-未许可-总数\n贵州广电"]},yAxis:{type:"value",name:"基站数(个)",axisLabel:{fontSize:16}},series:t.map(function(t){return{name:t.name,type:"bar",data:t.data,stack:t.stack,markPoint:{data:[{type:"max",name:"最大值"},{type:"min",name:"最小值"}]},markLine:{data:[{type:"average",name:"平均值"}]}}})}}var c={render:function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{staticClass:"content-template"},[t._m(0),t._v(" "),e("el-row",{attrs:{gutter:10}},[e("el-col",{attrs:{span:14}},[e("el-card",{staticClass:"box-card"},[e("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e("span",[t._v("不同制式下许可、未许可基站数量统计")])]),t._v(" "),e("div",{staticClass:"operator"},[e("div",{staticStyle:{height:"50%","margin-bottom":"30px"}},[e("div",{staticClass:"check-box"}),t._v(" "),t.barOptions?[e("v-echart",{directives:[{name:"loading",rawName:"v-loading",value:0==Object.keys(t.barOptions).length,expression:"Object.keys(barOptions).length == 0"}],ref:"echarts1",attrs:{options:t.barOptions}})]:[e("div",{staticClass:"not_data",staticStyle:{"line-height":"200px"}},[t._v("暂无数据")])]],2),t._v(" "),e("div",{staticStyle:{height:"50%"}},[e("div",{staticClass:"check-box"}),t._v(" "),t.barOptions02?[e("v-echart",{directives:[{name:"loading",rawName:"v-loading",value:0==Object.keys(t.barOptions02).length,expression:"Object.keys(barOptions02).length == 0"}],ref:"echarts2",attrs:{options:t.barOptions02}})]:[e("div",{staticClass:"not_data",staticStyle:{"line-height":"200px"}},[t._v("暂无数据")])]],2)])])],1),t._v(" "),e("el-col",{attrs:{span:10}},[e("el-card",{staticClass:"box-card"},[e("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e("span",[t._v("许可、未许可基站数量统计")])]),t._v(" "),e("div",{staticClass:"operator"},[t.pieOptions?[e("v-echart",{directives:[{name:"loading",rawName:"v-loading",value:0==Object.keys(t.pieOptions).length,expression:"Object.keys(pieOptions).length == 0"}],ref:"echarts3",attrs:{options:t.pieOptions}})]:[e("div",{staticClass:"not_data",staticStyle:{"line-height":"200px"}},[t._v("暂无数据")])]],2)])],1)],1)],1)},staticRenderFns:[function(){var t=this.$createElement,a=this._self._c||t;return a("div",{staticClass:"operate-box"},[a("span",{staticClass:"operate-table-title"},[this._v("基站许可、未许可数量统计")])])}]};var p=e("VU/8")(d,c,!1,function(t){e("AP0Z")},"data-v-578978eb",null);a.default=p.exports}});