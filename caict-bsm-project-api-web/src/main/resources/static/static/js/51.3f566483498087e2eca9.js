webpackJsonp([51],{VuRs:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var l=a("2F/+"),o=a("99Ht"),n={data:function(){return{uploadHeaders:{token:sessionStorage.getItem("token")},ajaxDomain:o.a.ajaxDomain,tableData:[],dialogVisible:!1,currentDetail:{},operator:[{detail:"全部",code:""},{detail:"移动",code:"mobile"},{detail:"电信",code:"telecom"},{detail:"联通",code:"unicom"},{detail:"广电",code:"guangdian"}],pageTotal:0,loading:!0,pageDTO:{page:1,rows:20,jobGuid:this.$route.query.jobGuid,userType:"",genNum:"",difference:""},testFileText:""}},mounted:function(){this.initTableData()},methods:{tableRowClassName:function(e){var t=e.row;e.rowIndex;return console.log(213,t.difference),1==t.difference?"success-row":2==t.difference?"error-row":"warning-row"},initTableData:function(){var e=this;this.loading||(this.loading=!0),this.$ajax.post({url:"/api/apiWeb/transfer/roadTest/findAllByWhere",data:this.pageDTO}).then(function(t){if(e.loading=!1,t.success)return e.tableData=t.data.list,void(e.pageTotal=t.data.total);t.success||e.$message.error(t.message)},function(){return e.loading=!1})},handleSelectChange:function(){this.initTableData()},handleSearch:function(){this.initTableData()},handleCurrentChange:function(e){this.pageDTO.page=e,this.initTableData()},handleSizeChange:function(e){this.pageDTO.rows=e,this.initTableData()},uploadSuccess:function(e,t,a){e.success?(this.$message.success(e.message),this.initTableData()):this.$message.error(e.message)},goBack:function(){this.$router.back()}},components:{StationDetail:l.a},filters:{differenceFilter:function(e){switch(parseInt(e)){case 1:return"正常";case 2:default:return"异常"}}}},r={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"content-template"},[a("div",{staticClass:"search-box"},[a("el-form",{staticClass:"search-form",attrs:{inline:!0,"label-position":"left"}},[a("el-form-item",{attrs:{label:"运营商："}},[a("el-select",{attrs:{placeholder:"请选择"},on:{change:e.handleSelectChange},model:{value:e.pageDTO.userType,callback:function(t){e.$set(e.pageDTO,"userType",t)},expression:"pageDTO.userType"}},e._l(e.operator,function(e){return a("el-option",{key:e.code,attrs:{label:e.detail,value:e.code}})}),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"代数:","label-width":"40px"}},[a("el-radio-group",{model:{value:e.pageDTO.genNum,callback:function(t){e.$set(e.pageDTO,"genNum",t)},expression:"pageDTO.genNum"}},[a("el-radio-button",{attrs:{label:""}},[e._v("全部")]),e._v(" "),a("el-radio-button",{attrs:{label:"2"}},[e._v("2G")]),e._v(" "),a("el-radio-button",{attrs:{label:"3"}},[e._v("3G")]),e._v(" "),a("el-radio-button",{attrs:{label:"4"}},[e._v("4G")]),e._v(" "),a("el-radio-button",{attrs:{label:"5"}},[e._v("5G")]),e._v(" "),a("el-radio-button",{attrs:{label:"GSM-R"}},[e._v("GSM-R")])],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"类别:","label-width":"40px"}},[a("el-radio-group",{model:{value:e.pageDTO.difference,callback:function(t){e.$set(e.pageDTO,"difference",t)},expression:"pageDTO.difference"}},[a("el-radio-button",{attrs:{label:""}},[e._v("全部")]),e._v(" "),a("el-radio-button",{attrs:{label:"1"}},[e._v("正常")]),e._v(" "),a("el-radio-button",{attrs:{label:"2"}},[e._v("异常")])],1)],1),e._v(" "),a("el-form-item",{staticClass:"form_item_btn"},[a("el-button",{staticClass:"default-button",attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.handleSearch}},[e._v("搜索")])],1)],1)],1),e._v(" "),a("div",[a("el-button",{staticClass:"station_map_btn",attrs:{icon:"el-icon-d-arrow-left",type:"success"},on:{click:e.goBack}},[a("span",[e._v("返回")])]),e._v(" "),a("span",{staticStyle:{"margin-left":"20px","font-size":"14px",color:"#888"}},[e._v("\n      "+e._s(e.testFileText)+"\n    ")]),e._v(" "),e.testFileText?a("el-button",{attrs:{type:"success"}},[a("span",[e._v("开始分析")])]):e._e()],1),e._v(" "),a("div",{staticClass:"table-box"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"table-content",attrs:{height:"100%",data:e.tableData,"row-class-name":e.tableRowClassName}},[a("el-table-column",{attrs:{prop:"userType",label:"运营商",align:"center","show-overflow-tooltip":!0}}),e._v(" "),a("el-table-column",{attrs:{prop:"genNum",label:"代数",align:"center","show-overflow-tooltip":!0},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.genNum)+"G")]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"techType",label:"技术体制",align:"center","show-overflow-tooltip":!0},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n          "+e._s(e._f("techTypeFilter")(t.row.techType))+"\n        ")]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"arcFrq",label:"载波号",align:"center","show-overflow-tooltip":!0}}),e._v(" "),a("el-table-column",{attrs:{prop:"carrierFreq",label:"载波频率",align:"center","show-overflow-tooltip":!0}}),e._v(" "),a("el-table-column",{attrs:{prop:"carrierBandth",label:"载波带宽",align:"center","show-overflow-tooltip":!0}}),e._v(" "),a("el-table-column",{attrs:{prop:"mcc",label:"移动国家编号",align:"center","show-overflow-tooltip":!0}}),e._v(" "),a("el-table-column",{attrs:{prop:"mnId",label:"系统编号",align:"center","show-overflow-tooltip":!0}}),e._v(" "),a("el-table-column",{attrs:{prop:"zoneId",label:"位置区编号",align:"center","show-overflow-tooltip":!0}}),e._v(" "),a("el-table-column",{attrs:{prop:"cellId",label:"小区编号",align:"center","show-overflow-tooltip":!0}}),e._v(" "),a("el-table-column",{attrs:{prop:"londe",label:"经度",align:"center","show-overflow-tooltip":!0}}),e._v(" "),a("el-table-column",{attrs:{prop:"latde",label:"纬度",align:"center","show-overflow-tooltip":!0}}),e._v(" "),a("el-table-column",{attrs:{prop:"difference",label:"状态",align:"center","show-overflow-tooltip":!0},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n          "+e._s(e._f("differenceFilter")(t.row.difference))+"\n        ")]}}])})],1),e._v(" "),a("div",{staticClass:"table-pagin"},[a("el-pagination",{attrs:{layout:"sizes, total, prev, pager, next, jumper","page-sizes":[20,50,100,200],"current-page":e.pageDTO.page,total:e.pageTotal},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1),e._v(" "),a("el-dialog",{attrs:{title:"扇区数据查看信息",visible:e.dialogVisible,"custom-class":"history-dialog",width:"998px","append-to-body":""},on:{"update:visible":function(t){e.dialogVisible=t}}},[a("station-detail",{attrs:{detail:e.currentDetail}})],1)],1)},staticRenderFns:[]};var i=a("VU/8")(n,r,!1,function(e){a("vrA1")},"data-v-47bedace",null);t.default=i.exports},vrA1:function(e,t){}});