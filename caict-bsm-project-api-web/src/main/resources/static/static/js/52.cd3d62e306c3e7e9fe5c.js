webpackJsonp([52],{agLR:function(t,a){},nXyW:function(t,a,e){"use strict";Object.defineProperty(a,"__esModule",{value:!0});var o=e("Xxa5"),l=e.n(o),n=e("exGp"),i=e.n(n),s=e("oFuF"),r=(e("U3S+"),e("2F/+")),c=(new s.a,{data:function(){return{dataTypeList:[{value:1,label:"新增"},{value:2,label:"变更"},{value:3,label:"注销"}],isShowWW:!1,isShowMob:!1,fileStaticData:[],stationData:[],stationDetail:"",stationOptions:[],jobDateBegin:"",jobDateEnd:"",isActiveNum:-1,popShow:!1,pageTotal:0,loading:!0,jobStationPageDTO:{applytable:"",btsId:"",btsName:"",cellId:"",cellName:"",currentUserGuid:"",dataType:"",jobId:"",page:1,rows:10}}},mounted:function(){var t=this;this.$nextTick(function(){t.jobStationPageDTO.jobId=t.$route.params.id||"",t._getStationList(),"ROLE_MO"===sessionStorage.getItem("WXDYFSR")?t.isShowMob=!0:t.isShowWW=!0})},methods:{selectChange:function(){this._getStationList()},_getStationList:function(){var t=this;return i()(l.a.mark(function a(){return l.a.wrap(function(a){for(;;)switch(a.prev=a.next){case 0:return t.loading||(t.loading=!0),a.abrupt("return",t.$ajax.post({url:"/api/transportJob/station/list",data:t.jobStationPageDTO}).then(function(a){a&&(a.status||t.$message.error(a.reason),a.status&&(t.stationData=a.records,t.pageTotal=a.total,t.loading=!1))},function(a){return t.loading=!1}));case 2:case"end":return a.stop()}},a,t)}))()},_getStationOptions:function(){var t=this;return i()(l.a.mark(function a(){return l.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:case"end":return t.stop()}},a,t)}))()},searchFun:function(){this.jobStationPageDTO.page=1,this._getStationList()},getStationDetail:function(t,a){var e=this;return i()(l.a.mark(function t(){return l.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:e.stationDetail=a,e.popShow=!0;case 2:case"end":return t.stop()}},t,e)}))()},changePage:function(t){this.jobStationPageDTO.page=t,this.isActiveNum=-1,this._getStationList()}},components:{StationDetail:r.a}}),p={render:function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{staticClass:"content-template"},[t.isShowWW?e("div",{staticClass:"top-nav"},[e("el-breadcrumb",{attrs:{"separator-class":"el-icon-arrow-right"}},[e("el-breadcrumb-item",{attrs:{to:"/matter"}},[t._v("事项列表")]),t._v(" "),e("el-breadcrumb-item",[t._v("数据列表")])],1)],1):t._e(),t._v(" "),t.isShowMob?e("div",{staticClass:"top-nav"},[e("el-breadcrumb",{attrs:{"separator-class":"el-icon-arrow-right"}},[e("el-breadcrumb-item",{attrs:{to:"/report/me"}},[t._v("我的上报")]),t._v(" "),e("el-breadcrumb-item",[t._v("数据列表")])],1)],1):t._e(),t._v(" "),e("div",{staticClass:"search-box"},[e("el-form",{staticClass:"search-form",attrs:{inline:!0,"label-position":"left"}},[e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:5}},[e("el-form-item",{attrs:{label:"申请表编号：","label-width":"120px"}},[e("el-input",{attrs:{clearable:""},model:{value:t.jobStationPageDTO.applytable,callback:function(a){t.$set(t.jobStationPageDTO,"applytable",a)},expression:"jobStationPageDTO.applytable"}})],1)],1),t._v(" "),e("el-col",{attrs:{span:5}},[e("el-form-item",{attrs:{label:"扇区名称："}},[e("el-input",{attrs:{clearable:""},model:{value:t.jobStationPageDTO.btsName,callback:function(a){t.$set(t.jobStationPageDTO,"btsName",a)},expression:"jobStationPageDTO.btsName"}})],1)],1),t._v(" "),e("el-col",{attrs:{span:5}},[e("el-form-item",{attrs:{label:"扇区识别码：","label-width":"120px"}},[e("el-input",{attrs:{clearable:""},model:{value:t.jobStationPageDTO.btsId,callback:function(a){t.$set(t.jobStationPageDTO,"btsId",a)},expression:"jobStationPageDTO.btsId"}})],1)],1),t._v(" "),e("el-col",{attrs:{span:5}},[e("el-form-item",{attrs:{label:"变更类型："}},[e("el-select",{attrs:{clearable:"",placeholder:"请选择变更类型"},on:{change:t.selectChange},model:{value:t.jobStationPageDTO.dataType,callback:function(a){t.$set(t.jobStationPageDTO,"dataType",a)},expression:"jobStationPageDTO.dataType"}},t._l(t.dataTypeList,function(t){return e("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})}),1)],1)],1),t._v(" "),e("el-col",{attrs:{span:4}},[e("el-button",{staticClass:"default-button",attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.searchFun}},[t._v("搜索")])],1)],1)],1)],1),t._v(" "),t._m(0),t._v(" "),e("div",{staticClass:"table-box"},[e("el-table",{staticClass:"table-content",attrs:{height:"100%",data:t.stationData,stripe:""}},[e("el-table-column",{attrs:{prop:"applicationCode",label:"申请表编号",align:"center","show-overflow-tooltip":!0}}),t._v(" "),e("el-table-column",{attrs:{prop:"cellName",label:"扇区名称",align:"center","show-overflow-tooltip":!0}}),t._v(" "),e("el-table-column",{attrs:{prop:"cellId",label:"扇区识别码",align:"center","show-overflow-tooltip":!0}}),t._v(" "),e("el-table-column",{attrs:{prop:"techType",label:"技术体制",align:"center","show-overflow-tooltip":!0}}),t._v(" "),e("el-table-column",{attrs:{prop:"location",label:"台址",align:"center","show-overflow-tooltip":!0}}),t._v(" "),e("el-table-column",{attrs:{prop:"longitude",label:"经度",align:"center","show-overflow-tooltip":!0}}),t._v(" "),e("el-table-column",{attrs:{prop:"latitude",label:"纬度",align:"center","show-overflow-tooltip":!0}}),t._v(" "),e("el-table-column",{attrs:{label:"操作",width:"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{attrs:{type:"text"},on:{click:function(e){return t.getStationDetail(a.$index,a.row)}}},[t._v("查看\n          ")])]}}])})],1),t._v(" "),e("div",{staticClass:"table-pagin"},[e("el-pagination",{attrs:{layout:"total, prev, pager, next, jumper","current-page":t.jobStationPageDTO.page,total:t.pageTotal},on:{"current-change":t.changePage}})],1)],1),t._v(" "),e("el-dialog",{attrs:{title:"扇区数据查看信息",visible:t.popShow,"custom-class":"history-dialog",width:"998px"},on:{"update:visible":function(a){t.popShow=a}}},[e("StationDetail",{attrs:{detail:t.stationDetail}})],1)],1)},staticRenderFns:[function(){var t=this.$createElement,a=this._self._c||t;return a("div",{staticClass:"operate-box"},[a("span",{staticClass:"operate-table-title"},[this._v("数据信息")])])}]};var u=e("VU/8")(c,p,!1,function(t){e("agLR")},"data-v-3f01234f",null);a.default=u.exports}});