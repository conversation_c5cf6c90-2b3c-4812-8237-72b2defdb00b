webpackJsonp([53],{"1RpI":function(t,a){},Ex1Y:function(t,a,e){"use strict";Object.defineProperty(a,"__esModule",{value:!0});var l,o,s=e("Xxa5"),n=e.n(s),r=e("exGp"),i=e.n(r),p=e("oFuF"),c=new p.a,u=(l=i()(n.a.mark(function t(a){var e;return n.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e={type:"post",url:"/api/station/list",data:a},t.abrupt("return",c.requestFun(e));case 2:case"end":return t.stop()}},t,this)})),o=i()(n.a.mark(function t(a){var e;return n.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e={type:"get",url:"/api/apiWeb/station/rsbtStation/findOneByGuid/"+a.stationId},t.abrupt("return",c.requestFun(e));case 2:case"end":return t.stop()}},t,this)})),function(t){return o.apply(this,arguments)}),d=new p.a,b={props:["id"],data:function(){return{from:this.$route.query.from,stationData:"",loading:!0,sectorData:[]}},mounted:function(){var t=this;this.$nextTick(function(){t._getStationDetail()})},methods:{returnFreq:function(t,a){return d.returnFreq(t,a)},_getStationDetail:function(){var t=this;return i()(n.a.mark(function a(){var e,l;return n.a.wrap(function(a){for(;;)switch(a.prev=a.next){case 0:return t.loading||(t.loading=!0),e={stationId:t.id||t.$route.params.stationId},a.next=4,u(e);case 4:(l=a.sent).status?(t.stationData=l.res.data,t.sectorData=t.stationData.sectionDTOList):t.$message.error("基站管理详情加载失败"),t.loading=!1;case 7:case"end":return a.stop()}},a,t)}))()},tableRowClassName:function(t){var a=t.row;t.rowIndex;return 1==a.analysisStatus&&1==a.isValid?"success-row":"error-row"}}},w={render:function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{staticClass:"content-template"},[e("div",{staticClass:"top-nav"},["compare-operate"==t.from?e("el-button",{attrs:{type:"primary"},on:{click:function(a){return t.$router.back()}}},[t._v("返回")]):e("el-breadcrumb",{attrs:{"separator-class":"el-icon-arrow-right"}},[e("el-breadcrumb-item",{attrs:{to:"/station/manage"}},[t._v("基站管理")]),t._v(" "),e("el-breadcrumb-item",[t._v("基站管理详情")])],1)],1),t._v(" "),e("div",{staticClass:"search-box"},[e("el-form",{staticStyle:{padding:"20px 20px 0"},attrs:{"label-width":"100px",inline:!1,"label-position":"left"}},[e("el-row",{attrs:{gutter:20}},[e("el-col",{attrs:{span:8}},[e("el-form-item",{attrs:{label:"基站名称：","label-width":"100px"}},[e("el-input",{staticClass:"input-width",attrs:{disabled:!0},model:{value:t.stationData.statName,callback:function(a){t.$set(t.stationData,"statName",a)},expression:"stationData.statName"}})],1)],1),t._v(" "),e("el-col",{attrs:{span:8}},[e("el-form-item",{attrs:{label:"基站识别码：","label-width":"100px"}},[e("el-input",{staticClass:"input-width",attrs:{disabled:!0},model:{value:t.stationData.stationCode,callback:function(a){t.$set(t.stationData,"stationCode",a)},expression:"stationData.stationCode"}})],1)],1),t._v(" "),e("el-col",{attrs:{span:8}},[e("el-form-item",{attrs:{label:"型号核准代码：","label-width":"120px"}},[e("el-input",{staticClass:"input-width",attrs:{disabled:!0},model:{value:t.stationData.modelCode,callback:function(a){t.$set(t.stationData,"modelCode",a)},expression:"stationData.modelCode"}})],1)],1),t._v(" "),e("el-col",{attrs:{span:8}},[e("el-form-item",{attrs:{label:"技术体制：","label-width":"100px"}},[e("el-input",{staticClass:"input-width",attrs:{disabled:!0},model:{value:t.stationData.netType,callback:function(a){t.$set(t.stationData,"netType",a)},expression:"stationData.netType"}})],1)],1),t._v(" "),e("el-col",{attrs:{span:8}},[e("el-form-item",{attrs:{label:"台址：","label-width":"100px"}},[e("el-input",{staticClass:"input-width",attrs:{disabled:!0},model:{value:t.stationData.statAddr,callback:function(a){t.$set(t.stationData,"statAddr",a)},expression:"stationData.statAddr"}})],1)],1),t._v(" "),e("el-col",{attrs:{span:8}},[e("el-form-item",{attrs:{label:"经度：","label-width":"100px"}},[e("el-input",{staticClass:"input-width",attrs:{disabled:!0},model:{value:t.stationData.statLg,callback:function(a){t.$set(t.stationData,"statLg",a)},expression:"stationData.statLg"}})],1)],1),t._v(" "),e("el-col",{attrs:{span:8}},[e("el-form-item",{attrs:{label:"纬度：","label-width":"100px"}},[e("el-input",{staticClass:"input-width",attrs:{disabled:!0},model:{value:t.stationData.statLa,callback:function(a){t.$set(t.stationData,"statLa",a)},expression:"stationData.statLa"}})],1)],1),t._v(" "),e("el-col",{attrs:{span:8}},[e("el-form-item",{attrs:{label:"海拔高度：","label-width":"100px"}},[e("el-input",{staticClass:"input-width",attrs:{disabled:!0},model:{value:t.stationData.statAt,callback:function(a){t.$set(t.stationData,"statAt",a)},expression:"stationData.statAt"}})],1)],1)],1)],1)],1),t._v(" "),e("div",{staticClass:"table-box"},[e("el-table",{staticClass:"table-content",attrs:{height:"100%",fit:"",data:t.sectorData,"row-class-name":t.tableRowClassName}},[e("el-table-column",{attrs:{prop:"sectionName",align:"center",width:"150px",fixed:"left","show-overflow-tooltip":!0,label:"扇区名称"}}),t._v(" "),e("el-table-column",{attrs:{prop:"sectionCode",align:"center",width:"150px","show-overflow-tooltip":!0,label:"扇区识别码"}}),t._v(" "),e("el-table-column",{attrs:{prop:"analysisStatus",label:"干扰分析",align:"center","show-overflow-tooltip":!0},scopedSlots:t._u([{key:"default",fn:function(a){return[t._v(t._s(1==a.row.analysisStatus?"未干扰":"受干扰"))]}}])}),t._v(" "),e("el-table-column",{attrs:{prop:"isValid",label:"频段是否合规",align:"center","show-overflow-tooltip":!0},scopedSlots:t._u([{key:"default",fn:function(a){return[t._v(t._s(1==a.row.isValid?"合规":"不合规"))]}}])}),t._v(" "),e("el-table-column",{attrs:{prop:"freqEfb",align:"center",width:"160px","show-overflow-tooltip":!0,label:"发射频率(起)(MHz)"}}),t._v(" "),e("el-table-column",{attrs:{prop:"freqEfe",align:"center",width:"160px","show-overflow-tooltip":!0,label:"发射频率(止)(MHz)"}}),t._v(" "),e("el-table-column",{attrs:{prop:"freqRfb",align:"center",width:"160px","show-overflow-tooltip":!0,label:"接收频率(起)(MHz)"}}),t._v(" "),e("el-table-column",{attrs:{prop:"freqRfe",align:"center",width:"160px","show-overflow-tooltip":!0,label:"接收频率(止)(MHz)"}}),t._v(" "),e("el-table-column",{attrs:{prop:"antHight",align:"center","show-overflow-tooltip":!0,width:"150px",label:"天线距地高度(m)"}}),t._v(" "),e("el-table-column",{attrs:{prop:"antMenu",align:"center","show-overflow-tooltip":!0,width:"120px",label:"天线生产厂家"}}),t._v(" "),e("el-table-column",{attrs:{prop:"antType",align:"center","show-overflow-tooltip":!0,width:"100px",label:"天线类型"}}),t._v(" "),e("el-table-column",{attrs:{prop:"antAngle",align:"center","show-overflow-tooltip":!0,width:"120px",label:"天线方位角(°)"}}),t._v(" "),e("el-table-column",{attrs:{prop:"antGain",align:"center","show-overflow-tooltip":!0,width:"120px",label:"天线增益(dBi)"}}),t._v(" "),e("el-table-column",{attrs:{prop:"maxPower",align:"center","show-overflow-tooltip":!0,width:"130px",label:"最大发射功率(W)"}}),t._v(" "),e("el-table-column",{attrs:{prop:"equMenu",align:"center","show-overflow-tooltip":!0,width:"130px",label:"设备生产厂家"}}),t._v(" "),e("el-table-column",{attrs:{prop:"antPole",align:"center","show-overflow-tooltip":!0,width:"120px",label:"极化方式"}}),t._v(" "),e("el-table-column",{attrs:{prop:"feedLose",align:"center","show-overflow-tooltip":!0,width:"150px",label:"馈线系统总损耗(dB)"}}),t._v(" "),e("el-table-column",{attrs:{prop:"equAuth",align:"center","show-overflow-tooltip":!0,width:"120px",label:"型号核准代码"}})],1)],1)])},staticRenderFns:[]};var f=e("VU/8")(b,w,!1,function(t){e("1RpI")},"data-v-2f321888",null);a.default=f.exports}});