webpackJsonp([54],{"/Uvy":function(e,t){},c1Nl:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var l=new(a("oFuF").a),i={data:function(){return{roleType:sessionStorage.getItem("roleType"),timer:null,showSearchBox:!1,showAdvancedSearch:!1,viewModel:1,tableData:[],isCompare:[{detail:"全部",code:""},{detail:"待提交",code:0},{detail:"校验中",code:1},{detail:"对比中",code:2},{detail:"待审核",code:3},{detail:"审核中",code:4},{detail:"干扰协调中",code:5},{detail:"审核确认中",code:6},{detail:"完结",code:7},{detail:"无委待确认",code:8},{detail:"无委确认中",code:9}],currentDetail:{},operator:[{detail:"全部",code:""},{detail:"移动",code:"mobile"},{detail:"电信",code:"telecom"},{detail:"联通",code:"unicom"},{detail:"广电",code:"guangdian"}],pageTotal:0,loading:!0,isAdvancedSearch:!1,pageDTO:{page:1,rows:10,netTs:"",genNum:"",orgName:/wuwei|admin/gi.test(sessionStorage.getItem("roleType"))?"mobile":"",fileNo:"",state:"1"},multipleSelection:[],dialogShow:!1,delDialogShow:!1,delItemObj:null,delBatchDialogShow:!1,editForm:{isEdit:!0,id:"",orgGuid:"",netTs:"",genNum:"2",freqEfb:"",freqEfe:"",freqRfb:"",freqRfe:"",fileNo:"",state:"1",orgName:""},formRules:{orgGuid:[{required:!0,message:"请选择组织机构",trigger:"change"}],netTs:[{required:!0,message:"请输入技术制式",trigger:"blur"}],genNum:[{required:!0,message:"请输入代数",trigger:"blur"}],freqEfb:[{required:!0,message:"请输入发射频率下限",trigger:"blur"}],freqEfe:[{required:!0,message:"请输入发射频率上限",trigger:"blur"}],freqRfb:[{required:!0,message:"请输入接收频率上限",trigger:"blur"}],freqRfe:[{required:!0,message:"请输入接收频率下限",trigger:"blur"}],fileNo:[{required:!0,message:"请输入频率使用许可证号或批准文号",trigger:"blur"}]},DD:this.$dd,orgList:[],allStatus:"1"}},watch:{dialogShow:function(e){e||this.initForm()},allStatus:function(e){var t=this;this.$ajax.get({url:"/api/apiWeb/transfer/fsaCheckRule/updateAllState/"+e}).then(function(e){e.success?t.initTableData():t.$message.error(result.message)})}},mounted:function(){var e=this;this.$nextTick(function(){e.getOrgList(),e.initTableData()})},beforeDestroy:function(){this.clearTimer()},methods:{initForm:function(){this.editForm={id:"",orgGuid:"",netTs:"",genNum:"2",freqEfb:"",freqEfe:"",freqRfb:"",freqRfe:"",fileNo:"",state:"1",orgName:"",orgType:""}},handleSelectionChange:function(e){this.multipleSelection=e},delAll:function(){this.delBatchDialogShow=!0},sureDelBatch:function(){var e=this;this.delBatchDialogShow=!1,this.$ajax.post({url:"/api/apiWeb/transfer/fsaCheckRule/deletes",data:{ids:this.multipleSelection.map(function(e){return e.id})}}).then(function(t){t.success?e.initTableData():e.$message.error(result.message)})},initTableData:function(){var e=this;this.loading||(this.loading=!0),this.tableData=[],this.$ajax.post({url:"/api/apiWeb/transfer/fsaCheckRule/findAllPageByWhere",data:this.pageDTO}).then(function(t){if(e.loading=!1,t.success)return e.tableData=t.data.list,void(e.pageTotal=t.data.total);t.success||e.$message.error(t.message)},function(){e.loading=!1,e.clearTimer()})},add:function(){this.editForm.isEdit=!0,this.dialogShow=!0},seeItem:function(e){var t=this;this.$ajax.get({url:"/api/apiWeb/transfer/fsaCheckRule/findOneDto/"+e.id}).then(function(e){e.success?(t.editForm=e.data,t.editForm.isEdit=!1,t.dialogShow=!0):t.$message.error(result.message)})},editItem:function(e){var t=this;this.$ajax.get({url:"/api/apiWeb/transfer/fsaCheckRule/findOneDto/"+e.id}).then(function(e){e.success?(t.editForm=e.data,t.editForm.isEdit=!0,t.dialogShow=!0):t.$message.error(result.message)})},delItem:function(e){this.delDialogShow=!0,this.delItemObj=e},sureDelItem:function(){var e=this;this.delDialogShow=!1;var t=this.delItemObj;this.$ajax.get({url:"/api/apiWeb/transfer/fsaCheckRule/delete/"+t.id}).then(function(t){t.success?e.initTableData():e.$message.error(result.message)})},handleSelectChange:function(){this.initTableData()},handleSearch:function(){var e=l.toTimestamp(this.pageDTO.appDateStart),t=l.toTimestamp(this.pageDTO.appDateEnd);if(e>t&&""!=e&""!=t)return this.$message.warning("开始时间不能大于结束时间，请重新选择"),!1;this.initTableData()},handleChangePage:function(e){this.pageDTO.page=e,this.initTableData()},openAdvancedSearch:function(){this.showAdvancedSearch=!this.showAdvancedSearch},clearTimer:function(){this.timer&&clearInterval(this.timer),this.timer=null},submit:function(){var e=this,t=!0;this.$refs.form.validate(function(a){a||(e.$message.error("请完善所有信息"),t=!1)}),t&&(this.orgList.map(function(t){t.orgGuid==e.editForm.orgGuid&&(e.editForm.orgName=t.orgName)}),this.$ajax.post({url:"/api/apiWeb/transfer/fsaCheckRule/save",data:this.editForm}).then(function(t){t.success?(e.initTableData(),e.dialogShow=!1):e.$message.error(t.message)}))},cancel:function(){this.dialogShow=!1},getOrgList:function(){var e=this;this.$ajax.get({url:"/api/apiWeb/security/org/findAllByIsDeleted/0"}).then(function(t){t.success?e.orgList=t.data.map(function(e){return{orgGuid:e.guid,orgName:e.orgName}}):e.$message.error(result.message)})}}},o={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"content-template"},[e.showSearchBox?e._e():a("el-button",{staticStyle:{"text-align":"left"},attrs:{type:"text"},on:{click:function(t){e.showSearchBox=!e.showSearchBox}}},[e._v("\n    展开搜索框 \n    "),a("span",{staticClass:"el-icon-arrow-down form-item-icon"})]),e._v(" "),e.showSearchBox?a("el-button",{staticStyle:{"text-align":"left"},attrs:{type:"text"},on:{click:function(t){e.showSearchBox=!e.showSearchBox}}},[e._v("\n    收起搜索框 \n    "),a("span",{staticClass:"el-icon-arrow-up form-item-icon"})]):e._e(),e._v(" "),e.showSearchBox?a("div",{staticClass:"search-box"},[a("el-form",{staticClass:"search-form",attrs:{inline:!0,"label-position":"left","label-width":"80px"}},[a("el-form-item",{staticClass:"form_item",attrs:{label:"技术制式:"}},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:e.pageDTO.netTs,callback:function(t){e.$set(e.pageDTO,"netTs",t)},expression:"pageDTO.netTs"}},e._l(e.DD.ruleNets,function(e){return a("el-option",{key:e,attrs:{label:e,value:e}})}),1)],1),e._v(" "),a("el-form-item",{staticClass:"form_item",attrs:{label:"代数:","label-width":"40px"}},[a("el-radio-group",{model:{value:e.pageDTO.genNum,callback:function(t){e.$set(e.pageDTO,"genNum",t)},expression:"pageDTO.genNum"}},[a("el-radio-button",{attrs:{label:""}},[e._v("全部")]),e._v(" "),a("el-radio-button",{attrs:{label:"2"}},[e._v("2G")]),e._v(" "),a("el-radio-button",{attrs:{label:"3"}},[e._v("3G")]),e._v(" "),a("el-radio-button",{attrs:{label:"4"}},[e._v("4G")]),e._v(" "),a("el-radio-button",{attrs:{label:"5"}},[e._v("5G")]),e._v(" "),a("el-radio-button",{attrs:{label:"GSM-R"}},[e._v("GSM-R")])],1)],1),e._v(" "),e.showAdvancedSearch?[a("el-form-item",{staticClass:"form_item",attrs:{label:"状态:"}},[a("el-radio-group",{model:{value:e.pageDTO.state,callback:function(t){e.$set(e.pageDTO,"state",t)},expression:"pageDTO.state"}},[a("el-radio",{attrs:{label:"1"}},[e._v("启用")]),e._v(" "),a("el-radio",{attrs:{label:"2"}},[e._v("停用")])],1)],1),e._v(" "),a("el-form-item",{staticClass:"form_item_two",attrs:{label:"频率使用许可证号或批准文号:","label-width":"200px"}},[a("el-input",{attrs:{clearable:""},model:{value:e.pageDTO.fileNo,callback:function(t){e.$set(e.pageDTO,"fileNo",t)},expression:"pageDTO.fileNo"}})],1)]:e._e(),e._v(" "),a("el-form-item",{staticStyle:{display:"inline-block",width:"auto"},attrs:{"label-width":"0"}},[a("el-button",{staticClass:"default-button",attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.handleSearch}},[e._v("搜索")]),e._v(" "),e.showAdvancedSearch?e._e():a("el-button",{attrs:{type:"text"},on:{click:e.openAdvancedSearch}},[e._v("\n          展开高级搜索 \n          "),a("span",{staticClass:"el-icon-arrow-down form-item-icon"})]),e._v(" "),e.showAdvancedSearch?a("el-button",{attrs:{type:"text"},on:{click:e.openAdvancedSearch}},[e._v("\n          收起高级搜索 \n          "),a("span",{staticClass:"el-icon-arrow-up form-item-icon"})]):e._e()],1)],2)],1):e._e(),e._v(" "),a("div",{staticClass:"operate-box",staticStyle:{display:"flex"}},[a("div",{staticStyle:{flex:"1"}},[a("span",{staticClass:"operate-table-title"},[e._v("规则列表")]),e._v(" "),/^wuweiProvincial$|^admin$/.test(e.roleType)?a("el-radio-group",{staticStyle:{margin:"30px 0"},model:{value:e.viewModel,callback:function(t){e.viewModel=t},expression:"viewModel"}},[a("el-radio-button",{attrs:{label:"1"}},[e._v("预览模式")]),e._v(" "),a("el-radio-button",{attrs:{label:"2"}},[e._v("编辑模式")])],1):e._e()],1),e._v(" "),/^wuweiProvincial$|^admin$/.test(e.roleType)&&"2"==e.viewModel?a("div",{staticClass:"btn_group"},[a("el-button",{staticClass:"button-add",attrs:{type:"primary"},on:{click:e.add}},[e._v("新增")]),e._v(" "),a("el-button",{staticClass:"button-add",staticStyle:{width:"100px"},attrs:{type:"danger"},on:{click:e.delAll}},[e._v("批量删除")]),e._v(" "),a("el-switch",{staticStyle:{"margin-left":"30px"},attrs:{"active-color":"#13ce66","inactive-color":"#ff4949","active-text":"全部启用","inactive-text":"全部禁用","active-value":"1","inactive-value":"2"},model:{value:e.allStatus,callback:function(t){e.allStatus=t},expression:"allStatus"}})],1):e._e()]),e._v(" "),/wuwei|admin/gi.test(e.roleType)?a("el-tabs",{attrs:{type:"card"},on:{"tab-click":e.handleSearch},model:{value:e.pageDTO.orgName,callback:function(t){e.$set(e.pageDTO,"orgName",t)},expression:"pageDTO.orgName"}},[a("el-tab-pane",{attrs:{label:"中国移动",name:"mobile"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"中国电信",name:"telecom"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"中国联通",name:"unicom"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"中国铁路",name:"railway"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"中国广电",name:"guangdian"}})],1):e._e(),e._v(" "),a("div",{staticClass:"table-box"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"table-content",attrs:{height:"100%",data:e.tableData,stripe:""},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55"}}),e._v(" "),a("el-table-column",{attrs:{prop:"orgName",label:"运营商",align:"center"}}),e._v(" "),a("el-table-column",{attrs:{prop:"state",label:"状态",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n          "+e._s(1==t.row.state?"启用":"停用")+"\n        ")]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"netTs",label:"技术制式",align:"center","show-overflow-tooltip":!0}}),e._v(" "),a("el-table-column",{attrs:{prop:"genNum",label:"代数",align:"center","show-overflow-tooltip":!0},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.genNum)+"G")]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"freqEfb",label:"发射频率下限(MHz)",align:"center","show-overflow-tooltip":!0}}),e._v(" "),a("el-table-column",{attrs:{prop:"freqEfe",label:"发射频率上限(MHz)",align:"center","show-overflow-tooltip":!0}}),e._v(" "),a("el-table-column",{attrs:{prop:"freqRfb",label:"接收频率下限(MHz)",align:"center","show-overflow-tooltip":!0}}),e._v(" "),a("el-table-column",{attrs:{prop:"freqRfe",label:"接收频率上限(MHz)",align:"center","show-overflow-tooltip":!0}}),e._v(" "),a("el-table-column",{attrs:{prop:"fileNo",label:"频率使用许可证号或批准文号",align:"center","show-overflow-tooltip":!0}}),e._v(" "),a("el-table-column",{attrs:{label:"操作",width:"150",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text",disabled:1===t.row.approveState},on:{click:function(a){return e.seeItem(t.row)}}},[e._v("查看")]),e._v(" "),2==e.viewModel?[/^wuweiProvincial$|^admin$/.test(e.roleType)?a("el-button",{attrs:{type:"text",disabled:1===t.row.approveState},on:{click:function(a){return e.editItem(t.row)}}},[e._v("编辑")]):e._e(),e._v(" "),/^wuweiProvincial$|^admin$/.test(e.roleType)?a("el-button",{attrs:{type:"text",disabled:1===t.row.approveState},on:{click:function(a){return e.delItem(t.row)}}},[e._v("删除")]):e._e()]:e._e()]}}])})],1),e._v(" "),a("div",{staticClass:"table-pagin"},[a("el-pagination",{attrs:{layout:"total, prev, pager, next, jumper","current-page":e.pageDTO.page,total:e.pageTotal},on:{"current-change":e.handleChangePage}})],1)],1),e._v(" "),a("el-dialog",{attrs:{title:"规则添加/修改",visible:e.dialogShow},on:{"update:visible":function(t){e.dialogShow=t}}},[a("el-form",{ref:"form",staticClass:"search-form",attrs:{inline:!0,"label-position":"right","label-width":"120px",disabled:!e.editForm.isEdit,model:e.editForm,rules:e.formRules}},[a("el-form-item",{staticClass:"form_edit_item",attrs:{prop:"netTs",label:"技术制式："}},[a("el-input",{attrs:{clearable:""},model:{value:e.editForm.netTs,callback:function(t){e.$set(e.editForm,"netTs",t)},expression:"editForm.netTs"}})],1),e._v(" "),a("el-form-item",{staticClass:"form_edit_item",attrs:{prop:"genNum",label:"代数：","label-width":"60px"}},[a("el-radio-group",{model:{value:e.editForm.genNum,callback:function(t){e.$set(e.editForm,"genNum",t)},expression:"editForm.genNum"}},[a("el-radio-button",{attrs:{label:"2"}},[e._v("2G")]),e._v(" "),a("el-radio-button",{attrs:{label:"3"}},[e._v("3G")]),e._v(" "),a("el-radio-button",{attrs:{label:"4"}},[e._v("4G")]),e._v(" "),a("el-radio-button",{attrs:{label:"5"}},[e._v("5G")]),e._v(" "),a("el-radio-button",{attrs:{label:"GSM-R"}},[e._v("GSM-R")])],1)],1),e._v(" "),a("el-form-item",{staticClass:"form_edit_item",attrs:{prop:"orgGuid",label:"组织机构类型："}},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:e.editForm.orgGuid,callback:function(t){e.$set(e.editForm,"orgGuid",t)},expression:"editForm.orgGuid"}},e._l(e.orgList,function(e,t){return a("el-option",{key:t,attrs:{label:e.orgName,value:e.orgGuid}})}),1)],1),e._v(" "),a("el-form-item",{staticClass:"form_edit_item",attrs:{prop:"state",label:"状态：","label-width":"60px"}},[a("el-radio-group",{model:{value:e.editForm.state,callback:function(t){e.$set(e.editForm,"state",t)},expression:"editForm.state"}},[a("el-radio",{attrs:{label:"1"}},[e._v("启用")]),e._v(" "),a("el-radio",{attrs:{label:"2"}},[e._v("停用")])],1)],1),e._v(" "),a("el-form-item",{staticClass:"form_edit_item",attrs:{prop:"freqEfb",label:"发射频率下限："}},[a("el-input",{attrs:{clearable:""},model:{value:e.editForm.freqEfb,callback:function(t){e.$set(e.editForm,"freqEfb",t)},expression:"editForm.freqEfb"}},[a("template",{slot:"append"},[e._v("MHz")])],2)],1),e._v(" "),a("el-form-item",{staticClass:"form_edit_item",attrs:{prop:"freqEfe",label:"发射频率上限："}},[a("el-input",{attrs:{clearable:""},model:{value:e.editForm.freqEfe,callback:function(t){e.$set(e.editForm,"freqEfe",t)},expression:"editForm.freqEfe"}},[a("template",{slot:"append"},[e._v("MHz")])],2)],1),e._v(" "),a("el-form-item",{staticClass:"form_edit_item",attrs:{prop:"freqRfb",label:"接收频率下限："}},[a("el-input",{attrs:{clearable:""},model:{value:e.editForm.freqRfb,callback:function(t){e.$set(e.editForm,"freqRfb",t)},expression:"editForm.freqRfb"}},[a("template",{slot:"append"},[e._v("MHz")])],2)],1),e._v(" "),a("el-form-item",{staticClass:"form_edit_item",attrs:{prop:"freqRfe",label:"接收频率上限："}},[a("el-input",{attrs:{clearable:""},model:{value:e.editForm.freqRfe,callback:function(t){e.$set(e.editForm,"freqRfe",t)},expression:"editForm.freqRfe"}},[a("template",{slot:"append"},[e._v("MHz")])],2)],1),e._v(" "),a("el-form-item",{attrs:{prop:"fileNo",label:"频率使用许可证号或批准文号：","label-width":"220px"}},[a("el-input",{attrs:{clearable:""},model:{value:e.editForm.fileNo,callback:function(t){e.$set(e.editForm,"fileNo",t)},expression:"editForm.fileNo"}})],1)],1),e._v(" "),e.editForm.isEdit?a("el-button",{attrs:{type:"primary"},on:{click:e.submit}},[e._v("添加/修改")]):e._e(),e._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:e.cancel}},[e._v("返回")])],1),e._v(" "),a("el-dialog",{attrs:{title:"确认删除",visible:e.delDialogShow,width:"30%"},on:{"update:visible":function(t){e.delDialogShow=t}}},[a("span",[e._v("确认删除这一条许可")]),e._v(" "),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.delDialogShow=!1}}},[e._v("取 消")]),e._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:e.sureDelItem}},[e._v("确 定")])],1)]),e._v(" "),a("el-dialog",{attrs:{title:"确认删除",visible:e.delBatchDialogShow,width:"30%"},on:{"update:visible":function(t){e.delBatchDialogShow=t}}},[a("span",[e._v("确认批量删除")]),e._v(" "),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.delBatchDialogShow=!1}}},[e._v("取 消")]),e._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:e.sureDelBatch}},[e._v("确 定")])],1)])],1)},staticRenderFns:[]};var r=a("VU/8")(i,o,!1,function(e){a("/Uvy")},"data-v-27262ad9",null);t.default=r.exports}});