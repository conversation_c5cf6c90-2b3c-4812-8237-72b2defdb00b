webpackJsonp([59],{"+Ze8":function(e,t,l){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var a={render:function(){var e=this,t=e.$createElement,l=e._self._c||t;return l("div",{staticClass:"content-template"},[e._m(0),e._v(" "),l("div",{staticClass:"search-box"},[l("el-form",{staticClass:"input-form",attrs:{"label-width":"200px",inline:!1,"label-position":"right"}},[l("el-form-item",{attrs:{label:"数据库IP："}},[l("el-input",{staticClass:"input-width",model:{value:e.Ip,callback:function(t){e.Ip=t},expression:"Ip"}})],1),e._v(" "),l("el-form-item",{attrs:{label:"端口号："}},[l("el-input",{staticClass:"input-width",model:{value:e.port,callback:function(t){e.port=t},expression:"port"}})],1),e._v(" "),l("el-form-item",{attrs:{label:"用户名："}},[l("el-input",{staticClass:"input-width",model:{value:e.username,callback:function(t){e.username=t},expression:"username"}})],1),e._v(" "),l("el-form-item",{attrs:{label:"密码："}},[l("el-input",{staticClass:"input-width",model:{value:e.password,callback:function(t){e.password=t},expression:"password"}})],1),e._v(" "),l("el-form-item",{attrs:{label:"同步周期设置："}},[l("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择"},model:{value:e.settings,callback:function(t){e.settings=t},expression:"settings"}},e._l(e.cycle,function(e){return l("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}),1)],1),e._v(" "),l("el-form-item",[l("el-button",{attrs:{size:"medium",type:"primary",icon:"el-icon-document-checked"}},[e._v("立即同步")])],1)],1)],1)])},staticRenderFns:[function(){var e=this.$createElement,t=this._self._c||e;return t("div",{staticClass:"top-nav"},[t("span",[this._v("数据库同步")])])}]};var s=l("VU/8")({data:function(){return{cycle:[{value:1,label:"三个月"},{value:2,label:"半年"},{value:3,label:"一年"}],Ip:"",port:"",username:"",password:"",settings:""}}},a,!1,function(e){l("/mCb")},"data-v-20bf04f3",null);t.default=s.exports},"/mCb":function(e,t){}});