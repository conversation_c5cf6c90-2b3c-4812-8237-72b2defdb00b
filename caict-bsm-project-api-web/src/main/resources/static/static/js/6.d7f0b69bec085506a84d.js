webpackJsonp([6],{"2hMo":function(e,t){},FtkG:function(e,t){},ShBV:function(e,t){},UhbE:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=a("Dd8w"),l=a.n(r),o={props:["formData"],data:function(){return{form:l()({appType:"",appCode:"",guid:"",appOrgDetailDTO:{appSubType:"0",orgName:"",orgCode:"",orgSysCode:"",orgAddr:"",orgPost:"",orgLinkPerson:"",orgPhone:"",orgFax:"",orgMobPhone:"",orgMail:""},appNetDetailDTO:{netName:"",netBand:"",netBandU:"kHz",netSvn:"",netTS:"",netTs:"",netArea:"",netUse:"",netFileNo:[""],netDate:"",netConfirmDate:"",netExpiredDate:""},appFreqDetailDTO:{freqType:"1",freqList:[{freqEfb:null,freqEfe:null,freqRfe:null,freqRfb:null,freqUnit:"MHz"}]},appFeeOrgDetailDTO:{orgName:"",orgCode:"",orgAddr:"",orgPost:"",orgLinkPerson:"",orgPhone:"",orgMobPhone:"",orgFax:"",orgMail:"",orgBank:"",orgAccName:"",orgAcc:""},appDataDTO:{appPerName:"",appDate:""},tableNo:"",memo:"",isRead:!0},this.formData),formRules:this.$formCheck}},mounted:function(){this.initForm()},methods:{initForm:function(){var e=[];e[0]=this.form.appNetDetailDTO.netConfirmDate,e[1]=this.form.appNetDetailDTO.netExpiredDate,this.$set(this.form.appNetDetailDTO,"netDate",e)},add:function(e){"freqList"==e&&this.form.appFreqDetailDTO.freqList.push({freqEfb:null,freqEfe:null,freqRfe:null,freqRfb:null,freqUnit:"MHz"})},delItem:function(e,t){1!=this.form.isRead?e.splice(t,1):this.$message.warning("当前为只读模式，不能进行编辑")},formCheck:function(e){var t=!0;return this.$refs[e].validate(function(e){e?console.log("submit!!"):t=!1}),t},submit:function(e){var t=this;this.formCheck(e)&&(this.form.appNetDetailDTO.netConfirmDate=this.form.appNetDetailDTO.netDate[0],this.form.appNetDetailDTO.netExpiredDate=this.form.appNetDetailDTO.netDate[1],this.form.isRead?this.$emit("dialogClose"):this.$axios.post(this.$api.applyTableManageAdd,this.form).then(function(e){e&&(t.$message({message:e.message,type:"success"}),console.log(123,t.$parent.$parent),t.$emit("dialogClose"))}))}}},s={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"table"},[e._m(0),e._v(" "),a("el-form",{ref:"form",attrs:{model:e.form,rules:e.formRules,"label-width":"160px",disabled:e.form.isRead}},[a("el-form-item",{attrs:{label:"申报表编号",prop:"appCode",rules:e.formRules.appCode}},[a("el-popover",{attrs:{trigger:"click",content:"“T____-____-____” 栏，系指申请设台时的申请表编号，“T”后由 12 位数字组成，其中，前 4 位表示地区编码，\n中间 4 位表示年份，后 4 位表示申请表序号，例如：“T1100-2006-0010”，表示北京地区 2006 年第 10 张台（站）设\n置申请表。新设台（站）时由无线电管理机构填写此栏。当设台单位信息、缴费单位信息变更或变更己建台站（网）\n的技术参数时，由用户填写已设台（站）的申请表编号。每次可申请修改同一通信网的多个台站的多项内容。"}},[a("el-input",{staticStyle:{width:"400px"},attrs:{slot:"reference",placeholder:"申报表编号"},slot:"reference",model:{value:e.form.appCode,callback:function(t){e.$set(e.form,"appCode",t)},expression:"form.appCode"}})],1)],1),e._v(" "),a("div",{staticClass:"sup_item"},[a("h2",[e._v("申请信息 - 设台单位")]),e._v(" "),a("el-form-item",{attrs:{label:"名称",prop:"appOrgDetailDTO.orgName",rules:e.formRules.orgName}},[a("el-popover",{attrs:{trigger:"click",content:"“设台单位名称”栏,系指申请设置使用无线电台（站）的单位或个人的全称。当申请设置空间电台时，填写卫星操\n作者名称"}},[a("el-input",{attrs:{slot:"reference",placeholder:"名称"},slot:"reference",model:{value:e.form.appOrgDetailDTO.orgName,callback:function(t){e.$set(e.form.appOrgDetailDTO,"orgName",t)},expression:"form.appOrgDetailDTO.orgName"}})],1)],1),e._v(" "),a("el-form-item",{attrs:{prop:"appSubType",rules:e.formRules.appSubType}},[a("el-radio-group",{model:{value:e.form.appOrgDetailDTO.appSubType,callback:function(t){e.$set(e.form.appOrgDetailDTO,"appSubType",t)},expression:"form.appOrgDetailDTO.appSubType"}},[a("el-radio",{attrs:{label:"0"}},[e._v("新设")]),e._v(" "),a("el-radio",{attrs:{label:"1"}},[e._v("变更")]),e._v(" "),a("el-radio",{attrs:{label:"2"}},[e._v("删除")])],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"组织机构代码",prop:"appOrgDetailDTO.orgCode",rules:e.formRules.orgCode}},[a("el-popover",{attrs:{trigger:"click",content:"“组织机构代码”栏，系指根据中华人民共和国国家标准《全国组织机构代码编制规则》（GB11714--1997），\n由组织机构代码登记主管部门给每个企业、事业单位、机关和社会团体颁发的在全国范围内唯一的、始终不变\n的法定代码。产业活动单位是本部的，如果没有法定代码，使用其所属的法人单位法定代码的前 8 位，第九位\n校验码填“B”。所有单位均应填报本项"}},[a("el-input",{attrs:{slot:"reference",placeholder:"组织机构代码"},slot:"reference",model:{value:e.form.appOrgDetailDTO.orgCode,callback:function(t){e.$set(e.form.appOrgDetailDTO,"orgCode",t)},expression:"form.appOrgDetailDTO.orgCode"}})],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"系统代码",prop:"appOrgDetailDTO.orgSysCode",rules:e.formRules.orgSysCode}},[a("el-popover",{attrs:{trigger:"click",content:"“系统代码”栏，系指设台单位所属部门的代码，由无线电管理机构填写"}},[a("el-input",{attrs:{slot:"reference",placeholder:"系统代码"},slot:"reference",model:{value:e.form.appOrgDetailDTO.orgSysCode,callback:function(t){e.$set(e.form.appOrgDetailDTO,"orgSysCode",t)},expression:"form.appOrgDetailDTO.orgSysCode"}})],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"通信地址",prop:"appOrgDetailDTO.orgAddr",rules:e.formRules.orgAddr}},[a("el-popover",{attrs:{trigger:"click",content:"“通信地址”栏，系指设台单位的通信地址。"}},[a("el-input",{attrs:{slot:"reference",placeholder:"通信地址"},slot:"reference",model:{value:e.form.appOrgDetailDTO.orgAddr,callback:function(t){e.$set(e.form.appOrgDetailDTO,"orgAddr",t)},expression:"form.appOrgDetailDTO.orgAddr"}})],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"邮政编码",prop:"appOrgDetailDTO.orgPost",rules:e.formRules.orgPost}},[a("el-input",{attrs:{placeholder:"邮政编码"},model:{value:e.form.appOrgDetailDTO.orgPost,callback:function(t){e.$set(e.form.appOrgDetailDTO,"orgPost",e._n(t))},expression:"form.appOrgDetailDTO.orgPost"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"联系人",prop:"appOrgDetailDTO.orgLinkPerson",rules:e.formRules.orgLinkPerson}},[a("el-input",{attrs:{placeholder:"联系人"},model:{value:e.form.appOrgDetailDTO.orgLinkPerson,callback:function(t){e.$set(e.form.appOrgDetailDTO,"orgLinkPerson",t)},expression:"form.appOrgDetailDTO.orgLinkPerson"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"联系电话",prop:"appOrgDetailDTO.orgPhone",rules:e.formRules.orgPhone}},[a("el-popover",{attrs:{trigger:"click",content:"“联系电话”栏，填写联系人的办公电话和手机号码。"}},[a("el-input",{attrs:{slot:"reference",placeholder:"联系电话"},slot:"reference",model:{value:e.form.appOrgDetailDTO.orgPhone,callback:function(t){e.$set(e.form.appOrgDetailDTO,"orgPhone",e._n(t))},expression:"form.appOrgDetailDTO.orgPhone"}})],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"传真号码",prop:"appOrgDetailDTO.orgFax",rules:e.formRules.orgFax}},[a("el-input",{attrs:{placeholder:"传真号码"},model:{value:e.form.appOrgDetailDTO.orgFax,callback:function(t){e.$set(e.form.appOrgDetailDTO,"orgFax",t)},expression:"form.appOrgDetailDTO.orgFax"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"手机号码",prop:"appOrgDetailDTO.orgMobPhone",rules:e.formRules.orgMobPhone}},[a("el-input",{attrs:{placeholder:"手机号码"},model:{value:e.form.appOrgDetailDTO.orgMobPhone,callback:function(t){e.$set(e.form.appOrgDetailDTO,"orgMobPhone",e._n(t))},expression:"form.appOrgDetailDTO.orgMobPhone"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"电子信箱",prop:"appOrgDetailDTO.orgMail",rules:e.formRules.orgMail}},[a("el-popover",{attrs:{trigger:"click",content:"“电子信箱”栏，填写联系人的电子信箱或单位公务信箱。"}},[a("el-input",{attrs:{slot:"reference",placeholder:"电子信箱"},slot:"reference",model:{value:e.form.appOrgDetailDTO.orgMail,callback:function(t){e.$set(e.form.appOrgDetailDTO,"orgMail",t)},expression:"form.appOrgDetailDTO.orgMail"}})],1)],1)],1),e._v(" "),a("div",{staticClass:"sup_item"},[a("h2",[e._v("申请信息")]),e._v(" "),a("el-form-item",{attrs:{label:"无线电系统/网络 名称",prop:"appNetDetailDTO.netName",rules:e.formRules.netName}},[a("el-popover",{attrs:{trigger:"click",content:"“无线电系统/网络 名称”栏，系指由用户命名的、拟设无线电台（站）所属的无线电通信系统或网络的名称。申 请设置空间电台时，不用填写。"}},[a("el-input",{attrs:{slot:"reference",placeholder:"无线电系统/网络 名称"},slot:"reference",model:{value:e.form.appNetDetailDTO.netName,callback:function(t){e.$set(e.form.appNetDetailDTO,"netName",t)},expression:"form.appNetDetailDTO.netName"}})],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"信道带宽/波道间隔",prop:"appNetDetailDTO.netBand",rules:e.formRules.netBand}},[a("el-popover",{attrs:{trigger:"click",content:"“信道带宽/波道间隔” 栏，系指国家无线电管理机构根据相关标准划定的信道带宽/波道间隔，或国家标准、行业\n标准中规定的信道带宽/波道间隔。申请设置空间电台或地球站时，不用填写。"}},[a("el-input",{attrs:{slot:"reference",placeholder:"信道带宽/波道间隔"},slot:"reference",model:{value:e.form.appNetDetailDTO.netBand,callback:function(t){e.$set(e.form.appNetDetailDTO,"netBand",t)},expression:"form.appNetDetailDTO.netBand"}},[a("template",{slot:"append"},[e._v("MHz")])],2)],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"通信业务/系统 类型",prop:"appNetDetailDTO.netSvn",rules:e.formRules.netSvn}},[a("el-popover",{attrs:{trigger:"click",content:"“通信业务/系统 类型”栏，由无线电管理机构填写。"}},[a("el-input",{attrs:{slot:"reference",placeholder:"通信业务/系统 类型"},slot:"reference",model:{value:e.form.appNetDetailDTO.netSvn,callback:function(t){e.$set(e.form.appNetDetailDTO,"netSvn",t)},expression:"form.appNetDetailDTO.netSvn"}})],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"业务性质",prop:"appNetDetailDTO.netSp",rules:e.formRules.netSp}},[a("el-popover",{attrs:{trigger:"hover",content:"“业务性质”栏，系指拟设的无线电台（站）所属的无线电业务性质，可选择填写并在相应的“□”内填写“√”\n号。其中“专用”系指国内各部门开展的专用通信业务；“公众”系指用于国际、国内公众通信的业务；“其他”系\n指不包括在上述范围内的业务。"}},[a("el-radio-group",{attrs:{slot:"reference"},slot:"reference",model:{value:e.form.appNetDetailDTO.netSp,callback:function(t){e.$set(e.form.appNetDetailDTO,"netSp",t)},expression:"form.appNetDetailDTO.netSp"}},[a("el-radio",{attrs:{label:"CO"}},[e._v("专用")]),e._v(" "),a("el-radio",{attrs:{label:"cp"}},[e._v("公众")]),e._v(" "),a("el-radio",{attrs:{label:"QT"}},[e._v("其他")])],1)],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"技术体制",prop:"appNetDetailDTO.netTs",rules:e.formRules.netTs}},[a("el-popover",{attrs:{trigger:"click",content:"“技术体制”栏，系指拟设的无线电台（站）所属的无线电通信系统或网络的技术体制，例如 GSM、WCDMA 等。"}},[a("el-input",{attrs:{slot:"reference",placeholder:"技术体制"},slot:"reference",model:{value:e.form.appNetDetailDTO.netTs,callback:function(t){e.$set(e.form.appNetDetailDTO,"netTs",t)},expression:"form.appNetDetailDTO.netTs"}})],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"使用范围",prop:"appNetDetailDTO.netArea",rules:e.formRules.netArea}},[a("el-popover",{attrs:{trigger:"hover",content:"“使用范围”栏，系指拟建无线电通信网的使用范围，可选择填写并在相应的“□”内填写“√”号。其中“国 际/跨边境（界）”系指拟建系统或网络可提供国际漫游；“全国”系指覆盖全国的通信系统或网络，“跨省”系指仅\n用于两省或两省以上的通信系统，其他类推。"}},[a("el-select",{attrs:{slot:"reference",placeholder:"使用范围"},slot:"reference",model:{value:e.form.appNetDetailDTO.netArea,callback:function(t){e.$set(e.form.appNetDetailDTO,"netArea",t)},expression:"form.appNetDetailDTO.netArea"}},[a("el-option",{attrs:{label:"国际/跨边境（界）",value:"1"}}),e._v(" "),a("el-option",{attrs:{label:"全国",value:"2"}}),e._v(" "),a("el-option",{attrs:{label:"跨省",value:"3"}}),e._v(" "),a("el-option",{attrs:{label:"省内",value:"4"}}),e._v(" "),a("el-option",{attrs:{label:"地市",value:"5"}}),e._v(" "),a("el-option",{attrs:{label:"县级",value:"6"}}),e._v(" "),a("el-option",{attrs:{label:"其他",value:"7"}})],1)],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"网络用途",prop:"appNetDetailDTO.netUse",rules:e.formRules.netUse}},[a("el-popover",{attrs:{trigger:"click",content:"“网络用途”栏，根据网络的实际用途进行填写。例如，防洪救灾、应急抢险、保障重大事件等,公众业务则不必\n填写。"}},[a("el-input",{attrs:{slot:"reference",placeholder:"网络用途"},slot:"reference",model:{value:e.form.appNetDetailDTO.netUse,callback:function(t){e.$set(e.form.appNetDetailDTO,"netUse",t)},expression:"form.appNetDetailDTO.netUse"}})],1)],1),e._v(" "),a("el-form-item",{staticClass:"group",staticStyle:{width:"100%"},attrs:{label:"频率使用许可证号或批准文号","label-width":"200px"}},[a("el-popover",{attrs:{trigger:"click",content:"“频率使用许可证号或批准文号”栏，系指无线电管理机构批准使用频率的使用证号或批准文号。当设台单位已取\n得“频率使用许可证”后，填写“频率使用许可证号”，否则填写批准文号。"}},e._l(e.form.appNetDetailDTO.netFileNo,function(t,r){return a("el-form-item",{key:r,attrs:{slot:"reference","label-width":"0"},slot:"reference"},[a("el-input",{attrs:{placeholder:"频率使用许可证号或批准文号"},model:{value:e.form.appNetDetailDTO.netFileNo[r],callback:function(t){e.$set(e.form.appNetDetailDTO.netFileNo,r,t)},expression:"form.appNetDetailDTO.netFileNo[index]"}},[a("template",{slot:"append"},[a("el-button-group",{staticStyle:{display:"flex"}},[a("el-button",{attrs:{type:"primary",icon:"el-icon-circle-plus-outline"},on:{click:function(t){return e.form.appNetDetailDTO.netFileNo.push("")}}}),e._v(" "),e.form.appNetDetailDTO.netFileNo.length>1?a("el-button",{attrs:{type:"danger",icon:"el-icon-delete"},on:{click:function(t){return e.form.appNetDetailDTO.netFileNo.splice(r,1)}}}):e._e()],1)],1)],2)],1)}),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"批准频率使用期限",prop:"appNetDetailDTO.netDate",rules:e.formRules.netDate}},[a("el-popover",{attrs:{trigger:"hover",content:"“批准频率使用期限”栏，按频率使用许可证或批文中批准的频率使用期限填写。"}},[a("el-date-picker",{attrs:{slot:"reference",type:"datetimerange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},slot:"reference",model:{value:e.form.appNetDetailDTO.netDate,callback:function(t){e.$set(e.form.appNetDetailDTO,"netDate",t)},expression:"form.appNetDetailDTO.netDate"}})],1)],1)],1),e._v(" "),a("div",{staticClass:"sup_item"},[a("el-popover",{attrs:{trigger:"hover",content:"“使用信（波）道的中心频率”栏和“使用频率范围”栏，填写其一即可，在相应的“□”内填写“√”号。"}},[a("h2",{attrs:{slot:"reference"},slot:"reference"},[e._v("使用信（波）道的中心频率 / 使用频率范围")])]),e._v(" "),a("el-form-item",{attrs:{prop:"appFreqDetailDTO.freqType",rules:e.formRules.freqType,"label-width":"0"}},[a("el-radio-group",{model:{value:e.form.appFreqDetailDTO.freqType,callback:function(t){e.$set(e.form.appFreqDetailDTO,"freqType",t)},expression:"form.appFreqDetailDTO.freqType"}},[a("el-popover",{attrs:{trigger:"hover",content:" “使用信（波）道的中心频率”栏，当用户按信（波）道设置无线电台（站）时填写此项，按照配对频率进行填写。\n“/”的左侧填写较低频率，“/”的右侧填写较高频率。公众移动通信系统可不填此栏。"}},[a("el-radio-button",{attrs:{slot:"reference",label:"1"},slot:"reference"},[e._v("使用信（波）道的中心频率")])],1),e._v(" "),a("el-popover",{attrs:{trigger:"hover",content:" “使用频率范围”栏，当设置的无线电台（站）使用某一频率范围或公众移动通信系统时填写使用频段的起始频率\n和终止频率，并按照配对频率进行填写，填写规则与第 19 条相同。当设置地球站时，‘/’的左侧填写地球站的接收\n频率，‘/’的右侧填写地球站的发射频率。对于单发射站，其发射频率填写在‘/’的右侧；对于单接收站，其接收\n频率填写在‘/’的左侧。如申请多个频段，则分别填写。"}},[a("el-radio-button",{attrs:{slot:"reference",label:"2"},slot:"reference"},[e._v("使用频率范围")])],1)],1)],1),e._v(" "),a("div",{staticClass:"sub_table"},[1==e.form.appFreqDetailDTO.freqType?a("div",{staticClass:"item sub_table_header"},[a("el-col",{attrs:{span:3}},[e._v("发射较低频率")]),e._v(" "),a("el-col",{attrs:{span:3}},[e._v("发射较高频率")]),e._v(" "),a("el-col",{attrs:{span:3}},[e._v("接收较低频率")]),e._v(" "),a("el-col",{attrs:{span:3}},[e._v("接收较高频率")])],1):e._e(),e._v(" "),2==e.form.appFreqDetailDTO.freqType?a("div",{staticClass:"item sub_table_header"},[a("el-col",{attrs:{span:3}},[e._v("接收起始频率")]),e._v(" "),a("el-col",{attrs:{span:3}},[e._v("发射起始频率")]),e._v(" "),a("el-col",{attrs:{span:3}},[e._v("接收终止频率")]),e._v(" "),a("el-col",{attrs:{span:3}},[e._v("发射终止频率")])],1):e._e(),e._v(" "),e._l(e.form.appFreqDetailDTO.freqList,function(t,r){return a("div",{key:r,staticClass:"item"},[a("el-col",{attrs:{span:3}},[a("el-form-item",{attrs:{"label-width":"0",prop:"freqRfe"}},[a("el-input",{model:{value:t.freqRfe,callback:function(a){e.$set(t,"freqRfe",a)},expression:"item.freqRfe"}},[a("template",{slot:"append"},[e._v("KHz")])],2)],1)],1),e._v(" "),a("el-col",{attrs:{span:3}},[a("el-form-item",{attrs:{"label-width":"0",prop:"freqEfb"}},[a("el-input",{model:{value:t.freqEfb,callback:function(a){e.$set(t,"freqEfb",a)},expression:"item.freqEfb"}},[a("template",{slot:"append"},[e._v("KHz")])],2)],1)],1),e._v(" "),a("el-col",{attrs:{span:3}},[a("el-form-item",{attrs:{"label-width":"0",prop:"freqRfb"}},[a("el-input",{model:{value:t.freqRfb,callback:function(a){e.$set(t,"freqRfb",a)},expression:"item.freqRfb"}},[a("template",{slot:"append"},[e._v("KHz")])],2)],1)],1),e._v(" "),a("el-col",{attrs:{span:3}},[a("el-form-item",{attrs:{"label-width":"0",prop:"freqEfe"}},[a("el-input",{model:{value:t.freqEfe,callback:function(a){e.$set(t,"freqEfe",a)},expression:"item.freqEfe"}},[a("template",{slot:"append"},[e._v("KHz")])],2)],1)],1),e._v(" "),a("el-col",{attrs:{span:1,align:"middle"}},[a("i",{staticClass:"item_remove el-icon-delete-solid",staticStyle:{position:"relative",top:"10px"},on:{click:function(t){return e.delItem(e.form.appFreqDetailDTO.freqList,r)}}})])],1)}),e._v(" "),a("el-button",{staticClass:"sub_table_add el-icon-document-add",attrs:{type:"success"},on:{click:function(t){return e.add("freqList")}}},[e._v("添加一条新数据")])],2)],1),e._v(" "),a("div",{staticClass:"sup_item"},[a("h2",[e._v("缴费单位")]),e._v(" "),a("el-form-item",{attrs:{label:"名称",prop:"appFeeOrgDetailDTO.orgName",rules:e.formRules.orgName}},[a("el-popover",{attrs:{trigger:"click",content:"“缴费单位名称”栏，系指交纳频率占用费的单位或个人的全称。"}},[a("el-input",{attrs:{slot:"reference",placeholder:"名称"},slot:"reference",model:{value:e.form.appFeeOrgDetailDTO.orgName,callback:function(t){e.$set(e.form.appFeeOrgDetailDTO,"orgName",t)},expression:"form.appFeeOrgDetailDTO.orgName"}})],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"组织机构代码",prop:"appFeeOrgDetailDTO.orgCode",rules:e.formRules.orgCode}},[a("el-input",{attrs:{placeholder:"组织机构代码"},model:{value:e.form.appFeeOrgDetailDTO.orgCode,callback:function(t){e.$set(e.form.appFeeOrgDetailDTO,"orgCode",t)},expression:"form.appFeeOrgDetailDTO.orgCode"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"邮政编码",prop:"appFeeOrgDetailDTO.orgPost",rules:e.formRules.orgPost}},[a("el-input",{attrs:{placeholder:"邮政编码"},model:{value:e.form.appFeeOrgDetailDTO.orgPost,callback:function(t){e.$set(e.form.appFeeOrgDetailDTO,"orgPost",e._n(t))},expression:"form.appFeeOrgDetailDTO.orgPost"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"通信地址",prop:"appFeeOrgDetailDTO.orgAddr",rules:e.formRules.orgAddr}},[a("el-input",{attrs:{placeholder:"通信地址"},model:{value:e.form.appFeeOrgDetailDTO.orgAddr,callback:function(t){e.$set(e.form.appFeeOrgDetailDTO,"orgAddr",t)},expression:"form.appFeeOrgDetailDTO.orgAddr"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"联系人",prop:"appFeeOrgDetailDTO.orgLinkPerson",rules:e.formRules.orgLinkPerson}},[a("el-input",{attrs:{placeholder:"联系人"},model:{value:e.form.appFeeOrgDetailDTO.orgLinkPerson,callback:function(t){e.$set(e.form.appFeeOrgDetailDTO,"orgLinkPerson",t)},expression:"form.appFeeOrgDetailDTO.orgLinkPerson"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"联系电话",prop:"appFeeOrgDetailDTO.orgPhone",rules:e.formRules.orgPhone}},[a("el-input",{attrs:{placeholder:"联系电话"},model:{value:e.form.appFeeOrgDetailDTO.orgPhone,callback:function(t){e.$set(e.form.appFeeOrgDetailDTO,"orgPhone",t)},expression:"form.appFeeOrgDetailDTO.orgPhone"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"传真号码",prop:"appFeeOrgDetailDTO.orgFax",rules:e.formRules.orgFax}},[a("el-input",{attrs:{placeholder:"传真号码"},model:{value:e.form.appFeeOrgDetailDTO.orgFax,callback:function(t){e.$set(e.form.appFeeOrgDetailDTO,"orgFax",t)},expression:"form.appFeeOrgDetailDTO.orgFax"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"手机号码",prop:"appFeeOrgDetailDTO.orgMobPhone",rules:e.formRules.orgMobPhone}},[a("el-input",{attrs:{placeholder:"手机号码"},model:{value:e.form.appFeeOrgDetailDTO.orgMobPhone,callback:function(t){e.$set(e.form.appFeeOrgDetailDTO,"orgMobPhone",t)},expression:"form.appFeeOrgDetailDTO.orgMobPhone"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"电子邮箱",prop:"appFeeOrgDetailDTO.orgMail",rules:e.formRules.orgMail}},[a("el-input",{attrs:{placeholder:"电子邮箱"},model:{value:e.form.appFeeOrgDetailDTO.orgMail,callback:function(t){e.$set(e.form.appFeeOrgDetailDTO,"orgMail",t)},expression:"form.appFeeOrgDetailDTO.orgMail"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"开户银行",prop:"appFeeOrgDetailDTO.orgBank",rules:e.formRules.orgBank}},[a("el-popover",{attrs:{trigger:"click",content:"“开户银行”、“账户名称”、“银行账号”栏，系指当缴费单位需要办理托收时填写。"}},[a("el-input",{attrs:{slot:"reference",placeholder:"开户银行"},slot:"reference",model:{value:e.form.appFeeOrgDetailDTO.orgBank,callback:function(t){e.$set(e.form.appFeeOrgDetailDTO,"orgBank",t)},expression:"form.appFeeOrgDetailDTO.orgBank"}})],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"账户名称",prop:"appFeeOrgDetailDTO.orgAccName",rules:e.formRules.orgAccName}},[a("el-popover",{attrs:{trigger:"click",content:"“开户银行”、“账户名称”、“银行账号”栏，系指当缴费单位需要办理托收时填写。"}},[a("el-input",{attrs:{slot:"reference",placeholder:"账户名称"},slot:"reference",model:{value:e.form.appFeeOrgDetailDTO.orgAccName,callback:function(t){e.$set(e.form.appFeeOrgDetailDTO,"orgAccName",t)},expression:"form.appFeeOrgDetailDTO.orgAccName"}})],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"银行账号",prop:"appFeeOrgDetailDTO.orgAcc",rules:e.formRules.orgAcc}},[a("el-popover",{attrs:{trigger:"click",content:"“开户银行”、“账户名称”、“银行账号”栏，系指当缴费单位需要办理托收时填写。"}},[a("el-input",{attrs:{slot:"reference",placeholder:"银行账号"},slot:"reference",model:{value:e.form.appFeeOrgDetailDTO.orgAcc,callback:function(t){e.$set(e.form.appFeeOrgDetailDTO,"orgAcc",t)},expression:"form.appFeeOrgDetailDTO.orgAcc"}})],1)],1)],1),e._v(" "),a("div",{staticClass:"sup_item"},[a("h2",[e._v("申请人承诺")]),e._v(" "),a("ul",[a("li",[e._v("1.本申请表填写的所有内容真实、准确；")]),e._v(" "),a("li",[e._v("\n            2.无线电台（站）的发射频率、功率等技术指标符合国家有关无线电管理规定及技术标准，不对其他无线电台（站）、系统造成有害干扰；\n          ")]),e._v(" "),a("li",[e._v("\n            3.无线电台（站）的设置、使用符合国家城市规划、环境保护等相关规定；\n          ")]),e._v(" "),a("li",[e._v("4.本单位如变更地址、名称等事项，将及时向无线电管理机构备案；")]),e._v(" "),a("li",[e._v("5.遵守国家有关无线电管理规定，并按时缴纳频率占用费。")])]),e._v(" "),a("el-form-item",{attrs:{label:"申请人（签章）",prop:"appDataDTO.appPerName",rules:e.formRules.appPerName}},[a("el-popover",{attrs:{trigger:"click",content:"“申请人承诺”栏，系指用户设台时需要申明和承诺的条款，并由申请人签字、盖章予以确认。"}},[a("el-input",{attrs:{slot:"reference",placeholder:"申请人（签章）"},slot:"reference",model:{value:e.form.appDataDTO.appPerName,callback:function(t){e.$set(e.form.appDataDTO,"appPerName",t)},expression:"form.appDataDTO.appPerName"}})],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"日期",prop:"appDataDTO.appDate",rules:e.formRules.appDate}},[a("el-date-picker",{attrs:{type:"date"},model:{value:e.form.appDataDTO.appDate,callback:function(t){e.$set(e.form.appDataDTO,"appDate",t)},expression:"form.appDataDTO.appDate"}})],1)],1),e._v(" "),a("div",{staticClass:"sup_item"},[a("h2",[e._v("其它信息")]),e._v(" "),a("el-form-item",{attrs:{label:"备注"}},[a("el-input",{attrs:{placeholder:"备注",type:"textarea",maxlength:"200","show-word-limit":""},model:{value:e.form.memo,callback:function(t){e.$set(e.form,"memo",t)},expression:"form.memo"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"表号"}},[a("el-popover",{attrs:{trigger:"click",content:"如需填写续表，其资料申请表编号与前表相同,并在“备注”部分中“表号”栏“/”左侧填写该表的顺序号，右侧\n填写表的总数。例如，2/4 表示此资料表号下共有 4 张申请表，此表为第 2 张表。"}},[a("el-input",{attrs:{slot:"reference",placeholder:"表号"},slot:"reference",model:{value:e.form.tableNo,callback:function(t){e.$set(e.form,"tableNo",t)},expression:"form.tableNo"}})],1)],1)],1)],1)],1)},staticRenderFns:[function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("p",{staticClass:"table_tips"},[e._v("\n      1.本表供申请设置各类无线电台(站)或变更已设台（站）站址、频率、功率等核定项目时使用。\n      "),a("br"),e._v("2.表 号 技术资料申报表名称 代 号 "),a("br"),e._v("国无管表 3 30MHz\n      以下无线电台(站)技术资料申报表 H "),a("br"),e._v("国无管表 4\n      陆地移动电台技术资料申报表 LM "),a("br"),e._v("国无管表 5\n      地面固定业务台（站）技术资料申报表 TF "),a("br"),e._v("国无管表 6\n      地球站技术资料申报表 E "),a("br"),e._v("国无管表 7 广播电台技术资料申报表 B\n      "),a("br"),e._v("国无管表 8 船舶电台技术资料申报表 S "),a("br"),e._v("国无管表 9\n      航空器电台技术资料申报表 A "),a("br"),e._v("国无管表 10 雷达站技术资料申报表 R\n      "),a("br"),e._v("国无管表 11 蜂窝无线电通信基站技术资料申报表 C "),a("br"),e._v("国无管表 12\n      直放站技术资料申报表 D "),a("br"),e._v("国无管表 13 无线电台(站)技术资料申报表 V\n      "),a("br"),e._v("国无管表 14 移动地球站技术资料申报表 ME "),a("br"),e._v("国无管表 15\n      静止轨道空间电台技术资料申报表 G "),a("br"),e._v("国无管表 16\n      非静止轨道空间电台（星座）资料申报表 NG\n    ")])}]};var n=a("VU/8")(o,s,!1,function(e){a("cWo5")},"data-v-6e2f6efa",null).exports,i={props:["data"],data:function(){return{form:l()({statAppType:"C",statTdi:"",stationData:{appCode:"",techType:"0",statName:"",statAddr:"",stCCode:"",stCSum:"",statLg:"",statLa:"",statAt:"",stServR:"",statDateStart:""},sectorDatas:[{atCCode:"",antAngle:"",atCsgn:"",atRang:"",atEang:"",freqType:"",freqEfb:"",freqEfe:"",freqRfb:"",freqRfe:"",freqDatas:[{freqUc:"",freqLc:""}],equData:{equModel:"",equAuth:"",equMenu:"",etEquSum:"",equPow:"",equPowU:""}}],antFeedDatas:[{atCCode:"",antType:"",antModel:"",antPole:"",at3DBE:"",at3DBR:"",antEGain:"",antRGain:"",antMenu:"",antHeight:"",feedLose:""}],memo:"",tableNo:"",isRead:!0},this.data),formRules:this.$formCheck,CCodeList:[]}},watch:{"form.sectorDatas":{handler:function(e){this.CCodeList=e.map(function(e){return e.CCode})},deep:!0}},mounted:function(){console.log(2222,this.data)},methods:{delItem:function(e,t){e.splice(t,1)},add:function(e,t){1==t?e.push({CCode:"",antAngle:"",atCsgn:"",atRang:"",atEang:"",freqType:"",freqEfb:"",freqEfe:"",freqRfb:"",freqRfe:"",freqDatas:[{freqUc:"",freqLc:""}],equData:[{equModel:"",equAuth:"",equMenu:"",etEquSum:"",equPow:"",equPowU:""}]}):2==t?e.push({freqUc:"",freqLc:""}):3==t?e.push({equModel:"",equAuth:"",equMenu:"",etEquSum:"",equPow:"",equPowU:""}):e.push({antType:"",antModel:"",antPole:"",at3DBE:"",at3DBR:"",antEGain:"",antRGain:"",antMenu:"",antHeight:"",feedLose:""})},seniorValidation:function(){var e=this,t=!1;return this.form.antFeedDatas.map(function(a){e.CCodeList.some(function(e){if(a.atCCode==e)return t=!0,!0})}),t||this.$message.error("天馈线数据--扇区必须与扇区数据中的扇区保持一致"),t}}},p={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"table"},[a("p",{staticClass:"table_tips"},[e._v("\n    1.本表用于以蜂窝方式组网的无线电通信系统基站，包括集群通信系统、公众移动通信系统（GSM、CDMA、\n    TD-SCDMA）和无线接入系统（如PHS、SCDMA等）等，使用全向天线的基站也填写此表。凡新设无线电台（站）或变更已设台（站）站址、频率或功率等核定项目时均应填写此表，并选择“新设”或“变更”\n  ")]),e._v(" "),a("el-form",{ref:"form",attrs:{model:e.form,rules:e.formRules,"label-width":"130px",disabled:e.form.isRead}},[a("el-form-item",{attrs:{label:"申报表编号",prop:"statTdi",rules:e.formRules.statTdi}},[a("el-popover",{attrs:{trigger:"hover",content:"此表“C____”，表示“技术资料申报表编号”，“C”后由4位数字组成。此栏由用户填写或者由无线电管理机构指导用户填写。当用户需要修改已设台（站）的数据时，必须使新填表格中的技术资料申报表编号与原技术资料申报表中的该栏编号相同。"}},[a("el-input",{attrs:{slot:"reference",placeholder:"申报表编号"},slot:"reference",model:{value:e.form.statTdi,callback:function(t){e.$set(e.form,"statTdi",t)},expression:"form.statTdi"}},[a("template",{slot:"prepend"},[e._v(e._s(e.form.statAppType))])],2)],1)],1),e._v(" "),a("div",{staticClass:"sup_item"},[a("h2",[e._v("台站数据")]),e._v(" "),a("el-form-item",{attrs:{label:"申请表编号",prop:"stationData.appCode",rules:e.formRules.appCode}},[a("el-popover",{attrs:{trigger:"hover",content:"“申请表编号”栏，系指申请设台时“无线电台（站）设置申请表”的编号。新设台（站）时由无线电管理机构填写此栏，更改已设台（站）数据时由用户填写原有台（站）的申请表编号。"}},[a("el-input",{attrs:{slot:"reference",placeholder:"申请表编号",disabled:!0},slot:"reference",model:{value:e.form.stationData.appCode,callback:function(t){e.$set(e.form.stationData,"appCode",t)},expression:"form.stationData.appCode"}})],1)],1),e._v(" "),a("el-form-item",{attrs:{prop:"stationData.techType",rules:e.formRules.techType}},[a("el-radio-group",{model:{value:e.form.stationData.techType,callback:function(t){e.$set(e.form.stationData,"techType",t)},expression:"form.stationData.techType"}},[a("el-radio",{attrs:{label:"0"}},[e._v("新设")]),e._v(" "),a("el-radio",{attrs:{label:"1"}},[e._v("变更")]),e._v(" "),a("el-radio",{attrs:{label:"2"}},[e._v("删除")])],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"台站名称",prop:"stationData.statName",rules:e.formRules.statName}},[a("el-popover",{attrs:{trigger:"hover",content:"填写该基站的具体名称"}},[a("el-input",{attrs:{slot:"reference",placeholder:"台站名称"},slot:"reference",model:{value:e.form.stationData.statName,callback:function(t){e.$set(e.form.stationData,"statName",t)},expression:"form.stationData.statName"}})],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"基站编号",prop:"stationData.stCCode",rules:e.formRules.stCCode}},[a("el-popover",{attrs:{trigger:"hover",content:"系指申请设台单位针对该基站的具体编号，由申请单位填写"}},[a("el-input",{attrs:{slot:"reference",placeholder:"基站编号"},slot:"reference",model:{value:e.form.stationData.stCCode,callback:function(t){e.$set(e.form.stationData,"stCCode",t)},expression:"form.stationData.stCCode"}})],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"台站地址",prop:"stationData.statAddr",rules:e.formRules.statAddr}},[a("el-input",{attrs:{placeholder:"台站地址"},model:{value:e.form.stationData.statAddr,callback:function(t){e.$set(e.form.stationData,"statAddr",t)},expression:"form.stationData.statAddr"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"扇区数量",prop:"stationData.stCSum",rules:e.formRules.stCSum}},[a("el-input",{attrs:{placeholder:"扇区数量"},model:{value:e.form.stationData.stCSum,callback:function(t){e.$set(e.form.stationData,"stCSum",t)},expression:"form.stationData.stCSum"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"地理坐标-东经",prop:"stationData.statLg",rules:e.formRules.statLg}},[a("el-input",{attrs:{placeholder:"东经"},model:{value:e.form.stationData.statLg,callback:function(t){e.$set(e.form.stationData,"statLg",t)},expression:"form.stationData.statLg"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"地理坐标-北纬",prop:"stationData.statLa",rules:e.formRules.statLa}},[a("el-input",{attrs:{placeholder:"北纬"},model:{value:e.form.stationData.statLa,callback:function(t){e.$set(e.form.stationData,"statLa",t)},expression:"form.stationData.statLa"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"海拔高度",prop:"stationData.statAt",rules:e.formRules.statAt}},[a("el-input",{attrs:{placeholder:"海拔高度"},model:{value:e.form.stationData.statAt,callback:function(t){e.$set(e.form.stationData,"statAt",t)},expression:"form.stationData.statAt"}},[a("template",{slot:"append"},[e._v("M")])],2)],1),e._v(" "),a("el-form-item",{attrs:{label:"服务半径",prop:"stationData.stServR",rules:e.formRules.stServR}},[a("el-input",{attrs:{placeholder:"服务半径"},model:{value:e.form.stationData.stServR,callback:function(t){e.$set(e.form.stationData,"stServR",t)},expression:"form.stationData.stServR"}},[a("template",{slot:"append"},[e._v("km")])],2)],1),e._v(" "),a("el-form-item",{attrs:{label:"启用日期",prop:"stationData.statDateStart",rules:e.formRules.statDateStart}},[a("el-date-picker",{attrs:{type:"date",placeholder:"启用日期"},model:{value:e.form.stationData.statDateStart,callback:function(t){e.$set(e.form.stationData,"statDateStart",t)},expression:"form.stationData.statDateStart"}})],1)],1),e._v(" "),a("hr"),e._v(" "),e._l(e.form.sectorDatas,function(t,r){return a("div",{key:r},[a("div",{staticClass:"sup_item"},[a("h2",[a("span",[e._v("扇区数据 - "+e._s(r+1))]),e._v(" "),a("i",{staticClass:"item_remove el-icon-delete-solid",on:{click:function(t){return e.delItem(e.form.sectorDatas,r)}}})]),e._v(" "),a("el-form-item",{attrs:{label:"扇区",prop:"sectorDatas."+r+".atCsgn",rules:e.formRules.atCCode}},[a("el-input",{attrs:{placeholder:"扇区",value:r+1}})],1),e._v(" "),a("el-form-item",{attrs:{label:"方位角"}},[a("el-input",{attrs:{placeholder:"方位角"},model:{value:t.antAngle,callback:function(a){e.$set(t,"antAngle",a)},expression:"item.antAngle"}},[a("template",{slot:"append"},[e._v("°")])],2)],1),e._v(" "),a("el-form-item",{attrs:{label:"扇区标识码"}},[a("el-input",{attrs:{placeholder:"扇区标识码"},model:{value:t.atCCode,callback:function(a){e.$set(t,"atCCode",a)},expression:"item.atCCode"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"收倾角"}},[a("el-input",{attrs:{placeholder:"收倾角"},model:{value:t.atRang,callback:function(a){e.$set(t,"atRang",a)},expression:"item.atRang"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"发倾角"}},[a("el-input",{attrs:{placeholder:"发倾角"},model:{value:t.atEang,callback:function(a){e.$set(t,"atEang",a)},expression:"item.atEang"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"发射频率范围(起)"}},[a("el-input",{attrs:{placeholder:"发射频率范围(起)"},model:{value:t.freqEfb,callback:function(a){e.$set(t,"freqEfb",a)},expression:"item.freqEfb"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"发射频率范围(止)"}},[a("el-input",{attrs:{placeholder:"发射频率范围(止)"},model:{value:t.freqEfe,callback:function(a){e.$set(t,"freqEfe",a)},expression:"item.freqEfe"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"接收频率范围(起)"}},[a("el-input",{attrs:{placeholder:"发射频率范围(起)"},model:{value:t.freqRfb,callback:function(a){e.$set(t,"freqRfb",a)},expression:"item.freqRfb"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"接收频率范围(止)"}},[a("el-input",{attrs:{placeholder:"发射频率范围(止)"},model:{value:t.freqRfe,callback:function(a){e.$set(t,"freqRfe",a)},expression:"item.freqRfe"}})],1),e._v(" "),a("div",{staticClass:"sub_table"},[a("div",{staticClass:"item sub_table_header"},[a("el-col",{attrs:{span:3}},[e._v("发射频率")]),e._v(" "),a("el-col",{attrs:{span:3}},[e._v("接收频率")])],1),e._v(" "),e._l(t.freqDatas,function(r,l){return a("div",{key:l},[a("el-col",{attrs:{span:3}},[a("el-form-item",{attrs:{"label-width":"0"}},[a("el-input",{model:{value:r.freqUc,callback:function(t){e.$set(r,"freqUc",t)},expression:"item02.freqUc"}})],1)],1),e._v(" "),a("el-col",{attrs:{span:3}},[a("el-form-item",{attrs:{"label-width":"0"}},[a("el-input",{model:{value:r.freqLc,callback:function(t){e.$set(r,"freqLc",t)},expression:"item02.freqLc"}})],1)],1),e._v(" "),a("el-col",{attrs:{span:1,align:"middle"}},[a("i",{staticClass:"item_remove el-icon-delete-solid",on:{click:function(a){return e.delItem(t.freqDatas,l)}}})])],1)}),e._v(" "),a("el-button",{staticClass:"sub_table_add el-icon-document-add",attrs:{type:"success"},on:{click:function(a){return e.add(t.freqDatas,2)}}},[e._v("添加一条新数据")])],2),e._v(" "),a("h2",{staticStyle:{"margin-top":"30px"}},[e._v("扇区数据 -- 收发信机数据")]),e._v(" "),a("div",{staticClass:"sub_table"},[a("div",{staticClass:"item sub_table_header"},[a("el-col",{attrs:{span:3}},[e._v("型号核准代码")]),e._v(" "),a("el-col",{attrs:{span:3}},[e._v("设备型号")]),e._v(" "),a("el-col",{attrs:{span:3}},[e._v("设备数量")]),e._v(" "),a("el-col",{attrs:{span:3}},[e._v("生产厂家")]),e._v(" "),a("el-col",{attrs:{span:3}},[e._v("发射功率")])],1),e._v(" "),a("div",[a("el-col",{attrs:{span:3}},[a("el-form-item",{attrs:{"label-width":"0"}},[a("el-input",{model:{value:t.equData.equAuth,callback:function(a){e.$set(t.equData,"equAuth",a)},expression:"item.equData.equAuth"}})],1)],1),e._v(" "),a("el-col",{attrs:{span:3}},[a("el-form-item",{attrs:{"label-width":"0"}},[a("el-input",{model:{value:t.equData.equModel,callback:function(a){e.$set(t.equData,"equModel",a)},expression:"item.equData.equModel"}})],1)],1),e._v(" "),a("el-col",{attrs:{span:3}},[a("el-form-item",{attrs:{"label-width":"0"}},[a("el-input",{model:{value:t.equData.etEquSum,callback:function(a){e.$set(t.equData,"etEquSum",a)},expression:"item.equData.etEquSum"}})],1)],1),e._v(" "),a("el-col",{attrs:{span:3}},[a("el-form-item",{attrs:{"label-width":"0"}},[a("el-input",{model:{value:t.equData.equMenu,callback:function(a){e.$set(t.equData,"equMenu",a)},expression:"item.equData.equMenu"}})],1)],1),e._v(" "),a("el-col",{attrs:{span:3}},[a("el-form-item",{attrs:{"label-width":"0"}},[a("el-input",{model:{value:t.equData.equPow,callback:function(a){e.$set(t.equData,"equPow",a)},expression:"item.equData.equPow"}},[a("el-select",{staticStyle:{width:"80px"},attrs:{slot:"append"},slot:"append",model:{value:t.equData.equPowU,callback:function(a){e.$set(t.equData,"equPowU",a)},expression:"item.equData.equPowU"}},[a("el-option",{attrs:{label:"W",value:"W"}}),e._v(" "),a("el-option",{attrs:{label:"dBm",value:"dBm"}})],1)],1)],1)],1)],1)])],1)])}),e._v(" "),a("hr"),e._v(" "),a("el-button",{staticClass:"sub_table_add el-icon-document-add add_big_item",attrs:{type:"success"},on:{click:function(t){return e.add(e.form.sectorDatas,1)}}},[e._v("添加一条新扇区数据")]),e._v(" "),a("div",{staticClass:"sup_item"},[a("h2",[e._v("天馈线数据")]),e._v(" "),a("div",{staticClass:"sub_table"},[a("div",{staticClass:"item sub_table_header"},[a("el-col",{attrs:{span:2}},[e._v("扇区")]),e._v(" "),a("el-col",{attrs:{span:2}},[e._v("天线类型")]),e._v(" "),a("el-col",{attrs:{span:2}},[e._v("天线型号")]),e._v(" "),a("el-col",{attrs:{span:2}},[e._v("极化方式")]),e._v(" "),a("el-col",{attrs:{span:5}},[e._v("3dB角宽")]),e._v(" "),a("el-col",{attrs:{span:5}},[e._v("天线增益(dBi)")]),e._v(" "),a("el-col",{attrs:{span:2}},[e._v("天线生产厂家")]),e._v(" "),a("el-col",{attrs:{span:2}},[e._v("天线高度(m)")]),e._v(" "),a("el-col",{attrs:{span:1}},[e._v("馈线系统总损耗(dB)")])],1),e._v(" "),e._l(e.form.antFeedDatas,function(t,r){return a("div",{key:r},[a("el-col",{attrs:{span:2}},[a("el-form-item",{attrs:{"label-width":"0"}},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:t.atCCode,callback:function(a){e.$set(t,"atCCode",a)},expression:"item.atCCode"}},e._l(e.CCodeList,function(e){return a("el-option",{key:e,attrs:{label:e,value:e}})}),1)],1)],1),e._v(" "),a("el-col",{attrs:{span:2}},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:t.antType,callback:function(a){e.$set(t,"antType",a)},expression:"item.antType"}},[a("el-option",{attrs:{label:"高增益全向天线",value:"BD"}}),e._v(" "),a("el-option",{attrs:{label:"对称振子天线",value:"BE"}})],1)],1),e._v(" "),a("el-col",{attrs:{span:2}},[a("el-form-item",{attrs:{"label-width":"0"}},[a("el-input",{model:{value:t.antModel,callback:function(a){e.$set(t,"antModel",a)},expression:"item.antModel"}})],1)],1),e._v(" "),a("el-col",{attrs:{span:2}},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:t.antPole,callback:function(a){e.$set(t,"antPole",a)},expression:"item.antPole"}},[a("el-option",{attrs:{label:"水平线极化",value:"H"}}),e._v(" "),a("el-option",{attrs:{label:"垂直线极化",value:"V"}})],1)],1),e._v(" "),a("el-col",{attrs:{span:5}},[a("div",{staticClass:"item_table_group"},[a("el-form-item",{attrs:{"label-width":"0"}},[a("el-input",{model:{value:t.at3DBE,callback:function(a){e.$set(t,"at3DBE",a)},expression:"item.at3DBE"}})],1),e._v(" "),a("span",[e._v("/")]),e._v(" "),a("el-form-item",{attrs:{"label-width":"0"}},[a("el-input",{model:{value:t.at3DBR,callback:function(a){e.$set(t,"at3DBR",a)},expression:"item.at3DBR"}})],1)],1)]),e._v(" "),a("el-col",{attrs:{span:5}},[a("div",{staticClass:"item_table_group"},[a("el-form-item",{attrs:{"label-width":"0"}},[a("el-input",{model:{value:t.antEGain,callback:function(a){e.$set(t,"antEGain",a)},expression:"item.antEGain"}})],1),e._v(" "),a("span",[e._v("/")]),e._v(" "),a("el-form-item",{attrs:{"label-width":"0"}},[a("el-input",{model:{value:t.antRGain,callback:function(a){e.$set(t,"antRGain",a)},expression:"item.antRGain"}})],1)],1)]),e._v(" "),a("el-col",{attrs:{span:2}},[a("el-form-item",{attrs:{"label-width":"0"}},[a("el-input",{model:{value:t.antMenu,callback:function(a){e.$set(t,"antMenu",a)},expression:"item.antMenu"}})],1)],1),e._v(" "),a("el-col",{attrs:{span:2}},[a("el-form-item",{attrs:{"label-width":"0"}},[a("el-input",{model:{value:t.antHeight,callback:function(a){e.$set(t,"antHeight",a)},expression:"item.antHeight"}})],1)],1),e._v(" "),a("el-col",{attrs:{span:1}},[a("el-form-item",{attrs:{"label-width":"0"}},[a("el-input",{model:{value:t.feedLose,callback:function(a){e.$set(t,"feedLose",a)},expression:"item.feedLose"}})],1)],1),e._v(" "),a("el-col",{attrs:{span:1,align:"middle"}},[a("i",{staticClass:"item_remove el-icon-delete-solid",on:{click:function(t){return e.delItem(e.form.antFeedDatas,r)}}})])],1)}),e._v(" "),a("el-button",{staticClass:"sub_table_add el-icon-document-add",attrs:{type:"success"},on:{click:function(t){return e.add(e.form.antFeedDatas,4)}}},[e._v("添加一条新数据")])],2)]),e._v(" "),a("div",{staticClass:"sup_item"},[a("h2",[e._v("其它信息")]),e._v(" "),a("el-form-item",{attrs:{label:"备注"}},[a("el-input",{attrs:{placeholder:"备注",type:"textarea",maxlength:"200","show-word-limit":""},model:{value:e.form.memo,callback:function(t){e.$set(e.form,"memo",t)},expression:"form.memo"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"表号",prop:"tableNo",rules:e.formRules.tableNo}},[a("el-input",{attrs:{placeholder:"表号"},model:{value:e.form.tableNo,callback:function(t){e.$set(e.form,"tableNo",t)},expression:"form.tableNo"}})],1)],1)],2)],1)},staticRenderFns:[]};var c=a("VU/8")(i,p,!1,function(e){a("2hMo")},"data-v-2ac7fa70",null).exports,f={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"query_box"},[a("el-form",{ref:"form",attrs:{model:e.formData,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"申请表编号"}},[a("el-input",{model:{value:e.formData.appCode,callback:function(t){e.$set(e.formData,"appCode",t)},expression:"formData.appCode"}})],1),e._v(" "),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.query}},[e._v("查询")]),e._v(" "),a("el-button",{attrs:{type:"success"},on:{click:e.clear}},[e._v("清空")])],1)],1)],1)},staticRenderFns:[]};var m=a("VU/8")({data:function(){return{formData:{page:1,rows:10,isSync:1,appCode:""}}},mounted:function(){this.query()},methods:{initFormData:function(){this.formData={page:1,rows:10,isSync:1,appCode:""}},query:function(){this.$emit("query",this.formData)},clear:function(){this.initFormData(),this.$emit("clear",this.formData)}}},f,!1,function(e){a("haHk")},"data-v-1763486a",null).exports,u={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"query_box"},[a("el-form",{ref:"form",attrs:{model:e.formData,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"申请表编号"}},[a("el-input",{model:{value:e.formData.appCode,callback:function(t){e.$set(e.formData,"appCode",t)},expression:"formData.appCode"}})],1),e._v(" "),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.query}},[e._v("查询")]),e._v(" "),a("el-button",{attrs:{type:"warning"},on:{click:e.applyTableSubmit}},[e._v("推送到国家库")])],1)],1)],1)},staticRenderFns:[]};var d=a("VU/8")({data:function(){return{formData:{page:1,rows:10,isSync:0,appCode:this.$route.query.appCode||""}}},mounted:function(){this.query()},methods:{initFormData:function(){this.formData={page:1,rows:10,isSync:0,appCode:""}},query:function(){this.$emit("query",this.formData)},clear:function(){this.initFormData(),this.$emit("clear",this.formData)},applyTableSubmit:function(){this.$emit("applyTableSubmit","fromQuery")}}},u,!1,function(e){a("FtkG")},"data-v-478daf41",null).exports,v={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"query_box"},[a("el-form",{ref:"form",attrs:{model:e.formData,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"申请表编号"}},[a("el-input",{model:{value:e.formData.appCode,callback:function(t){e.$set(e.formData,"appCode",t)},expression:"formData.appCode"}})],1),e._v(" "),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.query}},[e._v("查询")]),e._v(" "),a("el-button",{attrs:{type:"success"},on:{click:e.clear}},[e._v("清空")])],1)],1)],1)},staticRenderFns:[]};var D=a("VU/8")({data:function(){return{formData:{page:1,rows:10,isSync:2,appCode:""}}},mounted:function(){this.query()},methods:{initFormData:function(){this.formData={page:1,rows:10,isSync:2,appCode:""}},query:function(){this.$emit("query",this.formData)},clear:function(){this.initFormData(),this.$emit("clear",this.formData)}}},v,!1,function(e){a("ShBV")},"data-v-797a8ec7",null).exports,b=new(a("oFuF").a),g={data:function(){return{queryBox:"QueryBox03",formData:{},tableData:[],pageObj:{size:1,total:1},showTechNolength:20,multipleSelection:[],drawer:{title:"",show:!1,formData:{}},tech:{title:"",show:!1,formData:{}}}},components:{ApplyTable:n,TechTable11:c,QueryBox02:m,QueryBox03:d,QueryBox04:D},methods:{handleSelectionChange:function(e){this.multipleSelection=e},getTableData:function(e){var t=this;this.formData=e,this.$ajax.post({url:"/api/apiWeb/applytable/bsmApplyTable/findAllInByWhere",data:e}).then(function(e){e.success&&(t.tableData=e.data.list,t.formData.total=e.data.total)})},setCurrentPage:function(e){this.formData.page=e,this.getTableData(this.formData)},getMore:function(){this.showTechNolength=999999999},packUp:function(){this.showTechNolength=20},readTechNo:function(e,t){var a=this;this.tech.title="技术资料表编号 - "+t,this.$ajax.post({url:"/api/apiWeb/applytable/bsmApplyTable/findTechTable",data:{appCode:e.appCode,techNum:t}}).then(function(e){a.tech.formData=e.data,a.tech.show=!0})},readItem:function(e){var t=this;this.drawer.title="申请表编号 - "+e.appCode,this.$ajax.get({url:"/api/apiWeb/applytable/bsmApplyTable/findTechTable/"+e.guid}).then(function(e){t.drawer.formData=e.data,t.drawer.show=!0})},downloadItem:function(e){this.$ajax.get({url:"/api/apiWeb/applytable/bsmApplyTable/downloadApply/"+e.guid}).then(function(e){e.success&&b.funDownload(e.data)})},downloadTech:function(e){this.$ajax.post({url:"/api/apiWeb/applytable/bsmApplyTable/downloadTechTable",data:{appCode:e.stationData.appCode,techNum:e.stationData.statTdi}}).then(function(e){e.success&&b.funDownload(e.data)})},drawerClose:function(e){this.$confirm("确认关闭？").then(function(t){e()}).catch(function(e){})},handleTechNoClose:function(e){this.$confirm("确认关闭？").then(function(t){e()}).catch(function(e){})},applyTableEdit:function(){this.$refs.applyTable.form.isRead=!1},applyTableCheck:function(){var e=this,t=this.$refs.applyTable.form.appCode;this.$ajax.get({url:"/api/apiWeb/transferin/transportJobBranch/checkApplyCode/"+t}).then(function(t){t.success?e.applyTableSave():e.openOverWritingConfirm()}),this.$refs.applyTable.form.isRead=!0},applyTableSave:function(){var e=this,t=this.$refs.applyTable.form;this.$ajax.post({url:"/api/apiWeb/applytable/bsmApplyTable/editApply",data:t}).then(function(t){t.success||e.$message.error(t.message)}),this.$refs.applyTable.form.isRead=!0},applyTableSubmit:function(e){var t=this,a=function(e){t.$ajax.post({url:"/api/apiWeb/transferin/transportJobBranch/dataPush",data:e}).then(function(e){if(e.success){t.$message.success(e.message);var a=window.setTimeout(function(){window.clearTimeout(a),t.drawer.show=!1,t.getTableData(t.formData)},1e3)}else t.$message.error(e.message)})},r={};if("fromQuery"==e){if(r.appCodes=this.multipleSelection.map(function(e){return e.appCode}),0==r.appCodes.length)return void this.$alert("还未选择文件。","警告!",{confirmButtonText:"确定"});a(r)}else r.appCodes=[this.$refs.applyTable.form.appCode],a(r)},openOverWritingConfirm:function(){var e=this;this.$confirm("申请表编号已存在，是否永久覆盖该申请表, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){e.applyTableSave()}).catch(function(){e.$message({type:"info",message:"已取消"})})}},filters:{formatTime:function(e){return new Date(e).toLocaleDateString()}}},_={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"data_sync"},[a("el-tabs",{attrs:{type:"border-card"},model:{value:e.queryBox,callback:function(t){e.queryBox=t},expression:"queryBox"}},[a("el-tab-pane",{attrs:{label:"未推送",name:"QueryBox03"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"推送中",name:"QueryBox02"}}),e._v(" "),a("el-tab-pane",{attrs:{label:"已推送",name:"QueryBox04"}}),e._v(" "),a(e.queryBox,{tag:"component",on:{query:e.getTableData,clear:e.getTableData,applyTableSubmit:e.applyTableSubmit}}),e._v(" "),a("el-table",{staticStyle:{width:"100%"},attrs:{data:e.tableData,border:""},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55"}}),e._v(" "),a("el-table-column",{attrs:{prop:"appCode",label:"申请表编号",width:"180"}}),e._v(" "),a("el-table-column",{attrs:{prop:"techTableList",label:"技术资料表编号"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._l(t.row.techTableList,function(r,l){return[l<e.showTechNolength?a("span",{key:r,staticClass:"cell_border",on:{click:function(a){return e.readTechNo(t.row,r)}}},[e._v(e._s(r))]):e._e()]}),e._v(" "),20==e.showTechNolength?a("span",{staticClass:"cell_border",on:{click:function(t){return e.getMore()}}},[e._v("更多>>")]):a("span",{staticClass:"cell_border",on:{click:function(t){return e.packUp()}}},[e._v("收起")])]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"appDate",label:"新建日期",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(e._f("formatTime")(t.row.appDate)))]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"appDate",label:"操作",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.readItem(t.row)}}},[e._v("查看")]),e._v(" "),a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.downloadItem(t.row)}}},[e._v("下载")])]}}])})],1),e._v(" "),a("el-pagination",{attrs:{"page-size":e.formData.rows,"pager-count":5,layout:"prev, pager, next",total:e.formData.total},on:{"current-change":e.setCurrentPage}})],1),e._v(" "),e.drawer.show?a("el-drawer",{attrs:{title:e.drawer.title,visible:e.drawer.show,direction:"rtl",size:"70%","before-close":e.drawerClose},on:{"update:visible":function(t){return e.$set(e.drawer,"show",t)}}},["QueryBox03"==e.queryBox?a("el-button-group",{staticClass:"drawer_button_group"},[a("el-button",{attrs:{type:"primary",icon:"el-icon-edit",size:"small"},on:{click:e.applyTableEdit}},[e._v("编辑")]),e._v(" "),a("el-button",{attrs:{type:"success",icon:"el-icon-s-management",size:"small"},on:{click:e.applyTableCheck}},[e._v("保存")]),e._v(" "),a("el-button",{attrs:{type:"warning",size:"small"},on:{click:e.applyTableSubmit}},[e._v("推送到国家库")])],1):e._e(),e._v(" "),a("ApplyTable",{ref:"applyTable",attrs:{formData:e.drawer.formData}})],1):e._e(),e._v(" "),a("el-dialog",{attrs:{title:e.tech.title,visible:e.tech.show,width:"70%","before-close":e.handleTechNoClose},on:{"update:visible":function(t){return e.$set(e.tech,"show",t)}}},[a("el-button",{attrs:{type:"success",size:"small"},on:{click:function(t){return e.downloadTech(e.tech.formData)}}},[e._v("下载")]),e._v(" "),a("TechTable11",{ref:"techTable",attrs:{data:e.tech.formData}}),e._v(" "),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:function(t){e.tech.show=!1}}},[e._v("确 定")])],1)],1)],1)},staticRenderFns:[]};var h=a("VU/8")(g,_,!1,function(e){a("YKwB")},"data-v-53586805",null);t.default=h.exports},YKwB:function(e,t){},cWo5:function(e,t){},haHk:function(e,t){}});