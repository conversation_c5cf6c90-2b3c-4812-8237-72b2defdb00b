webpackJsonp([60],{SCAp:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var l={data:function(){return{tableData:[],dialogVisible:!1,currentDetail:{},operator:[{detail:"全部",code:""},{detail:"移动",code:"mobile"},{detail:"电信",code:"telecom"},{detail:"联通",code:"unicom"}],pageTotal:0,loading:!0,pageDTO:{page:1,rows:20,userType:"",genNum:"",dataType:"",jobGuid:this.$route.query.jobGuid}}},mounted:function(){this.initTableData()},methods:{initTableData:function(){var e=this;this.loading||(this.loading=!0),this.$ajax.post({url:"/api/apiWeb/transferin/approvalTransportJobIn/findDetailListByPage",data:this.pageDTO}).then(function(t){if(e.loading=!1,t.success)return e.tableData=t.data.list,void(e.pageTotal=t.data.total);t.success||e.$message.error(t.message)},function(){return e.loading=!1})},currentRowView:function(e){this.$router.push({path:"/work/offices/detail",query:{guid:e.guid}})},handleSelectChange:function(){this.initTableData()},handleSearch:function(){this.initTableData()},handleCurrentChange:function(e){this.pageDTO.page=e,this.initTableData()},handleSizeChange:function(e){this.pageDTO.rows=e,this.initTableData()}},components:{StationDetail:a("2F/+").a},filters:{dataTypeToChinese:function(e){switch(e=parseInt(e)){case 1:return"新增";case 2:return"变更";case 3:return"注销";default:return"全部"}},formatUserType:function(e){switch(e){case"mobile":return"移动";case"unicom":return"联通";case"telecom":return"电信";default:return"全部"}}}},o={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"content-template"},[a("div",{staticClass:"search-box"},[a("el-form",{staticClass:"search-form",attrs:{inline:!0,"label-position":"left"}},[a("el-form-item",{attrs:{label:"代数:","label-width":"40px"}},[a("el-radio-group",{model:{value:e.pageDTO.genNum,callback:function(t){e.$set(e.pageDTO,"genNum",t)},expression:"pageDTO.genNum"}},[a("el-radio-button",{attrs:{label:""}},[e._v("全部")]),e._v(" "),a("el-radio-button",{attrs:{label:"2"}},[e._v("2G")]),e._v(" "),a("el-radio-button",{attrs:{label:"3"}},[e._v("3G")]),e._v(" "),a("el-radio-button",{attrs:{label:"4"}},[e._v("4G")]),e._v(" "),a("el-radio-button",{attrs:{label:"5"}},[e._v("5G")])],1)],1),e._v(" "),a("el-form-item",{attrs:{label:"类别:","label-width":"40px"}},[a("el-radio-group",{model:{value:e.pageDTO.dataType,callback:function(t){e.$set(e.pageDTO,"dataType",t)},expression:"pageDTO.dataType"}},[a("el-radio-button",{attrs:{label:""}},[e._v("全部")]),e._v(" "),a("el-radio-button",{attrs:{label:"1"}},[e._v("新增")]),e._v(" "),a("el-radio-button",{attrs:{label:"2"}},[e._v("更新")]),e._v(" "),a("el-radio-button",{attrs:{label:"3"}},[e._v("注销")])],1)],1),e._v(" "),a("el-form-item",{staticClass:"form_item_btn"},[a("el-button",{staticClass:"default-button",attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.handleSearch}},[e._v("搜索")])],1)],1)],1),e._v(" "),e._m(0),e._v(" "),a("div",{staticClass:"table-box"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"table-content",attrs:{height:"100%",data:e.tableData}},[a("el-table-column",{attrs:{prop:"gmtCreate",label:"创建时间",align:"center","show-overflow-tooltip":!0},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n          "+e._s(e._f("format")(t.row.gmtCreate))+"\n        ")]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"userType",label:"运营商",align:"center","show-overflow-tooltip":!0},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n          "+e._s(e._f("formatUserType")(t.row.userType))+"\n        ")]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"isApproved",label:"是否通过",align:"center","show-overflow-tooltip":!0},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n          "+e._s(1==t.row.isApproved?"是":"否")+"\n        ")]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"opDetail",label:"审批意见",align:"center","show-overflow-tooltip":!0}}),e._v(" "),a("el-table-column",{attrs:{prop:"genNum",label:"代数",align:"center","show-overflow-tooltip":!0},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(t.row.genNum)+"G")])]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"isCompare",label:"类别",align:"center","show-overflow-tooltip":!0},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e._f("dataTypeToChinese")(t.row.dataType)))])]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"isCompare",label:"状态",align:"center","show-overflow-tooltip":!0},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",{class:e._f("isCompareStyle")(t.row.isCompare)},[e._v("\n            "+e._s(e._f("isCompare")(t.row.isCompare))+"\n          ")])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"操作",width:"100",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return 12!=t.row.isCompare?[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.currentRowView(t.row)}}},[e._v("查看")])]:void 0}}],null,!0)})],1),e._v(" "),a("div",{staticClass:"table-pagin"},[a("el-pagination",{attrs:{layout:"sizes, total, prev, pager, next, jumper","page-sizes":[20,50,100,200],"current-page":e.pageDTO.page,total:e.pageTotal},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1),e._v(" "),a("el-dialog",{attrs:{title:"扇区数据查看信息",visible:e.dialogVisible,"custom-class":"history-dialog",width:"998px","append-to-body":""},on:{"update:visible":function(t){e.dialogVisible=t}}},[a("station-detail",{attrs:{detail:e.currentDetail}})],1)],1)},staticRenderFns:[function(){var e=this.$createElement,t=this._self._c||e;return t("div",{staticClass:"operate-box"},[t("span",{staticClass:"operate-table-title"},[this._v("数据列表")])])}]};var n=a("VU/8")(l,o,!1,function(e){a("uOR8")},"data-v-1e86a38c",null);t.default=n.exports},uOR8:function(e,t){}});