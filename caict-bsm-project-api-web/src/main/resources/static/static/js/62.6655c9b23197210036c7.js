webpackJsonp([62],{"o/5+":function(e,t){},uu97:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var s=a("2F/+"),n=a("99Ht"),o=null,i={data:function(){return{uploadHeaders:{token:sessionStorage.getItem("token")},ajaxDomain:n.a.ajaxDomain,tableData:[],dialogVisible:!1,currentDetail:{},pageTotal:0,loading:!0,pageDTO:{appGuid:this.$route.query.appGuid,page:1,rows:20,fileNo:""}}},mounted:function(){this.initTableData()},methods:{tableRowClassName:function(e){var t=e.row;e.rowIndex;return console.log(213,t.isHandle),1==t.isHandle?"error-row":"success-row"},initTableData:function(){var e=this;this.loading||(this.loading=!0),this.$ajax.post({url:"/api/apiWeb/station/interfere/findResultList",data:this.pageDTO}).then(function(t){if(e.loading=!1,t.success)return e.tableData=t.data.list.map(function(e){return e.isComparison=!1,e}),void(e.pageTotal=t.data.total);t.success||e.$message.error(t.message)},function(){return e.loading=!1})},startComparison:function(e){var t=this;e.isComparison=!0;var a=window.setTimeout(function(){e.isComparison=!1,t.$message.error("比对未成功！"),window.clearTimeout(a)},1e5);this.$ajax.post({url:"/api/apiWeb/transfer/roadTest/contrast",data:{guid:e.guid}}).then(function(s){e.isComparison=!1,window.clearTimeout(a),s.success?(t.$message.success(s.message),t.initTableData()):t.$message.error(s.message)})},currentRowView:function(e){this.$router.push({path:"/station/road-test-contrast",query:{jobGuid:e.guid}})},handleSelectChange:function(){this.initTableData()},handleSearch:function(){this.initTableData()},handleCurrentChange:function(e){this.pageDTO.page=e,this.initTableData()},handleSizeChange:function(e){this.pageDTO.rows=e,this.initTableData()},beforeUpload:function(e){console.log(23423424),o=this.$loading({lock:!0,text:"正在上传中...",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"})},uploadSuccess:function(e,t,a){o.close(),e.success?(this.$message.success(e.message),this.initTableData()):this.$message.error(e.message)}},components:{StationDetail:s.a},filters:{dataTypeToChinese:function(e){switch(e=parseInt(e)){case 1:return"新增";case 2:return"变更";case 3:return"注销";default:return"全部"}},formatUserGuid:function(e){switch(e){case"users15878825327624703":return"移动";case"users15878825327624706":return"联通";case"users15878825327624705":return"电信";default:return"全部"}},statusFilter:function(e){switch(parseInt(e)){case 1:return"未比对";case 2:return"已比对";default:return"异常"}}}},l={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"content-template"},[a("div",{staticClass:"table-box"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"table-content",attrs:{height:"100%",data:e.tableData,"row-class-name":e.tableRowClassName}},[a("el-table-column",{attrs:{prop:"cellName",label:"小区名",align:"center","show-overflow-tooltip":!0}}),e._v(" "),a("el-table-column",{attrs:{prop:"cellId",label:"小区识别码",align:"center","show-overflow-tooltip":!0}}),e._v(" "),a("el-table-column",{attrs:{prop:"btsName",label:"基站名称",align:"center","show-overflow-tooltip":!0}}),e._v(" "),a("el-table-column",{attrs:{prop:"btsId",label:"基站识别码",align:"center","show-overflow-tooltip":!0}}),e._v(" "),a("el-table-column",{attrs:{prop:"techType",label:"技术体制",align:"center","show-overflow-tooltip":!0}}),e._v(" "),a("el-table-column",{attrs:{prop:"longitude",label:"经度",align:"center","show-overflow-tooltip":!0}}),e._v(" "),a("el-table-column",{attrs:{prop:"latitude",label:"技术体制",align:"center","show-overflow-tooltip":!0}}),e._v(" "),a("el-table-column",{attrs:{prop:"isHandle",label:"状态",align:"center","show-overflow-tooltip":!0},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(1==t.row.isHandle?"干扰":"未干扰"))]}}])})],1),e._v(" "),a("div",{staticClass:"table-pagin"},[a("el-pagination",{attrs:{layout:"sizes, total, prev, pager, next, jumper","page-sizes":[20,50,100,200],"current-page":e.pageDTO.page,total:e.pageTotal},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1),e._v(" "),a("el-dialog",{attrs:{title:"扇区数据查看信息",visible:e.dialogVisible,"custom-class":"history-dialog",width:"998px","append-to-body":""},on:{"update:visible":function(t){e.dialogVisible=t}}},[a("station-detail",{attrs:{detail:e.currentDetail}})],1)],1)},staticRenderFns:[]};var r=a("VU/8")(i,l,!1,function(e){a("o/5+")},"data-v-181b99be",null);t.default=r.exports}});