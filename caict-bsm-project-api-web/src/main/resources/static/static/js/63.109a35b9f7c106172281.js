webpackJsonp([63],{E77E:function(e,t){},rgvp:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var s=a("Xxa5"),i=a.n(s),n=a("exGp"),l=a.n(n),r=a("SR6C"),c=new(a("oFuF").a),o={data:function(){return{loading:!1,elicenseData:{},isShowTime:!1,options:[],vaildOptions:[{value:1,label:"换发"},{value:2,label:"停用"},{value:4,label:"注销"}],disOptions:[{value:3,label:"恢复"},{value:4,label:"注销"}],overOptions:[{value:4,label:"注销"}],isShowSelect:!0,licenseOpDTO:{guid:"",stationOpType:"",startDate:"",endDate:""},newStartTime:"",newEndTime:"",isShowRadio1:!1,isShowRadio2:!1,isShowRadio3:!1}},mounted:function(){var e=this;this.$nextTick(function(){e.init()})},created:function(){},watch:{},methods:{returnState:function(e){switch(parseInt(e)){case 1:return"已发";case 2:return"过期";case 3:return"停用";case 4:return"注销";default:return"异常"}},selectLicenseState:function(e){this.isShowTime=1===e},selectRadio:function(e){this.isShowTime=1===e},goBack:function(){this.$router.go(-1)},init:function(){var e=this;return l()(i.a.mark(function t(){return i.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,c.getStorage("elicenseDetail");case 2:e.elicenseData=t.sent,t.t0=parseInt(e.elicenseData.licenseState),t.next=1===t.t0?6:2===t.t0?9:3===t.t0?12:4===t.t0?15:18;break;case 6:return e.options=e.vaildOptions,e.isShowRadio1=!0,t.abrupt("break",19);case 9:return e.options=e.overOptions,e.isShowRadio3=!0,t.abrupt("break",19);case 12:return e.options=e.disOptions,e.isShowRadio2=!0,t.abrupt("break",19);case 15:return e.$message.warning("执照已被注销无法操作！！！"),e.isShowSelect=!1,t.abrupt("break",19);case 18:return t.abrupt("break",19);case 19:e.elicenseData.licenseState=e.returnState(e.elicenseData.licenseState);case 20:case"end":return t.stop()}},t,e)}))()},upDataLicense:function(){var e=this;return l()(i.a.mark(function t(){var a,s,n,l,o;return i.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(e.licenseOpDTO.guid=e.elicenseData.guid,1!==e.licenseOpDTO.stationOpType){t.next=16;break}if(a=c.toTimestamp(e.newStartTime),s=c.toTimestamp(e.newEndTime),n=c.toTimestamp(e.elicenseData.licenseEndDate),!(a<=n)){t.next=7;break}return e.$message.warning("换发后的开始时间须大于执照之前的结束时间！"),t.abrupt("return",!1);case 7:if(""!==a&&""!==s){t.next=10;break}return e.$message.warning("请设置执照有效时间"),t.abrupt("return",!1);case 10:if(!(a>s&&""!=a&""!=s)){t.next=13;break}return e.$message.warning("开始时间不能大于结束时间，请从新选择"),t.abrupt("return",!1);case 13:l=[a,s],e.licenseOpDTO.startDate=l[0],e.licenseOpDTO.endDate=l[1];case 16:return t.next=18,r.d(e.licenseOpDTO);case 18:(o=t.sent).status&&o.res.success?(e.$message({message:"执照操作成功",type:"success"}),e.$router.push("/license/index")):e.$message.warning(o.res.reason);case 20:case"end":return t.stop()}},t,e)}))()}}},u={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"content-template"},[a("div",{staticClass:"top-nav"},[a("el-breadcrumb",{attrs:{"separator-class":"el-icon-arrow-right"}},[a("el-breadcrumb-item",{attrs:{to:"/license/index"}},[e._v("电子执照")]),e._v(" "),a("el-breadcrumb-item",[e._v("电子执照管理")])],1)],1),e._v(" "),e._m(0),e._v(" "),a("div",{staticClass:"search-box"},[a("el-form",{staticClass:"form-list",attrs:{inline:!1,"label-position":"right","label-width":"120px"}},[a("el-form-item",{attrs:{label:"执照编号："}},[a("el-input",{staticClass:"input-width",attrs:{readonly:!0},model:{value:e.elicenseData.licenseCode,callback:function(t){e.$set(e.elicenseData,"licenseCode",t)},expression:"elicenseData.licenseCode"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"基站名称："}},[a("el-input",{staticClass:"input-width",attrs:{readonly:!0},model:{value:e.elicenseData.stationName,callback:function(t){e.$set(e.elicenseData,"stationName",t)},expression:"elicenseData.stationName"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"基站识别码："}},[a("el-input",{staticClass:"input-width",attrs:{readonly:!0},model:{value:e.elicenseData.stationCode,callback:function(t){e.$set(e.elicenseData,"stationCode",t)},expression:"elicenseData.stationCode"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"该执照状态："}},[a("el-input",{staticClass:"input-width",attrs:{readonly:!0},model:{value:e.elicenseData.licenseState,callback:function(t){e.$set(e.elicenseData,"licenseState",t)},expression:"elicenseData.licenseState"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"执照有效期："}},[a("el-date-picker",{staticClass:"input-width",attrs:{readonly:"",type:"datetime"},model:{value:e.elicenseData.licenseDateB,callback:function(t){e.$set(e.elicenseData,"licenseDateB",t)},expression:"elicenseData.licenseDateB"}}),e._v(" "),a("span",{staticClass:"date-picker-span"},[e._v("至")]),e._v(" "),a("el-date-picker",{staticClass:"input-width",attrs:{readonly:"",type:"datetime"},model:{value:e.elicenseData.licenseDateE,callback:function(t){e.$set(e.elicenseData,"licenseDateE",t)},expression:"elicenseData.licenseDateE"}})],1),e._v(" "),e.isShowRadio1?a("el-form-item",{attrs:{label:"电子执照设置："}},[a("el-radio-group",{staticClass:"input-width",on:{change:e.selectRadio},model:{value:e.licenseOpDTO.stationOpType,callback:function(t){e.$set(e.licenseOpDTO,"stationOpType",t)},expression:"licenseOpDTO.stationOpType"}},[a("el-radio",{attrs:{label:1}},[e._v("换发")]),e._v(" "),a("el-radio",{attrs:{label:2}},[e._v("停用")]),e._v(" "),a("el-radio",{attrs:{label:4}},[e._v("注销")])],1)],1):e._e(),e._v(" "),e.isShowRadio2?a("el-form-item",{attrs:{label:"电子执照设置"}},[a("el-radio-group",{staticClass:"input-width",on:{change:e.selectRadio},model:{value:e.licenseOpDTO.stationOpType,callback:function(t){e.$set(e.licenseOpDTO,"stationOpType",t)},expression:"licenseOpDTO.stationOpType"}},[a("el-radio",{attrs:{label:3}},[e._v("恢复")]),e._v(" "),a("el-radio",{attrs:{label:4}},[e._v("注销")])],1)],1):e._e(),e._v(" "),e.isShowRadio3?a("el-form-item",{attrs:{label:"电子执照设置"}},[a("el-radio-group",{staticClass:"input-width",on:{change:e.selectRadio},model:{value:e.licenseOpDTO.stationOpType,callback:function(t){e.$set(e.licenseOpDTO,"stationOpType",t)},expression:"licenseOpDTO.stationOpType"}},[a("el-radio",{attrs:{label:4}},[e._v("注销")])],1)],1):e._e(),e._v(" "),e.isShowTime?a("el-form-item",{attrs:{label:"有效期设置："}},[a("el-date-picker",{staticClass:"input-width",attrs:{type:"datetime",placeholder:"选择开始日期"},model:{value:e.newStartTime,callback:function(t){e.newStartTime=t},expression:"newStartTime"}}),e._v(" "),a("span",{staticClass:"date-picker-span"},[e._v("至")]),e._v(" "),a("el-date-picker",{staticClass:"input-width",attrs:{type:"datetime",placeholder:"选择结束日期"},model:{value:e.newEndTime,callback:function(t){e.newEndTime=t},expression:"newEndTime"}})],1):e._e(),e._v(" "),a("el-form-item",[e.isShowSelect?a("el-button",{staticClass:"default-button",attrs:{type:"primary"},on:{click:e.upDataLicense}},[e._v("确定")]):e._e(),e._v(" "),a("el-button",{staticClass:"default-button",on:{click:function(t){return e.$router.push("/license/index")}}},[e._v("返回")])],1)],1)],1)])},staticRenderFns:[function(){var e=this.$createElement,t=this._self._c||e;return t("div",{staticClass:"operate-box"},[t("span",{staticClass:"operate-table-title"},[this._v("电子执照相关设置")])])}]};var p=a("VU/8")(o,u,!1,function(e){a("E77E")},"data-v-088c0d31",null);t.default=p.exports}});