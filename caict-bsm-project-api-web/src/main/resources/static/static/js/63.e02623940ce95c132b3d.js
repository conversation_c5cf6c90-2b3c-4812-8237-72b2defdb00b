webpackJsonp([63],{"2Y4+":function(t,e){},FlnW:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var o=a("Dd8w"),r=a.n(o),l=a("Xxa5"),s=a.n(l),n=a("exGp"),i=a.n(n),c=a("oFuF"),u=new c.a,p={props:{jobId:{type:String,default:"",required:!0}},data:function(){return{tableCSVData:[],tableFileData:[],jobState:null,jobCode:""}},created:function(){this.getTableData()},methods:{getTableData:function(){var t=this;this.$ajax.get({url:"/api/apiWeb/transfer/transportJob/detail/"+this.jobId}).then(function(e){if(e.success){var a=e.data;t.tableCSVData=a.transportFileDTOList?a.transportFileDTOList:[],t.tableFileData=a.transportFileAttachedDTOList?a.transportFileDTOList:[]}})},handleViewFile:function(t){u.funDownload(t.filePath)}},filters:{processStatus:function(t){switch(t){case 0:return"处理失败";case 1:return"正在处理";case 2:return"处理成功";default:return""}},processStatusStyle:function(t){switch(t){case 1:return"text-warning";case 2:return"text-primary";case 3:return"text-success";case 4:return"text-danger";default:return"text-info"}},fileProcessStyle:function(t){return 2===t},fileRemoveStyle:function(t){return 2===t},fileFormatSize:function(t){return WebUploader.Base.formatSize(t)}}},d={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"border-color-primary repost-box"},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"operate-box"},[a("span",{staticClass:"operate-table-title"},[t._v("基站上传记录")])]),t._v(" "),a("el-table",{staticClass:"table-content",attrs:{height:"300px",data:t.tableCSVData,"default-sort":{prop:"gmtCreate",order:"descending"},lazy:"",stripe:""}},[a("el-table-column",{attrs:{prop:"fileLocalName",label:"文件名","show-overflow-tooltip":!0}}),t._v(" "),a("el-table-column",{attrs:{sortable:"","sort-orders":["descending","ascending"],prop:"gmtCreate",label:"上传时间","show-overflow-tooltip":!0,align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(t._f("format")(e.row.gmtCreate)))]}}])}),t._v(" "),a("el-table-column",{attrs:{prop:"fileState",label:"处理状态","show-overflow-tooltip":!0,align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",{class:t._f("processStatusStyle")(e.row.fileState)},[t._v(t._s(t._f("processStatus")(e.row.fileState)))])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"操作",width:"150",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return t.handleViewFile(e.row)}}},[t._v("查看")])]}}])})],1)],1),t._v(" "),a("el-card",{staticClass:"box-card",staticStyle:{"margin-top":"20px"}},[a("div",{staticClass:"operate-box"},[a("span",{staticClass:"operate-table-title"},[t._v("材料上传记录")])]),t._v(" "),a("el-table",{staticClass:"table-content",attrs:{height:"300px",data:t.tableFileData,"default-sort":{prop:"gmtCreate",order:"descending"},lazy:"",stripe:""}},[a("el-table-column",{attrs:{prop:"fileLocalName",label:"文件名","show-overflow-tooltip":!0}}),t._v(" "),a("el-table-column",{attrs:{sortable:"","sort-orders":["descending","ascending"],prop:"gmtCreate",label:"上传时间","show-overflow-tooltip":!0,align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(t._f("format")(e.row.gmtCreate)))]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"操作",width:"150",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return t.handleViewFile(e.row)}}},[t._v("查看")])]}}])})],1)],1)],1)},staticRenderFns:[]};var b=a("VU/8")(p,d,!1,function(t){a("2Y4+")},"data-v-123ca6e7",null).exports,f=a("Innn"),m=new c.a,g={data:function(){return{timer:null,imgUrl:"",showImgVisible:!1,tableData:[],outerVisible:!1,isAdvancedSearch:!1,stationOptions:[{detail:"未处理",code:"1"},{detail:"未提交，已处理",code:"2"},{detail:"已提交，处理未通过",code:"3"},{detail:"已提交，处理通过",code:"4"},{detail:"无效事项",code:"5"}],currentJobName:"",currentJobId:"",pageTotal:0,loading:!1,jobCode:"",jobId:"",jobPageDTO:{jobCode:"",jobDateEnd:"",jobDateStart:"",jobId:"",jobName:"",jobState:"",page:1,rows:10},goDetailShow:!1,goDetailObj:{},detailTableData:[]}},mounted:function(){this.getData()},beforeDestroy:function(){this.clearTimer()},methods:{getData:function(){var t=this,e=arguments;return i()(s.a.mark(function a(){var o,r,l;return s.a.wrap(function(a){for(;;)switch(a.prev=a.next){case 0:return a.next=2,f.a(t.jobPageDTO);case 2:(o=a.sent).status&&(r=[o.res.data.list],t.tableData=r[0],l=[o.res.data.total],t.pageTotal=l[0]),e[0]||(t.timer=setInterval(function(){t.getData(1)},5e3));case 5:case"end":return a.stop()}},a,t)}))()},selectChange:function(){this.jobPageDTO.page=1},searchFun:function(){var t=m.toTimestamp(this.jobPageDTO.jobDateStart),e=m.toTimestamp(this.jobPageDTO.jobDateEnd);if(e>0&&(e+=86399999),t>e&&""!=t&""!=e)return this.$message.warning("开始时间不能大于结束时间，请重新选择"),!1;this.jobPageDTO.page=1,this.clearTimer(),this.getData()},handleViewDetail:function(t){this.currentJobName=t.jobName,this.currentJobId=t.guid,this.outerVisible=!0},handleProcessDetail:function(t){t.dataType?this.$router.push({path:"/report-increment",query:{jobName:t.jobName,dataType:t.dataType}}):this.$router.push({path:"/report-full-dose",query:{jobName:t.jobName}})},handleUndoDetail:function(t){var e=this;t.guid&&this.$ajax.get({url:"/api/apiWeb/transfer/transportJob/withdraw/"+t.guid}).then(function(t){try{t.success?(e.jobPageDTO.page=1,e.getData(),e.$message.success("撤销成功！")):e.$message.error(t.message)}catch(t){e.$message.error("撤销失败！")}})},handleDeleteDetail:function(t){},changePage:function(t){this.jobPageDTO.page=t,this.getData()},openAdvancedSearch:function(){this.isAdvancedSearch=!this.isAdvancedSearch,this.isAdvancedSearch||(this.jobPageDTO=r()({},this.jobPageDTO,{jobDateEnd:"",jobDateStart:""}))},clearTimer:function(){clearInterval(this.timer),this.timer=null},goDetail:function(t){this.goDetailShow=!0,this.goDetailObj=r()({},t,{page:1,sumPage:1,rows:30}),this.getDetail(this.goDetailObj)},getDetail:function(t){var e=this;this.$ajax.post({url:"/api/apiWeb/transfer/transportJobBranch/findAllBranch",data:{page:t.page,rows:t.rows,jobGuid:t.guid}}).then(function(t){t.success&&(e.detailTableData=t.data.list)})}},components:{ReportDetail:b},filters:{genNumChinese:function(t){switch(parseInt(t)){case 2:return"2G";case 3:return"3G";case 4:return"4G";case 5:return"5G";default:return""}},dataTypeChinese:function(t){switch(parseInt(t)){case 1:return"新增";case 2:return"变更";case 3:return"注销";default:return""}}}},h={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"content-template"},[a("div",{staticClass:"search-box"},[a("el-form",{staticClass:"search-form",attrs:{inline:!0,"label-position":"left"}},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{xs:16,sm:12,lg:8}},[a("el-form-item",{attrs:{label:"事项名称："}},[a("el-input",{model:{value:t.jobPageDTO.jobName,callback:function(e){t.$set(t.jobPageDTO,"jobName",e)},expression:"jobPageDTO.jobName"}})],1)],1),t._v(" "),a("el-col",{attrs:{xs:16,sm:12,lg:8}},[a("el-form-item",{attrs:{label:"处理状态："}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择"},model:{value:t.jobPageDTO.jobState,callback:function(e){t.$set(t.jobPageDTO,"jobState",e)},expression:"jobPageDTO.jobState"}},t._l(t.stationOptions,function(t){return a("el-option",{key:t.code,attrs:{label:t.detail,value:t.code}})}),1)],1)],1),t._v(" "),a("el-col",{attrs:{xs:16,sm:12,lg:8}},[a("el-form-item",{directives:[{name:"show",rawName:"v-show",value:!t.isAdvancedSearch,expression:"!isAdvancedSearch"}]},[a("el-button",{attrs:{type:"primary",size:"medium",icon:"el-icon-search"},on:{click:t.searchFun}},[t._v("搜索")]),t._v(" "),a("el-button",{attrs:{type:"text"},on:{click:t.openAdvancedSearch}},[t._v("\n              展开更多搜索 \n              "),a("span",{staticClass:"el-icon-arrow-down form-item-icon"})])],1)],1)],1),t._v(" "),a("el-row",{directives:[{name:"show",rawName:"v-show",value:t.isAdvancedSearch,expression:"isAdvancedSearch"}],attrs:{gutter:20}},[a("el-col",{attrs:{xs:16,sm:12,lg:8}},[a("el-form-item",{attrs:{label:"开始时间："}},[a("el-date-picker",{attrs:{type:"date",placeholder:"选择开始时间","default-time":"12:00:00"},model:{value:t.jobPageDTO.jobDateStart,callback:function(e){t.$set(t.jobPageDTO,"jobDateStart",e)},expression:"jobPageDTO.jobDateStart"}})],1)],1),t._v(" "),a("el-col",{attrs:{xs:16,sm:12,lg:8}},[a("el-form-item",{attrs:{label:"结束时间："}},[a("el-date-picker",{attrs:{type:"date",placeholder:"选择结束时间","default-time":"12:00:00"},model:{value:t.jobPageDTO.jobDateEnd,callback:function(e){t.$set(t.jobPageDTO,"jobDateEnd",e)},expression:"jobPageDTO.jobDateEnd"}})],1)],1),t._v(" "),a("el-col",{attrs:{xs:16,sm:12,lg:8}},[a("el-form-item",[a("el-button",{staticClass:"default-button",attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.searchFun}},[t._v("搜索")]),t._v(" "),a("el-button",{attrs:{type:"text"},on:{click:t.openAdvancedSearch}},[t._v("\n              隐藏更多搜索 \n              "),a("span",{staticClass:"el-icon-arrow-up form-item-icon"})])],1)],1)],1)],1)],1),t._v(" "),t._m(0),t._v(" "),a("div",{staticClass:"table-box"},[a("el-table",{staticClass:"table-content",attrs:{height:"100%",data:t.tableData,stripe:""}},[a("el-table-column",{attrs:{prop:"jobName",label:"事项名称","show-overflow-tooltip":!0,align:"center"}}),t._v(" "),a("el-table-column",{attrs:{prop:"jobDate",label:"上报时间","show-overflow-tooltip":!0,align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(t._f("format")(e.row.jobDate)))]}}])}),t._v(" "),a("el-table-column",{attrs:{prop:"jobState",label:"处理状态","show-overflow-tooltip":!0,align:"left"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",{class:t._f("jobStateStyle")(e.row.jobState)},[t._v(t._s(t._f("jobState")(e.row.jobState)))])]}}])}),t._v(" "),a("el-table-column",{attrs:{prop:"isCompare",label:"审核状态","show-overflow-tooltip":!0,align:"left"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",{class:t._f("isCompareStyle")(e.row.isCompare)},[t._v(t._s(t._f("isCompare")(e.row.isCompare)))])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"操作",width:"200",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return t.handleViewDetail(e.row)}}},[t._v("查看")]),t._v(" "),a("el-button",{attrs:{type:"text"},on:{click:function(a){return t.goDetail(e.row)}}},[t._v("详情")]),t._v(" "),a("el-button",{attrs:{type:"text",disabled:parseInt(e.row.isCompare)>=1},on:{click:function(a){return t.handleProcessDetail(e.row)}}},[t._v("编辑")]),t._v(" "),a("el-button",{attrs:{type:"text",disabled:/[45679]/gi.test(e.row.isCompare)},on:{click:function(a){return t.handleUndoDetail(e.row)}}},[t._v("撤消")])]}}])})],1),t._v(" "),a("div",{staticClass:"table-pagin"},[a("el-pagination",{attrs:{layout:"total, prev, pager, next, jumper","current-page":t.jobPageDTO.page,total:t.pageTotal,"page-size":10},on:{"current-change":t.changePage}})],1)],1),t._v(" "),this.outerVisible?a("div",[a("el-dialog",{attrs:{visible:t.outerVisible,title:"事项详情（"+t.currentJobName+"）",width:"980px"},on:{"update:visible":function(e){t.outerVisible=e}}},[a("report-detail",{attrs:{"job-id":t.currentJobId}}),t._v(" "),a("div",{staticStyle:{"text-align":"center"},attrs:{slot:"footer"},slot:"footer"},[a("el-button",{staticClass:"default-button",attrs:{type:"primary",plain:""},on:{click:function(e){t.outerVisible=!1}}},[t._v("关闭窗口")])],1)],1)],1):t._e(),t._v(" "),a("el-dialog",{attrs:{width:"980px",title:"图片预览",visible:t.showImgVisible,"append-to-body":""},on:{"update:visible":function(e){t.showImgVisible=e}}},[a("img",{attrs:{src:t.imgUrl,height:"100%",width:"100%"}})]),t._v(" "),a("el-dialog",{attrs:{width:"1200px",title:t.goDetailObj.jobName+"详情",visible:t.goDetailShow,"append-to-body":""},on:{"update:visible":function(e){t.goDetailShow=e}}},[a("el-table",{staticClass:"table-content",attrs:{height:"600px",data:t.detailTableData,stripe:""}},[a("el-table-column",{attrs:{prop:"regionName",label:"地区",align:"center"}}),t._v(" "),a("el-table-column",{attrs:{prop:"genNum",label:"代数",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(t._f("genNumChinese")(e.row.genNum)))])]}}])}),t._v(" "),a("el-table-column",{attrs:{prop:"dataType",label:"类型",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(t._f("dataTypeChinese")(e.row.dataType)))])]}}])}),t._v(" "),a("el-table-column",{attrs:{prop:"stationCount",label:"基站数量",align:"center"}}),t._v(" "),a("el-table-column",{attrs:{prop:"cellCount",label:"扇区数量",align:"center"}}),t._v(" "),a("el-table-column",{attrs:{prop:"isCompare",label:"状态",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[12==e.row.isCompare?a("span",{class:t._f("isCompareStyle")(e.row.isCompare)},[t._v("完结")]):a("span",{class:t._f("isCompareStyle")(e.row.isCompare)},[t._v(t._s(t._f("isCompare")(e.row.isCompare)))])]}}])})],1)],1)],1)},staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"operate-box"},[e("span",{staticClass:"operate-table-title"},[this._v("我的上报")])])}]},v=a("VU/8")(g,h,!1,null,null,null);e.default=v.exports}});