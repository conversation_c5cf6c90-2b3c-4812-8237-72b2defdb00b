webpackJsonp([64],{"+/eI":function(t,a){},gsyq:function(t,a,e){"use strict";Object.defineProperty(a,"__esModule",{value:!0});var l=e("bOdI"),s=e.n(l),i=e("H+Cu"),r=new(e("oFuF").a),o={components:{CompareDetail:i.a},data:function(){var t;return{isAble:!1,dialogVisible:!1,textarea:"审核通过，许可办理！",fileRadio:!0,tableData:[],transportRawBts:[],transportSchedule:[],compareResult:"",pageTotal:0,pageDTO:(t={appGuid:"",jobId:""},s()(t,"appGuid",""),s()(t,"page",1),s()(t,"rows",10),s()(t,"resultType","1"),t),fileData:[],add:"",del:"",edit:"",nor:""}},computed:{appGuid:function(){return this.$route.params.guid},jobId:function(){return this.$route.params.job}},mounted:function(){var t=this;this.$nextTick(function(){t.getCount(),t.initTableData(),t.initFileDetail()})},methods:{initTableData:function(){var t=this;this.pageDTO.jobId=this.jobId,this.pageDTO.appGuid=this.appGuid,this.$ajax.post({url:"/api/apiWeb/transfer/transportCompareResult/getCompareScheduleListByPage",data:this.pageDTO}).then(function(a){a.success?a.data?(t.tableData=a.data.list,t.pageTotal=a.data.total):(t.tableData=[],t.pageTotal=0):(t.$message.error(a.message),t.tableData=[],t.pageTotal=0)},function(){t.tableData=[],t.pageTotal=0})},getCount:function(){var t=this;this.$ajax.get({url:"/api/apiWeb/transfer/transportCompareResult/getCount/"+this.appGuid}).then(function(a){if(a.success){var e=[a.data.add,a.data.edit,a.data.del,a.data.nor];return t.add=e[0],t.edit=e[1],t.del=e[2],void(t.nor=e[3])}a.success||t.$message.error(a.message)})},currentRowView:function(t){var a=this;this.compareResult=t.resultType,this.$ajax.get({url:"/api/apiWeb/transfer/transportCompareResult/findDetail/"+t.guid}).then(function(t){if(t.success)return a.dialogVisible=!0,a.transportRawBts=[t.data.transportRawBtsDTO],void(a.transportSchedule=[t.data.approvalScheduleDTO]);t.success||a.$message.error(t.message)})},downloadFile:function(t){r.funDownload(t)},handleChangePage:function(t){this.pageDTO.page=t,this.initTableData()},commitReview:function(){var t=this;this.$message("正在入库……"),this.isAble=!0;var a={opDetail:this.textarea,jobId:this.jobId,guid:this.appGuid,isApproved:this.fileRadio?1:2};this.$ajax.post({url:"/api/apiWeb/transfer/approvalTransportJob/approveApproveJob",data:a}).then(function(a){a.success&&t.$message.success(a.message)}),setTimeout(function(){t.$router.push("/audit/records")},5e3)},initFileDetail:function(){var t=this;this.$ajax.get({url:"/api/apiWeb/transfer/transportFile/findFileListByJob/"+this.appGuid}).then(function(a){a.success?t.fileData=a.data:a.status||t.$message.error(a.message)})}},watch:{"pageDTO.resultType":function(){this.initTableData()}}},n={render:function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{staticClass:"content-template"},[e("div",{staticClass:"top-nav"},[e("el-breadcrumb",{attrs:{"separator-class":"el-icon-arrow-right"}},[e("el-breadcrumb-item",{attrs:{to:"/compare/records"}},[t._v("数据比对")]),t._v(" "),e("el-breadcrumb-item",[t._v("比对审核")])],1)],1),t._v(" "),e("div",{staticClass:"compare-container"},[e("el-card",{attrs:{shadow:"always"}},[e("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e("span",[t._v("比对详情列表")])]),t._v(" "),e("div",{staticClass:"compare-list"},[[e("el-tabs",{model:{value:t.pageDTO.resultType,callback:function(a){t.$set(t.pageDTO,"resultType",a)},expression:"pageDTO.resultType"}},[e("el-tab-pane",{attrs:{name:"1"}},[e("span",{attrs:{slot:"label"},slot:"label"},[t._v("\n                新增异常\n                "),t.add>0?e("el-badge",{attrs:{value:t.add,size:"mini"}}):t._e()],1)]),t._v(" "),e("el-tab-pane",{attrs:{name:"2"}},[e("span",{attrs:{slot:"label"},slot:"label"},[t._v("\n                变更异常\n                "),t.edit>0?e("el-badge",{attrs:{value:t.edit,size:"mini"}}):t._e()],1)]),t._v(" "),e("el-tab-pane",{attrs:{name:"3"}},[e("span",{attrs:{slot:"label"},slot:"label"},[t._v("\n                注销异常\n                "),t.del>0?e("el-badge",{attrs:{value:t.del,size:"mini"}}):t._e()],1)]),t._v(" "),e("el-tab-pane",{attrs:{name:"4"}},[e("span",{attrs:{slot:"label"},slot:"label"},[t._v("\n                比对通过\n                "),t.nor>0?e("el-badge",{attrs:{value:t.nor,size:"mini"}}):t._e()],1)])],1)],t._v(" "),e("el-table",{staticClass:"table-content",attrs:{height:"300px",fit:"",stripe:"",data:t.tableData}},[e("el-table-column",{attrs:{prop:"btsName",align:"left",label:"基站名称","show-overflow-tooltip":!0}}),t._v(" "),e("el-table-column",{attrs:{prop:"btsId",align:"left",label:"基站识别码","show-overflow-tooltip":!0}}),t._v(" "),e("el-table-column",{attrs:{prop:"cellName",align:"center",label:"小区名称"}}),t._v(" "),e("el-table-column",{attrs:{prop:"cellId",align:"center",label:"小区识别码","show-overflow-tooltip":!0}}),t._v(" "),e("el-table-column",{attrs:{prop:"compareResult",align:"left",label:"比对结果","show-overflow-tooltip":!0},scopedSlots:t._u([{key:"default",fn:function(a){return["比对信息一致！"==a.row.compareResult?e("span",{staticClass:"text-success"},[t._v(t._s(a.row.compareResult))]):e("span",{staticClass:"text-danger"},[t._v(t._s(a.row.compareResult))])]}}])}),t._v(" "),e("el-table-column",{attrs:{label:"操作",width:"100px",align:"center"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{attrs:{type:"text"},on:{click:function(e){return t.currentRowView(a.row)}}},[t._v("查看")])]}}])})],1),t._v(" "),e("div",{staticClass:"table-pagin"},[e("el-pagination",{attrs:{layout:"total, prev, pager, next, jumper","current-page":t.pageDTO.page,total:t.pageTotal},on:{"current-change":t.handleChangePage}})],1)],2)]),t._v(" "),e("el-card",{staticStyle:{margin:"20px 0"},attrs:{shadow:"always"}},[e("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e("span",[t._v("附件列表")])]),t._v(" "),e("el-table",{staticClass:"table-content",attrs:{height:"300px",fit:"",stripe:"",data:t.fileData}},[e("el-table-column",{attrs:{prop:"fileLocalName",align:"left",label:"文件名称","show-overflow-tooltip":!0}}),t._v(" "),e("el-table-column",{attrs:{prop:"gmtCreate",align:"left",label:"创建时间","show-overflow-tooltip":!0},scopedSlots:t._u([{key:"default",fn:function(a){return[t._v(t._s(t._f("format")(a.row.gmtCreate)))]}}])}),t._v(" "),e("el-table-column",{attrs:{align:"left",label:"操作","show-overflow-tooltip":!0},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{attrs:{type:"text"},on:{click:function(e){return t.downloadFile(a.row.filePath)}}},[t._v("下载")])]}}])})],1)],1),t._v(" "),e("el-card",{staticStyle:{margin:"20px 0"},attrs:{shadow:"always"}},[e("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[e("span",[t._v("办理意见")])]),t._v(" "),e("div",{staticClass:"approve-list"},[e("el-form",{ref:"form",attrs:{"label-width":"100px"}},[e("el-form-item",{attrs:{label:"申请条件："}},[e("el-radio-group",{model:{value:t.fileRadio,callback:function(a){t.fileRadio=a},expression:"fileRadio"}},[e("el-radio",{attrs:{label:!0}},[t._v("通过审核")]),t._v(" "),e("el-radio",{attrs:{label:!1}},[t._v("未通过审核")])],1)],1),t._v(" "),e("el-form-item",{attrs:{label:"审核意见："}},[e("el-input",{attrs:{type:"textarea",rows:4,placeholder:"在此输入审核意见"},model:{value:t.textarea,callback:function(a){t.textarea=a},expression:"textarea"}})],1)],1),t._v(" "),e("div",{staticStyle:{"text-align":"center"}},[t.fileRadio?e("el-button",{staticClass:"default-button",attrs:{loading:t.isAble,type:"primary"},on:{click:t.commitReview}},[t._v("准予入库")]):e("el-button",{staticClass:"default-button",attrs:{loading:t.isAble,type:"primary"},on:{click:t.commitReview}},[t._v("不予入库")])],1)],1)])],1),t._v(" "),e("el-dialog",{attrs:{title:"比对详情","append-to-body":"",visible:t.dialogVisible,width:"980px"},on:{"update:visible":function(a){t.dialogVisible=a}}},[e("compare-detail",{attrs:{"table-data1":t.transportSchedule,"table-data2":t.transportRawBts,"compare-result":t.compareResult}}),t._v(" "),e("span",{attrs:{slot:"footer"},slot:"footer"},[e("el-button",{attrs:{type:"primary"},on:{click:function(a){t.dialogVisible=!1}}},[t._v("关闭")])],1)],1)],1)},staticRenderFns:[]};var p=e("VU/8")(o,n,!1,function(t){e("+/eI")},"data-v-0735d5ca",null);a.default=p.exports}});