webpackJsonp([65],{"5/aG":function(t,e){},"fGh+":function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var l=a("H+Cu"),o=new(a("oFuF").a),r={components:{CompareDetail:l.a},data:function(){return{roleType:sessionStorage.getItem("roleType"),dialogVisible:!1,textarea:"",fileRadio:1,tableData:[],fileData:[],filePath:"",transportRawBts:[],transportSchedule:[],compareResult:"",pageTotal:0,pageDTO:{guid:this.$route.params.guid,page:1,rows:10,resultType:"1"},poinion:"",add:"",edit:"",del:"",nor:""}},mounted:function(){var t=this;this.$nextTick(function(){t.initTableData()})},methods:{initTableData:function(){var t=this,e="";e=/wuwei/.test(this.roleType)?"/api/apiWeb/transferin/transportJobBranch/findHistoryByGuid":"/apiWeb/transfer/transportJobBranch/findAllDetailByUser",this.$ajax.post({url:e,data:this.pageDTO}).then(function(e){if(e.success)return/wuwei/.test(t.roleType)?(t.tableData=e.data.stationDTOList||[],t.pageTotal=e.data.total||0,t.fileData=e.data.transportFileAttachedDTOList,void(t.poinion=e.data.poinion)):(t.tableData=e.data.list||[],t.pageTotal=e.data.total||0,t.fileData=e.data.transportFileAttachedDTOList,void(t.poinion=e.data.poinion));e.success||(t.$message.error(e.message),t.tableData=[],t.pageTotal=0)},function(){t.tableData=[],t.pageTotal=0})},downloadFile:function(t){o.funDownload(t)},handleChangePage:function(t){this.pageDTO.page=t,this.initTableData()}},watch:{"pageDTO.resultType":function(){this.initTableData()}},filters:{fileFormatSize:function(t){return WebUploader.Base.formatSize(t)}}},n={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"content-template"},[a("div",{staticClass:"top-nav"},[a("el-breadcrumb",{attrs:{"separator-class":"el-icon-arrow-right"}},[a("el-breadcrumb-item",{attrs:{to:"/audit/records"}},[t._v("审核记录")]),t._v(" "),a("el-breadcrumb-item",[t._v("审核详情")])],1)],1),t._v(" "),a("div",{staticClass:"compare-container"},[a("el-card",{attrs:{shadow:"always"}},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("列表数据")])]),t._v(" "),a("div",{staticClass:"compare-list"},[[a("el-tabs",{model:{value:t.pageDTO.resultType,callback:function(e){t.$set(t.pageDTO,"resultType",e)},expression:"pageDTO.resultType"}},[a("el-tab-pane",{attrs:{name:"1"}},[a("span",{attrs:{slot:"label"},slot:"label"},[t._v("\n                比对通过\n                "),t.nor>0?a("el-badge",{attrs:{value:t.nor,size:"mini"}}):t._e()],1)])],1)],t._v(" "),/wuwei/.test(t.roleType)?a("el-table",{attrs:{height:"700px",fit:"",stripe:"",data:t.tableData}},[a("el-table-column",{attrs:{prop:"appCode",label:"申请表编号",align:"center","show-overflow-tooltip":!0}}),t._v(" "),a("el-table-column",{attrs:{prop:"statAppType",label:"技术资料申报表类型",align:"center","show-overflow-tooltip":!0}}),t._v(" "),a("el-table-column",{attrs:{prop:"statTdi",label:"技术资料申报表编号",align:"center","show-overflow-tooltip":!0}}),t._v(" "),a("el-table-column",{attrs:{prop:"statName",label:"台站名称",align:"center","show-overflow-tooltip":!0}}),t._v(" "),a("el-table-column",{attrs:{prop:"statAddr",label:"台站地址",align:"center","show-overflow-tooltip":!0}}),t._v(" "),a("el-table-column",{attrs:{prop:"statLg",label:"经度",align:"center","show-overflow-tooltip":!0}}),t._v(" "),a("el-table-column",{attrs:{prop:"statLa",label:"纬度",align:"center","show-overflow-tooltip":!0}}),t._v(" "),a("el-table-column",{attrs:{prop:"statAt",label:"海拔高度",align:"center","show-overflow-tooltip":!0}}),t._v(" "),a("el-table-column",{attrs:{prop:"statDateStart",label:"启用日期",align:"center","show-overflow-tooltip":!0},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n              "+t._s(t._f("format")(e.row.statDateStart))+"\n            ")]}}],null,!1,2960510817)})],1):a("el-table",{attrs:{height:"700px",fit:"",stripe:"",data:t.tableData}},[a("el-table-column",{attrs:{prop:"cellName",label:"扇区名称",align:"center","show-overflow-tooltip":!0}}),t._v(" "),a("el-table-column",{attrs:{prop:"cellId",label:"扇区识别码",align:"center","show-overflow-tooltip":!0}}),t._v(" "),a("el-table-column",{attrs:{prop:"genNum",label:"代数",align:"center","show-overflow-tooltip":!0},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.genNum)+"G")]}}])}),t._v(" "),a("el-table-column",{attrs:{prop:"techType",label:"技术体制",align:"center","show-overflow-tooltip":!0}}),t._v(" "),a("el-table-column",{attrs:{prop:"location",label:"台址",align:"center","show-overflow-tooltip":!0}}),t._v(" "),a("el-table-column",{attrs:{prop:"longitude",label:"经度",align:"center","show-overflow-tooltip":!0}}),t._v(" "),a("el-table-column",{attrs:{prop:"latitude",label:"纬度",align:"center","show-overflow-tooltip":!0}}),t._v(" "),a("el-table-column",{attrs:{prop:"uploadDate",label:"上传时间",align:"center","show-overflow-tooltip":!0},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(t._f("format")(e.row.uploadDate)))]}}])}),t._v(" "),a("el-table-column",{attrs:{prop:"dataType",label:"申请类型",align:"center","show-overflow-tooltip":!0},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(t._f("convertDataType")(e.row.dataType)))]}}])})],1),t._v(" "),a("div",{staticClass:"table-pagin"},[a("el-pagination",{attrs:{layout:"total, prev, pager, next","current-page":t.pageDTO.page,total:t.pageTotal},on:{"current-change":t.handleChangePage}})],1)],2)]),t._v(" "),a("el-card",{staticStyle:{margin:"20px 0"},attrs:{shadow:"always"}},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("附件列表")])]),t._v(" "),a("el-table",{attrs:{fit:"",stripe:"",data:t.fileData}},[a("el-table-column",{attrs:{prop:"fileLocalName",label:"文件名称","show-overflow-tooltip":!0}}),t._v(" "),a("el-table-column",{attrs:{prop:"gmtCreate",align:"center",label:"创建时间","show-overflow-tooltip":!0},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(t._f("format")(e.row.gmtCreate)))]}}])}),t._v(" "),a("el-table-column",{attrs:{align:"center",label:"操作","show-overflow-tooltip":!0},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return t.downloadFile(e.row.filePath)}}},[t._v("下载")])]}}])})],1)],1),t._v(" "),a("el-card",{staticStyle:{margin:"20px 0"},attrs:{shadow:"always"}},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("办理意见")])]),t._v(" "),a("div",{staticClass:"approve-list"},[a("el-form",{ref:"form",attrs:{"label-width":"100px"}},[a("el-form-item",{attrs:{label:"审核意见："}},[a("el-input",{attrs:{disabled:"",type:"textarea",rows:4,placeholder:"无审核意见！"},model:{value:t.poinion,callback:function(e){t.poinion=e},expression:"poinion"}})],1)],1),t._v(" "),a("div",{staticStyle:{"text-align":"center"}},[a("el-button",{attrs:{icon:"el-icon-back",type:"primary"},on:{click:function(e){return t.$router.go(-1)}}},[t._v("返回")])],1)],1)])],1),t._v(" "),a("el-dialog",{attrs:{title:"比对详情","append-to-body":"",visible:t.dialogVisible,width:"980px"},on:{"update:visible":function(e){t.dialogVisible=e}}},[a("compare-detail",{attrs:{"table-data1":t.transportSchedule,"table-data2":t.transportRawBts,"compare-result":t.compareResult}}),t._v(" "),a("span",{attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:function(e){t.dialogVisible=!1}}},[t._v("关闭")])],1)],1)],1)},staticRenderFns:[]};var s=a("VU/8")(r,n,!1,function(t){a("5/aG")},"data-v-0292232c",null);e.default=s.exports}});