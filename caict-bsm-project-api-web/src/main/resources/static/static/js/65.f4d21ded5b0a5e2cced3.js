webpackJsonp([65],{"sG/v":function(e,t){},tdLL:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=a("Xxa5"),r=a.n(o),s=a("exGp"),i=a.n(s),l=a("//Fk"),n=a.n(l),c=a("hdr7"),u=a("7dRK"),d=a("oFuF"),p=a("99Ht"),f="",m=new d.a,b={title:"csv",exteensions:"csv",mimeTypes:".csv"},h={title:"file",exteensions:"doc,docx,pdf,zip,rar,xls,xlsx,csv,jpg,png",mimeTypes:".doc,.docx,.pdf,.zip,.rar,.xls,.xlsx,.csv,.jpg,.png"},g={data:function(){return{BaseURL:p.a,isCsvCompare:!1,processCSVLoading:!1,tableCSVData:[],tableFileData:[],jobState:null,isCompare:null,jobCode:"",jobId:"",inputReadonly:!1,isShowDialog:!1,commitLoading:!1,commitDisabled:!0,fileType1:b,fileType2:h,formData:{jobId:""},ruleForm:{jobName:"",dataType:this.$route.query.dataType||"1"},rules:{jobName:[{required:!0,message:"请输入事项名称！",trigger:["blur","change"]}]},isDataTypeDisabled:!1}},created:function(){this.ruleForm.jobName=this.$route.query.jobName,this.ruleForm.jobName&&this.getTableData()},beforeDestroy:function(){window.clearInterval(f)},methods:{getTableData:function(){var e=this,t=this.ruleForm.jobName;return new n.a(function(a,o){e.$ajax.get({url:"/api/apiWeb/transfer/transportJob/createTransportJob/"+t}).then(function(t){if(t.success){var o=t.data;e.formData.jobId=o.guid,e.jobId=o.guid,e.jobState=o.jobState,e.isCompare=o.isCompare,e.tableCSVData=o.transportFileDTOList,e.tableFileData=o.transportFileAttachedDTOList,o.transportFileDTOList&&o.transportFileDTOList.forEach(function(t){4==t.fileState&&(e.isCsvCompare=!0)}),a(!0)}else e.$message.error(t.message),a(!1)})})},beforeUploadCheck:function(e){this.ruleForm.jobName||e.preventDefault()},handleUploadFileFinished:function(e){this.$refs.fileUploader.uploadReset(),this.getTableData()},handleRemoveFile:function(e){var t=this;if(!e.guid)return this.$message.error("非常抱歉，暂时还无法进行删除！");this.$confirm("此操作将永久删除该文件, 是否继续?","温馨提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){t.$ajax.get({url:"/api/apiWeb/document/upload/deleteFile/"+e.guid}).then(function(e){try{e.success?(t.getTableData(),t.$message.success("删除成功！")):t.$message.error(e.message)}catch(e){t.$message.error("删除失败！")}})}).catch(function(){t.$message.info("已取消删除操作。")})},handleCSVChange:function(e,t){var a=this;return i()(r.a.mark(function e(){var o;return r.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!a.jobId){e.next=2;break}return e.abrupt("return",t(!0));case 2:if(o=!1,a.$refs.ruleForm.validate(function(e){if(!e)return!1;o=!0}),o){e.next=6;break}return e.abrupt("return",t(!1,"请输入事项名称！"));case 6:return e.next=8,a.getTableData();case 8:if(!e.sent){e.next=11;break}return e.abrupt("return",t(!0));case 11:t(!1);case 12:case"end":return e.stop()}},e,a)}))()},handleRemoveCSV:function(e){var t=this;this.$confirm("此操作将永久删除该文件, 是否继续?","温馨提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){t.$ajax.get({url:"/api/apiWeb/document/upload/deleteFile/"+e.guid}).then(function(e){try{e.success?(t.getTableData(),t.$message.success("当前文件删除成功，现在您可以重新上传！")):t.$message.error(e.message)}catch(e){t.$message.error("删除失败！")}})}).catch(function(){t.$message.info("已取消删除操作。")})},handleProcessCSV:function(e){var t=this;if(this.processCSVLoading=!0,!e.guid)return this.$message.error("非常抱歉，暂时还无法进行操作！");this.$message("校验中……"),e.fileState=4,this.isCsvCompare=!0,this.$ajax.post({url:"/api/apiWeb/transfer/transportJob/processingTransportJob",data:{jobGuid:this.jobId,fileId:e.guid,dataType:this.ruleForm.dataType}}).then(function(e){try{e.success?(t.isDataTypeDisabled=!0,window.clearInterval(f),f=window.setInterval(function(){t.getTableData()},3e3),t.getTableData(),t.$message({message:e.message,type:"success",duration:5e3})):(t.getTableData(),t.$message({message:e.message,type:"error",duration:5e3}))}catch(e){t.$message.error("操作失败！")}t.isCsvCompare=!1,t.processCSVLoading=!1})},handleUploadCSVFinished:function(e){this.$refs.csvUploader.uploadReset(),this.getTableData()},handleViewFile:function(e){m.funDownload(e.filePath)},commitJob:function(){var e=this;if(this.$message.closeAll(),1==this.jobState){return this.$message.error("无效异常事项，请删除！")}if(2==this.jobState){return this.$message.error("已经提交过！")}this.commitLoading||(this.commitLoading=!0),window.clearInterval(f),this.$ajax.get({url:"/api/apiWeb/transfer/transportJob/commitTransportJob/"+this.jobId}).then(function(t){e.commitLoading=!1,t.success?(e.jobCode=t.data.jobCode,window.clearInterval(f)):e.$message.error(t.message)},function(){e.commitLoading=!1}),this.$message.success("提交任务成功，正在处理，请稍后。");var t=window.setTimeout(function(){window.clearTimeout(t),e.$router.push({path:"/report/me"})},2e3)}},components:{CSVUploader:c.a,FileUploader:u.a},watch:{jobId:function(e){e?this.inputReadonly=!0:(this.commitDisabled=!0,this.inputReadonly=!1)},isCompare:function(e){this.commitDisabled="4"===e}},filters:{processStatus:function(e){switch(e){case 0:return"文件开始上传";case 1:return"文件上传失败";case 2:return"文件上传成功待处理";case 3:return"文件校验中";case 4:return"文件校验失败";case 5:return"文件校验成功";default:return"文件处理失败"}},processStatusStyle:function(e){switch(e){case 2:return"text-primary";case 5:return"text-success";case 3:return"text-info";default:return"text-danger"}},fileProcessStyle:function(e){return 2!==e},fileRemoveStyle:function(e){return 2!==e},fileFormatSize:function(e){return WebUploader.Base.formatSize(1024*e)}}},v={render:function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("div",{staticClass:"border-color-primary repost-box"},[o("el-form",{ref:"ruleForm",staticClass:"repost-form",attrs:{"status-icon":"",model:e.ruleForm,rules:e.rules,inline:!1,"label-position":"left","label-width":"100px"}},[o("el-form-item",{attrs:{label:"事项名称：",prop:"jobName"}},[o("el-input",{attrs:{autofocus:!0,readonly:!1,placeholder:"输入如：201908080808",autocomplete:"off",clearable:""},model:{value:e.ruleForm.jobName,callback:function(t){e.$set(e.ruleForm,"jobName",t)},expression:"ruleForm.jobName"}})],1),e._v(" "),o("el-form-item",{attrs:{label:"数据类型：",prop:"dataType"}},[o("el-radio-group",{attrs:{disabled:e.isDataTypeDisabled},model:{value:e.ruleForm.dataType,callback:function(t){e.$set(e.ruleForm,"dataType",t)},expression:"ruleForm.dataType"}},[o("el-radio-button",{attrs:{label:"1"}},[e._v("新增")]),e._v(" "),o("el-radio-button",{attrs:{label:"2"}},[e._v("变更")]),e._v(" "),o("el-radio-button",{attrs:{label:"3"}},[e._v("注销")]),e._v(" "),o("el-radio-button",{attrs:{label:"6"}},[e._v("延续")])],1)],1),e._v(" "),o("el-form-item",{attrs:{label:"基站数据："}},[o("CSVUploader",{ref:"csvUploader",attrs:{url:e.BaseURL.ajaxDomain+"/apiWeb/document/upload/fileUpload","upload-button":"filePicker1",accept:e.fileType1,"auto-upload":!0,"form-data":e.formData},on:{fileChange:e.handleCSVChange,uploadFinished:e.handleUploadCSVFinished},nativeOn:{click:function(t){return e.beforeUploadCheck(t)}}})],1),e._v(" "),o("el-form-item",[o("el-card",{staticClass:"box-card"},[o("div",{staticClass:"operate-box"},[o("span",{staticClass:"operate-table-title"},[e._v("基站上传记录")])]),e._v(" "),o("el-table",{staticClass:"table-content",attrs:{height:"300px",data:e.tableCSVData,lazy:"",stripe:""}},[o("el-table-column",{attrs:{prop:"fileLocalName",label:"文件名","show-overflow-tooltip":!0}}),e._v(" "),o("el-table-column",{attrs:{sortable:"","sort-by":"descending","sort-orders":["descending","ascending"],"show-overflow-tooltip":!0,prop:"gmtCreate",label:"上传时间",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(e._f("format")(t.row.gmtCreate)))]}}])}),e._v(" "),o("el-table-column",{attrs:{prop:"fileState",label:"处理状态","show-overflow-tooltip":!0,align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[o("span",{class:e._f("processStatusStyle")(t.row.fileState)},[e._v(e._s(e._f("processStatus")(t.row.fileState)))])]}}])}),e._v(" "),o("el-table-column",{attrs:{label:"操作",width:"150",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[o("el-button",{attrs:{type:"text"},on:{click:function(a){return e.handleViewFile(t.row)}}},[e._v("查看")]),e._v(" "),o("el-button",{attrs:{type:"text",disabled:e._f("fileProcessStyle")(t.row.fileState)},on:{click:function(a){return e.handleProcessCSV(t.row)}}},[e._v("校验")]),e._v(" "),o("el-button",{attrs:{type:"text",disabled:e._f("fileRemoveStyle")(t.row.fileState)},on:{click:function(a){return e.handleRemoveCSV(t.row)}}},[e._v("删除")])]}}])})],1)],1)],1),e._v(" "),e.jobId?o("el-form-item",{attrs:{label:"相关材料："}},[o("FileUploader",{ref:"fileUploader",attrs:{url:e.BaseURL.ajaxDomain+"/apiWeb/document/upload/fileUpload","upload-button":"filePicker2",accept:e.fileType2,multiple:!0,"auto-upload":!1,"form-data":e.formData},on:{uploadFinished:e.handleUploadFileFinished},nativeOn:{click:function(t){return e.beforeUploadCheck(t)}}})],1):e._e(),e._v(" "),e.jobId?o("el-form-item",[o("el-card",{staticClass:"box-card"},[o("div",{staticClass:"operate-box"},[o("span",{staticClass:"operate-table-title"},[e._v("材料上传记录")])]),e._v(" "),o("el-table",{staticClass:"table-content",attrs:{height:"300px",data:e.tableFileData,lazy:"",stripe:""}},[o("el-table-column",{attrs:{prop:"fileLocalName",label:"文件名","show-overflow-tooltip":!0}}),e._v(" "),o("el-table-column",{attrs:{sortable:"","sort-by":"descending","sort-orders":["descending","ascending"],prop:"gmtCreate",label:"上传时间","show-overflow-tooltip":!0,align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(e._f("format")(t.row.gmtCreate)))]}}],null,!1,2931789341)}),e._v(" "),o("el-table-column",{attrs:{label:"操作",width:"100",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[o("el-button",{attrs:{type:"text"},on:{click:function(a){return e.handleViewFile(t.row)}}},[e._v("查看")]),e._v(" "),o("el-button",{attrs:{type:"text"},on:{click:function(a){return e.handleRemoveFile(t.row)}}},[e._v("删除")])]}}],null,!1,619021041)})],1)],1)],1):e._e()],1),e._v(" "),o("div",{staticClass:"upload-btn"},[o("el-row",[o("el-button",{attrs:{type:"primary",icon:"el-icon-check",loading:e.commitLoading,disabled:e.commitDisabled},on:{click:e.commitJob}},[e._v("提交")]),e._v(" "),o("el-button",{attrs:{type:"primary",icon:"el-icon-arrow-left",plain:""},on:{click:function(t){return e.$router.push("/report/me")}}},[e._v("我的上报")])],1),e._v(" "),o("el-tag",{staticStyle:{"margin-top":"20px"},attrs:{type:"success"}},[e._v("提示：上传文件成功后！请点击提交按钮")])],1),e._v(" "),o("el-dialog",{staticClass:"dialog-title",attrs:{title:"申报成功",width:"40%",visible:e.isShowDialog},on:{"update:visible":function(t){e.isShowDialog=t}}},[o("div",{staticStyle:{display:"inline-block"}},[o("img",{staticStyle:{float:"left"},attrs:{src:a("vC+O")}}),e._v(" "),o("div",{staticStyle:{float:"left","line-height":"30px","margin-left":"30px"}},[o("div",{staticStyle:{"font-family":"PingFangSC-Regular","font-size":"18px",color:"#252525","text-align":"left"}},[e._v("恭喜你申报成功")]),e._v(" "),o("span",[e._v("办件流水号："+e._s(e.jobCode))])])]),e._v(" "),o("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[o("el-button",{attrs:{plain:""},on:{click:function(t){e.isShowDialog=!1}}},[e._v("关闭窗口")]),e._v(" "),o("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.$router.push("/report/me")}}},[e._v("前往查看")])],1)])],1)},staticRenderFns:[]};var y=a("VU/8")(g,v,!1,function(e){a("sG/v")},"data-v-0dd69b8a",null);t.default=y.exports}});