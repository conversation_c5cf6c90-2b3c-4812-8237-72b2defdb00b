webpackJsonp([66],{"5ghg":function(e,t){},"jL/3":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=a("woOf"),l=a.n(n),o=a("Dd8w"),i=a.n(o),r=a("0xDb"),s={equAuth:"",freqEf:"",freqRf:"",freqBand:"",freqPass:"",ftFreqCsgn:"",isSave:!1},u={equModel:"",equMb:"",equCode:"",equMenu:"",equAuth:"",equType:"",equPow:"",antGain:"",antEpole:"",antHight:"",isSave:!1},c={},p=function(e){var t=e.stationForm,a=t.linceseCodeYear,n=t.linceseCodeType2,l=t.linceseCodeCity,o=t.linceseCodeType,i=t.linceseCodeNum02;e.stationForm.linceseCode=a+n+l+o+i},d={name:"newOrg",data:function(){return{stationType:[{value:"AL",label:"航空无线电导航陆地电台"},{value:"AT",label:"业余电台"},{value:"BC",label:"广播电台（声音）"},{value:"BT",label:"广播电台（电视）"},{value:"FA",label:"航空电台"},{value:"FB",label:"基地电台"},{value:"FC",label:"海（江）岸电台"},{value:"FX",label:"固定电台"},{value:"LR",label:"无线电定位陆地电台"},{value:"MA",label:"航空器电台"},{value:"ML",label:"陆地移动电台"},{value:"MR",label:"无线电定位移动电台"},{value:"MS",label:"船舶电台"},{value:"NP",label:"无线电导航电台（水上无线电导航和航空无线电导航电台除外）"},{value:"NQ",label:"水上无线电导航电台"},{value:"SQ",label:"气象辅助电台"},{value:"SS",label:"标准频率和时间信号电台"},{value:"QT",label:"前述17分钟类未包含的电台"}],stationCityCode:[{label:"贵州省",value:5200},{label:"贵阳市",value:5201},{label:"六盘水市",value:5202},{label:"铜仁市",value:5206},{label:"黔西南布依族苗族自治州",value:5223},{label:"黔东南苗族侗族自治州",value:5226},{label:"黔南布依族苗族自治州",value:5227},{label:"遵义市",value:5203},{label:"安顺市",value:5204},{label:"毕节市",value:5205}],stationType2:["G","S","J","H","T"],stationYear:function(){for(var e=[],t=1900;t<2100;t++)e.push(t);return e}(),commitLoadding:!1,stationForm:{staName:"",idenCode:"",userName:"",location:"",longitude:["","",""],latitude:["","",""],detail:"",linceseCodeType:"",linceseCodeYear:"",linceseCodeType2:"",linceseCodeCity:"",linceseCodeNum02:"",linceseCode:"",orgCode:"",linceseStartDate:"",linceseEndDate:"",specialCase:"",licenseAuth:""},generalInfoData:[],equipmentInfoData:[],antennaInfoData:[],rules:{orgCode:[{required:!0,message:"请输入统一社会信用代码或身份证明号码",trigger:"blur"}],linceseStartDate:[{required:!0,message:"请选择有效日期的起始日期",trigger:"blur"}],linceseEndDate:[{required:!0,message:"请选择有效日期的结束日期",trigger:"blur"}],staName:[{required:!0,message:"请输入台站名称",trigger:"blur"}],linceseCodeNum02:[{required:!0,message:"请输入编号流水号",trigger:"blur"},{pattern:/^\d{7}$/,message:"必须是7位数字",trigger:"blur"}]}}},created:function(){var e=this;this.$nextTick(function(){e.initData()})},methods:{initData:function(){var e=this,t=this.$route.query.stationGuid;if(!t)return this.$message.error("非常抱歉，没有相关数据！");this.$ajax.post({url:"/api/apiSta/sta/sta/sta ",data:{stationGuid:t}}).then(function(t){t.success?(e.stationForm=i()({},e.stationForm,t.data),e.stationForm.linceseCodeYear=e.stationForm.linceseCode.slice(0,4),e.stationForm.linceseCodeType2=e.stationForm.linceseCode.slice(4,5),e.stationForm.linceseCodeCity=e.stationForm.linceseCode.slice(5,9),e.stationForm.linceseCodeType=e.stationForm.linceseCode.slice(9,11),e.stationForm.linceseCodeNum02=e.stationForm.linceseCode.slice(11,18),e.initTableData()):e.$message.error(t.reason)})},initTableData:function(){var e=Object(r.a)(this.stationForm),t=e.staEquList,a=e.staAntennaList,n=e.staFreqList;e.linceseStartDate,e.linceseEndDate;this.equipmentInfoData=t.map(function(e,t){var n=a[t];return e.isSave=!0,n?l()(e,n):e}),this.generalInfoData=n.map(function(e,a){return e.equAuth=t[a].equAuth,e.isSave=!0,e})},handleCommitStation:function(){var e=this,t=!1;if(this.$refs.stationForm.validate(function(e){if(!e)return!1;t=!0}),!t)return this.$message.error("验证没通过，请检查您填写的内容是否正确！");var a=this.generalInfoData.filter(function(e){return!e.isSave});a.length?this.$confirm("您添加的发射/接收参数有"+a.length+"项没确定保存, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){e.createStation()}).catch(function(){e.$message.info("您已取消提交操作！")}):this.createStation()},createStation:function(){var e=this;this.commitLoadding=!0;var t=Object(r.a)(this.stationForm);t.staEquList=this.equipmentInfoData.map(function(e){var t=e.equModel,a=e.equMb,n=e.equCode,l=e.equMenu;return{equAuth:e.equAuth,equModel:t,equMb:a,equCode:n,equMenu:l,equType:e.equType,equPow:e.equPow}}),t.staAntennaList=this.equipmentInfoData.map(function(e){return{equAuth:e.equAuth,antGain:e.antGain,antEpole:e.antEpole,antHight:e.antHight}}),t.staFreqList=this.generalInfoData.map(function(e){return{equAuth:e.equAuth,freqBand:e.freqBand,freqPass:e.freqPass,ftFreqCsgn:e.ftFreqCsgn,freqEf:e.freqEf,freqRf:e.freqRf}}),this.$ajax.post({url:"/api/apiSta/sta/sta/updateSta ",data:t}).then(function(t){try{t.success?e.$confirm("添加成功！","非常恭喜您",{confirmButtonText:"前往查看",cancelButtonText:"关闭窗口",type:"success",center:!0}).then(function(){e.$router.push("/license-print")}).catch(function(){e.initData()}):e.$message.error(t.reason)}catch(t){e.$message.error("非常抱歉，添加失败！")}e.commitLoadding=!1},function(){return e.commitLoadding=!1})},handleEquipmentAddItem:function(){var e=Object(r.a)(u);this.equipmentInfoData.unshift(e)},handleEquipmentEditItem:function(e,t){c.equipmentInfoData=i()({},t),t.isSave=!1,this.equipmentInfoData.splice(e,1,i()({},t))},handleEquipmentRemoveItem:function(e,t){var a=this,n=this.generalInfoData.findIndex(function(e){return e.equAuth===t.equAuth});-1!==n?this.$confirm("此操作将删除您填写所对应的发射/接收参数信息, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){a.equipmentInfoData.splice(e,1),a.generalInfoData.splice(n,1)}).catch(function(){}):this.equipmentInfoData.splice(e,1)},handleEquipmentCancleItem:function(e,t){var a=t.equAuth,n=t.guid;if(a){var l=c.equipmentInfoData;if(l&&l.guid===n)return this.equipmentInfoData.splice(e,1,i()({},l)),void delete c.equipmentInfoData}this.equipmentInfoData.splice(e,1)},handleEquipmentComfirmItem:function(e,t){if(!t.equAuth)return this.$message.warning("请填写当前设备的型号核准代码！");for(var a=0;a<this.equipmentInfoData.length;a++)e!==a&&this.equipmentInfoData[a];t.isSave=!0,this.equipmentInfoData.splice(e,1,t)},handleGeneralAddItem:function(){var e=this.equipmentInfoData.filter(function(e){return!e.isSave});if(e.length)return this.$message.error("您添加的设备中有"+e.length+"项没保存，请先确定保存才能添加！");if(!this.equipmentInfoData.length)return this.$message.warning("您还没有添加设备，请先添加设备！");var t=null;if(this.generalInfoData&&this.generalInfoData.length)for(var a=this.equipmentInfoData.length-this.generalInfoData.length,n=0;n<a;n++)(t=Object(r.a)(s)).equAuth=this.equipmentInfoData[n].equAuth,this.generalInfoData.unshift(t);else this.generalInfoData=this.equipmentInfoData.map(function(e){return(t=Object(r.a)(s)).equAuth=e.equAuth,t})},handleGeneralEditItem:function(e,t){c.generalInfoData=i()({},t),t.isSave=!1,this.generalInfoData.splice(e,1,i()({},t))},handleGeneralRemoveItem:function(e,t){var a=this,n=this.equipmentInfoData.findIndex(function(e){return e.equAuth===t.equAuth});this.$confirm("此操作将删除您填写所对应的设备及天线信息, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){a.equipmentInfoData.splice(e,1),a.generalInfoData.splice(n,1)}).catch(function(){})},handleGeneralCancleItem:function(e,t){var a=c.generalInfoData;if(a&&a.guid===t.guid)return this.generalInfoData.splice(e,1,i()({},a)),void delete c.generalInfoData;this.equipmentInfoData.splice(e,1)},handleGeneralComfirmItem:function(e,t){t.isSave=!0,this.generalInfoData.splice(e,1,t)}},watch:{"stationForm.linceseCodeYear":function(e){p(this)},"stationForm.linceseCodeType2":function(e){p(this)},"stationForm.linceseCodeCity":function(e){p(this)},"stationForm.linceseCodeType":function(e){p(this)},"stationForm.linceseCodeNum02":function(e){p(this)}}},m={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"content-container"},[a("el-breadcrumb",{attrs:{"separator-class":"el-icon-arrow-right"}},[a("el-breadcrumb-item",{attrs:{to:"/license-print"}},[e._v("台站管理")]),e._v(" "),a("el-breadcrumb-item",[e._v("查看执照")])],1),e._v(" "),a("el-card",{staticClass:"table-box",staticStyle:{"margin-top":"20px"}},[a("div",{staticClass:"operate-box"},[a("span",{staticClass:"operate-table-title"},[e._v("执照主要信息")])]),e._v(" "),a("el-form",{ref:"stationForm",staticClass:"search-form",attrs:{model:e.stationForm,rules:e.rules,"label-width":"200px",inline:!0,"label-position":"right"}},[a("el-row",{attrs:{gutter:0}},[a("el-col",{attrs:{span:20,lg:12,xl:8}},[a("el-form-item",{attrs:{label:"执照编号：",prop:"linceseCode"}},[a("div",{staticClass:"zzbh_box"},[a("el-select",{attrs:{"value-key":"2020"},model:{value:e.stationForm.linceseCodeYear,callback:function(t){e.$set(e.stationForm,"linceseCodeYear",t)},expression:"stationForm.linceseCodeYear"}},e._l(e.stationYear,function(e,t){return a("el-option",{key:t,attrs:{label:e,value:e}})}),1),e._v(" "),a("el-select",{model:{value:e.stationForm.linceseCodeType2,callback:function(t){e.$set(e.stationForm,"linceseCodeType2",t)},expression:"stationForm.linceseCodeType2"}},e._l(e.stationType2,function(e,t){return a("el-option",{key:t,attrs:{label:e,value:e}})}),1),e._v(" "),a("el-select",{model:{value:e.stationForm.linceseCodeCity,callback:function(t){e.$set(e.stationForm,"linceseCodeCity",t)},expression:"stationForm.linceseCodeCity"}},e._l(e.stationCityCode,function(t,n){return a("el-option",{key:n,attrs:{label:t.value,value:t.value}},[e._v(e._s(t.value)+" - "+e._s(t.label))])}),1),e._v(" "),a("el-select",{model:{value:e.stationForm.linceseCodeType,callback:function(t){e.$set(e.stationForm,"linceseCodeType",t)},expression:"stationForm.linceseCodeType"}},e._l(e.stationType,function(t,n){return a("el-option",{key:n,attrs:{label:t.value,value:t.value}},[e._v(e._s(t.value)+" - "+e._s(t.label))])}),1),e._v(" "),a("el-form-item",{attrs:{"label-width":"0",prop:"linceseCodeNum02"}},[a("el-input",{staticClass:"input-with-select",attrs:{clearable:""},model:{value:e.stationForm.linceseCodeNum02,callback:function(t){e.$set(e.stationForm,"linceseCodeNum02",t)},expression:"stationForm.linceseCodeNum02"}})],1)],1)])],1),e._v(" "),a("el-col",{attrs:{span:20,lg:12,xl:8}},[a("el-form-item",{attrs:{label:"有效日期："}},[a("el-col",{attrs:{span:11}},[a("el-form-item",{attrs:{prop:"linceseStartDate"}},[a("el-date-picker",{attrs:{type:"date",placeholder:"起始时间"},model:{value:e.stationForm.linceseStartDate,callback:function(t){e.$set(e.stationForm,"linceseStartDate",t)},expression:"stationForm.linceseStartDate"}})],1)],1),e._v(" "),a("el-col",{staticClass:"center-line",attrs:{span:2}},[e._v("-")]),e._v(" "),a("el-col",{attrs:{span:11}},[a("el-form-item",{attrs:{prop:"linceseEndDate"}},[a("el-date-picker",{attrs:{type:"date",placeholder:"结束日期"},model:{value:e.stationForm.linceseEndDate,callback:function(t){e.$set(e.stationForm,"linceseEndDate",t)},expression:"stationForm.linceseEndDate"}})],1)],1)],1)],1),e._v(" "),a("el-col",{attrs:{span:20,lg:12,xl:8}},[a("el-form-item",{attrs:{label:"台站名称：",prop:"staName"}},[a("el-input",{attrs:{clearable:""},model:{value:e.stationForm.staName,callback:function(t){e.$set(e.stationForm,"staName",t)},expression:"stationForm.staName"}})],1)],1),e._v(" "),a("el-col",{attrs:{span:20,lg:12,xl:8}},[a("el-form-item",{attrs:{label:"无线电台识别码：",prop:"idenCode"}},[a("el-input",{attrs:{clearable:""},model:{value:e.stationForm.idenCode,callback:function(t){e.$set(e.stationForm,"idenCode",t)},expression:"stationForm.idenCode"}})],1)],1),e._v(" "),a("el-col",{attrs:{span:20,lg:12,xl:8}},[a("el-form-item",{attrs:{label:"使用人：",prop:"userName"}},[a("el-input",{attrs:{clearable:""},model:{value:e.stationForm.userName,callback:function(t){e.$set(e.stationForm,"userName",t)},expression:"stationForm.userName"}})],1)],1),e._v(" "),a("el-col",{attrs:{span:20,lg:12,xl:8}},[a("el-form-item",{attrs:{label:"统一社会信用代码：",prop:"orgCode"}},[a("el-input",{attrs:{clearable:""},model:{value:e.stationForm.orgCode,callback:function(t){e.$set(e.stationForm,"orgCode",t)},expression:"stationForm.orgCode"}})],1)],1),e._v(" "),a("el-col",{attrs:{span:20,lg:12,xl:8}},[a("el-form-item",{attrs:{label:"台址/使用区域：",prop:"location"}},[a("el-input",{attrs:{clearable:""},model:{value:e.stationForm.location,callback:function(t){e.$set(e.stationForm,"location",t)},expression:"stationForm.location"}})],1)],1),e._v(" "),a("el-col",{attrs:{span:20,lg:12,xl:8}},[a("el-form-item",{attrs:{label:"其他必要信息：",prop:"detail"}},[a("el-input",{attrs:{clearable:""},model:{value:e.stationForm.detail,callback:function(t){e.$set(e.stationForm,"detail",t)},expression:"stationForm.detail"}})],1)],1),e._v(" "),a("el-col",{attrs:{span:20,lg:12,xl:8}},[a("el-form-item",{staticClass:"long_and_lat",attrs:{label:"地理坐标（经度）："}},[a("el-input",{attrs:{placeholder:"请以度分秒的型式填写",clearable:""},model:{value:e.stationForm.longitude[0],callback:function(t){e.$set(e.stationForm.longitude,0,t)},expression:"stationForm.longitude[0]"}}),e._v(" "),a("span",[e._v("°")]),e._v(" "),a("el-input",{attrs:{placeholder:"请以度分秒的型式填写",clearable:""},model:{value:e.stationForm.longitude[1],callback:function(t){e.$set(e.stationForm.longitude,1,t)},expression:"stationForm.longitude[1]"}}),e._v(" "),a("span",[e._v("＇")]),e._v(" "),a("el-input",{attrs:{placeholder:"请以度分秒的型式填写",clearable:""},model:{value:e.stationForm.longitude[2],callback:function(t){e.$set(e.stationForm.longitude,2,t)},expression:"stationForm.longitude[2]"}}),e._v(" "),a("span",[e._v("＂")])],1)],1),e._v(" "),a("el-col",{attrs:{span:20,lg:12,xl:8}},[a("el-form-item",{staticClass:"long_and_lat",attrs:{label:"地理坐标（纬度）："}},[a("el-input",{attrs:{placeholder:"请以度分秒的型式填写",clearable:""},model:{value:e.stationForm.latitude[0],callback:function(t){e.$set(e.stationForm.latitude,0,t)},expression:"stationForm.latitude[0]"}}),e._v(" "),a("span",[e._v("°")]),e._v(" "),a("el-input",{attrs:{placeholder:"请以度分秒的型式填写",clearable:""},model:{value:e.stationForm.latitude[1],callback:function(t){e.$set(e.stationForm.latitude,1,t)},expression:"stationForm.latitude[1]"}}),e._v(" "),a("span",[e._v("＇")]),e._v(" "),a("el-input",{attrs:{placeholder:"请以度分秒的型式填写",clearable:""},model:{value:e.stationForm.latitude[2],callback:function(t){e.$set(e.stationForm.latitude,2,t)},expression:"stationForm.latitude[2]"}}),e._v(" "),a("span",[e._v("＂")])],1)],1),e._v(" "),a("el-col",{attrs:{span:20,lg:12,xl:8}},[a("el-form-item",{attrs:{label:"发证机关：",prop:"licenseAuth"}},[a("el-input",{attrs:{clearable:""},model:{value:e.stationForm.licenseAuth,callback:function(t){e.$set(e.stationForm,"licenseAuth",t)},expression:"stationForm.licenseAuth"}})],1)],1),e._v(" "),a("el-col",{attrs:{span:20,lg:12,xl:8}},[a("el-form-item",{attrs:{label:"颁发日期：",prop:"linceseStartDate"}},[a("el-date-picker",{attrs:{clearable:"",type:"date"},model:{value:e.stationForm.linceseStartDate,callback:function(t){e.$set(e.stationForm,"linceseStartDate",t)},expression:"stationForm.linceseStartDate"}})],1)],1),e._v(" "),a("el-col",{attrs:{span:20,lg:12,xl:8}},[a("el-form-item",{attrs:{label:"特别规定事项：",prop:"specialCase"}},[a("el-input",{attrs:{clearable:"",type:"textarea",autosize:{minRows:3}},model:{value:e.stationForm.specialCase,callback:function(t){e.$set(e.stationForm,"specialCase",t)},expression:"stationForm.specialCase"}})],1)],1)],1)],1)],1),e._v(" "),a("el-card",{staticClass:"table-box",staticStyle:{margin:"20px 0"}},[a("div",{staticClass:"operate-box"},[a("span",{staticClass:"operate-table-title"},[e._v("发射设备及天线")]),e._v(" "),a("el-button",{attrs:{type:"primary",plain:"",icon:"el-icon-plus"},on:{click:e.handleEquipmentAddItem}},[e._v("点击添加")])],1),e._v(" "),a("el-table",{staticClass:"table-content",attrs:{border:"",height:"400px",data:e.equipmentInfoData}},[a("el-table-column",{attrs:{fixed:"left",width:"200",prop:"equAuth",align:"center",label:"设备型号核准代码"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.isSave?a("span",[e._v(e._s(t.row.equAuth))]):a("el-input",{attrs:{clearable:""},model:{value:t.row.equAuth,callback:function(a){e.$set(t.row,"equAuth",a)},expression:"scope.row.equAuth"}})]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"equModel",align:"center",label:"设备型号"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.isSave?a("span",[e._v(e._s(t.row.equModel))]):a("el-input",{attrs:{clearable:""},model:{value:t.row.equModel,callback:function(a){e.$set(t.row,"equModel",a)},expression:"scope.row.equModel"}})]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"equMb",align:"center",label:"主备用标识"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.isSave?a("span",[e._v(e._s(t.row.equMb))]):a("el-input",{attrs:{clearable:""},model:{value:t.row.equMb,callback:function(a){e.$set(t.row,"equMb",a)},expression:"scope.row.equMb"}})]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"equCode",align:"center",label:"出厂号"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.isSave?a("span",[e._v(e._s(t.row.equCode))]):a("el-input",{attrs:{clearable:""},model:{value:t.row.equCode,callback:function(a){e.$set(t.row,"equCode",a)},expression:"scope.row.equCode"}})]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"equMenu",align:"center",label:"生产厂家"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.isSave?a("span",[e._v(e._s(t.row.equMenu))]):a("el-input",{attrs:{clearable:""},model:{value:t.row.equMenu,callback:function(a){e.$set(t.row,"equMenu",a)},expression:"scope.row.equMenu"}})]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"equType",align:"center",label:"工作方式"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.isSave?a("span",[e._v(e._s(t.row.equType))]):a("el-input",{attrs:{clearable:""},model:{value:t.row.equType,callback:function(a){e.$set(t.row,"equType",a)},expression:"scope.row.equType"}})]}}])}),e._v(" "),a("el-table-column",{attrs:{width:"300",prop:"equPow",align:"center",label:"发射功率"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.isSave?a("span",[e._v(e._s(t.row.equPow))]):a("el-input",{attrs:{clearable:""},model:{value:t.row.equPow,callback:function(a){e.$set(t.row,"equPow",a)},expression:"scope.row.equPow"}})]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"antGain",align:"center",label:"天线增益(dBi)"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.isSave?a("span",[e._v(e._s(t.row.antGain))]):a("el-input",{attrs:{clearable:""},model:{value:t.row.antGain,callback:function(a){e.$set(t.row,"antGain",a)},expression:"scope.row.antGain"}})]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"antEpole",align:"center",label:"极化方式"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.isSave?a("span",[e._v(e._s(t.row.antEpole))]):a("el-input",{attrs:{clearable:""},model:{value:t.row.antEpole,callback:function(a){e.$set(t.row,"antEpole",a)},expression:"scope.row.antEpole"}})]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"antHight",align:"center",label:"天线距地高度(m)"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.isSave?a("span",[e._v(e._s(t.row.antHight))]):a("el-input",{attrs:{clearable:""},model:{value:t.row.antHight,callback:function(a){e.$set(t.row,"antHight",a)},expression:"scope.row.antHight"}})]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"操作",width:"100",align:"center",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.isSave?a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.handleEquipmentEditItem(t.$index,t.row)}}},[e._v("编辑")]):e._e(),e._v(" "),t.row.isSave?a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.handleEquipmentRemoveItem(t.$index,t.row)}}},[e._v("删除")]):e._e(),e._v(" "),t.row.isSave?e._e():a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.handleEquipmentComfirmItem(t.$index,t.row)}}},[e._v("确定")]),e._v(" "),t.row.isSave?e._e():a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.handleEquipmentCancleItem(t.$index,t.row)}}},[e._v("取消")])]}}])})],1)],1),e._v(" "),a("el-card",{staticClass:"table-box"},[a("div",{staticClass:"operate-box"},[a("span",{staticClass:"operate-table-title"},[e._v("发射/接收参数")]),e._v(" "),a("el-button",{attrs:{type:"primary",plain:"",icon:"el-icon-plus"},on:{click:e.handleGeneralAddItem}},[e._v("点击添加")])],1),e._v(" "),a("el-table",{staticClass:"table-content",attrs:{border:"",height:"400px",data:e.generalInfoData}},[a("el-table-column",{attrs:{width:"200",prop:"equAuth",align:"center",fixed:"left",label:"设备型号核准代码"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(t.row.equAuth))]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"freqEf",align:"center",width:"300",label:"发射频率范围"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.isSave?a("span",[e._v(e._s(t.row.freqEf))]):a("el-input",{attrs:{clearable:"",placeholder:"起始频率-终止频率"},model:{value:t.row.freqEf,callback:function(a){e.$set(t.row,"freqEf",a)},expression:"scope.row.freqEf"}})]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"freqRf",align:"center",width:"300",label:"接收频率范围"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.isSave?a("span",[e._v(e._s(t.row.freqRf))]):a("el-input",{attrs:{clearable:"",placeholder:"起始频率-终止频率"},model:{value:t.row.freqRf,callback:function(a){e.$set(t.row,"freqRf",a)},expression:"scope.row.freqRf"}})]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"freqBand",align:"center",width:"300",label:"带宽"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.isSave?a("span",[e._v(e._s(t.row.freqBand))]):a("el-input",{attrs:{clearable:""},model:{value:t.row.freqBand,callback:function(a){e.$set(t.row,"freqBand",a)},expression:"scope.row.freqBand"}})]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"freqPass",align:"center",label:"对应频率使用许可证编号"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.isSave?a("span",[e._v(e._s(t.row.freqPass))]):a("el-input",{attrs:{clearable:""},model:{value:t.row.freqPass,callback:function(a){e.$set(t.row,"freqPass",a)},expression:"scope.row.freqPass"}})]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"ftFreqCsgn",align:"center",label:"扇区标识码"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.isSave?a("span",[e._v(e._s(t.row.ftFreqCsgn))]):a("el-input",{attrs:{clearable:""},model:{value:t.row.ftFreqCsgn,callback:function(a){e.$set(t.row,"ftFreqCsgn",a)},expression:"scope.row.ftFreqCsgn"}})]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"操作",width:"100",align:"center",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.isSave?a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.handleGeneralEditItem(t.$index,t.row)}}},[e._v("编辑")]):e._e(),e._v(" "),t.row.isSave?a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.handleGeneralRemoveItem(t.$index,t.row)}}},[e._v("删除")]):e._e(),e._v(" "),t.row.isSave?e._e():a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.handleGeneralComfirmItem(t.$index,t.row)}}},[e._v("确定")]),e._v(" "),t.row.isSave?e._e():a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.handleGeneralCancleItem(t.$index,t.row)}}},[e._v("取消")])]}}])})],1)],1),e._v(" "),a("div",{staticClass:"btn-group"},[a("el-button",{attrs:{type:"primary",icon:"el-icon-check",loading:e.commitLoadding},on:{click:e.handleCommitStation}},[e._v("提交修改")]),e._v(" "),a("el-button",{attrs:{type:"primary",icon:"el-icon-close",plain:""},on:{click:e.initData}},[e._v("放弃修改")]),e._v(" "),a("el-button",{attrs:{type:"primary",icon:"el-icon-arrow-left",plain:""},on:{click:function(t){return e.$router.push("/license-print")}}},[e._v("返回")])],1)],1)},staticRenderFns:[]};var f=a("VU/8")(d,m,!1,function(e){a("5ghg")},"data-v-0280e42a",null);t.default=f.exports}});