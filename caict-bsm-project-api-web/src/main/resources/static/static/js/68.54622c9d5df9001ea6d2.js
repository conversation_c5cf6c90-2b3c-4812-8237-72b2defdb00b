webpackJsonp([68],{Otjz:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r=a("Xxa5"),l=a.n(r),n=a("exGp"),o=a.n(n),i=a("oFuF"),s=a("b+pq"),c=new i.a,p={name:"dataError",data:function(){return{errorData:[],pageTotal:0,loading:!1,postData:{applytableCode:"",currentUserGuid:"",exceptionType:"",page:1,rows:10},multipleSelection:[],dialogVisible:!1,applytableCode:"",exceptionType:"",options:[{value:"1",label:"新设异常"},{value:"2",label:"变更异常"},{value:"3",label:"注销异常"},{value:"4",label:"申请表异常"}]}},mounted:function(){var t=this;this.$nextTick(function(){t.getErrorData()})},methods:{getErrorData:function(){var t=this;return o()(l.a.mark(function e(){var a,r;return l.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t.loading||(t.loading=!0),e.next=3,s.c(t.postData);case 3:(a=e.sent).status?(r=[a.res.records,a.res.total],t.errorData=r[0],t.pageTotal=r[1]):t.$message.error("异常数据加载失败！"),t.loading=!1;case 6:case"end":return e.stop()}},e,t)}))()},searchFun:function(){var t=[this.applytableCode,this.exceptionType,1];this.postData.applytableCode=t[0],this.postData.exceptionType=t[1],this.postData.page=t[2],this.getErrorData()},checkDataDetail:function(t){""!=c.getStorage("errorDataDetail")&&c.removeStorage("errorDataDetail"),c.setStorage("errorDataDetail",t),this.$router.push({path:"/sync/unusual/detail"})},changePage:function(t){this.postData.page=t,this.getErrorData()},select:function(){}}},u={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"content-template"},[a("div",{staticClass:"search-box"},[a("el-form",{staticClass:"search-form",attrs:{inline:!0,"label-position":"left"}},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"申请表编号：","label-width":"120px"}},[a("el-input",{attrs:{placeholder:"请输入申请表编号"},model:{value:t.applytableCode,callback:function(e){t.applytableCode=e},expression:"applytableCode"}})],1)],1),t._v(" "),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"错误类型："}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择"},model:{value:t.exceptionType,callback:function(e){t.exceptionType=e},expression:"exceptionType"}},t._l(t.options,function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})}),1)],1)],1),t._v(" "),a("el-col",{attrs:{span:5}},[a("el-form-item",[a("el-button",{staticClass:"default-button",attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.searchFun}},[t._v("搜索\n              ")])],1)],1)],1)],1)],1),t._v(" "),a("div",{staticClass:"table-box"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"table-content",attrs:{height:"100%",fit:"",stripe:"",data:t.errorData}},[a("el-table-column",{attrs:{prop:"applicationNo",align:"center",label:"申请表编号","show-overflow-tooltip":!0}}),t._v(" "),a("el-table-column",{attrs:{prop:"btsName",align:"center",label:"基站名称","show-overflow-tooltip":!0}}),t._v(" "),a("el-table-column",{attrs:{prop:"btsId",align:"center",label:"基站识别码","show-overflow-tooltip":!0}}),t._v(" "),a("el-table-column",{attrs:{prop:"cellName",align:"center",label:"扇区名称","show-overflow-tooltip":!0}}),t._v(" "),a("el-table-column",{attrs:{prop:"cellId",align:"center",label:"扇区识别码","show-overflow-tooltip":!0}}),t._v(" "),a("el-table-column",{attrs:{prop:"techType",align:"center",label:"技术体制","show-overflow-tooltip":!0}}),t._v(" "),a("el-table-column",{attrs:{prop:"location",align:"center",label:"台址","show-overflow-tooltip":!0}}),t._v(" "),a("el-table-column",{attrs:{label:"操作",align:"center",width:"75"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{staticClass:"default-button",attrs:{type:"success",size:"mini",icon:"el-icon-view",plain:"",circle:""},on:{click:function(a){return t.checkDataDetail(e.row)}}})]}}])})],1),t._v(" "),a("div",{staticClass:"table-pagin"},[a("el-pagination",{staticStyle:{float:"right"},attrs:{layout:"total, prev, pager, next, jumper","current-page":t.postData.page,total:t.pageTotal},on:{"current-change":t.changePage}})],1),t._v(" "),a("el-dialog",{attrs:{title:"",visible:t.dialogVisible,width:"30%"},on:{"update:visible":function(e){t.dialogVisible=e}}},[a("div",[t._v("合并到申请表")]),t._v(" "),a("el-select"),t._v(" "),a("div",{staticStyle:{color:"red"}},[a("div",[t._v("1.合并到现有申请表")]),t._v(" "),a("div",[t._v("2.新建申请表")])]),t._v(" "),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{staticClass:"default-button",on:{click:function(e){t.dialogVisible=!1}}},[t._v("取 消")]),t._v(" "),a("el-button",{staticClass:"default-button",on:{click:function(e){t.dialogVisible=!1}}},[t._v("确 定")])],1)],1)],1)])},staticRenderFns:[]},d=a("VU/8")(p,u,!1,null,null,null);e.default=d.exports},"b+pq":function(t,e,a){"use strict";a.d(e,"c",function(){return d}),a.d(e,"d",function(){return v}),a.d(e,"b",function(){return b}),a.d(e,"a",function(){return f});var r,l,n,o,i=a("Xxa5"),s=a.n(i),c=a("exGp"),p=a.n(c),u=new(a("oFuF").a),d=(r=p()(s.a.mark(function t(e){var a;return s.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return a={type:"post",url:"/api/asyncRawBtsException/list",data:e},t.abrupt("return",u.requestFun(a));case 2:case"end":return t.stop()}},t,this)})),function(t){return r.apply(this,arguments)}),v=(l=p()(s.a.mark(function t(e){var a;return s.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return a={type:"post",url:"/api/bsmLicenseExprecord/list",data:e},t.abrupt("return",u.requestFun(a));case 2:case"end":return t.stop()}},t,this)})),function(t){return l.apply(this,arguments)}),b=(n=p()(s.a.mark(function t(e){var a;return s.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return a={type:"get",url:"/api/bsmLicenseExprecord/export",data:e},t.abrupt("return",u.requestFun(a));case 2:case"end":return t.stop()}},t,this)})),function(t){return n.apply(this,arguments)}),f=(o=p()(s.a.mark(function t(e){var a;return s.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return a={type:"post",url:"/api/asyncRawBts/async",data:e},t.abrupt("return",u.requestFun(a));case 2:case"end":return t.stop()}},t,this)})),function(t){return o.apply(this,arguments)})}});