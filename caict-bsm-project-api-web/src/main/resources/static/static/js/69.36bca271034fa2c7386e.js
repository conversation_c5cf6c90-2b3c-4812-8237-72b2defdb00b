webpackJsonp([69],{b0sC:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=a("Xxa5"),o=a.n(n),r=a("exGp"),l=a.n(r),s=a("oFuF"),i=a("nvLp"),u=a("99Ht"),c=new s.a,p={data:function(){return{roleType:sessionStorage.getItem("roleType"),logData:[],pageTotal:0,loading:!0,logPageDTO:{jobCode:"",jobId:"",jobName:"",page:1,rows:10,type:1},logIndexPageDTO:{userType:"",page:1,rows:10},operate:[{value:"mobile",label:"移动"},{value:"telecom",label:"电信"},{value:"unicom",label:"联通"},{value:"guangdian",label:"广电"},{value:"",label:"全部"}]}},mounted:function(){var e=this;this.$nextTick(function(){e._getLogList()})},methods:{_getLogList:function(){var e=this;return l()(o.a.mark(function t(){var a,n,r;return o.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e.loading||(e.loading=!0),a="",n="",a="/apiWeb/transfer/transportJob/findAllPageByUser",t.next=5,i.a(a,e.logPageDTO);case 5:(n=t.sent).status&&n.res.success?(r=[n.res.data.list,n.res.data.total],e.logData=r[0],e.pageTotal=r[1]):e.$message.warning("加载失败！"),e.loading=!1;case 8:case"end":return t.stop()}},t,e)}))()},getLogList:function(){this._getLogList(),this.logIndexPageDTO.page=1},searchFun:function(){this.logPageDTO.page=1,this._getLogList()},downloadDetail:function(e,t){this.$router.push({path:"/report/all-detail",query:{guid:t.guid}})},downloadAddData:function(e,t){this.$ajax.post({url:"/api/apiWeb/transfer/transportRawBtsDeal/exportExcel",data:{jobGuid:t.guid}}).then(function(e){e.success&&c.funDownload("/"+e.data)})},getLogDetail:function(e,t){/wuwei/.test(this.roleType),this.$router.push({path:"/logs/detail",query:{guid:t.guid}})},downloadFile:function(e,t){var a=this;console.log(t),this.$ajax.get({url:"/api/apiWeb/transfer/logTransportJob/exportLog/"+t.fileGuid}).then(function(e){if(e.success){a.$message.success(e.message);var t=document.createElement("a");t.setAttribute("download",""),t.href=u.a.ajaxDomain+"/"+e.data,t.click()}})},changePage:function(e){/wuwei/.test(this.roleType)?this.logIndexPageDTO.page=e:this.logPageDTO.page=e,this._getLogList()}},filters:{processStatus:function(e){switch(e){case" 0":return"文件开始上传";case"1":return"文件上传成功未处理";case"2":return"文件处理成功";case"3":return"文件上传失败";case"4":return"文件校验中";case"5":return"文件校验成功";case"6":return"文件校验失败";default:return""}},excprocessStatusStyle:function(e){switch(e){case"0":case"1":return"text-primary";case"2":case"5":return"text-success";case"3":case"6":return"text-danger";default:return"text-info"}},csvStateFilter:function(e){switch(e){case 0:return"文件开始上传";case 1:return"文件上传成功未处理";case 2:return"文件处理成功";case 3:return"文件上传失败";case 4:return"文件校验中";case 5:default:return"文件处理失败"}},processStatusStyle:function(e){switch(e){case"3":return"text-danger";default:return"text-info"}}},components:{}},g={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"content-template"},[a("div",{staticClass:"search-box"},[a("el-form",{staticClass:"search-form",attrs:{inline:!0,"label-position":"left"}},[a("el-row",{attrs:{gutter:20}},[/wuwei/.test(e.roleType)?e._e():a("el-col",{attrs:{span:5,xs:10,sm:10,md:9,lg:8,xl:5}},[a("el-form-item",{attrs:{label:"事项名称："}},[a("el-input",{attrs:{clearable:""},model:{value:e.logPageDTO.jobName,callback:function(t){e.$set(e.logPageDTO,"jobName",t)},expression:"logPageDTO.jobName"}})],1)],1),e._v(" "),/wuwei/.test(e.roleType)?a("el-col",{attrs:{span:5,xs:10,sm:10,md:9,lg:8,xl:5}},[a("el-form-item",{attrs:{label:"运营商:"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择"},on:{change:e.getLogList},model:{value:e.logIndexPageDTO.userType,callback:function(t){e.$set(e.logIndexPageDTO,"userType",t)},expression:"logIndexPageDTO.userType"}},e._l(e.operate,function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}),1)],1)],1):e._e(),e._v(" "),a("el-col",{attrs:{span:4,xs:3,sm:3,md:4,lg:4,xl:4}},[a("el-form-item",[a("el-button",{staticClass:"default-button",attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.searchFun}},[e._v("搜索")])],1)],1)],1)],1)],1),e._v(" "),e._m(0),e._v(" "),a("div",{staticClass:"table-box"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"table-content",attrs:{height:"100%",fit:"",stripe:"",data:e.logData}},[/wuwei/.test(e.roleType)?e._e():a("el-table-column",{attrs:{prop:"jobName",align:"center",label:"事项名称","show-overflow-tooltip":!0}}),e._v(" "),a("el-table-column",{attrs:{prop:"isCompare",label:"审核状态","show-overflow-tooltip":!0,align:"left"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",{class:e._f("isCompareStyle")(t.row.isCompare)},[e._v("\n            "+e._s(e._f("isCompare")(t.row.isCompare))+"\n          ")])]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"approvalCount",label:"审核通过数量","show-overflow-tooltip":!0,align:"left"}}),e._v(" "),a("el-table-column",{attrs:{prop:"disapprovalCount",label:"未审核通过数量","show-overflow-tooltip":!0,align:"left"}}),e._v(" "),a("el-table-column",{attrs:{label:"操作",width:"260",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.downloadDetail(t.$index,t.row)}}},[e._v("下载")]),e._v(" "),a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.downloadAddData(t.$index,t.row)}}},[e._v("下载增量数据")]),e._v(" "),a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.getLogDetail(t.$index,t.row)}}},[e._v("查看错误数据")])]}}])})],1),e._v(" "),a("div",{staticClass:"table-pagin"},[/wuwei/.test(e.roleType)?e._e():a("el-pagination",{attrs:{layout:"total, prev, pager, next, jumper","current-page":e.logPageDTO.page,total:e.pageTotal},on:{"current-change":e.changePage}}),e._v(" "),/wuwei/.test(e.roleType)?a("el-pagination",{attrs:{layout:"total, prev, pager, next, jumper","current-page":e.logIndexPageDTO.page,total:e.pageTotal},on:{"current-change":e.changePage}}):e._e()],1)],1)])},staticRenderFns:[function(){var e=this.$createElement,t=this._self._c||e;return t("div",{staticClass:"operate-box"},[t("span",{staticClass:"operate-table-title"},[this._v("交互数据")])])}]},d=a("VU/8")(p,g,!1,null,null,null);t.default=d.exports},nvLp:function(e,t,a){"use strict";a.d(t,"a",function(){return u});var n,o=a("Xxa5"),r=a.n(o),l=a("exGp"),s=a.n(l),i=new(a("oFuF").a),u=(n=s()(r.a.mark(function e(t,a){var n;return r.a.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return n={type:"post",url:t,data:a},e.abrupt("return",i.requestFun(n));case 2:case"end":return e.stop()}},e,this)})),function(e,t){return n.apply(this,arguments)})}});