webpackJsonp([72],{ZKFu:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var l=a("Dd8w"),n=a.n(l),r=(a("NYxO"),new(a("oFuF").a)),o={data:function(){return{roleType:sessionStorage.getItem("roleType"),timer:null,tableData:[],isCompare:[{detail:"全部",code:""},{detail:"无委确认通过（待审核）",code:10},{detail:"无委审核中（提交审核处理中）",code:11},{detail:"审核处理数据失败",code:12},{detail:"审核处理数据成功",code:13}],operator:[{detail:"全部",code:""},{detail:"移动",code:"mobile"},{detail:"电信",code:"telecom"},{detail:"联通",code:"unicom"},{detail:"广电",code:"guangdian"}],pageTotal:0,loading:!0,isAdvancedSearch:!1,pageDTO:{page:1,rows:10,isCompare:"",userType:"",appDateStart:"",appDateEnd:"",jobName:""}}},mounted:function(){var e=this;this.$nextTick(function(){e.initTableData(1),e.timer=setInterval(function(){e.initTableData(2)},5e3)})},beforeDestroy:function(){this.clearTimer()},methods:{initTableData:function(e){var t=this;this.loading||1!=e||(this.loading=!0),this.$ajax.post({url:"/api/apiWeb/transfer/transportJob/findAllPage",data:this.pageDTO}).then(function(e){if(t.loading=!1,e.success)return t.tableData=e.data.list,void(t.pageTotal=e.data.total);e.success||t.$message.error(e.message)},function(){t.loading=!1,t.clearTimer()})},enterChildrenTask:function(e){this.$router.push({path:"/audit/records-children-task",query:{guid:e.guid,jobName:e.jobName}})},handleSelectChange:function(){this.initTableData()},handleSearch:function(){var e=r.toTimestamp(this.pageDTO.appDateStart),t=r.toTimestamp(this.pageDTO.appDateEnd);if(e>t&&""!=e&""!=t)return this.$message.warning("开始时间不能大于结束时间，请重新选择"),!1;this.initTableData()},handleChangePage:function(e){this.pageDTO.page=e,this.initTableData()},openAdvancedSearch:function(){this.isAdvancedSearch=!this.isAdvancedSearch,this.isAdvancedSearch||(this.pageDTO=n()({},this.pageDTO,{appDateStart:"",appDateEnd:"",isCompare:0}))},clearTimer:function(){clearInterval(this.timer),this.timer=null}}},s={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"content-template"},[a("div",{staticClass:"search-box"},[a("el-form",{staticClass:"search-form",attrs:{inline:!0,"label-position":"left"}},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{xs:16,sm:12,lg:8}},[a("el-form-item",{attrs:{label:"审核类型："}},[a("el-select",{attrs:{placeholder:"请选择"},on:{change:e.handleSelectChange},model:{value:e.pageDTO.isCompare,callback:function(t){e.$set(e.pageDTO,"isCompare",t)},expression:"pageDTO.isCompare"}},e._l(e.isCompare,function(e){return a("el-option",{key:e.code,attrs:{label:e.detail,value:e.code}})}),1)],1)],1),e._v(" "),a("el-col",{attrs:{xs:16,sm:12,lg:8}},[a("el-form-item",{attrs:{label:"运营商："}},[a("el-select",{attrs:{clearable:!0,placeholder:"请选择"},on:{change:e.handleSelectChange},model:{value:e.pageDTO.userType,callback:function(t){e.$set(e.pageDTO,"userType",t)},expression:"pageDTO.userType"}},e._l(e.operator,function(e){return a("el-option",{key:e.code,attrs:{label:e.detail,value:e.code}})}),1)],1)],1),e._v(" "),a("el-col",{directives:[{name:"show",rawName:"v-show",value:!e.isAdvancedSearch,expression:"!isAdvancedSearch"}],attrs:{xs:16,sm:12,lg:8}},[a("el-form-item",{directives:[{name:"show",rawName:"v-show",value:!e.isAdvancedSearch,expression:"!isAdvancedSearch"}]},[a("el-button",{staticClass:"default-button",attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.handleSearch}},[e._v("搜索")]),e._v(" "),a("el-button",{attrs:{type:"text"},on:{click:e.openAdvancedSearch}},[e._v("\n              展开高级搜索 \n              "),a("span",{staticClass:"el-icon-arrow-down form-item-icon"})])],1)],1)],1),e._v(" "),a("el-row",{directives:[{name:"show",rawName:"v-show",value:e.isAdvancedSearch,expression:"isAdvancedSearch"}],attrs:{gutter:20}},[a("el-col",{attrs:{xs:16,sm:12,lg:8}},[a("el-form-item",{attrs:{label:"开始时间："}},[a("el-date-picker",{attrs:{"value-format":"timestamp",type:"datetime",placeholder:"选择日期：","default-time":"00:00:01"},model:{value:e.pageDTO.appDateStart,callback:function(t){e.$set(e.pageDTO,"appDateStart",t)},expression:"pageDTO.appDateStart"}})],1)],1),e._v(" "),a("el-col",{attrs:{xs:16,sm:12,lg:8}},[a("el-form-item",{attrs:{label:"结束时间："}},[a("el-date-picker",{attrs:{"value-format":"timestamp",type:"datetime",placeholder:"选择日期","default-time":"23:59:59"},model:{value:e.pageDTO.appDateEnd,callback:function(t){e.$set(e.pageDTO,"appDateEnd",t)},expression:"pageDTO.appDateEnd"}})],1)],1),e._v(" "),a("el-col",{attrs:{xs:16,sm:12,lg:8}},[a("el-form-item",[a("el-button",{staticClass:"default-button",attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.handleSearch}},[e._v("搜索")]),e._v(" "),a("el-button",{attrs:{type:"text"},on:{click:e.openAdvancedSearch}},[e._v("\n              隐藏高级搜索 \n              "),a("span",{staticClass:"el-icon-arrow-up form-item-icon"})])],1)],1)],1)],1)],1),e._v(" "),e._m(0),e._v(" "),a("div",{staticClass:"table-box"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"table-content",attrs:{height:"100%",data:e.tableData,stripe:""}},[a("el-table-column",{attrs:{prop:"jobName",label:"主任务名",align:"center"}}),e._v(" "),a("el-table-column",{attrs:{prop:"gmtModified",label:"审核时间",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n          "+e._s(e._f("format")(t.row.gmtModified))+"\n        ")]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"gmtCreate",label:"创建时间",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n          "+e._s(e._f("format")(t.row.gmtCreate))+"\n        ")]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"userType",label:"运营商",align:"center","show-overflow-tooltip":!0},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n          "+e._s(e._f("operatorName")(t.row.userType))+"\n        ")]}}])}),e._v(" "),/wuwei/.test(e.roleType)?a("el-table-column",{attrs:{prop:"jobStatus",label:"审核状态",align:"center","show-overflow-tooltip":!0},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v("\n          "+e._s(e._f("jobStatusFilter")(t.row.jobStatus))+"\n        ")]}}],null,!1,2380988868)}):a("el-table-column",{attrs:{prop:"isCompare",label:"审核状态",align:"center","show-overflow-tooltip":!0},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",{class:e._f("isCompareStyle")(t.row.isCompare)},[e._v("\n            "+e._s(e._f("isCompare")(t.row.isCompare))+"\n          ")])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"操作",width:"100",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text",disabled:1===t.row.approveState},on:{click:function(a){return e.enterChildrenTask(t.row)}}},[e._v("查看子任务")])]}}])})],1),e._v(" "),a("div",{staticClass:"table-pagin"},[a("el-pagination",{attrs:{layout:"total, prev, pager, next, jumper","current-page":e.pageDTO.page,total:e.pageTotal},on:{"current-change":e.handleChangePage}})],1)],1)])},staticRenderFns:[function(){var e=this.$createElement,t=this._self._c||e;return t("div",{staticClass:"operate-box"},[t("span",{staticClass:"operate-table-title"},[this._v("主任务列表")])])}]},i=a("VU/8")(o,s,!1,null,null,null);t.default=i.exports}});