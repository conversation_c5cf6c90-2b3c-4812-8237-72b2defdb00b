webpackJsonp([72],{EDX1:function(t,a,e){"use strict";Object.defineProperty(a,"__esModule",{value:!0});var n=e("Dd8w"),o=e.n(n),l=new(e("oFuF").a),r={data:function(){return{tableData:[],dataType:[{detail:"全部",code:""},{detail:"新增",code:"1"},{detail:"变更",code:"2"},{detail:"注销",code:"3"}],currentDetail:{},pageTotal:0,loading:!0,pageDTO:{page:1,rows:20,guid:this.$route.query.guid},abandonItem:null,abandonTableData:[],abandonPageTotal:0,abandonPageDTO:{page:1,rows:20},dialogAbandonShow:!1}},mounted:function(){var t=this;this.$nextTick(function(){t.initTableData()})},methods:{initTableData:function(){var t=this;this.loading||(this.loading=!0),this.$ajax.post({url:"/api/apiWeb/transferin/approvalTransportJobIn/findDetailByPage",data:this.pageDTO}).then(function(a){if(t.loading=!1,a.success)return t.tableData=a.data.list,void(t.pageTotal=a.data.total);a.success||t.$message.error(a.message)},function(){return t.loading=!1})},getAbandonTableData:function(){var t=this;this.dialogAbandonShow=!0,this.$ajax.post({url:"/api/apiWeb/transferin/approvalTransportJobIn/findGiveUpDataList",data:o()({guid:this.abandonItem.appGuid},this.abandonPageDTO)}).then(function(a){t.abandonTableData=a.data.list,t.abandonPageTotal=a.data.total})},currentRowView:function(t){7==t.isCompare?(this.abandonItem=t,this.getAbandonTableData()):this.$router.push({path:"/data-sync-management",query:{appCode:t.appCode}})},getBaseStateNum:function(t){t.guid?this.$router.push({name:"station-manage",params:{stationId:t.stationGuid},query:{appGuid:t.appGuid,from:"compare-operate"}}):this.$message.error("无法获得基站id")},downloadData:function(){if(!(this.abandonTableData.length<=0)){var t=this.abandonTableData[0].appGuid;this.$ajax.get({url:"/apiWeb/transferin/approvalTransportJobIn/downloadGiveUpData/"+t}).then(function(t){if(t.success){var a=t.data;l.funDownload(a)}})}},handleSelectChange:function(){this.initTableData()},handleSearch:function(){this.initTableData()},handleCurrentChange:function(t){this.pageDTO.page=t,this.initTableData()},handleAbandonCurrentChange:function(t){this.abandonPageDTO.page=t,this.getAbandonTableData()},handleSizeChange:function(t){this.pageDTO.rows=t,this.initTableData()},handleAbandonSizeChange:function(t){this.abandonPageDTO.rows=t,this.getAbandonTableData()}},filters:{dataTypeToChinese:function(t){switch(t=parseInt(t)){case 1:return"新增";case 2:return"变更";case 3:return"注销";default:return"全部"}},formatUserGuid:function(t){switch(t){case"users15878825327624703":return"移动";case"users15878825327624706":return"联通";case"users15878825327624705":return"电信";default:return"全部"}},isCompareListDetailFilter:function(t){switch(t=parseInt(t)){case 1:return"生成申请表中";case 2:return"生成申请表成功(待推送)";case 3:return"生成申请表失败";case 4:return"申请表推送中";case 5:return"申请表推送成功";case 6:return"申请表推送失败";case 7:return"放弃";default:return"异常"}}}},s={render:function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{staticClass:"content-template"},[e("div",{staticClass:"operate-box"},[e("el-breadcrumb",{attrs:{"separator-class":"el-icon-arrow-right"}},[e("el-breadcrumb-item",{attrs:{to:{path:"/work/offices"}}},[t._v("办件查询")]),t._v(" "),e("el-breadcrumb-item",[t._v("数据列表")])],1)],1),t._v(" "),e("div",{staticClass:"table-box"},[e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"table-content",attrs:{height:"100%",data:t.tableData,stripe:""}},[e("el-table-column",{attrs:{prop:"gmtCreate",label:"创建时间",align:"center","show-overflow-tooltip":!0},scopedSlots:t._u([{key:"default",fn:function(a){return[t._v("\n          "+t._s(t._f("format")(a.row.gmtCreate))+"\n        ")]}}])}),t._v(" "),e("el-table-column",{attrs:{prop:"isApproved",label:"是否通过",align:"center","show-overflow-tooltip":!0},scopedSlots:t._u([{key:"default",fn:function(a){return[t._v("\n          "+t._s(1==a.row.isApproved?"是":"否")+"\n        ")]}}])}),t._v(" "),e("el-table-column",{attrs:{prop:"appCode",label:"申请表编号",align:"center","show-overflow-tooltip":!0}}),t._v(" "),e("el-table-column",{attrs:{prop:"genNum",label:"代数",align:"center","show-overflow-tooltip":!0},scopedSlots:t._u([{key:"default",fn:function(a){return[e("span",[t._v(t._s(a.row.genNum)+"G")])]}}])}),t._v(" "),e("el-table-column",{attrs:{prop:"isCompare",label:"类别",align:"center","show-overflow-tooltip":!0},scopedSlots:t._u([{key:"default",fn:function(a){return[e("span",[t._v(t._s(t._f("dataTypeToChinese")(a.row.dataType)))])]}}])}),t._v(" "),e("el-table-column",{attrs:{prop:"isCompare",label:"状态",align:"center","show-overflow-tooltip":!0},scopedSlots:t._u([{key:"default",fn:function(a){return[e("span",{class:a.row.isCompare},[t._v("\n            "+t._s(t._f("isCompareListDetailFilter")(a.row.isCompare))+"\n          ")])]}}])}),t._v(" "),e("el-table-column",{attrs:{label:"操作",width:"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{attrs:{type:"text"},on:{click:function(e){return t.currentRowView(a.row)}}},[t._v("查看")])]}}])})],1),t._v(" "),e("div",{staticClass:"table-pagin"},[e("el-pagination",{attrs:{layout:"sizes, total, prev, pager, next, jumper","page-sizes":[20,50,100,200],"current-page":t.pageDTO.page,total:t.pageTotal},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)],1),t._v(" "),e("el-dialog",{attrs:{title:"查看放弃详情",visible:t.dialogAbandonShow,width:"85%"},on:{"update:visible":function(a){t.dialogAbandonShow=a}}},[e("el-button",{attrs:{type:"primary"},on:{click:t.downloadData}},[t._v("下载全部")]),t._v(" "),e("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"table-content",attrs:{height:"500px",data:t.abandonTableData}},[e("el-table-column",{attrs:{prop:"appCode",label:"申请表编号",align:"center","show-overflow-tooltip":!0}}),t._v(" "),e("el-table-column",{attrs:{prop:"statName",label:"台站名称",align:"center","show-overflow-tooltip":!0}}),t._v(" "),e("el-table-column",{attrs:{prop:"statAddr",label:"台站地址",align:"center","show-overflow-tooltip":!0}}),t._v(" "),e("el-table-column",{attrs:{prop:"analysisStatus",label:"干扰分析",align:"center","show-overflow-tooltip":!0},scopedSlots:t._u([{key:"default",fn:function(a){return[t._v(t._s(1==a.row.analysisStatus?"未干扰":"受干扰"))]}}])}),t._v(" "),e("el-table-column",{attrs:{prop:"fastStatus",label:"是否在大射电望远镜受扰范围",align:"center","show-overflow-tooltip":!0},scopedSlots:t._u([{key:"default",fn:function(a){return[t._v(t._s(1==a.row.fastStatus?"否":"是"))]}}])}),t._v(" "),e("el-table-column",{attrs:{prop:"statLg",label:"经度",align:"center","show-overflow-tooltip":!0}}),t._v(" "),e("el-table-column",{attrs:{prop:"statLa",label:"纬度",align:"center","show-overflow-tooltip":!0}}),t._v(" "),e("el-table-column",{attrs:{prop:"statAt",label:"海拔高度",align:"center","show-overflow-tooltip":!0}}),t._v(" "),e("el-table-column",{attrs:{prop:"statDateStart",label:"启用日期",align:"center","show-overflow-tooltip":!0},scopedSlots:t._u([{key:"default",fn:function(a){return[t._v(t._s(t._f("format")(a.row.statDateStart)))]}}])}),t._v(" "),e("el-table-column",{attrs:{label:"操作",width:"120",align:"center"},scopedSlots:t._u([{key:"default",fn:function(a){return[e("el-button",{attrs:{type:"text"},on:{click:function(e){return t.getBaseStateNum(a.row)}}},[t._v("详情")])]}}])})],1),t._v(" "),e("div",{staticClass:"table-pagin"},[e("el-pagination",{attrs:{layout:"sizes, total, prev, pager, next, jumper","page-sizes":[20,50,100,200],"current-page":t.abandonPageDTO.page,total:t.abandonPageTotal},on:{"size-change":t.handleAbandonSizeChange,"current-change":t.handleAbandonCurrentChange}})],1)],1)],1)},staticRenderFns:[]},i=e("VU/8")(r,s,!1,null,null,null);a.default=i.exports}});