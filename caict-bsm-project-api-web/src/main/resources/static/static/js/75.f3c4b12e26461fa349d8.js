webpackJsonp([75],{aI18:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var l=a("Xxa5"),o=a.n(l),n=a("exGp"),i=a.n(n),s=a("mvHQ"),r=a.n(s),c=a("Dd8w"),u=a.n(c),d=a("oFuF"),p=a("2F/+"),g=new d.a,b={data:function(){return{dataTypeList:[{value:"",label:"全部"},{value:1,label:"新增"},{value:2,label:"变更"},{value:3,label:"注销"}],periodValue:[],selectionList:[],fileStaticData:[],stationData:[],stationDetail:"",stationOptions:[],jobDateBegin:"",jobDateEnd:"",popShow:!1,pageTotal:0,loading:!0,disabledBtn1:!1,disabledBtn2:!1,disabledBtn3:!1,jobStationPageDTO:{guid:this.$route.query.guid,cellName:null,dataType:"",cellId:null,page:1,rows:50,genNum:""}}},mounted:function(){this.initPeriodHandler(),this.getStationData()},methods:{getStationData:function(){var t=this;if(this.loading||(this.loading=!0),null!=this.periodValue){this.jobStationPageDTO.startDate=this.periodValue[0],this.jobStationPageDTO.endDate=this.periodValue[1];var e=this.jobStationPageDTO;this.$ajax.post({url:"/api/apiWeb/transfer/transportRawBtsDeal/findAllPage",data:e}).then(function(e){e&&(e.success?e.success&&(t.stationData=e.data.list,t.pageTotal=e.data.total,t.loading=!1):t.$message.error(e.message))},function(e){return t.loading=!1})}else this.$message.warning("请填写时间，并点击搜索！")},initPeriodHandler:function(){var t=(new Date).getTime(),e=(new Date).getTime();e-=63072e7,t+=63072e7,this.periodValue=[e,t]},downloadAllHandler:function(){var t=this;this.$message.closeAll(),0!==this.pageTotal?this.$confirm("您将下载"+this.pageTotal+"个文件, 点击确定后即可下载并且不可取消，是否继续?","友情提示：",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){t.disabledBtn1=!0;var e=t.$notify.warning({title:"温馨提示：",message:"由于本次过程比较漫长请耐心等待，您也可以浏览其他。正在打包中，请稍后......",duration:0});t.$ajax.post({url:"/api/apiWeb/transfer/transportRawBtsDeal/exportExcel",isDownload:!0,data:u()({},t.jobStationPageDTO,{guids:[]})}).then(function(a){if(t.disabledBtn1=!1,e.close(),a)if(a.success){var l=a.data;g.funDownload(l),t.$message.success("下载完成，请注意查收！")}else t.$message.success(a.message)},function(a){e.close(),t.disabledBtn1=!1})}):this.$message.warning("无可下载数据！")},downloadCheckHandler:function(){var t=this;this.$message.closeAll(),0!==this.selectionList.length?this.$confirm("您将下载"+this.selectionList.length+"个文件,点击确定后即可下载并且不可取消，是否继续?","友情提示：",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){t.disabledBtn2=!0;var e=t.$notify.warning({title:"温馨提示：",message:"由于本次过程比较漫长请您耐心等待，您也可以浏览其他。正在打包中，请稍后......",duration:0}),a=JSON.parse(r()(t.jobStationPageDTO));a.guids=t.selectionList.map(function(t){return t.guid}),t.$ajax.post({url:"/api/apiWeb/transfer/transportRawBtsDeal/exportExcel",isDownload:!0,data:a}).then(function(a){if(t.disabledBtn2=!1,e.close(),a)if(1==a.success){var l=a.data;g.funDownload(l),t.$message.success("下载完成，请注意查收！")}else t.$message.success(a.message)},function(a){e.close(),t.disabledBtn2=!1})}):this.$message.warning("请勾选您要下载的文件")},selectRows:function(t){this.selectionList=t},selectOptionChange:function(){this.getStationData()},searchFun:function(){this.jobStationPageDTO.page=1,this.getStationData()},getStationDetail:function(t,e){var a=this;return i()(o.a.mark(function t(){return o.a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:a.stationDetail=e,a.popShow=!0;case 2:case"end":return t.stop()}},t,a)}))()},handleCurrentChange:function(t){this.jobStationPageDTO.page=t,this.getStationData()},handleSizeChange:function(t){this.jobStationPageDTO.page=1,this.jobStationPageDTO.rows=t,this.getStationData()}},components:{StationDetail:p.a}},h={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"content-template"},[a("div",{staticClass:"search-box"},[a("el-form",{staticClass:"search-form",attrs:{inline:!0,"label-position":"left"}},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"扇区名称："}},[a("el-input",{attrs:{clearable:""},model:{value:t.jobStationPageDTO.cellName,callback:function(e){t.$set(t.jobStationPageDTO,"cellName",e)},expression:"jobStationPageDTO.cellName"}})],1)],1),t._v(" "),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"扇区码："}},[a("el-input",{attrs:{clearable:""},model:{value:t.jobStationPageDTO.cellId,callback:function(e){t.$set(t.jobStationPageDTO,"cellId",e)},expression:"jobStationPageDTO.cellId"}})],1)],1)],1),t._v(" "),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{xs:24,sm:13,lg:10,xl:8}},[a("el-form-item",{attrs:{label:"代数："}},[a("el-radio-group",{on:{change:t.selectOptionChange},model:{value:t.jobStationPageDTO.genNum,callback:function(e){t.$set(t.jobStationPageDTO,"genNum",e)},expression:"jobStationPageDTO.genNum"}},[a("el-radio-button",{attrs:{label:""}},[t._v("全部")]),t._v(" "),a("el-radio-button",{attrs:{label:"2"}},[t._v("2G")]),t._v(" "),a("el-radio-button",{attrs:{label:"3"}},[t._v("3G")]),t._v(" "),a("el-radio-button",{attrs:{label:"4"}},[t._v("4G")]),t._v(" "),a("el-radio-button",{attrs:{label:"5"}},[t._v("5G")]),t._v(" "),a("el-radio-button",{attrs:{label:"GSM-R"}},[t._v("GSM-R")])],1)],1)],1),t._v(" "),a("el-col",{attrs:{xs:24,sm:10,lg:9,xl:7}},[a("el-form-item",{attrs:{label:"申请类型："}},[a("el-radio-group",{on:{change:t.selectOptionChange},model:{value:t.jobStationPageDTO.dataType,callback:function(e){t.$set(t.jobStationPageDTO,"dataType",e)},expression:"jobStationPageDTO.dataType"}},t._l(t.dataTypeList,function(e){return a("el-radio-button",{key:e.value,attrs:{label:e.value}},[t._v(t._s(e.label))])}),1)],1)],1),t._v(" "),a("el-col",{attrs:{xs:24,sm:10,lg:2,xl:6}},[a("el-button",{staticClass:"default-button",attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.searchFun}},[t._v("搜索")])],1)],1)],1)],1),t._v(" "),a("div",{staticClass:"operate-box"},[a("span",{staticClass:"operate-table-title"},[t._v("数据列表")]),t._v(" "),a("el-button",{staticClass:"default-button",attrs:{type:"primary",icon:"el-icon-download",disabled:t.disabledBtn1},on:{click:t.downloadAllHandler}},[t._v("条件下载")]),t._v(" "),a("el-button",{staticClass:"default-button",attrs:{type:"primary",icon:"el-icon-check",disabled:t.disabledBtn2},on:{click:t.downloadCheckHandler}},[t._v("下载勾选")])],1),t._v(" "),a("div",{staticClass:"table-box"},[a("el-table",{staticClass:"table-content",attrs:{height:"100%",stripe:"",data:t.stationData},on:{"selection-change":t.selectRows}},[a("el-table-column",{attrs:{type:"selection",align:"center"}}),t._v(" "),a("el-table-column",{attrs:{prop:"cellName",label:"扇区名称",align:"center","show-overflow-tooltip":!0}}),t._v(" "),a("el-table-column",{attrs:{prop:"cellId",label:"扇区识别码",align:"center","show-overflow-tooltip":!0}}),t._v(" "),a("el-table-column",{attrs:{prop:"genNum",label:"代数",align:"center","show-overflow-tooltip":!0},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(e.row.genNum)+"G")]}}])}),t._v(" "),a("el-table-column",{attrs:{prop:"techType",label:"技术体制",align:"center","show-overflow-tooltip":!0}}),t._v(" "),a("el-table-column",{attrs:{prop:"location",label:"台址",align:"center","show-overflow-tooltip":!0}}),t._v(" "),a("el-table-column",{attrs:{prop:"longitude",label:"经度",align:"center","show-overflow-tooltip":!0}}),t._v(" "),a("el-table-column",{attrs:{prop:"latitude",label:"纬度",align:"center","show-overflow-tooltip":!0}}),t._v(" "),a("el-table-column",{attrs:{prop:"uploadDate",label:"上传时间",align:"center","show-overflow-tooltip":!0},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(t._f("format")(e.row.uploadDate)))]}}])}),t._v(" "),a("el-table-column",{attrs:{prop:"dataType",label:"申请类型",align:"center","show-overflow-tooltip":!0},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(t._f("convertDataType")(e.row.dataType)))]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"操作",width:"100",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return t.getStationDetail(e.$index,e.row)}}},[t._v("查看")])]}}])})],1),t._v(" "),a("div",{staticClass:"table-pagin"},[a("el-pagination",{attrs:{layout:"sizes, total, prev, pager, next, jumper","page-sizes":[50,100,200,500],"current-page":t.jobStationPageDTO.page,total:t.pageTotal},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)],1),t._v(" "),a("el-dialog",{attrs:{title:"扇区数据查看信息",visible:t.popShow,"custom-class":"history-dialog",width:"998px"},on:{"update:visible":function(e){t.popShow=e}}},[a("StationDetail",{attrs:{detail:t.stationDetail}})],1)],1)},staticRenderFns:[]},f=a("VU/8")(b,h,!1,null,null,null);e.default=f.exports}});