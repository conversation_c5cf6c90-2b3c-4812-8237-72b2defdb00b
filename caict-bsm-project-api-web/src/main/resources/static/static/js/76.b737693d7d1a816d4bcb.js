webpackJsonp([76],{FlNo:function(t,e,l){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var o=new(l("oFuF").a),a={data:function(){return{roleType:sessionStorage.getItem("roleType"),logData:[],loading:!1,pageTotal:0,logPageDTO:{page:1,rows:10}}},watch:{logData:function(t){t.map(function(t){if(t.logDetail){var e=t.logDetail.search("（"),l=t.logDetail.search("）");t.logDetail.slice(e+1,l)+" "}})}},mounted:function(){this.getTableData()},methods:{getTableData:function(){var t=this,e=this.$route.query.guid;this.$ajax.post({url:"/apiWeb/transfer/transportRawBtsDealLog/findAllDetailPageByJob/"+e,data:this.logPageDTO}).then(function(e){if(e.success)return t.logData=e.data.list,void(t.pageTotal=e.data.total);t.$message.warning("加载失败！"),t.loading=!1})},changePage:function(t){this.logPageDTO.page=t,this.getTableData()},returnFreq:function(t,e){return o.returnFreq2(t,e)},downloadErrorLog:function(){var t=this,e=this.$route.query.guid;this.$confirm("此操作将消耗大量时间，需要耐心等待, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){var l=t.$loading({lock:!0,text:"正在打包中...",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"});t.$ajax.get({url:"/api/apiWeb/transfer/transportRawBtsDealLog/exportDetailPageByJob/"+e}).then(function(t){l.close(),t.success&&o.funDownload("/export/"+t.data)})}).catch(function(){t.$message({type:"info",message:"已取消"})})}},filters:{dataTypeFilter:function(t){switch(t=parseInt(t)){case 1:return"新增";case 2:return"变更";case 3:return"注销";default:return"异常"}},isValidFilter:function(t){switch(t=parseInt(t)){case 1:return"审核通过";case 2:return"审核未通过";default:return"异常"}}}},r={render:function(){var t=this,e=t.$createElement,l=t._self._c||e;return l("div",{staticClass:"content-template"},[l("div",{staticClass:"top-nav"},[l("el-breadcrumb",{attrs:{"separator-class":"el-icon-arrow-right"}},[l("el-breadcrumb-item",{attrs:{to:"/logs"}},[t._v("异常信息")]),t._v(" "),l("el-breadcrumb-item",[t._v("异常信息详情")])],1)],1),t._v(" "),l("div",{staticClass:"operate-box"},[l("span",{staticClass:"operate-table-title"},[t._v("异常信息详情")]),t._v(" "),l("el-button",{attrs:{type:"primary"},on:{click:t.downloadErrorLog}},[t._v("下载错误日志")])],1),t._v(" "),l("div",{staticClass:"table-box"},[l("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"table-content",attrs:{height:"100%",fit:"",stripe:"",data:t.logData}},[l("el-table-column",{attrs:{prop:"logDetail",align:"center",label:"异常原因","min-width":"500","show-overflow-tooltip":!0}}),t._v(" "),l("el-table-column",{attrs:{prop:"dataType",label:"数据变更类型",align:"center",width:"200px","show-overflow-tooltip":!0},scopedSlots:t._u([{key:"default",fn:function(e){return[l("span",[t._v(t._s(t._f("dataTypeFilter")(e.row.dataType)))])]}}])}),t._v(" "),l("el-table-column",{attrs:{prop:"isValid",label:"状态",align:"center",width:"200px","show-overflow-tooltip":!0},scopedSlots:t._u([{key:"default",fn:function(e){return[l("span",[t._v(t._s(t._f("isValidFilter")(e.row.isValid)))])]}}])}),t._v(" "),l("el-table-column",{attrs:{prop:"btsName",label:"基站名称",align:"center",width:"200px","show-overflow-tooltip":!0}}),t._v(" "),l("el-table-column",{attrs:{prop:"btsId",label:"基站识别码",align:"center",width:"120px","show-overflow-tooltip":!0}}),t._v(" "),l("el-table-column",{attrs:{prop:"cellName",label:"扇区名称",align:"center",width:"150px","show-overflow-tooltip":!0}}),t._v(" "),l("el-table-column",{attrs:{prop:"cellId",label:"扇区识别码",align:"center",width:"150px","show-overflow-tooltip":!0}}),t._v(" "),l("el-table-column",{attrs:{prop:"techType",label:"技术体制",align:"center",width:"100px","show-overflow-tooltip":!0}}),t._v(" "),l("el-table-column",{attrs:{prop:"location",label:"台址",align:"center",width:"150px","show-overflow-tooltip":!0}}),t._v(" "),l("el-table-column",{attrs:{prop:"longitude",label:"经度",align:"center",width:"100px","show-overflow-tooltip":!0}}),t._v(" "),l("el-table-column",{attrs:{prop:"latitude",label:"纬度",align:"center",width:"100px","show-overflow-tooltip":!0}}),t._v(" "),l("el-table-column",{attrs:{prop:"county",label:"所属地区",align:"center",width:"100px","show-overflow-tooltip":!0}}),t._v(" "),l("el-table-column",{attrs:{prop:"deviceModel",label:"设备型号",align:"center",width:"100px","show-overflow-tooltip":!0}}),t._v(" "),l("el-table-column",{attrs:{prop:"vendorName",label:"设备生产厂家",align:"center",width:"120px","show-overflow-tooltip":!0}}),t._v(" "),l("el-table-column",{attrs:{prop:"modelCode",label:"型号核准代码",align:"center",width:"120px","show-overflow-tooltip":!0}}),t._v(" "),l("el-table-column",{attrs:{prop:"sendStartFreq",align:"center",width:"160px","show-overflow-tooltip":!0,label:"发射频率/频段(MHz)"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(t.returnFreq(e.row,"EF")))]}}])}),t._v(" "),l("el-table-column",{attrs:{prop:"accStartFreq",align:"center",width:"160px","show-overflow-tooltip":!0,label:"接收频率/频段(MHz)"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(t._s(t.returnFreq(e.row,"RF")))]}}])}),t._v(" "),l("el-table-column",{attrs:{prop:"height",label:"天线距地高度（M）",align:"center",width:"150px","show-overflow-tooltip":!0}}),t._v(" "),l("el-table-column",{attrs:{prop:"antennaFactory",label:"天线生产厂家",align:"center",width:"120px","show-overflow-tooltip":!0}}),t._v(" "),l("el-table-column",{attrs:{prop:"antennaModel",label:"天线类型",align:"center",width:"100px","show-overflow-tooltip":!0}}),t._v(" "),l("el-table-column",{attrs:{prop:"antennaAzimuth",label:"天线方位角(°)",align:"center",width:"100px","show-overflow-tooltip":!0}}),t._v(" "),l("el-table-column",{attrs:{prop:"antennaGain",label:"天线增益(dBi)",align:"center",width:"100px","show-overflow-tooltip":!0}}),t._v(" "),l("el-table-column",{attrs:{prop:"maxEmissivePower",label:"最大发射功率(W)",align:"center",width:"120px","show-overflow-tooltip":!0}}),t._v(" "),l("el-table-column",{attrs:{prop:"polarizationMode",label:"极化方式",align:"center",width:"100px","show-overflow-tooltip":!0}}),t._v(" "),l("el-table-column",{attrs:{prop:"feederLoss",label:"馈线系统总损耗(dB)",align:"center",width:"120px","show-overflow-tooltip":!0}}),t._v(" "),l("el-table-column",{attrs:{prop:"altitude",label:"海拔高度(m)",align:"center",width:"100px","show-overflow-tooltip":!0}})],1),t._v(" "),l("div",{staticClass:"table-pagin"},[l("el-pagination",{attrs:{layout:"total, prev, pager, next, jumper","current-page":t.logPageDTO.page,total:t.pageTotal},on:{"current-change":t.changePage}})],1)],1)])},staticRenderFns:[]},n=l("VU/8")(a,r,!1,null,null,null);e.default=n.exports}});