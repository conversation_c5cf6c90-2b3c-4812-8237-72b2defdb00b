webpackJsonp([77],{XeaP:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});new(a("oFuF").a);var r={data:function(){return{form:{username:"",password:"",domain:"",port:""}}},mounted:function(){},methods:{initTableData:function(e){var t=this;this.loading||1!=e||(this.loading=!0),this.$ajax.post({url:"/api/apiWeb/transfer/approvalTransportJob/findAllPageAppTransportJob/"+sessionStorage.getItem("roleType"),data:this.pageDTO}).then(function(e){if(t.loading=!1,e.success)return t.tableData=e.data.list,void(t.pageTotal=e.data.total);e.success||t.$message.error(e.message)},function(){t.loading=!1,t.clearTimer()})}}},o={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"content-template"},[a("div",{staticClass:"search-box"},[a("el-form",{staticClass:"search-form",attrs:{inline:!0,"label-position":"left"}},[a("el-form-item",{attrs:{label:"账号："}},[a("el-input",{attrs:{clearable:""},model:{value:e.form.username,callback:function(t){e.$set(e.form,"username",t)},expression:"form.username"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"密码："}},[a("el-input",{attrs:{clearable:""},model:{value:e.form.password,callback:function(t){e.$set(e.form,"password",t)},expression:"form.password"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"地址："}},[a("el-input",{attrs:{clearable:""},model:{value:e.form.domain,callback:function(t){e.$set(e.form,"domain",t)},expression:"form.domain"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"端口："}},[a("el-input",{attrs:{clearable:""},model:{value:e.form.port,callback:function(t){e.$set(e.form,"port",t)},expression:"form.port"}})],1),e._v(" "),a("el-form-item",[a("el-button",{staticClass:"default-button",attrs:{type:"primary",icon:"el-icon-search"}},[e._v("连接")])],1)],1)],1)])},staticRenderFns:[]},s=a("VU/8")(r,o,!1,null,null,null);t.default=s.exports}});