webpackJsonp([78],{"702p":function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=new(a("oFuF").a),r={data:function(){return{timer:null,tableData:[],outerVisible:!1,compareOptions:[{detail:"进行中",code:"1"},{detail:"已完成",code:"2"}],jobDateBegin:"",jobDateEnd:"",loading:!0,curjobId:"",pageTotal:0,jobPageDTO:{jobDateEnd:"",jobDateStart:"",jobId:"",appGuid:"",userType:"telecom",genNum:"2",page:1,rows:10,isCompare:""}}},mounted:function(){var e=this;this.getData(),this.timer=setInterval(function(){e.getData(1)},5e3)},beforeDestroy:function(){this.clearTimer()},methods:{getData:function(e){var t=this;e||this.loading||(this.loading=!0),this.$route.query.jobId?(this.jobPageDTO.jobId=this.$route.query.jobId,this.jobPageDTO.appGuid=this.$route.query.guid):(this.jobPageDTO.jobId="",this.jobPageDTO.appGuid=""),this.$ajax.post({url:"/api/apiWeb/transfer/approvalTransportJob/findPageCompareJobVOListByPage",data:this.jobPageDTO}).then(function(e){if(t.loading=!1,e.success){var a=[e.data,e.total];return t.tableData=a[0],void(t.pageTotal=a[1])}e.status||t.$message.error(e.reason)},function(){return t.loading=!1})},selectChange:function(){this.getData()},searchFun:function(){var e=o.toTimestamp(this.jobDateBegin),t=o.toTimestamp(this.jobDateEnd);if(t>0&&(t+=86399999),e>t&&""!=e&""!=t)return this.$message.warning("开始时间不能大于结束时间，请重新选择"),!1;var a=[e,t,1];this.jobPageDTO.jobDateStart=a[0],this.jobPageDTO.jobDateEnd=a[1],this.jobPageDTO.page=a[2],this.getData()},curCompareDetail:function(e){this.curjobId=e.jobId,this.$router.push("/compare/records/"+e.jobGuid+"/"+e.guid)},curCompareApprove:function(e){this.curjobId=e.jobId,this.$router.push("/compare/records/audit/"+e.jobGuid+"/"+e.guid)},changePage:function(e){this.jobPageDTO.page=e,this.getData()},clearTimer:function(){clearInterval(this.timer),this.timer=null}},filters:{userTypeFilter:function(e){return"mobile"==e?"移动":"telecom"==e?"电信":"unicom"==e?"联通":"guangdian"==e?"广电":void 0}},watch:{$route:function(e){e.query.cjid||this.getData()},"jobPageDTO.userType":function(){this.searchFun()},"jobPageDTO.genNum":function(){this.searchFun()}}},l={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"content-template"},[a("div",{staticClass:"search-box"},[a("el-form",{staticClass:"search-form",attrs:{inline:!0,"label-position":"left"}},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"比对状态："}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择"},on:{change:e.selectChange},model:{value:e.jobPageDTO.isCompare,callback:function(t){e.$set(e.jobPageDTO,"isCompare",t)},expression:"jobPageDTO.isCompare"}},e._l(e.compareOptions,function(e){return a("el-option",{key:e.code,attrs:{label:e.detail,value:e.code}})}),1)],1)],1),e._v(" "),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"开始时间："}},[a("el-date-picker",{attrs:{type:"date",placeholder:"选择开始时间","default-time":"12:00:00"},model:{value:e.jobDateBegin,callback:function(t){e.jobDateBegin=t},expression:"jobDateBegin"}})],1)],1),e._v(" "),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"结束时间："}},[a("el-date-picker",{attrs:{type:"date",placeholder:"选择结束时间","default-time":"12:00:00"},model:{value:e.jobDateEnd,callback:function(t){e.jobDateEnd=t},expression:"jobDateEnd"}})],1)],1)],1),e._v(" "),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"运营商"}},[a("el-radio-group",{model:{value:e.jobPageDTO.userType,callback:function(t){e.$set(e.jobPageDTO,"userType",t)},expression:"jobPageDTO.userType"}},[a("el-radio-button",{attrs:{label:"telecom"}},[e._v("电信")]),e._v(" "),a("el-radio-button",{attrs:{label:"mobile"}},[e._v("移动")]),e._v(" "),a("el-radio-button",{attrs:{label:"unicom"}},[e._v("联通")]),e._v(" "),a("el-radio-button",{attrs:{label:"guangdian"}},[e._v("广电")])],1)],1)],1),e._v(" "),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"代数"}},[a("el-radio-group",{model:{value:e.jobPageDTO.genNum,callback:function(t){e.$set(e.jobPageDTO,"genNum",t)},expression:"jobPageDTO.genNum"}},[a("el-radio-button",{attrs:{label:"2"}},[e._v("2G")]),e._v(" "),a("el-radio-button",{attrs:{label:"3"}},[e._v("3G")]),e._v(" "),a("el-radio-button",{attrs:{label:"4"}},[e._v("4G")]),e._v(" "),a("el-radio-button",{attrs:{label:"5"}},[e._v("5G")]),e._v(" "),a("el-radio-button",{attrs:{label:"GSM-R"}},[e._v("GSM-R")])],1)],1)],1),e._v(" "),a("el-col",{attrs:{span:8}},[a("el-form-item",[a("el-button",{staticClass:"default-button",attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.searchFun}},[e._v("搜索")])],1)],1)],1)],1)],1),e._v(" "),e._m(0),e._v(" "),a("div",{staticClass:"table-box"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"table-content",attrs:{height:"100%",data:e.tableData,stripe:""}},[a("el-table-column",{attrs:{prop:"gmtCreate",label:"上报时间","show-overflow-tooltip":!0,align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(e._f("format")(t.row.gmtCreate)))]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"isCompare",label:"审核状态","show-overflow-tooltip":!0,align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",{class:e._f("isCompareStyle")(t.row.isCompare)},[e._v(e._s(e._f("isCompareBigTask")(t.row.isCompare)))])]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"idenCount",label:"比对通过扇区数","show-overflow-tooltip":!0,align:"center"}}),e._v(" "),a("el-table-column",{attrs:{prop:"diffCount",label:"比对未通过扇区数","show-overflow-tooltip":!0,align:"center"}}),e._v(" "),a("el-table-column",{attrs:{prop:"userType",label:"运营商","show-overflow-tooltip":!0,align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(e._s(e._f("userTypeFilter")(t.row.userType)))]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"操作",width:"100",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[9==t.row.isCompare?a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.curCompareApprove(t.row)}}},[e._v("审核")]):a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.curCompareDetail(t.row)}}},[e._v("查看")])]}}])})],1),e._v(" "),a("div",{staticClass:"table-pagin"},[a("el-pagination",{attrs:{layout:"total, prev, pager, next, jumper","current-page":e.jobPageDTO.page,total:e.pageTotal},on:{"current-change":e.changePage}})],1)],1),e._v(" "),a("div",{staticClass:"report-dialog"},[a("el-dialog",{attrs:{title:"上报详情",visible:e.outerVisible,width:"70%"},on:{"update:visible":function(t){e.outerVisible=t}}},[a("compare-detail",{attrs:{"job-id":e.curjobId}}),e._v(" "),a("div",{staticStyle:{"text-align":"center"}},[a("el-button",{staticClass:"default-button",attrs:{type:"primary"},on:{click:function(t){e.outerVisible=!1}}},[e._v("返回列表")])],1)],1)],1)])},staticRenderFns:[function(){var e=this.$createElement,t=this._self._c||e;return t("div",{staticClass:"operate-box"},[t("span",{staticClass:"operate-table-title"},[this._v("比对记录列表")])])}]},i=a("VU/8")(r,l,!1,null,null,null);t.default=i.exports}});