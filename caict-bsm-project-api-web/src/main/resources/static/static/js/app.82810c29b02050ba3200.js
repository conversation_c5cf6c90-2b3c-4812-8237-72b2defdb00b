webpackJsonp([81],{"4L2F":function(e,t){},"99Ht":function(e,t,r){"use strict";t.a={ajaxDomain:"http://127.0.0.1:8085"}},JCKA:function(e,t){var r=function(e,t){var r=3.141592653589793,a=6378245,n=.006693421622965943,s=52.35987755982988,i=function(e,t){var r=o(e,t);return g(r.mgLat,r.mgLon)},g=function(e,t){var r=t,a=e,n=Math.sqrt(r*r+a*a)+2e-5*Math.sin(a*s),i=Math.atan2(a,r)+3e-6*Math.cos(r*s),g=n*Math.cos(i)+.0065;return{latitude:n*Math.sin(i)+.006,longitude:g}},o=function(e,t){var s=l(t-105,e-35),i=u(t-105,e-35),g=e/180*r,o=Math.sin(g);o=1-n*o*o;var m=Math.sqrt(o);return{mgLat:e+(s=180*s/(a*(1-n)/(o*m)*r)),mgLon:t+(i=180*i/(a/m*Math.cos(g)*r))}},l=function(e,t){var a=2*e-100+3*t+.2*t*t+.1*e*t+.2*Math.sqrt(Math.abs(e));return a+=2*(20*Math.sin(6*e*r)+20*Math.sin(2*e*r))/3,a+=2*(20*Math.sin(t*r)+40*Math.sin(t/3*r))/3,a+=2*(160*Math.sin(t/12*r)+320*Math.sin(t*r/30))/3},u=function(e,t){var a=300+e+2*t+.1*e*e+.1*e*t+.1*Math.sqrt(Math.abs(e));return a+=2*(20*Math.sin(6*e*r)+20*Math.sin(2*e*r))/3,a+=2*(20*Math.sin(e*r)+40*Math.sin(e/3*r))/3,a+=2*(150*Math.sin(e/12*r)+300*Math.sin(e/30*r))/3};return function(e,t){return i(e,t)}(e,t)},a=function(e,t){var r,a,n,s,i,g,o=3.141592653589793,l=52.35987755982988,u=function(e,t){var r,a,n,s=6378245,i=.006693421622965943,g=(n=2*(r=t-105)-100+3*(a=e-35)+.2*a*a+.1*r*a+.2*Math.sqrt(Math.abs(r)),n+=2*(20*Math.sin(6*r*o)+20*Math.sin(2*r*o))/3,n+=2*(20*Math.sin(a*o)+40*Math.sin(a/3*o))/3,n+=2*(160*Math.sin(a/12*o)+320*Math.sin(a*o/30))/3),l=function(e,t){var r=300+e+2*t+.1*e*e+.1*e*t+.1*Math.sqrt(Math.abs(e));return r+=2*(20*Math.sin(6*e*o)+20*Math.sin(2*e*o))/3,r+=2*(20*Math.sin(e*o)+40*Math.sin(e/3*o))/3,r+=2*(150*Math.sin(e/12*o)+300*Math.sin(e/30*o))/3}(t-105,e-35),u=e/180*o,m=Math.sin(u);m=1-i*m*m;var c=Math.sqrt(m);return{latitude:g=180*g/(s*(1-i)/(m*c)*o),longitude:l=180*l/(s/c*Math.cos(u)*o)}},m=function(e,t){if(r=e,(a=t)<72.004||a>137.8347||r<.8293||r>55.8271)return{latitude:e,longitude:t};var r,a,n=u(e,t);return{latitude:e+n.latitude,longitude:t+n.longitude}},c=function(e,t){for(var r,a,n=.01,s=.01,i=e-n,g=t-s,o=e+n,l=t+s,u=0;;){var c=m(r=(i+o)/2,a=(g+l)/2);if(n=c.latitude-e,s=c.longitude-t,Math.abs(n)<1e-9&&Math.abs(s)<1e-9)break;if(n>0?o=r:i=r,s>0?l=a:g=a,++u>1e4)break}return{latitude:r,longitude:a}};return r=t-.0065,a=e-.006,n=Math.sqrt(r*r+a*a)-2e-5*Math.sin(a*l),s=Math.atan2(a,r)-3e-6*Math.cos(r*l),i=n*Math.cos(s),g=n*Math.sin(s),c(g,i)},n=function(e,t){var r=52.35987755982988,a=t-.0065,n=e-.006,s=Math.sqrt(a*a+n*n)-2e-5*Math.sin(n*r),i=Math.atan2(n,a)-3e-6*Math.cos(a*r),g=s*Math.cos(i);return{latitude:s*Math.sin(i),longitude:g}},s=function(e,t){var r=52.35987755982988,a=Math.sqrt(t*t+e*e)+2e-5*Math.sin(e*r),n=Math.atan2(e,t)+3e-6*Math.cos(t*r);return{longitude:a*Math.cos(n)+.0065,latitude:a*Math.sin(n)+.006}};console.log(23,function(e,t,r,a){var n=function(e){var t=Math.sin(e/2);return t*t},s=function(e){return e*Math.PI/180};e=s(e),t=s(t),r=s(r),a=s(a);var i=Math.abs(t-a),g=n(Math.abs(e-r))+Math.cos(e)*Math.cos(r)*n(i),o=12756.49*Math.asin(Math.sqrt(g));return o}(30.674304,104.119421,30.662407,104.069727)),console.log(55,a(30.674304,104.119421)),e.exports={GpsToBaidu:r,BaiduToGps:a,BaiduToGCJ:n,GCJToBaidu:s,GCJToGps:function(e,t){var r=s(e,t);return a(r.latitude,r.longitude)},GpsToGCJ:function(e,t){var a=r(e,t);return n(a.latitude,a.longitude)}}},NHnr:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});r("j1ja");var a,n=r("7+uW"),s=r("zL8q"),i=r.n(s),g=(r("tvR6"),r("dla8"),r("4L2F"),{render:function(){var e=this.$createElement,t=this._self._c||e;return t("div",{staticClass:"body",attrs:{id:"app"}},[t("router-view")],1)},staticRenderFns:[]}),o=r("VU/8")({name:"App"},g,!1,null,null,null).exports,l=r("YaEn"),u=r("Z1y9"),m=r("bOdI"),c=r.n(m),d=function(e,t,r){if(""!=t)if(/^(-?\d+)(\.\d+)?/gi.test(t)){var a=e.option;if(console.log(t.toString().length),t.toString().length>a.length)r(new Error(e.message));else{var n=t.toString().split(".")[1]?t.toString().split(".")[1].toString().length:0;console.log(n),n>a.decimal?r(new Error(e.message)):r()}}else r(new Error(e.message));else r()},p=(a={regionDetails:[{required:!0,message:"必须选择地区",trigger:"blur"},{validator:function(e,t,r){0!=t.length?r():r(new Error(e.message))},message:"必须选择地区",trigger:"blur"}],orgGUID:[{max:36,message:"申请单位 GUID不能超过36位",trigger:"blur"}],feeGUID:[{max:36,message:"缴费单位 GUID不能超过36位",trigger:"blur"}],netSVN:[{max:10,message:"通信业务/系统类型不能超过10位",trigger:"blur"}],netSP:[{max:8,message:"业务性质不能超过8位",trigger:"blur"}],netTS:[{max:8,message:"技术体制不能超过8位",trigger:"blur"}],netArea:[{max:8,message:"使用范围不能超过8位",trigger:"blur"}],netUse:[{max:80,message:"网络用途不能超过80位",trigger:"blur"}],netSATName:[{max:80,message:"卫星/星座名称不能超过80位",trigger:"blur"}],netLg:[{max:10,message:"标称轨道经度不能超过10位",trigger:"blur"}],netStartDate:[{type:"date",message:"请输入启用日期",trigger:"blur"}],netConfirmDate:[{type:"date",message:"请输入批准日期",trigger:"blur"}],netExpiredDate:[{type:"date",message:"请输入报废日期",trigger:"blur"}],appCode:[{required:!0,message:"无线电台（站）申请表编号不能为空",trigger:"blur"},{max:14,message:"无线电台（站）申请表编号不能超过14位",trigger:"blur"}],appFileNo:[{max:40,message:"频率使用许可证号或批准文号不能超过40位",trigger:"blur"}],appAttachment:[{max:120,message:"文件/批文名称不能超过120位",trigger:"blur"}],appFileUrl:[{max:120,message:"附件URL不能超过120位",trigger:"blur"}],appFreqLc:[{validator:d,option:{length:14,decimal:7},message:"请输入14位小数，且小数点后不大于7位",trigger:"blur"}],appFreqUc:[{validator:d,option:{length:14,decimal:7},message:"请输入14位小数，且小数点后不大于7位",trigger:"blur"}],appFreqLfb:[{validator:d,option:{length:14,decimal:7},message:"请输入14位小数，且小数点后不大于7位",trigger:"blur"}],appFreqLfe:[{validator:d,option:{length:14,decimal:7},message:"请输入14位小数，且小数点后不大于7位",trigger:"blur"}],appFreqLb:[{validator:d,option:{length:14,decimal:7},message:"请输入14位小数，且小数点后不大于7位",trigger:"blur"}],appFreqUfb:[{validator:d,option:{length:14,decimal:7},message:"请输入14位小数，且小数点后不大于7位",trigger:"blur"}],appFreqUfe:[{validator:d,option:{length:14,decimal:7},message:"请输入14位小数，且小数点后不大于7位",trigger:"blur"}],appFreqUb:[{validator:d,option:{length:14,decimal:7},message:"请输入14位小数，且小数点后不大于7位",trigger:"blur"}],statAppType:[{required:!0,message:"技术资料申报表类型不能为空",trigger:"blur"},{max:8,message:"技术资料申报表类型不能超过8位",trigger:"blur"}],appFreqCode:[{max:36,message:"国家频率数据库对应码不能超过36位",trigger:"blur"}],appFreqType:[{max:1,message:"频率类型（信道或频段）不能超过1位",trigger:"blur"}],appObjectType:[{required:!0,message:"申请对象类型不能为空",trigger:"blur"},{max:8,message:"申请对象类型不能超过8位",trigger:"blur"}],appPaName:[{required:!0,message:"姓名不能为空",trigger:"blur"},{max:40,message:"姓名不能超过40位",trigger:"blur"}],appPaGender:[{required:!0,message:"性别不能为空",trigger:"blur"},{max:1,message:"性别不能超过1位",trigger:"blur"}],appPaBirthday:[{required:!0,message:"出生日期不能为空",trigger:"blur"}],appSubType:[{required:!0,message:"申请类型不能为空",trigger:"blur"},{max:8,message:"申请类型不能超过8位",trigger:"blur"}],appPaNation:[{required:!0,message:"民族不能为空",trigger:"blur"},{max:40,message:"民族不能超过40位",trigger:"blur"}],appPaDegree:[{required:!0,message:"文化程度不能为空",trigger:"blur"},{max:20,message:"文化程度不能超过20位",trigger:"blur"}],appPaIdCard:[{required:!0,message:"身份证号不能为空",trigger:"blur"},{max:20,message:"身份证号不能超过20位",trigger:"blur"}],appPaServPost:[{max:6,message:"单位邮政编码不能超过6位",trigger:"blur"}],appPaLiveAddr:[{max:80,message:"常住地址不能超过80位",trigger:"blur"}],appPaLivePost:[{max:6,message:"常用地址邮政编码不能超过6位",trigger:"blur"}],appPaContactAddr:[{max:80,message:"联系地址不能超过80位",trigger:"blur"}],appPaContactPost:[{max:6,message:"联系地址邮政编码不能超过6位",trigger:"blur"}],appPaServOrg:[{max:80,message:"服务单位不能超过80位",trigger:"blur"}],appPaEmail:[{max:80,message:"电子邮件不能超过80位",trigger:"blur"}],appPaOrgPhone:[{max:40,message:"单位电话不能超过40位",trigger:"blur"}],appPaHomePhone:[{max:40,message:"住宅电话不能超过40位",trigger:"blur"}],appPaMobile:[{max:40,message:"移动电话不能超过40位",trigger:"blur"}],appPaStatType:[{max:8,message:"台站类别不能超过40位",trigger:"blur"}],appPaEntityType:[{max:1,message:"集体/个人不能超过1位",trigger:"blur"}],appPaOpeDegree:[{max:20,message:"操作证等级不能超过20位",trigger:"blur"}],appPaOpeCode:[{max:20,message:"操作证书编号不能超过20位",trigger:"blur"}],appPaCallSign:[{max:20,message:"呼号不能超过20位",trigger:"blur"}],appClubsName:[{max:80,message:"参加社团或俱乐部名称不能超过80位",trigger:"blur"}],appClubsMemberCode:[{max:20,message:"会员编号不能超过20位",trigger:"blur"}],appClubsSupOrg:[{max:80,message:"主管单位名称不能超过80位",trigger:"blur"}],appPaStatAddr:[{max:80,message:"台站地址不能超过80位",trigger:"blur"}],appPaOtherInfo:[{max:512,message:"其他说明事项不能超过512位",trigger:"blur"}],appFtlb:[{required:!0,message:"申请频率使用期限 （开始日期）不能为空",trigger:"blur"}],appFtle:[{required:!0,message:"申请频率使用期限 （截止日期）不能为空",trigger:"blur"}],appType:[{required:!0,message:"申请表类型不能为空",trigger:"blur"},{max:8,message:"申请表类型不能超过8位",trigger:"blur"}],orgName:[{required:!0,message:"不能为空",trigger:"change"},{max:80,message:"不能超过80位",trigger:"blur"}],orgAreaCode:[{required:!0,message:"地区代码不能为空",trigger:"change"},{max:8,message:"地区代码不能超过8位",trigger:"change"}],techType:[{required:!0,message:"请选择类型",trigger:"change"}],orgCode:[{required:!0,message:"组织机构代码不能为空",trigger:"blur"},{max:9,message:"组织机构代码不能超过9位",trigger:"blur"}],orgSysCode:[{max:8,message:"系统代码不能超过8位",trigger:"blur"}],orgType:[{required:!0,message:"请选择单位类型",trigger:"change"},{max:3,message:"单位类型不能超过3位",trigger:"change"}],orgAddr:[{max:80,message:"通信地址不能超过80位",trigger:"blur"}],orgPost:[{max:6,message:"邮政编码不能超过6位",trigger:"blur"}],orgLinkPerson:[{max:40,message:"联系人不能超过40位",trigger:"blur"}],orgPersonId:[{max:40,message:"联系人身份证号码不能超过40位",trigger:"blur"}],orgSupCode:[{max:9,message:"上级组织机构代码不能超过9位",trigger:"blur"}],orgPhone:[{max:40,message:"联系电话不能超过40位",trigger:"blur"}],orgMobPhone:[{max:40,message:"手机号码不能超过40位",trigger:"blur"},{type:"number",message:"必须是数字",trigger:"blur"}],orgFax:[{max:40,message:"组织机构传真不能超过40位",trigger:"blur"}],orgBank:[{max:80,message:"开户银行不能超过80位",trigger:"blur"}],orgAccountName:[{max:80,message:"账户名称不能超过80位",trigger:"blur"}],orgAccount:[{max:40,message:"银行账号不能超过40位",trigger:"blur"}],orgHostility:[{pattern:/^\d?$/,message:"设台单位性质不能超过1位",trigger:"blur"}],orgWebSite:[{max:80,message:"网址不能超过80位",trigger:"blur"}],orgMail:[{max:80,message:"电子邮箱不能超过80位",trigger:"blur"}],netName:[{max:40,message:"无线电系统/网络名称不能超过40位",trigger:"blur"}],netBand:[{pattern:/^\d+(\.\d+)?$/,message:"必须是数字",trigger:"blur"},{validator:d,option:{length:14,decimal:7},message:"请输入14位小数，且小数点后不大于7位",trigger:"blur"}],statTdi:[{required:!0,message:"技术资料申报表编号不能为空",trigger:"blur"},{max:4,message:"技术资料申报表编号不能超过4位",trigger:"blur"}],statType:[{max:8,message:"台站类别不能超过8位",trigger:"change"}],statName:[{max:80,message:"无线电台站名称不能超过80位",trigger:"blur"}],workTime:[{required:!0,message:"工作时间不能为空",trigger:"blur"}],statAddr:[{max:80,message:"无线电台站地址不能超过80位",trigger:"blur"}],statAreaCode:[{max:8,message:"无线电台站所在地地区编码不能超过8位",trigger:"blur"}],statWork:[{max:8,message:"工作方式不能超过8位",trigger:"change"}],statStatus:[{max:8,message:"台站状态不能超过8位",trigger:"blur"}],statEquSum:[{max:9999999,type:"number",message:"（台站总）设备数量不能超过7位",trigger:"blur"}],statLg:[{pattern:/^-?((0|1?[0-7]?[0-9]?)(([.][0-9]{1,7})?)|180(([.][0]{1,7})?))$/,message:"请输入正确的地理坐标-东经,精确到小数点后7位"}],statLa:[{pattern:/^-?((0|[1-8]?[0-9]?)(([.][0-9]{1,7})?)|90(([.][0]{1,7})?))$/,message:"请输入正确的地理坐标-北纬,精确到小数点后7位"}],statAt:[{validator:d,option:{length:6,decimal:2},message:"请输入6位小数，且小数点后不大于2位",trigger:"blur"}],etEquSum:[{pattern:/^\d{0,7}$/,message:"设备数量不能超过7位",trigger:"blur"}],etEquEuse:[{max:20,message:"电池有效期不能超过20位",trigger:"blur"}],etEquFntval:[{validator:d,option:{length:10,decimal:3},message:"请输入10位小数，且小数点后不大于3位",trigger:"blur"}],etEquPup:[{validator:d,option:{length:10,decimal:3},message:"请输入10位小数，且小数点后不大于3位",trigger:"blur"}],etEquPdn:[{validator:d,option:{length:10,decimal:3},message:"请输入10位小数，且小数点后不大于3位",trigger:"blur"}],etEquChrip:[{validator:d,option:{length:10,decimal:3},message:"请输入10位小数，且小数点后不大于3位",trigger:"blur"}],etEquPwid1:[{validator:d,option:{length:10,decimal:3},message:"请输入10位小数，且小数点后不大于3位",trigger:"blur"}],etEquPwid2:[{validator:d,option:{length:10,decimal:3},message:"请输入10位小数，且小数点后不大于3位",trigger:"blur"}],etEquPr:[{validator:d,option:{length:10,decimal:3},message:"请输入10位小数，且小数点后不大于3位",trigger:"blur"}],etEquPf:[{validator:d,option:{length:10,decimal:3},message:"请输入10位小数，且小数点后不大于3位",trigger:"blur"}],etEquRf:[{validator:d,option:{length:10,decimal:3},message:"请输入10位小数，且小数点后不大于3位",trigger:"blur"}],etEquRfBand:[{validator:d,option:{length:10,decimal:3},message:"请输入10位小数，且小数点后不大于3位",trigger:"blur"}],etEquSen:[{validator:d,option:{length:10,decimal:3},message:"请输入10位小数，且小数点后不大于3位",trigger:"blur"}],etEquSenU:[{max:8,message:"接收机灵敏度单位不能超过8位",trigger:"blur"}],etEquSenerr:[{max:10,message:"接收机灵敏度对应的误码率指标不能超过10位",trigger:"blur"}],etEquRwid:[{validator:d,option:{length:14,decimal:7},message:"请输入14位小数，且小数点后不大于7位",trigger:"blur"}],etEquMt:[{max:8,message:"调制方式不能超过8位",trigger:"blur"}],etEquCcode:[{max:20,message:"扇区号不能超过20位",trigger:"blur"}],etEquUpu:[{max:8,message:"上行发射功率/信道的单位不能超过8位",trigger:"blur"}],etEquDnu:[{max:8,message:"下行发射功率/信道的单位不能超过8位",trigger:"blur"}],comObjectObjName:[{required:!0,message:"不能为空",trigger:"blur"}],comObjectObjTdi:[{required:!0,message:"不能为空",trigger:"blur"}],memo:[{max:512,message:"备注不能超过512位",trigger:"blur"}],equModel:[{max:40,message:"设备型号不能超过40位",trigger:"blur"}],equAuth:[{max:40,message:"型号核准代码不能超过40位",trigger:"blur"}],equMenu:[{max:80,message:"设备生产厂家不能超过80位",trigger:"blur"}],equCode:[{max:40,message:"设备出厂号不能超过40位",trigger:"blur"}],equPf:[{max:8,message:"功率标识不能超过8位",trigger:"blur"}],equPow:[{validator:d,option:{length:14,decimal:7},message:"请输入14位小数，且小数点后不大于7位",trigger:"blur"}],equMb:[{max:1,message:"主/备用标识不能超过1位",trigger:"blur"}],equType:[{max:8,message:"设备工作方式不能超过8位",trigger:"blur"}],stLmmtr:[{max:1,message:"机车制式电台不能超过1位",trigger:"blur"}],stUserArea:[{max:80,message:"使用区域不能超过80位",trigger:"blur"}],stTfCode:[{max:40,message:"站代号不能超过40位",trigger:"blur"}],stTfTransCaPu:[{max:8,message:"传输容量单位不能超过8位",trigger:"blur"}],stTfTransCaP:[{validator:d,option:{length:14,decimal:7},message:"请输入14位小数，且小数点后不大于7位",trigger:"blur"}],stENetCode:[{max:20,message:"网络编号不能超过20位",trigger:"blur"}],antWorkType:[{max:8,message:"工作方式不能超过8位",trigger:"blur"}],antPole:[{max:8,message:"极化方式不能超过8位",trigger:"blur"}],antRpole:[{max:8,message:"收极化方式不能超过8位",trigger:"blur"}],antEpole:[{max:8,message:"发极化方式不能超过8位",trigger:"blur"}],antModel:[{max:40,message:"天线型号不能超过8位",trigger:"blur"}],antMenu:[{max:80,message:"天线生产厂家不能超过80位",trigger:"blur"}],antGain:[{validator:d,option:{length:6,decimal:3},message:"请输入6位小数，且小数点后不大于3位",trigger:"blur"}],antEgain:[{validator:d,option:{length:6,decimal:3},message:"请输入6位小数，且小数点后不大于3位",trigger:"blur"}],antAngle:[{validator:d,option:{length:7,decimal:4},message:"请输入7位小数，且小数点后不大于4位",trigger:"blur"}],antSize:[{max:20,message:"天线尺寸不能超过20位",trigger:"blur"}],feedMenu:[{max:80,message:"馈线生产厂家不能超过80位",trigger:"blur"}],feedModel:[{max:40,message:"馈线型号不能超过40位",trigger:"blur"}],feedLength:[{validator:d,option:{length:6,decimal:3},message:"请输入6位小数，且小数点后不大于3位",trigger:"blur"}],feedLose:[{validator:d,option:{length:6,decimal:3},message:"请输入6位小数，且小数点后不大于3位",trigger:"blur"}],antCode:[{pattern:/^\d{1,36}$/,message:"天线序号必须是数字，且不能超过36位",trigger:"blur"}],feedCode:[{pattern:/^\d{1,36}$/,message:"馈线序号必须是数字，且不能超过36位",trigger:"blur"}],freqType:[{max:1,message:"信（波）道划分类型：段， 波道不能超过1位",trigger:"blur"}],freqLc:[{validator:d,option:{length:14,decimal:7},message:"请输入14位小数，且小数点后不大于7位",trigger:"blur"}],freqUc:[{validator:d,option:{length:14,decimal:7},message:"请输入14位小数，且小数点后不大于7位",trigger:"blur"}],freqEfb:[{validator:d,option:{length:14,decimal:7},message:"请输入14位小数，且小数点后不大于7位",trigger:"blur"}],freqEfe:[{validator:d,option:{length:14,decimal:7},message:"请输入14位小数，且小数点后不大于7位",trigger:"blur"}],freqEBand:[{validator:d,option:{length:14,decimal:7},message:"请输入14位小数，且小数点后不大于7位",trigger:"blur"}],freqRfb:[{validator:d,option:{length:14,decimal:7},message:"请输入14位小数，且小数点后不大于7位",trigger:"blur"}],freqRfe:[{validator:d,option:{length:14,decimal:7},message:"请输入14位小数，且小数点后不大于7位",trigger:"blur"}],freqRBand:[{validator:d,option:{length:14,decimal:7},message:"请输入14位小数，且小数点后不大于7位",trigger:"blur"}],freqMod:[{max:8,message:"调制方式不能超过8位",trigger:"blur"}],freqMb:[{max:1,message:"主/备用频率标识不能超过1位",trigger:"blur"}],freqCode:[{max:36,message:"国家频率数据库对应码不能超过36位",trigger:"blur"}],antRgain:[{validator:d,option:{length:6,decimal:3},message:"请输入6位小数，且小数点后不大于3位",trigger:"blur"}],antHight:[{validator:d,option:{length:7,decimal:3},message:"请输入7位小数，且小数点后不大于3位",trigger:"blur"}],antType:[{max:8,message:"天线类型不能超过8位",trigger:"blur"}]},c()(a,"stLmmtr",[{required:!0,message:"不能为空",trigger:"change"}]),c()(a,"stUserArea",[{required:!0,message:"使用区域",trigger:"blur"}]),c()(a,"stEMultiAddr",[{max:8,message:"多址方式不能超过8位",trigger:"blur"}]),c()(a,"stEComArea",[{max:8,message:"通信范围不能超过8位",trigger:"blur"}]),c()(a,"stPosType",[{max:8,message:"位置类型不能超过8位",trigger:"blur"}]),c()(a,"stEPos",[{max:40,message:"位置类型不能超过40位",trigger:"blur"}]),c()(a,"stESat",[{max:40,message:"空间电台(星座)名称不能超过40位",trigger:"blur"}]),c()(a,"stELg",[{max:20,message:"标称轨道经度不能超过20位",trigger:"blur"}]),c()(a,"stBSgn",[{max:40,message:"台标不能超过40位",trigger:"blur"}]),c()(a,"stBLevel",[{max:8,message:"台站级别不能超过8位",trigger:"blur"}]),c()(a,"stBBm",[{max:8,message:"广播制式不能超过8位",trigger:"change"}]),c()(a,"stBEdu",[{max:1,message:"教育台不能超过1位",trigger:"blur"}]),c()(a,"stBIc",[{max:1,message:"差转台不能超过1位",trigger:"blur"}]),c()(a,"stBCoverArea",[{max:40,message:"覆盖区域不能超过40位",trigger:"blur"}]),c()(a,"stRWaMin",[{max:20,message:"工作方位角(起)不能超过20位",trigger:"blur"}]),c()(a,"stRWaMax",[{max:20,message:"工作方位角(止)不能超过20位",trigger:"blur"}]),c()(a,"stREMin",[{max:20,message:"俯仰角(起)不能超过20位",trigger:"blur"}]),c()(a,"stREMax",[{max:20,message:"俯仰角(止)不能超过20位",trigger:"blur"}]),c()(a,"stCCode",[{max:40,message:"基站编号不能超过40位",trigger:"blur"}]),c()(a,"stCSum",[{pattern:/^\d{0,3}$/,message:"扇区数量不能超过3位",trigger:"blur"}]),c()(a,"stServR",[{validator:d,option:{length:10,decimal:3},message:"请输入10位小数，且小数点后不大于3位",trigger:"blur"}]),c()(a,"stDTecType",[{max:20,message:"技术体制不能超过20位",trigger:"blur"}]),c()(a,"stDType",[{max:1,message:"直放站类型不能超过1位",trigger:"change"}]),c()(a,"stMeStaName",[{max:80,message:"卫星移动通信系统名称不能超过80位",trigger:"blur"}]),c()(a,"stMeSta1",[{max:80,message:"关口站名称1不能超过80位",trigger:"blur"}]),c()(a,"stMeSta2",[{max:80,message:"关口站名称2不能超过80位",trigger:"blur"}]),c()(a,"stMeSta3",[{max:80,message:"关口站名称3不能超过80位",trigger:"blur"}]),c()(a,"stMeStype",[{max:1,message:"星座/卫星不能超过1位",trigger:"blur"}]),c()(a,"stMeName1",[{max:80,message:"空间电台（星座）名称1不能超过80位",trigger:"blur"}]),c()(a,"stMeLg1",[{validator:d,option:{length:10,decimal:7},message:"请输入10位小数，且小数点后不大于7位",trigger:"blur"}]),c()(a,"stMeLg2",[{validator:d,option:{length:10,decimal:7},message:"请输入10位小数，且小数点后不大于7位",trigger:"blur"}]),c()(a,"stMeName2",[{max:80,message:"空间电台（星座）名称2不能超过80位",trigger:"blur"}]),c()(a,"stMeFBand",[{validator:d,option:{length:14,decimal:7},message:"请输入10位小数，且小数点后不大于7位",trigger:"blur"}]),c()(a,"stBType1",[{max:8,message:"广播电台台站类别1不能超过8位",trigger:"blur"}]),c()(a,"stBType2",[{max:8,message:"广播电台台站类别2不能超过8位",trigger:"blur"}]),c()(a,"etEquNo",[{max:4,message:"设备序号不能超过4位",trigger:"blur"}]),c()(a,"CCode",[{required:!0,message:"扇区不能为空",trigger:"blur"}]),c()(a,"etAfType",[{max:1,message:"自动选频不能超过1位",trigger:"blur"}]),c()(a,"etPowU",[{max:8,message:"功率单位不能超过8位",trigger:"blur"}]),c()(a,"etEquCl",[{max:20,message:"呼号不能超过20位",trigger:"blur"}]),c()(a,"etEquTl",[{validator:d,option:{length:9,decimal:3},message:"请输入9位小数，且小数点后不大于3位",trigger:"blur"}]),c()(a,"etEquRnq",[{validator:d,option:{length:9,decimal:3},message:"请输入9位小数，且小数点后不大于3位",trigger:"blur"}]),c()(a,"etEquTypeId",[{max:8,message:"设备类型不能超过8位",trigger:"blur"}]),c()(a,"etEquType",[{max:20,message:"设备类别不能超过20位",trigger:"blur"}]),c()(a,"etEquUse",[{max:40,message:"使用方式不能超过40位",trigger:"blur"}]),c()(a,"etEquPowMax",[{validator:d,option:{length:10,decimal:3},message:"请输入10位小数，且小数点后不大于3位",trigger:"blur"}]),c()(a,"etEquPowAvg",[{validator:d,option:{length:10,decimal:3},message:"请输入10位小数，且小数点后不大于3位",trigger:"blur"}]),c()(a,"etEquUpow",[{validator:d,option:{length:14,decimal:7},message:"请输入14位小数，且小数点后不大于7位",trigger:"blur"}]),c()(a,"etEquDpow",[{validator:d,option:{length:14,decimal:7},message:"请输入14位小数，且小数点后不大于7位",trigger:"blur"}]),c()(a,"ftFreqNo",[{max:2,message:"频率序号不能超过2位",trigger:"blur"}]),c()(a,"ftFreqInfoType",[{max:8,message:"发送信息类型不能超过8位",trigger:"blur"}]),c()(a,"ftFreqHcl",[{max:20,message:"呼号不能超过20位",trigger:"blur"}]),c()(a,"ftFreqType",[{max:1,message:"波道间隔/信道带宽不能超过1位",trigger:"blur"}]),c()(a,"ftFreqMc",[{max:8,message:"调制特性类型不能超过8位",trigger:"blur"}]),c()(a,"ftFreqMc1",[{max:20,message:"调制特性1不能超过20位",trigger:"blur"}]),c()(a,"ftFreqMc2",[{max:20,message:"调制特性2不能超过20位",trigger:"blur"}]),c()(a,"ftFreqMc3",[{max:20,message:"调制特性3不能超过20位",trigger:"blur"}]),c()(a,"etEquName",[{max:80,message:"设备名称不能超过80位",trigger:"blur"}]),c()(a,"etEquCode",[{max:10,message:"型式认可代码不能超过10位",trigger:"blur"}]),c()(a,"etEquEsgn",[{max:20,message:"发射标识不能超过20位",trigger:"blur"}]),c()(a,"etEquEpos",[{max:40,message:"设备电台安装位置不能超过40位",trigger:"blur"}]),c()(a,"etEquAtype",[{max:8,message:"设备天线类型不能超过8位",trigger:"blur"}]),c()(a,"etEquApos",[{max:40,message:"天线安装位置不能超过40位",trigger:"blur"}]),c()(a,"stShipType",[{max:8,message:"船舶种类不能超过8位",trigger:"change"}]),c()(a,"stCallSign",[{max:20,message:"呼号不能超过20位",trigger:"blur"}]),c()(a,"stSMmsi",[{max:20,message:"MMSI号不能超过20位",trigger:"blur"}]),c()(a,"stSCs",[{max:20,message:"船舶登记号不能超过20位",trigger:"blur"}]),c()(a,"stShipName",[{max:80,message:"船舶名称不能超过80位",trigger:"blur"}]),c()(a,"stSPn",[{max:80,message:"船籍港名不能超过80位",trigger:"blur"}]),c()(a,"stSAaic",[{max:20,message:"帐务结算机构代码不能超过20位",trigger:"blur"}]),c()(a,"stST",[{validator:d,option:{length:10,decimal:2},message:"请输入10位小数，且小数点后不大于2位",trigger:"blur"}]),c()(a,"stSP",[{validator:d,option:{length:10,decimal:2},message:"请输入10位小数，且小数点后不大于2位",trigger:"blur"}]),c()(a,"stANrm",[{max:40,message:"国籍和注册号码不能超过40位",trigger:"blur"}]),c()(a,"stAModel",[{max:40,message:"航空器型号不能超过40位",trigger:"blur"}]),c()(a,"stAType",[{max:8,message:" 航空器类型不能超过8位",trigger:"change"}]),c()(a,"stACall",[{max:1,message:"选呼或报呼不能超过1位",trigger:"blur"}]),c()(a,"stACallOi",[{max:20,message:"呼号或其他标识不能超过20位",trigger:"blur"}]),c()(a,"stASgn",[{max:20,message:"航空器识别码不能超过20位",trigger:"blur"}]),c()(a,"stASt",[{validator:d,option:{length:10,decimal:2},message:"请输入10位小数，且小数点后不大于2位",trigger:"blur"}]),c()(a,"stAPiloting",[{max:1,message:"按地标飞行不能超过1位",trigger:"change"}]),c()(a,"feeType",[{required:!0,message:"缴费类型不能为空",trigger:"blur"},{pattern:/^\d?$/,message:"缴费类型不能超过1位",trigger:"blur"}]),c()(a,"feeSum",[{required:!0,message:"频占费收费金额不能为空",trigger:"blur"},{validator:d,option:{length:8,decimal:2},message:"请输入8位小数，且小数点后不大于2位",trigger:"blur"}]),c()(a,"feeDerateProportion",[{validator:d,option:{length:4,decimal:3},message:"请输入4位小数，且小数点后不大于3位",trigger:"blur"}]),c()(a,"feeDerateAccording",[{max:36,message:"减免依据不能超过36位",trigger:"blur"}]),c()(a,"feePayMonth",[{required:!0,message:"缴费月份不能为空",trigger:"blur"},{pattern:/^\d{0,2}$/,message:"缴费类型不能超过2位",trigger:"blur"}]),c()(a,"feeOperation",[{required:!0,message:"操作人不能为空",trigger:"blur"},{max:40,message:"操作人不能超过40位",trigger:"blur"}]),c()(a,"feeOperationDate",[{required:!0,message:"操作时间不能为空",trigger:"blur"}]),c()(a,"feeAudit",[{max:40,message:"审核人不能超过40位",trigger:"blur"}]),c()(a,"ftFreqCcode",[{max:20,message:"扇区号不能超过20位",trigger:"blur"}]),c()(a,"ftFreqCsgn",[{max:20,message:"扇区标识码不能超过20位",trigger:"blur"}]),c()(a,"ftFreqDupdn",[{max:1,message:"直放站的上行/下行 标识不能超过1位",trigger:"blur"}]),c()(a,"ftFreqUnitType",[{max:8,message:"频率单位不能超过8位",trigger:"blur"}]),c()(a,"ftFreqFep",[{max:8,message:"发射极化方式不能超过8位",trigger:"blur"}]),c()(a,"ftFreqFrp",[{max:8,message:"接收极化方式不能超过8位",trigger:"blur"}]),c()(a,"ftFreqEpow",[{validator:d,option:{length:14,decimal:7},message:"请输入14位小数，且小数点后不大于7位",trigger:"blur"}]),c()(a,"ftFreqPowflag",[{max:8,message:"功率标识不能超过8位",trigger:"blur"}]),c()(a,"ftFreqEirp",[{validator:d,option:{length:14,decimal:7},message:"请输入14位小数，且小数点后不大于7位",trigger:"blur"}]),c()(a,"ftFreqPowMax",[{validator:d,option:{length:10,decimal:3},message:"请输入10位小数，且小数点后不大于3位",trigger:"blur"}]),c()(a,"ftFreqPowAvg",[{validator:d,option:{length:10,decimal:3},message:"请输入10位小数，且小数点后不大于3位",trigger:"blur"}]),c()(a,"atSeB",[{validator:d,option:{length:10,decimal:7},message:"请输入10位小数，且小数点后不大于7位",trigger:"blur"}]),c()(a,"atSeE",[{validator:d,option:{length:10,decimal:7},message:"请输入10位小数，且小数点后不大于7位",trigger:"blur"}]),c()(a,"atAngB",[{validator:d,option:{length:10,decimal:7},message:"请输入10位小数，且小数点后不大于7位",trigger:"blur"}]),c()(a,"atAngE",[{validator:d,option:{length:10,decimal:7},message:"请输入10位小数，且小数点后不大于7位",trigger:"blur"}]),c()(a,"atRnt",[{validator:d,option:{length:9,decimal:3},message:"请输入9位小数，且小数点后不大于3位",trigger:"blur"}]),c()(a,"atBwid",[{validator:d,option:{length:10,decimal:7},message:"请输入10位小数，且小数点后不大于7位",trigger:"blur"}]),c()(a,"atLel",[{validator:d,option:{length:9,decimal:3},message:"请输入9位小数，且小数点后不大于3位",trigger:"blur"}]),c()(a,"atSspeed",[{validator:d,option:{length:9,decimal:3},message:"请输入9位小数，且小数点后不大于3位",trigger:"blur"}]),c()(a,"atCcode",[{max:20,message:"扇区号不能超过20位",trigger:"blur"}]),c()(a,"at3DBE",[{validator:d,option:{length:10,decimal:7},message:"请输入10位小数，且小数点后不大于7位",trigger:"blur"}]),c()(a,"at3DBR",[{validator:d,option:{length:10,decimal:7},message:"请输入10位小数，且小数点后不大于7位",trigger:"blur"}]),c()(a,"atRang",[{validator:d,option:{length:10,decimal:7},message:"请输入10位小数，且小数点后不大于7位",trigger:"blur"}]),c()(a,"atEang",[{validator:d,option:{length:10,decimal:7},message:"请输入10位小数，且小数点后不大于7位",trigger:"blur"}]),c()(a,"atCsgn",[{max:20,message:"扇区标识码不能超过20位",trigger:"blur"}]),c()(a,"atUpdn",[{max:1,message:"直放站上行/下行不能超过1位",trigger:"blur"}]),c()(a,"atSum",[{pattern:/^\d{0,3}$/,message:"天线数量不能超过3位",trigger:"blur"}]),c()(a,"atQua",[{max:20,message:"接收系统品质不能超过20位",trigger:"blur"}]),c()(a,"atUnitType",[{max:8,message:"天线增益单位不能超过8位",trigger:"blur"}]),c()(a,"seComm",[{validator:d,option:{length:7,decimal:4},message:"请输入7位小数，且小数点后不大于4位",trigger:"blur"}]),c()(a,"seAngle",[{validator:d,option:{length:7,decimal:4},message:"请输入7位小数，且小数点后不大于4位",trigger:"blur"}]),c()(a,"seDis",[{validator:d,option:{length:7,decimal:4},message:"请输入7位小数，且小数点后不大于4位",trigger:"blur"}]),c()(a,"atAntUpang",[{validator:d,option:{length:10,decimal:7},message:"请输入10位小数，且小数点后不大于7位",trigger:"blur"}]),c()(a,"atAntNo",[{max:4,message:"天线序号不能超过4位",trigger:"blur"}]),c()(a,"feeTime",[{required:!0,message:"缴费日期不能为空",trigger:"blur"}]),c()(a,"feeYear",[{required:!0,message:"缴费年度不能为空",trigger:"blur"},{pattern:/^\d{0,4}$/,message:"缴费年度不能超过4位",trigger:"blur"}]),c()(a,"feeBill",[{validator:d,option:{length:11,decimal:2},message:"请输入11位小数，且小数点后不大于2位",trigger:"blur"}]),c()(a,"feePayment",[{validator:d,option:{length:11,decimal:2},message:"请输入11位小数，且小数点后不大于2位",trigger:"blur"}]),c()(a,"feeFreq",[{validator:d,option:{length:11,decimal:2},message:"请输入11位小数，且小数点后不大于2位",trigger:"blur"}]),c()(a,"feeElse",[{validator:d,option:{length:11,decimal:2},message:"请输入11位小数，且小数点后不大于2位",trigger:"blur"}]),c()(a,"feeLate",[{validator:d,option:{length:11,decimal:2},message:"请输入11位小数，且小数点后不大于2位",trigger:"blur"}]),c()(a,"feeBillCode",[{max:40,message:"缴费单据号码不能超过40位",trigger:"blur"}]),c()(a,"feeUrgencyTime",[{required:!0,message:"催缴时间不能为空",trigger:"blur"}]),c()(a,"feeUrgencyMode",[{max:40,message:"催缴方式不能超过40位",trigger:"blur"}]),c()(a,"feeUrgencyOperation",[{required:!0,message:"催缴操作人员不能为空",trigger:"blur"},{max:40,message:"催缴操作人员不能超过40位",trigger:"blur"}]),c()(a,"feeUrgencyPerson",[{max:40,message:"催缴单位联系人不能超过40位",trigger:"blur"}]),c()(a,"feeUrgencyFileCode",[{max:20,message:"催缴文件编号不能超过20位",trigger:"blur"}]),c()(a,"licenseType",[{required:!0,message:"执照类型不能为空",trigger:"blur"},{max:8,message:"执照类型不能超过8位",trigger:"blur"}]),c()(a,"licenseCode",[{max:20,message:"执照号不能超过20位",trigger:"blur"}]),c()(a,"licenseOrgName",[{max:60,message:"执照单位名称不能超过60位",trigger:"blur"}]),c()(a,"licenseManager",[{max:60,message:"核发单位名称不能超过60位",trigger:"blur"}]),c()(a,"userName",[{required:!0,message:"用户名不能为空",trigger:"blur"}]),c()(a,"loginName",[{required:!0,message:"登录名不能为空",trigger:"blur"}]),c()(a,"loginPassword",[{required:!0,message:"密码不能为空",trigger:"blur"}]),c()(a,"userType",[{required:!0,message:"用户类型不能为空",trigger:"blur"}]),a),b=new(r("oFuF").a);n.default.filter("operatorName",function(e){return"mobile"===e?"移动":"unicom"===e?"联通":"telecom"===e?"电信":"guangdian"===e?"广电":"未知运营商"}),n.default.filter("format",function(e){return e?b.format(e,"yyyy-mm-dd hh:ii:ss"):""}),n.default.filter("jobState",function(e){switch(parseInt(e)){case 0:return"未提交";case 1:return"异常";case 2:return"正常";default:return"异常"}}),n.default.filter("jobStateStyle",function(e){switch(parseInt(e)){case 0:return"text-warning";case 1:return"text-danger";case 2:return"text-success";default:return"text-danger"}}),n.default.filter("baseStationState",function(e){switch(e){case"0":return"正常";case"1":return"异常"}}),n.default.filter("baseStationStateClass",function(e){switch(e){case"0":return"text-success";case"1":return"text-danger"}}),n.default.filter("isCompareBigTask",function(e){switch(parseInt(e)){case 10:return"无委确认通过（待审核）";case 11:return"无委审核中（提交审核处理中）";case 12:return"审核处理数据失败";case 13:return"审核处理数据成功";default:return"异常"}}),n.default.filter("isCompareChildrenTask",function(e){switch(parseInt(e)){case 12:return"分任务待审核";case 13:return"分任务提交审核中（入库中）";case 14:return"分任务审核失败";case 15:return"分任务审核入库成功";default:return"异常"}}),n.default.filter("isCompare",function(e){switch(parseInt(e)){case 0:return"待提交";case 1:return"提交失败";case 2:return"提交成功（待对比）";case 3:return"对比中";case 4:return"对比失败";case 5:return"对比成功（无委待确认）";case 6:return"无委确认中";case 7:return"无委确认不通过";case 8:return"无委确认通过（待审核）";case 9:return"审核中";case 10:return"完结";case 12:return"分任务待生成申请表";case 13:return"分任务生成申请表中";case 14:return"分任务生成申请表失败";case 15:return"分任务生成申请表成功待入库";case 16:return"分任务提交审核中（入库中）";case 17:return"分任务审核失败";case 18:return"分任务审核入库成功";case 19:return"任务放弃生成申请表";case 20:return"已撤消";default:return"异常"}}),n.default.filter("jobStatusFilter",function(e){switch(e=parseInt(e)){case 10:return"待审核";case 11:return"无委审核中";case 12:return"审核处理数据失败";case 13:return"审核处理数据成功";default:return""}}),n.default.filter("isCompareStyle",function(e){switch(parseInt(e)){case 0:return"text-info";case 1:case 2:case 3:case 4:case 5:case 6:return"text-warning";case 7:return"text-success";case 8:case 9:return"text-warning";case 12:return"text-success";default:return"text-danger"}}),n.default.filter("convertDataType",function(e){switch(e){case"1":return"新增";case"2":return"变更";case"3":return"注销";default:return""}}),n.default.filter("operator",function(e){return"mobile"===e?"贵州移动":"unicom"===e?"贵州联通":"telecom"===e?"贵州电信":"guangdian"===e?"广电":"未知运营商"}),n.default.filter("techTypeFilter",function(e){switch(parseInt(e)){case 0:return"中国移动GSM";case 1:return"中国联通GSM";case 2:return"中国电信CDMA";case 3:return"中国移动TD-SCDMA";case 4:return"中国联通WCDMA";case 5:return"中国电信CDMA2000-EVDO";case 6:return"中国移动TD-LTE";case 7:return"中国联通LTE FDD";case 8:return"中国电信LTE FDD";case 9:return"中国联通TD-LTE";case 10:return"中国电信TD-LTE";case 11:return"中国移动LTE FDD";case 12:return"中国移动5G NR";case 13:return"中国联通5G NR";case 14:return"中国电信5G NR";case 15:return"中国广电5G NR";default:return"异常"}});var h=r("Xxa5"),f=r.n(h),x=r("exGp"),v=r.n(x),q={getOrgList:function(e,t){var r=this;return v()(f.a.mark(function a(){var n;return f.a.wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return n=[],r.next=3,u.a.get({url:"/apiWeb/security/org/findAllToWeb"}).then(function(e){e.success&&(n=e.data.map(function(e){return{label:e.orgName,value:e.orgName}}))});case 3:return e[t]=n,r.abrupt("return");case 5:case"end":return r.stop()}},a,r)}))()}},M=r("JCKA");try{console.log(324,Object(M.GpsToBaidu)(100,100))}catch(e){console.log(e)}n.default.config.productionTip=!1,n.default.use(i.a),n.default.prototype.$ajax=u.a,n.default.prototype.$formCheck=p;var y=new n.default({el:"#app",router:l.a,components:{App:o},template:"<App/>"}),P=new n.default({data:{stationMapData:[]}});window.vueBus=P,window.PublicData=q;try{/\?caict_bsm=/.test(window.location)?sessionStorage.setItem("isFromPlatform","true"):sessionStorage.setItem("isFromPlatform","false"),l.a.beforeEach(function(e,t,r){"true"==sessionStorage.getItem("isFromPlatform")?y.$ajax.post({url:"/checklogin"}).then(function(e){if("false"==e.data){window.open("http://*************/SRRC_SC","_parent")}else r()}):r()})}catch(e){}},YaEn:function(e,t,r){"use strict";var a=r("7+uW"),n=r("/ocq"),s=(r("nR8Q"),r("Z1y9"),r("zL8q")),i=(r.n(s),function(){return Promise.all([r.e(0),r.e(10)]).then(r.bind(null,"W2Q3"))}),g=function(){return r.e(19).then(r.bind(null,"ccNR"))},o=function(){return Promise.all([r.e(0),r.e(28)]).then(r.bind(null,"NB22"))},l=function(){return r.e(1).then(r.bind(null,"+H76"))};a.default.use(n.a);var u=new n.a({mode:"history",base:"/",srcollBehavior:function(e,t,r){if(e.hash)return{selector:e.hash}},routes:[{path:"/",component:i},{path:"/index",component:i},{path:"/login_bsm",component:i},{path:"/layout",component:function(){return Promise.all([r.e(0),r.e(2)]).then(r.bind(null,"HS7g"))},children:[{path:"/gis-map",component:function(){return r.e(21).then(r.bind(null,"Yxm3"))}},{path:"/home",component:function(){return r.e(48).then(r.bind(null,"KR8f"))}},{path:"/license-print",component:function(){return Promise.all([r.e(0),r.e(12)]).then(r.bind(null,"YzRj"))}},{path:"/sta-detail",component:function(){return Promise.all([r.e(0),r.e(67)]).then(r.bind(null,"jL/3"))}},{path:"/license-history",component:function(){return Promise.all([r.e(0),r.e(29)]).then(r.bind(null,"maPF"))}},{path:"/new-sta",component:function(){return Promise.all([r.e(0),r.e(13)]).then(r.bind(null,"gLOc"))}},{path:"/data-sync-management",component:function(){return r.e(6).then(r.bind(null,"UhbE"))}},{path:"/data-sync-config",component:function(){return Promise.all([r.e(0),r.e(30)]).then(r.bind(null,"ShcR"))}},{path:"/apply-table-management",component:function(){return Promise.all([r.e(0),r.e(45)]).then(r.bind(null,"D5KM"))}},{path:"/apply-table-management-detail",component:function(){return Promise.all([r.e(0),r.e(43)]).then(r.bind(null,"sZxL"))}},{path:"/station/manage",component:function(){return r.e(16).then(r.bind(null,"iuGx"))}},{path:"/station/protection-of-key-areas",component:function(){return r.e(15).then(r.bind(null,"HW5h"))}},{path:"/station/base-station-cross-district",component:function(){return r.e(42).then(r.bind(null,"0keS"))}},{path:"/station/road-test-list",component:function(){return Promise.all([r.e(0),r.e(46)]).then(r.bind(null,"/K8a"))}},{path:"/station/road-test-contrast",component:function(){return Promise.all([r.e(0),r.e(51)]).then(r.bind(null,"VuRs"))}},{path:"/station/two-table-single",component:function(){return Promise.all([r.e(0),r.e(17)]).then(r.bind(null,"TRA0"))}},{path:"/station/emphasis-data",component:function(){return Promise.all([r.e(0),r.e(18)]).then(r.bind(null,"l7hw"))}},{path:"/station/5G-Disturb",component:function(){return Promise.all([r.e(0),r.e(39)]).then(r.bind(null,"NAEz"))}},{path:"/station/5G-Disturb-detail",component:function(){return Promise.all([r.e(0),r.e(62)]).then(r.bind(null,"uu97"))}},{path:"/station/map",component:function(){return r.e(9).then(r.bind(null,"KGyj"))}},{path:"/station/apply/:guid",name:"station-apply",component:function(){return r.e(37).then(r.bind(null,"+J6M"))}},{path:"/station/manage/:stationId",name:"station-manage",component:function(){return r.e(57).then(r.bind(null,"Ex1Y"))}},{path:"/statistic/synthesize",component:function(){return Promise.all([r.e(0),r.e(55)]).then(r.bind(null,"eZ8r"))}},{path:"/statistic/station",component:function(){return Promise.all([r.e(0),r.e(66)]).then(r.bind(null,"NDr1"))}},{path:"/statistic/two-table",component:function(){return Promise.all([r.e(0),r.e(61)]).then(r.bind(null,"vMW0"))}},{path:"/statistic/station-history",component:function(){return Promise.all([r.e(0),r.e(4)]).then(r.bind(null,"8fru"))}},{path:"/statistic/interact",component:function(){return Promise.all([r.e(0),r.e(49)]).then(r.bind(null,"PKyv"))}},{path:"/statistic/license",component:function(){return Promise.all([r.e(0),r.e(25)]).then(r.bind(null,"A3lv"))}},{path:"/sync/export",component:function(){return Promise.all([r.e(0),r.e(35)]).then(r.bind(null,"1gPi"))}},{path:"/sync/unusual",component:function(){return r.e(69).then(r.bind(null,"Otjz"))}},{path:"/sync/unusual/detail",component:function(){return r.e(53).then(r.bind(null,"9wDR"))}},{path:"/report/all",component:function(){return r.e(70).then(r.bind(null,"b0sC"))}},{path:"/report/all-detail",component:function(){return Promise.all([r.e(0),r.e(75)]).then(r.bind(null,"aI18"))}},{path:"/report/me",component:function(){return Promise.all([r.e(0),r.e(63)]).then(r.bind(null,"FlnW"))}},{path:"/report/index",component:function(){return Promise.all([r.e(0),r.e(7)]).then(r.bind(null,"WCPl"))}},{path:"/report-full-dose",component:function(){return Promise.all([r.e(0),r.e(56)]).then(r.bind(null,"5bhU"))}},{path:"/report-increment",component:function(){return Promise.all([r.e(0),r.e(65)]).then(r.bind(null,"tdLL"))}},{path:"/report/sync",component:function(){return r.e(59).then(r.bind(null,"+Ze8"))}},{path:"/stationdata/:id",component:function(){return Promise.all([r.e(0),r.e(52)]).then(r.bind(null,"nXyW"))}},{path:"/compare/operate",component:o},{path:"/compare/records",component:function(){return r.e(79).then(r.bind(null,"702p"))}},{path:"/compare/records/audit/:job/:guid",component:function(){return Promise.all([r.e(0),r.e(38)]).then(r.bind(null,"gsyq"))}},{path:"/compare/records/:job/:guid",component:function(){return Promise.all([r.e(0),r.e(44)]).then(r.bind(null,"SisZ"))}},{path:"/logs",component:function(){return r.e(71).then(r.bind(null,"fey7"))}},{path:"/logs/detail",component:function(){return r.e(76).then(r.bind(null,"FlNo"))}},{path:"/license-sync/license",component:function(){return Promise.all([r.e(0),r.e(27)]).then(r.bind(null,"CBYx"))}},{path:"/license-sync/sync",component:function(){return Promise.all([r.e(0),r.e(5)]).then(r.bind(null,"ewg/"))}},{path:"/matter",component:function(){return Promise.all([r.e(0),r.e(22)]).then(r.bind(null,"+mbF"))}},{path:"/work/todos",component:function(){return r.e(11).then(r.bind(null,"MuOj"))}},{path:"/work/todos/detail",component:o},{path:"/work/offices",component:function(){return Promise.all([r.e(0),r.e(40)]).then(r.bind(null,"+lrs"))}},{path:"/work/office-list-level02",component:function(){return Promise.all([r.e(0),r.e(60)]).then(r.bind(null,"SCAp"))}},{path:"/work/offices/detail",component:function(){return r.e(72).then(r.bind(null,"EDX1"))}},{path:"/work/sync",component:function(){return r.e(41).then(r.bind(null,"OAkS"))}},{path:"/database/database-config",component:function(){return r.e(78).then(r.bind(null,"XeaP"))}},{path:"/audit/records",component:function(){return Promise.all([r.e(0),r.e(73)]).then(r.bind(null,"ZKFu"))}},{path:"/audit/records-children-task",component:function(){return Promise.all([r.e(0),r.e(74)]).then(r.bind(null,"N/4v"))}},{path:"/audit/records/:guid",component:function(){return Promise.all([r.e(0),r.e(47)]).then(r.bind(null,"fGh+"))}},{path:"/license/index",component:function(){return Promise.all([r.e(0),r.e(23)]).then(r.bind(null,"ZxJZ"))}},{path:"/license/manage",component:function(){return Promise.all([r.e(0),r.e(36)]).then(r.bind(null,"rgvp"))}},{path:"/license/operator",component:function(){return Promise.all([r.e(0),r.e(24)]).then(r.bind(null,"Rljh"))}},{path:"/organ",component:g,name:"org-detail"},{path:"/organ/detail",component:g},{path:"/organ/detail/new",component:function(){return r.e(20).then(r.bind(null,"c+7W"))},name:"new-org"},{path:"/datasync",component:function(){return r.e(8).then(r.bind(null,"Fjsf"))}},{path:"/info",component:function(){return r.e(14).then(r.bind(null,"7uCn"))}},{path:"/404",component:function(){return r.e(68).then(r.bind(null,"QPJ4"))}},{path:"/back",component:function(){return r.e(77).then(r.bind(null,"8M5p"))}},{path:"/examine",component:function(){return Promise.all([r.e(0),r.e(50)]).then(r.bind(null,"HYm3"))}},{path:"/seeReport",component:function(){return Promise.all([r.e(0),r.e(54)]).then(r.bind(null,"0oIW"))}},{path:"/validation",name:"Validation",component:function(){return r.e(58).then(r.bind(null,"c1Nl"))}},{path:"/help-file",name:"HelpFIle",component:function(){return r.e(64).then(r.bind(null,"vtpD"))}},{path:"/system/user",name:"SystemUser",component:function(){return Promise.all([r.e(0),r.e(31)]).then(r.bind(null,"3omw"))}},{path:"/system/menu",name:"SystemMenu",component:function(){return Promise.all([r.e(0),r.e(33)]).then(r.bind(null,"dSrB"))}},{path:"/system/role",name:"SystemRole",component:function(){return Promise.all([r.e(0),r.e(34)]).then(r.bind(null,"Fmaq"))}},{path:"/system/data",name:"SystemData",component:function(){return Promise.all([r.e(0),r.e(32)]).then(r.bind(null,"bstG"))}},{path:"/system/organization",name:"SystemOrganization",component:function(){return Promise.all([r.e(0),r.e(26)]).then(r.bind(null,"35Ab"))}},{path:"/frequency-evaluation",name:"FrequencyEvaluation",component:function(){return Promise.all([r.e(0),r.e(3)]).then(r.bind(null,"3v7q"))}},{path:"*",name:"NotFound",component:l}]},{path:"*",name:"NotFound",component:l},{path:"*",component:function(e){return r.e(1).then(function(){var t=[r("+H76")];e.apply(null,t)}.bind(this)).catch(r.oe)}}]});t.a=u},Z1y9:function(e,t,r){"use strict";var a=r("Dd8w"),n=r.n(a),s=r("Zrlr"),i=r.n(s),g=r("wxAW"),o=r.n(g),l=r("//Fk"),u=r.n(l),m=r("mvHQ"),c=r.n(m),d=r("mtWM"),p=r.n(d),b=r("zL8q"),h=(r.n(b),r("mw3O")),f=(r.n(h),r("nR8Q")),x=r("YaEn"),v=r("99Ht").a.ajaxDomain,q=p.a.create({baseURL:v,timeout:18e5,responseType:"json",withCredentials:!1,formData:!1,isDownload:!1,isMessage:!0}),M=document.querySelector("#ajaxLoading");function y(){M&&(M.style.display="block")}function P(){M&&(M.style.display="none")}M.onclick=function(){this.style.display="none"},q.interceptors.request.use(function(e){var t="";return"POST"===e.method.toUpperCase()&&(e.formData?(t="application/json;charset=UTF-8",e.data=e.data):(t="application/json;charset=UTF-8",(Object(f.c)(e.data)||Object(f.a)(e.data))&&(e.data=c()(e.data)))),"GET"===e.method.toUpperCase()&&(t="application/json;charset=UTF-8"),t="application/json;charset=UTF-8",e.headers["Content-Type"]=t,e.headers.token=sessionStorage.getItem("token")||"",e.url=e.url.replace(/\/api\//gi,"/"),e},function(e){try{e.config.isMessage&&(b.Message.closeAll(),b.Message.error(e&&e.data.error))}catch(e){}return u.a.reject(e.data.error)}),q.interceptors.response.use(function(e){if(-1!=e.data.message)return e;x.a.push({path:"/login"})},function(e){try{var t=e.response,r=e.config;if(!t&&r.isMessage&&-1!==(e+"").search("timeout")&&(b.Message.closeAll(),b.Message.error("哎哟，请求超时咯。请稍后再重试！")),t&&t.status&&r.isMessage)switch(b.Message.closeAll(),t.status){case 400:b.Message.error('非常抱歉，"Bad Request"！');break;case 401:b.Message.error("非常抱歉，未经授权！");break;case 403:b.Message.error("非常抱歉，拒绝访问！");break;case 404:b.Message.error('非常抱歉，"Not Found"！');break;case 413:b.Message.error("非常抱歉，上传文件只能在100M以内！");break;case 500:b.Message.error("非常抱歉，服务器出错了！");break;case 502:b.Message.error('非常抱歉，"Bad Gateway"！');break;case 503:b.Message.error('非常抱歉，"Service Unavailable"！');break;case 504:b.Message.error('非常抱歉，"Gateway Timeout"！');break;case 505:b.Message.error('非常抱歉，"HTTP Version Not Supported"！');break;default:b.Message.error("您遇到了一个未知错误，必要情况下请联系管理员！")}}catch(e){}return u.a.reject(e)});var w=function(){function e(){i()(this,e)}return o()(e,null,[{key:"post",value:function(e){var t=e.isShowLoading||!1;return t&&y(),new u.a(function(r,a){e.method="POST",e.data=e.data||"",e.params=e.params||{},e.formData=e.formData||!1,e.isMessage=!1!==e.isMessage,q(n()({},e)).then(function(e){t&&P(),200!==e.status?a(e):r(e.data)}).catch(function(e){t&&P(),a(e)})})}},{key:"get",value:function(e){var t=e.isShowLoading||!1;return t&&y(),new u.a(function(r,a){e.method="GET",e.params=e.params||{},e.isMessage=!1!==e.isMessage,q(n()({},e)).then(function(e){t&&P(),200!==e.status?a(e):r(e.data)}).catch(function(e){t&&P(),a(e)})})}},{key:"delete",value:function(e){var t=e.isShowLoading||!1;return t&&y(),new u.a(function(r,a){e.method="delete",e.params=e.params||{},e.isMessage=!1!==e.isMessage,q(n()({},e)).then(function(e){t&&P(),200!==e.status?a(e):r(e.data)}).catch(function(e){t&&P(),a(e)})})}}]),e}();t.a=w},dla8:function(e,t){},nR8Q:function(e,t,r){"use strict";t.c=function(e){return"object"===(void 0===e?"undefined":i()(e))&&"[object object]"===Object.prototype.toString.call(e).toLowerCase()&&0!==n()(e).length},t.a=function(e){return"[object Array]"===Object.prototype.toString.call(e)&&e.length},t.b=function(e){return"[object Number]"===Object.prototype.toString.call(e)&&!isNaN(e)},t.d=function(e){return"[object String]"===Object.prototype.toString.call(e)&&""!==e};var a=r("fZjL"),n=r.n(a),s=r("pFYg"),i=r.n(s)},oFuF:function(e,t,r){"use strict";var a=r("mvHQ"),n=r.n(a),s=r("//Fk"),i=r.n(s),g=r("Zrlr"),o=r.n(g),l=r("wxAW"),u=r.n(l),m=r("GG98"),c=r.n(m),d=r("zL8q"),p=(r.n(d),r("YaEn")),b=r("nR8Q"),h=r("99Ht").a.ajaxDomain,f=function(){function e(){o()(this,e)}return u()(e,[{key:"requestFun",value:function(e){return d.Message.closeAll(),e.url=h+e.url.replace(/\/api\//gi,"/"),"get"===e.type?new i.a(function(t,r){c.a.get(e.url).query(e.data).set("token",sessionStorage.getItem("token")).accept("json").end(function(r,a){200==a.status?"/api/logout"===e.url?(window.localStorage.clear(),p.a.push("/login")):-1===a.body.status?(d.Message.error("身份认证失效,请重新登录!"),window.localStorage.clear(),p.a.push("/login")):t({status:!0,res:a.body}):t({status:!1,reason:"操作失败请重试!"})})}):"post"===e.type||"put"===e.type?new i.a(function(t,r){c.a[e.type](e.url).send(e.data).set("token",sessionStorage.getItem("token")).accept("json").end(function(r,a){if(200==a.status){if(-1==a.body.message)return void p.a.push("/login");"/api/logout"===e.url?(window.localStorage.clear(),p.a.push("/login")):-1===a.body.status?(d.Message.error("身份认证失效,请重新登录!"),window.localStorage.clear(),p.a.push("/login")):t({status:!0,res:a.body})}else t({status:!1,reason:"操作失败请重试!"})})}):void 0}},{key:"setStorage",value:function(e,t){switch(!0){case Object(b.c)(t):case Object(b.a)(t):window.localStorage.setItem(e,n()(t));break;case Object(b.b)(t):case Object(b.d)(t):window.localStorage.setItem(e,t)}}},{key:"getStorage",value:function(e){var t=window.localStorage.getItem(e);return t?JSON.parse(t):""}},{key:"removeStorage",value:function(e){window.localStorage.removeItem(e)}},{key:"removeAllStorage",value:function(){window.localStorage.clear()}},{key:"format",value:function(e,t){var r,a=void 0,n=void 0,s=void 0,i=void 0,g=void 0;return t||(t=e||"yyyy-mm-dd hh:ii:ss",e=null),e?e instanceof Date||(e=new Date(e)):e=new Date,r=e.getFullYear(),a=e.getMonth()+1,n=e.getDate(),s=e.getHours(),i=e.getMinutes(),g=e.getSeconds(),a<10&&(a="0"+a),n<10&&(n="0"+n),s<10&&(s="0"+s),i<10&&(i="0"+i),g<10&&(g="0"+g),t.replace("yyyy",r).replace("mm",a).replace("dd",n).replace("hh",s).replace("ii",i).replace("ss",g)}},{key:"toTimestamp",value:function(e){return""!=e&&e?Number(e):null}},{key:"fileSizeFormat",value:function(e){return e?Number(e).toFixed(2):0}},{key:"funDownload",value:function(e){if(e=h+"/"+e,/(iP)/g.test(navigator.userAgent))return this.$message.error("您的是设备不支持文件下载，请使用桌面浏览器"),!1;if(x("chrome")||x("firefox")||x("safari")){var t=document.createElement("a");if(t.href=e,void 0!==t.download){var r=e.substring(e.lastIndexOf("/")+1,e.length);t.download=r}if(document.createEvent){var a=document.createEvent("MouseEvents");return a.initEvent("click",!0,!0),t.dispatchEvent(a),!0}}else{var n=document.createElement("iframe");n.style.display="none",n.src=e,document.body.appendChild(n)}return-1===e.indexOf("?")&&(e+="?download"),window.open(e,"_self"),!0}},{key:"funPrint",value:function(e){if(x("edge")){var t=window.open("/api/"+e,"newwindow","height=700, width=900, top=200, left=100, toolbar=no, menubar=no, scrollbars=no, resizable=no,location=n o, status=no");setTimeout(function(){t.print()},4e3)}else{var r=window.open("/api/"+e,"newwindow","height=400, width=800, top=100, left=100, toolbar=no, menubar=no, scrollbars=no, resizable=no,location=n o, status=no");r.onload=function(){r.print()}}}},{key:"returnFreq",value:function(e,t){var r="";if("EF"===t)for(var a=e.freq_EFB,n=e.freq_EFE,s=a.split(","),i=n.split(","),g=0;g<s.length;g++)r=r+s[g]+"~"+i[g],g<s.length-1&&(r+=",");else for(var o=e.freq_RFB,l=e.freq_RFE,u=o.split(","),m=l.split(","),c=0;c<u.length;c++)r=r+u[c]+"~"+m[c],c<u.length-1&&(r+=",");return r}},{key:"returnFreq2",value:function(e,t){var r="";if("EF"===t)for(var a=e.sendStartFreq,n=e.sendEndFreq,s=a.split(","),i=n.split(","),g=0;g<s.length;g++)r=r+s[g]+"~"+i[g],g<s.length-1&&(r+=",");else for(var o=e.accStartFreq,l=e.accEndFreq,u=o.split(","),m=l.split(","),c=0;c<u.length;c++)r=r+u[c]+"~"+m[c],c<u.length-1&&(r+=",");return r}}]),e}();function x(e){return navigator.userAgent.toLowerCase().indexOf(e)>-1}t.a=f},tvR6:function(e,t){}},["NHnr"]);