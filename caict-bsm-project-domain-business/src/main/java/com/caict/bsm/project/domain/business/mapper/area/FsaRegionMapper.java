package com.caict.bsm.project.domain.business.mapper.area;

import com.caict.bsm.project.system.data.mapper.BasicMapper;
import com.caict.bsm.project.system.model.entity.business.area.FsaRegion;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by yanchengpeng on 2021/3/25.
 */
@Repository
public interface FsaRegionMapper extends BasicMapper<FsaRegion>{

    @Select("select * from FSA_REGION " +
            "WHERE parent_id = (select id from FSA_REGION where code = #{regionId}) ")
    List<FsaRegion> findAreaByRegionId(@Param("regionId") String regionId);

    @Select("select * from FSA_REGION " +
            "WHERE code = #{regionCode} ")
    FsaRegion findOneByRegionCode(@Param("regionCode")String regionCode);

    @Select("update FSA_REGION set REGION_DETAILS = #{appCode} " +
            "WHERE code = #{regionCode} ")
    void updateByCode(@Param("regionCode")String regionCode,@Param("appCode")String appCode);
}
