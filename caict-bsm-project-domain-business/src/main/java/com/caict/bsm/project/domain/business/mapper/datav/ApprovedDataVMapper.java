package com.caict.bsm.project.domain.business.mapper.datav;

import com.caict.bsm.project.system.data.mapper.BasicMapper;
import com.caict.bsm.project.system.model.dto.datav.ApprovedDataVDTO;
import com.caict.bsm.project.system.model.dto.security.RsbtOrgDTO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by dengsy on 2020-05-18.
 */
@Repository
public interface ApprovedDataVMapper extends BasicMapper<Object> {

    /**
     * 查询所有运营商
     * */
    @Select("select distinct c.*, d.type as userType " +
            "from RSBT_STATION b " +
            " LEFT JOIN RSBT_ORG c ON b.ORG_CODE = c.ORG_CODE LEFT JOIN RSBT_ORG_APPENDIX d ON c.GUID = d.GUID")
    List<RsbtOrgDTO> findApprovedDataOrg();

    /**
     * 信号交互数据统计
     * */
    @Select("select count(*) as sumApproved,c.gen_num || 'G' as dataType,b.org_code as orgType\n" +
            "from RSBT_LICENSE a left join RSBT_STATION b on a.station_guid = b.guid " +
            "   LEFT JOIN RSBT_STATION_APPENDIX c ON b.GUID = c.GUID " +
            " where b.org_code = #{orgCode} " +
            " and c.gen_num = #{dataType} " +
            "and c.is_sync = '1' " +
            " group by c.gen_num,b.org_code ")
    List<ApprovedDataVDTO> ApprovedDataGenNumStatistic(@Param("orgCode") String orgCode, @Param("dataType") String dataType);

    /**
     * 制式交互数据统计
     * */
    @Select(" select count(*) as sumApproved,c.net_ts as dataType,b.org_code as orgType\n" +
            "from RSBT_LICENSE a left join RSBT_STATION b on a.station_guid = b.guid " +
            " LEFT JOIN RSBT_NET c ON b.NET_GUID = c.GUID " +
            "left join RSBT_STATION_APPENDIX d on a.station_guid = d.guid " +
            "where b.ORG_CODE = #{orgCode} " +
            "and c.net_ts = #{dataType} " +
            "and d.is_sync = '1' " +
            "group by c.net_ts,b.org_code ")
    List<ApprovedDataVDTO> ApprovedDataNetNumStatistic(@Param("orgCode") String orgCode, @Param("dataType") String dataType);

    @Select("select count(*) from RSBT_STATION left join RSBT_STATION_APPENDIX ON RSBT_STATION.GUID = RSBT_STATION_APPENDIX.GUID" +
            " where RSBT_STATION.ORG_CODE = #{orgCode} and RSBT_STATION_APPENDIX.gen_num = #{dataType} ")
    int getStationCountByGen(@Param("orgCode") String orgCode, @Param("dataType") String dataType);

    @Select("select count(*) from RSBT_STATION left join RSBT_NET ON RSBT_STATION.NET_GUID = RSBT_NET.GUID " +
            " where RSBT_STATION.ORG_CODE = #{orgCode} and RSBT_NET.net_ts = #{dataType} ")
    int getStationCountByNet(@Param("orgCode") String orgCode, @Param("dataType") String dataType);
}
