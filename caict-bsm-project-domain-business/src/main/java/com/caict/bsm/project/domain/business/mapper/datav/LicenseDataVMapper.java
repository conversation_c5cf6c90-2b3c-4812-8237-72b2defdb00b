package com.caict.bsm.project.domain.business.mapper.datav;

import com.caict.bsm.project.system.data.mapper.BasicMapper;
import com.caict.bsm.project.system.model.dto.datav.LicenseDataVDTO;
import com.caict.bsm.project.system.model.dto.security.RsbtOrgDTO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by dengsy on 2020-05-18.
 */
@Repository
public interface LicenseDataVMapper extends BasicMapper<Object> {

    /**
     * 查询所有运营商
     * */
    @Select("SELECT DISTINCT " +
            "RSBT_ORG_APPENDIX.TYPE AS USERTYPE,RSBT_ORG.ORG_NAME   " +
            "  FROM " +
            "RSBT_LICENSE,RSBT_STATION,RSBT_ORG,RSBT_ORG_APPENDIX " +
            "WHERE " +
            "RSBT_LICENSE.STATION_GUID = RSBT_STATION.GUID " +
            "AND RSBT_STATION.ORG_CODE = RSBT_ORG.ORG_CODE " +
            "AND RSBT_ORG_APPENDIX.GUID = RSBT_ORG.GUID ")
    List<RsbtOrgDTO> findLicenseOrg();

    /**
     * 电子执照统计
     * */
    @Select("SELECT count( * ) AS sumLicense,  " +
            "             RSBT_LICENSE_T.LICENSE_STATE AS licenseState,  " +
            "             RSBT_LICENSE_T.org_type AS orgType, " +
            "             case RSBT_LICENSE_T.org_type when 'mobile' then '移动' " +
            "               when 'telecom' then '电信' " +
            "                 when 'unicom' then '联通' " +
            "when 'railway' then '铁路' " +
            "when 'guangdian' then '广电' end as orgName " +
            "             FROM RSBT_LICENSE  " +
            "             INNER JOIN RSBT_LICENSE_T ON RSBT_LICENSE.GUID = RSBT_LICENSE_T.GUID  " +
            "             INNER JOIN RSBT_STATION_APPENDIX ON RSBT_LICENSE.STATION_GUID = RSBT_STATION_APPENDIX.GUID  " +
            "and (RSBT_LICENSE_T.LICENSE_COUNTY = #{licenseDataVDTO.selectedArea,jdbcType=VARCHAR} or #{licenseDataVDTO.selectedArea,jdbcType=VARCHAR} is null) " +
            "and (RSBT_LICENSE_T.ORG_TYPE = #{licenseDataVDTO.orgType,jdbcType=VARCHAR} or #{licenseDataVDTO.orgType,jdbcType=VARCHAR} is null) " +
            "and (RSBT_LICENSE_T.TECH_TYPE = #{licenseDataVDTO.netType,jdbcType=VARCHAR} or #{licenseDataVDTO.netType,jdbcType=VARCHAR} is null) " +
            "and (RSBT_STATION_APPENDIX.IS_SYNC = #{licenseDataVDTO.isSync,jdbcType=VARCHAR} or #{licenseDataVDTO.isSync,jdbcType=VARCHAR} is null) " +
//            "and RSBT_STATION_APPENDIX.IS_DELETED = '0' " +
            "and  (RSBT_LICENSE_T.LICENSE_COUNTY in (select name from FSA_REGION where parent_id = #{licenseDataVDTO.regionId,jdbcType=VARCHAR}) or #{licenseDataVDTO.regionId,jdbcType=VARCHAR} is null) " +
            "and (RSBT_LICENSE.LICENSE_DATE >= #{licenseDataVDTO.sDateBegin,jdbcType=VARCHAR} or #{licenseDataVDTO.sDateBegin,jdbcType=VARCHAR} is null) " +
            "and (RSBT_LICENSE.LICENSE_DATE <= #{licenseDataVDTO.eDateEnd,jdbcType=VARCHAR} or #{licenseDataVDTO.eDateEnd,jdbcType=VARCHAR} is null) " +
            "             GROUP BY  " +
            "             RSBT_LICENSE_T.LICENSE_STATE,  " +
            "             RSBT_LICENSE_T.org_type")
    List<LicenseDataVDTO> licenseStatistic(@Param("licenseDataVDTO")LicenseDataVDTO licenseDataVDTO);

    /**
     * 电子执照统计
     * */
    @Select("select count(*) AS sumLicense,aa.licensestate AS licenseState,aa.orgType as orgType from fsa_region, " +
            "(SELECT RSBT_LICENSE_T.LICENSE_STATE AS licenseState,  " +
            "                         RSBT_LICENSE_T.org_type AS orgType, " +
            "                         RSBT_LICENSE_T.license_county county, " +
            "                         fsa_region.parent_id, " +
            "                         case RSBT_LICENSE_T.org_type when 'mobile' then '移动' " +
            "                           when 'telecom' then '电信' " +
            "                             when 'unicom' then '联通' " +
            "when 'railway' then '铁路' " +
            "when 'guangdian' then '广电' end as orgName " +
            "                         FROM RSBT_LICENSE,RSBT_LICENSE_T,RSBT_STATION_APPENDIX,fsa_region  " +
            "                         where RSBT_LICENSE.GUID = RSBT_LICENSE_T.GUID  " +
            "                         and RSBT_LICENSE_T.license_county = fsa_region.name " +
            "                         and RSBT_LICENSE.STATION_GUID = RSBT_STATION_APPENDIX.GUID " +
            "and (RSBT_LICENSE_T.ORG_TYPE = #{licenseDataVDTO.orgType,jdbcType=VARCHAR} or #{licenseDataVDTO.orgType,jdbcType=VARCHAR} is null) " +
            "and (RSBT_LICENSE_T.TECH_TYPE = #{licenseDataVDTO.netType,jdbcType=VARCHAR} or #{licenseDataVDTO.netType,jdbcType=VARCHAR} is null) " +
            "and (RSBT_STATION_APPENDIX.IS_SYNC = #{licenseDataVDTO.isSync,jdbcType=VARCHAR} or #{licenseDataVDTO.isSync,jdbcType=VARCHAR} is null) " +
//            "and RSBT_STATION_APPENDIX.IS_DELETED = '0' " +
            "and (RSBT_LICENSE.LICENSE_DATE >= #{licenseDataVDTO.sDateBegin,jdbcType=VARCHAR} or #{licenseDataVDTO.sDateBegin,jdbcType=VARCHAR} is null) " +
            "and (RSBT_LICENSE.LICENSE_DATE <= #{licenseDataVDTO.eDateEnd,jdbcType=VARCHAR} or #{licenseDataVDTO.eDateEnd,jdbcType=VARCHAR} is null) " +
            "                         ) aa " +
            "                         where fsa_region.id = aa.parent_id " +
            "                         and (fsa_region.name = #{licenseDataVDTO.selectedArea,jdbcType=VARCHAR} or #{licenseDataVDTO.selectedArea,jdbcType=VARCHAR} is null) " +
            "                         GROUP BY  " +
            "                         aa.licenseState,  " +
            "                         aa.orgType")
    List<LicenseDataVDTO> licenseAreaStatistic(@Param("licenseDataVDTO")LicenseDataVDTO licenseDataVDTO);
}
