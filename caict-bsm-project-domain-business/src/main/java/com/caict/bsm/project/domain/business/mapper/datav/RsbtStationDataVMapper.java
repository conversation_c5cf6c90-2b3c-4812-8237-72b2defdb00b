package com.caict.bsm.project.domain.business.mapper.datav;

import com.caict.bsm.project.system.data.mapper.BasicMapper;
import com.caict.bsm.project.system.model.dto.datav.RsbtStationDataVDTO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by dengsy on 2020-05-18.
 */
@Repository
public interface RsbtStationDataVMapper extends BasicMapper<Object> {

    /**
     * 基站数据状态统计
     * */
    @Select("select count(*) as sumStation,a.Data_Type,a.ORG_TYPE as orgType from fsa_region, " +
            "            (select distinct RSBT_STATION.Guid,RSBT_STATION.STAT_DATE_START,RSBT_STATION.STAT_AREA_CODE,  " +
            "            APPROVAL_SCHEDULE_LOG.Data_Type,APPROVAL_SCHEDULE_LOG.ORG_TYPE,fsa_region.parent_id   " +
            "            from RSBT_STATION,RSBT_STATION_T,RSBT_STATION_APPENDIX,APPROVAL_SCHEDULE_LOG,fsa_region   " +
            "            where RSBT_STATION.GUID = RSBT_STATION_T.GUID  " +
            "            AND RSBT_STATION.GUID = RSBT_STATION_APPENDIX.GUID  " +
            "            AND RSBT_STATION_T.ST_C_CODE = APPROVAL_SCHEDULE_LOG.BTS_ID  " +
            "            and RSBT_STATION.Stat_Area_Code = fsa_region.code " +
            "            and (APPROVAL_SCHEDULE_LOG.COUNTY = #{rsbtStationDataVDTO.county,jdbcType=VARCHAR} or #{rsbtStationDataVDTO.county,jdbcType=VARCHAR} is null) " +
            "            and (RSBT_STATION.STAT_DATE_START >= #{rsbtStationDataVDTO.startDate,jdbcType=VARCHAR} or #{rsbtStationDataVDTO.startDate,jdbcType=VARCHAR} is null) " +
            "            and (RSBT_STATION.STAT_DATE_START <= #{rsbtStationDataVDTO.endDate,jdbcType=VARCHAR} or #{rsbtStationDataVDTO.endDate,jdbcType=VARCHAR} is null) " +
            "            and (RSBT_STATION_APPENDIX.IS_SYNC = #{rsbtStationDataVDTO.isSync,jdbcType=VARCHAR} or #{rsbtStationDataVDTO.isSync,jdbcType=VARCHAR} is null) " +
            "            ) a  " +
            "            where fsa_region.id = a.parent_id " +
            "            and (fsa_region.code = #{rsbtStationDataVDTO.regionId,jdbcType=VARCHAR} or #{rsbtStationDataVDTO.regionId,jdbcType=VARCHAR} is null) " +
            "            group by a.ORG_TYPE,Data_Type")
    List<RsbtStationDataVDTO> selectStationStatistics(@Param("rsbtStationDataVDTO") RsbtStationDataVDTO rsbtStationDataVDTO);

    /**
     * 基站数据按市状态统计
     * */
    @Select("select count(*) as sumStation,a.Data_Type,a.ORG_TYPE as orgType from fsa_region, " +
            "            (select Rsbt_Station_Bak.STAT_DATE_START,Rsbt_Station_Bak.county,  " +
            "            Rsbt_Station_Bak.Data_Type,Rsbt_Station_Bak.ORG_TYPE,fsa_region.parent_id  " +
            "            from Rsbt_Station_Bak,fsa_region  " +
            "            where Rsbt_Station_Bak.county = fsa_region.name " +
            "            and (Rsbt_Station_Bak.ORG_TYPE = #{rsbtStationDataVDTO.orgType,jdbcType=VARCHAR} or #{rsbtStationDataVDTO.orgType,jdbcType=VARCHAR} is null) " +
            "            and (Rsbt_Station_Bak.STAT_DATE_START >= #{rsbtStationDataVDTO.startDate,jdbcType=VARCHAR} or #{rsbtStationDataVDTO.startDate,jdbcType=VARCHAR} is null) " +
            "            and (Rsbt_Station_Bak.STAT_DATE_START <= #{rsbtStationDataVDTO.endDate,jdbcType=VARCHAR} or #{rsbtStationDataVDTO.endDate,jdbcType=VARCHAR} is null) " +
            "            and (Rsbt_Station_Bak.IS_SYNC = #{rsbtStationDataVDTO.isSync,jdbcType=VARCHAR} or #{rsbtStationDataVDTO.isSync,jdbcType=VARCHAR} is null) " +
            "            ) a  " +
            "            where fsa_region.id = a.parent_id " +
            "            and (fsa_region.name = #{rsbtStationDataVDTO.county,jdbcType=VARCHAR} or #{rsbtStationDataVDTO.county,jdbcType=VARCHAR} is null) " +
            "            group by a.ORG_TYPE,Data_Type")
    List<RsbtStationDataVDTO> selectAreaStationStatistics(@Param("rsbtStationDataVDTO") RsbtStationDataVDTO rsbtStationDataVDTO);

    /**
     * 基站数据制式统计
     * */
    @Select("select count(*) as sumStation,a.TECH_TYPE AS DATATYPE,a.ORG_TYPE as orgType from fsa_region, " +
            "            (select distinct RSBT_STATION.Guid,RSBT_STATION.STAT_DATE_START,RSBT_STATION.STAT_AREA_CODE,  " +
            "            APPROVAL_SCHEDULE_LOG.TECH_TYPE,APPROVAL_SCHEDULE_LOG.ORG_TYPE,fsa_region.parent_id   " +
            "            from RSBT_STATION,RSBT_STATION_T,RSBT_STATION_APPENDIX,APPROVAL_SCHEDULE_LOG,fsa_region   " +
            "            where RSBT_STATION.GUID = RSBT_STATION_T.GUID  " +
            "            AND RSBT_STATION.GUID = RSBT_STATION_APPENDIX.GUID  "+
            "            AND RSBT_STATION_T.ST_C_CODE = APPROVAL_SCHEDULE_LOG.BTS_ID  " +
            "            and RSBT_STATION.Stat_Area_Code = fsa_region.code " +
            "            and (APPROVAL_SCHEDULE_LOG.COUNTY = #{rsbtStationDataVDTO.county,jdbcType=VARCHAR} or #{rsbtStationDataVDTO.county,jdbcType=VARCHAR} is null) " +
            "            and (RSBT_STATION.STAT_DATE_START >= #{rsbtStationDataVDTO.startDate,jdbcType=VARCHAR} or #{rsbtStationDataVDTO.startDate,jdbcType=VARCHAR} is null) " +
            "            and (RSBT_STATION.STAT_DATE_START <= #{rsbtStationDataVDTO.endDate,jdbcType=VARCHAR} or #{rsbtStationDataVDTO.endDate,jdbcType=VARCHAR} is null) " +
            "            and (RSBT_STATION_APPENDIX.IS_SYNC = #{rsbtStationDataVDTO.isSync,jdbcType=VARCHAR} or #{rsbtStationDataVDTO.isSync,jdbcType=VARCHAR} is null) " +
            "            ) a  " +
            "            where fsa_region.id = a.parent_id " +
            "            and (fsa_region.code = #{rsbtStationDataVDTO.regionId,jdbcType=VARCHAR} or #{rsbtStationDataVDTO.regionId,jdbcType=VARCHAR} is null) " +
            "            group by a.ORG_TYPE,TECH_TYPE")
    List<RsbtStationDataVDTO> selectStationNetSum(@Param("rsbtStationDataVDTO") RsbtStationDataVDTO rsbtStationDataVDTO);

    /**
     * 基站数据制式统计
     * */
    @Select("select count(*) as sumStation,a.TECH_TYPE AS DATATYPE,a.ORG_TYPE as orgType from fsa_region, " +
            "            (select distinct RSBT_STATION.Guid,RSBT_STATION.STAT_DATE_START,RSBT_STATION.STAT_AREA_CODE,  " +
            "            APPROVAL_SCHEDULE_LOG.TECH_TYPE,APPROVAL_SCHEDULE_LOG.ORG_TYPE,fsa_region.parent_id   " +
            "            from RSBT_STATION,RSBT_STATION_T,RSBT_STATION_APPENDIX,APPROVAL_SCHEDULE_LOG,fsa_region   " +
            "            where RSBT_STATION.GUID = RSBT_STATION_T.GUID  " +
            "            AND RSBT_STATION_T.ST_C_CODE = APPROVAL_SCHEDULE_LOG.BTS_ID  " +
            "            AND RSBT_STATION.GUID = RSBT_STATION_APPENDIX.GUID  "+
            "            and RSBT_STATION.Stat_Area_Code = fsa_region.code " +
            "            and (APPROVAL_SCHEDULE_LOG.COUNTY = #{rsbtStationDataVDTO.county,jdbcType=VARCHAR} or #{rsbtStationDataVDTO.county,jdbcType=VARCHAR} is null) " +
            "            and (APPROVAL_SCHEDULE_LOG.ORG_TYPE = #{rsbtStationDataVDTO.orgType,jdbcType=VARCHAR} or #{rsbtStationDataVDTO.orgType,jdbcType=VARCHAR} is null) " +
            "            and (RSBT_STATION.STAT_DATE_START >= #{rsbtStationDataVDTO.startDate,jdbcType=VARCHAR} or #{rsbtStationDataVDTO.startDate,jdbcType=VARCHAR} is null) " +
            "            and (RSBT_STATION.STAT_DATE_START <= #{rsbtStationDataVDTO.endDate,jdbcType=VARCHAR} or #{rsbtStationDataVDTO.endDate,jdbcType=VARCHAR} is null) " +
            "            and (RSBT_STATION_APPENDIX.IS_SYNC = #{rsbtStationDataVDTO.isSync,jdbcType=VARCHAR} or #{rsbtStationDataVDTO.isSync,jdbcType=VARCHAR} is null) " +
            "            ) a  " +
            "            where fsa_region.id = a.parent_id " +
            "            and (fsa_region.name = #{rsbtStationDataVDTO.county,jdbcType=VARCHAR} or #{rsbtStationDataVDTO.county,jdbcType=VARCHAR} is null) " +
            "            group by a.ORG_TYPE,TECH_TYPE")
    List<RsbtStationDataVDTO> selectAreaStationNetSum(@Param("rsbtStationDataVDTO") RsbtStationDataVDTO rsbtStationDataVDTO);

//    /**
//     * 获取所有制式
//     * */
//    @Select({"<script>",
//            "select distinct APPROVAL_SCHEDULE_LOG.TECH_TYPE  " +
//            "from RSBT_STATION " +
//            "LEFT JOIN RSBT_STATION_T ON RSBT_STATION.GUID = RSBT_STATION_T.GUID " +
//            "LEFT JOIN APPROVAL_SCHEDULE_LOG ON RSBT_STATION_T.ST_C_CODE = APPROVAL_SCHEDULE_LOG.BTS_ID  " +
//            "WHERE APPROVAL_SCHEDULE_LOG.COUNTY in ",
//            "<foreach collection='rsbtStationDataVDTO.areas' item='areas' open='(' separator=',' close=')'>",
//            "#{areas}",
//            "</foreach>",
//            " and (RSBT_STATION.STAT_DATE_START &gt;= #{rsbtStationDataVDTO.startDate,jdbcType=VARCHAR} or #{rsbtStationDataVDTO.startDate,jdbcType=VARCHAR} is null) ",
//            "and (RSBT_STATION.STAT_DATE_START  &lt;= #{rsbtStationDataVDTO.endDate,jdbcType=VARCHAR} or #{rsbtStationDataVDTO.endDate,jdbcType=VARCHAR} is null) ",
//            "</script>"})
//    List<String> findDataType(@Param("rsbtStationDataVDTO") RsbtStationDataVDTO rsbtStationDataVDTO);

    /**
     * 获取所有制式
     * */
    @Select({"<script>",
            "select distinct( aa.tech_type) from fsa_region,( " ,
                    "select a.parent_id,b.tech_type from RSBT_STATION t,fsa_region a,rsbt_station_appendix b where t.stat_area_code = a.code " ,
                    "and t.guid = b.guid " ,
                    "and (b.org_type = #{rsbtStationDataVDTO.orgType,jdbcType=VARCHAR} or #{rsbtStationDataVDTO.orgType,jdbcType=VARCHAR} is null) " ,
                    "and (b.is_sync = #{rsbtStationDataVDTO.isSync,jdbcType=VARCHAR} or #{rsbtStationDataVDTO.isSync,jdbcType=VARCHAR} is null) " ,
                    " and (t.STAT_DATE_START &gt;= #{rsbtStationDataVDTO.startDate,jdbcType=VARCHAR} or #{rsbtStationDataVDTO.startDate,jdbcType=VARCHAR} is null) ",
                    "and (t.STAT_DATE_START  &lt;= #{rsbtStationDataVDTO.endDate,jdbcType=VARCHAR} or #{rsbtStationDataVDTO.endDate,jdbcType=VARCHAR} is null) " ,
                    "and b.is_deleted = '0' ",
                    "group by a.parent_id,b.tech_type) aa where aa.parent_id = fsa_region.id " ,
                    "and fsa_region.name in " ,
                    "<foreach collection='rsbtStationDataVDTO.areas' item='areas' open='(' separator=',' close=')'>",
                        "#{areas}",
                    "</foreach> ",
            "</script>"})
    List<String> findDataType(@Param("rsbtStationDataVDTO") RsbtStationDataVDTO rsbtStationDataVDTO);

    /**
     * 获取所有制式
     * */
    @Select({"<script>",
            "select distinct( aa.tech_type) from fsa_region,( " +
                    "select a.id,b.tech_type from RSBT_STATION t,fsa_region a,rsbt_station_appendix b where t.stat_area_code = a.code " +
                    "and t.guid = b.guid " +
                    "and (b.org_type = #{rsbtStationDataVDTO.orgType,jdbcType=VARCHAR} or #{rsbtStationDataVDTO.orgType,jdbcType=VARCHAR} is null) " ,
                    " and (t.STAT_DATE_START &gt;= #{rsbtStationDataVDTO.startDate,jdbcType=VARCHAR} or #{rsbtStationDataVDTO.startDate,jdbcType=VARCHAR} is null) ",
                    "and (t.STAT_DATE_START  &lt;= #{rsbtStationDataVDTO.endDate,jdbcType=VARCHAR} or #{rsbtStationDataVDTO.endDate,jdbcType=VARCHAR} is null) ",
                    "and (b.IS_SYNC = #{rsbtStationDataVDTO.isSync,jdbcType=VARCHAR} or #{rsbtStationDataVDTO.isSync,jdbcType=VARCHAR} is null) ",
                    "group by a.id,b.tech_type) aa where aa.id = fsa_region.id " +
                    "and fsa_region.name in " +
                    "<foreach collection='rsbtStationDataVDTO.areas' item='areas' open='(' separator=',' close=')'>",
                        "#{areas}",
                    "</foreach> ",
            "</script>"})
    List<String> findAreaDataType(@Param("rsbtStationDataVDTO") RsbtStationDataVDTO rsbtStationDataVDTO);


//    /**
//     * 新增制式统计
//     * */
//    @Select({"<script>",
//            "select count(*) as sumStation,a.TECH_TYPE as dataType,COUNTY ",
//            "from (SELECT DISTINCT RSBT_STATION.GUID, RSBT_STATION.STAT_DATE_START, RSBT_STATION_APPENDIX.COUNTY AS COUNTY, APPROVAL_SCHEDULE_LOG.ORG_TYPE, RSBT_STATION_APPENDIX.TECH_TYPE AS TECH_TYPE",
//            "FROM RSBT_STATION" +
//                    " LEFT JOIN RSBT_STATION_T ON RSBT_STATION_T.GUID = RSBT_STATION.GUID" +
//                    " LEFT JOIN RSBT_STATION_APPENDIX ON RSBT_STATION_APPENDIX.GUID = RSBT_STATION.GUID" +
//                    " LEFT JOIN APPROVAL_SCHEDULE_LOG ON RSBT_STATION_T.ST_C_CODE = APPROVAL_SCHEDULE_LOG.BTS_ID ",
//            "WHERE APPROVAL_SCHEDULE_LOG.COUNTY in ",
//            "<foreach collection='rsbtStationDataVDTO.areas' item='areas' open='(' separator=',' close=')'>",
//            "#{areas}",
//            "</foreach> ",
//            " and (RSBT_STATION.STAT_DATE_START &gt;= #{rsbtStationDataVDTO.startDate,jdbcType=VARCHAR} or #{rsbtStationDataVDTO.startDate,jdbcType=VARCHAR} is null) ",
//            "and (RSBT_STATION.STAT_DATE_START &lt;= #{rsbtStationDataVDTO.endDate,jdbcType=VARCHAR} or #{rsbtStationDataVDTO.endDate,jdbcType=VARCHAR} is null) ) a ",
//            "group by COUNTY,TECH_TYPE",
//            "</script>"})
//    List<RsbtStationDataVDTO> selectSingleStastic(@Param("rsbtStationDataVDTO") RsbtStationDataVDTO rsbtStationDataVDTO);

    /**
     * 新增制式统计
     * */
    @Select({"<script>",
            "select aa.total sumStation,fsa_region.name county,aa.tech_type dataType from fsa_region,( " ,
                    "select count(*) total,a.parent_id,b.tech_type from RSBT_STATION t,fsa_region a,rsbt_station_appendix b where t.stat_area_code = a.code " ,
                    "and t.guid = b.guid " ,
                    "and (b.org_type = #{rsbtStationDataVDTO.orgType,jdbcType=VARCHAR} or #{rsbtStationDataVDTO.orgType,jdbcType=VARCHAR} is null) " ,
                    "and (b.is_sync = #{rsbtStationDataVDTO.isSync,jdbcType=VARCHAR} or #{rsbtStationDataVDTO.isSync,jdbcType=VARCHAR} is null) " ,
                    "and (t.STAT_DATE_START &gt;= #{rsbtStationDataVDTO.startDate,jdbcType=DATE} or #{rsbtStationDataVDTO.startDate,jdbcType=DATE} is null) ",
                    "and (t.STAT_DATE_START &lt;= #{rsbtStationDataVDTO.endDate,jdbcType=DATE} or #{rsbtStationDataVDTO.endDate,jdbcType=DATE} is null) ",
                    "and b.is_deleted = '0' ",
                    "group by a.parent_id,b.tech_type) aa where aa.parent_id = fsa_region.id " ,
                    "and fsa_region.name in ",
            "<foreach collection='rsbtStationDataVDTO.areas' item='areas' open='(' separator=',' close=')'>",
                "#{areas}",
            "</foreach> ",
            "</script>"})
    List<RsbtStationDataVDTO> selectSingleStastic(@Param("rsbtStationDataVDTO") RsbtStationDataVDTO rsbtStationDataVDTO);

    /**
     * 按区域新增制式统计
     * */
    @Select({"<script>",
            "select aa.total sumStation,fsa_region.name county,aa.tech_type dataType from fsa_region,( " ,
                    "select count(*) total,a.id,b.tech_type from RSBT_STATION t,fsa_region a,rsbt_station_appendix b where t.stat_area_code = a.code " ,
                    "and t.guid = b.guid " ,
                    "and (b.org_type = #{rsbtStationDataVDTO.orgType,jdbcType=VARCHAR} or #{rsbtStationDataVDTO.orgType,jdbcType=VARCHAR} is null) " ,
                    "and (t.STAT_DATE_START &gt;= #{rsbtStationDataVDTO.startDate,jdbcType=DATE} or #{rsbtStationDataVDTO.startDate,jdbcType=DATE} is null) ",
                    "and (t.STAT_DATE_START &lt;= #{rsbtStationDataVDTO.endDate,jdbcType=DATE} or #{rsbtStationDataVDTO.endDate,jdbcType=DATE} is null) ",
                    "and (b.IS_SYNC = #{rsbtStationDataVDTO.isSync,jdbcType=VARCHAR} or #{rsbtStationDataVDTO.isSync,jdbcType=VARCHAR} is null) ",
                    "group by a.id,b.tech_type) aa where aa.id = fsa_region.id " ,
                    "and fsa_region.name in ",
            "<foreach collection='rsbtStationDataVDTO.areas' item='areas' open='(' separator=',' close=')'>",
                "#{areas}",
            "</foreach> ",
            "</script>"})
    List<RsbtStationDataVDTO> selectAreaSingleStastic(@Param("rsbtStationDataVDTO") RsbtStationDataVDTO rsbtStationDataVDTO);


//    /**
//     * 获取所有信号
//     * */
//    @Select({"<script>",
//            "select distinct RSBT_STATION_APPENDIX.GEN_NUM || 'G' " +
//            "from RSBT_STATION " +
//            "LEFT JOIN RSBT_NET ON RSBT_STATION.NET_GUID = RSBT_NET.GUID  " +
//            "LEFT JOIN RSBT_STATION_T ON RSBT_STATION_T.GUID = RSBT_STATION.GUID  " +
//            "LEFT JOIN RSBT_STATION_APPENDIX ON RSBT_STATION_APPENDIX.GUID = RSBT_STATION.GUID  " +
//            "LEFT JOIN ( ",
//            " SELECT * FROM  APPROVAL_SCHEDULE_LOG WHERE COUNTY IN ",
//            "<foreach collection='rsbtStationDataVDTO.areas' item='areas' open='(' separator=',' close=')'>",
//            "#{areas}",
//            "</foreach>",
//            " ) APPROVAL_LOG ON RSBT_STATION_T.ST_C_CODE = APPROVAL_LOG.BTS_ID  " +
//            " WHERE (RSBT_STATION.STAT_DATE_START &gt;= #{rsbtStationDataVDTO.startDate,jdbcType=VARCHAR} or #{rsbtStationDataVDTO.startDate,jdbcType=VARCHAR} is null) ",
//            "and (RSBT_STATION.STAT_DATE_START  &lt;= #{rsbtStationDataVDTO.endDate,jdbcType=VARCHAR} or #{rsbtStationDataVDTO.endDate,jdbcType=VARCHAR} is null) ",
//            "</script>"})
//    List<String> findGenNum(@Param("rsbtStationDataVDTO") RsbtStationDataVDTO rsbtStationDataVDTO);

    /**
     * 获取所有信号
     * */
    @Select({"<script>",
            "select distinct( aa.gen_num) || 'G' from fsa_region,( " ,
                    "select a.parent_id,b.gen_num from RSBT_STATION t,fsa_region a,rsbt_station_appendix b where t.stat_area_code = a.code " ,
                    "and t.guid = b.guid " ,
                    "and (b.org_type = #{rsbtStationDataVDTO.orgType,jdbcType=VARCHAR} or #{rsbtStationDataVDTO.orgType,jdbcType=VARCHAR} is null) " ,
            "and (b.is_sync = #{rsbtStationDataVDTO.isSync,jdbcType=VARCHAR} or #{rsbtStationDataVDTO.isSync,jdbcType=VARCHAR} is null) " ,
            " and (t.STAT_DATE_START &gt;= #{rsbtStationDataVDTO.startDate,jdbcType=VARCHAR} or #{rsbtStationDataVDTO.startDate,jdbcType=VARCHAR} is null) ",
            "and (t.STAT_DATE_START  &lt;= #{rsbtStationDataVDTO.endDate,jdbcType=VARCHAR} or #{rsbtStationDataVDTO.endDate,jdbcType=VARCHAR} is null) ",
            "and b.is_deleted = '0' ",
            "group by a.parent_id,b.gen_num) aa where aa.parent_id = fsa_region.id " ,
                    "and fsa_region.name in " ,
                    "<foreach collection='rsbtStationDataVDTO.areas' item='areas' open='(' separator=',' close=')'>",
            "#{areas}",
            "</foreach> ",
            "</script>"})
    List<String> findGenNum(@Param("rsbtStationDataVDTO") RsbtStationDataVDTO rsbtStationDataVDTO);

    /**
     * 按区域获取所有信号
     * */
    @Select({"<script>",
            "select distinct( aa.gen_num) || 'G' from fsa_region,( " +
                    "select a.id,b.gen_num from RSBT_STATION t,fsa_region a,rsbt_station_appendix b where t.stat_area_code = a.code " +
                    "and t.guid = b.guid " +
                    "and (b.org_type = #{rsbtStationDataVDTO.orgType,jdbcType=VARCHAR} or #{rsbtStationDataVDTO.orgType,jdbcType=VARCHAR} is null) " ,
            " and (t.STAT_DATE_START &gt;= #{rsbtStationDataVDTO.startDate,jdbcType=VARCHAR} or #{rsbtStationDataVDTO.startDate,jdbcType=VARCHAR} is null) ",
            "and (t.STAT_DATE_START  &lt;= #{rsbtStationDataVDTO.endDate,jdbcType=VARCHAR} or #{rsbtStationDataVDTO.endDate,jdbcType=VARCHAR} is null) ",
            "and (b.IS_SYNC = #{rsbtStationDataVDTO.isSync,jdbcType=VARCHAR} or #{rsbtStationDataVDTO.isSync,jdbcType=VARCHAR} is null) ",
            "group by a.id,b.gen_num) aa where aa.id = fsa_region.id " +
                    "and fsa_region.name in " +
                    "<foreach collection='rsbtStationDataVDTO.areas' item='areas' open='(' separator=',' close=')'>",
            "#{areas}",
            "</foreach> ",
            "</script>"})
    List<String> findAreaGenNum(@Param("rsbtStationDataVDTO") RsbtStationDataVDTO rsbtStationDataVDTO);



//    /**
//     * 新增信号统计
//     * */
//    @Select({"<script>",
//            "select count(*) as sumStation,a.GEN_NUM || 'G' as dataType,COUNTY ",
//            "from (SELECT DISTINCT" ,
//            "RSBT_STATION.GUID,RSBT_STATION.STAT_DATE_START,RSBT_STATION_APPENDIX.COUNTY AS COUNTY,RSBT_STATION_APPENDIX.GEN_NUM " ,
//            "FROM RSBT_STATION LEFT JOIN RSBT_STATION_T ON RSBT_STATION.GUID = RSBT_STATION_T.GUID " ,
//            "LEFT JOIN RSBT_STATION_APPENDIX ON RSBT_STATION.GUID = RSBT_STATION_APPENDIX.GUID " ,
//            "and RSBT_STATION_APPENDIX.COUNTY IN ",
//            "<foreach collection='rsbtStationDataVDTO.areas' item='areas' open='(' separator=',' close=')'>",
//            "#{areas}",
//            "</foreach>",
//            "and (RSBT_STATION.STAT_DATE_START &gt;= #{rsbtStationDataVDTO.startDate,jdbcType=VARCHAR} or #{rsbtStationDataVDTO.startDate,jdbcType=VARCHAR} is null) ",
//            "and (RSBT_STATION.STAT_DATE_START &lt;= #{rsbtStationDataVDTO.endDate,jdbcType=VARCHAR} or #{rsbtStationDataVDTO.endDate,jdbcType=VARCHAR} is null) ) a ",
//            "group by COUNTY,GEN_NUM",
//            "</script>"})
//    List<RsbtStationDataVDTO> selectCommStatistic(@Param("rsbtStationDataVDTO") RsbtStationDataVDTO rsbtStationDataVDTO);

    /**
     * 新增信号统计
     * */
    @Select({"<script>",
            "select aa.total sumStation,fsa_region.name county,aa.gen_num || 'G' dataType from fsa_region,( " ,
            "select count(*) total,a.parent_id,b.gen_num from RSBT_STATION t,fsa_region a,rsbt_station_appendix b where t.stat_area_code = a.code " ,
            "and t.guid = b.guid " ,
            "and (b.org_type = #{rsbtStationDataVDTO.orgType,jdbcType=VARCHAR} or #{rsbtStationDataVDTO.orgType,jdbcType=VARCHAR} is null) " ,
            "and (b.is_sync = #{rsbtStationDataVDTO.isSync,jdbcType=VARCHAR} or #{rsbtStationDataVDTO.isSync,jdbcType=VARCHAR} is null) " ,
            "and (t.STAT_DATE_START &gt;= #{rsbtStationDataVDTO.startDate,jdbcType=DATE} or #{rsbtStationDataVDTO.startDate,jdbcType=DATE} is null) ",
            "and (t.STAT_DATE_START &lt;= #{rsbtStationDataVDTO.endDate,jdbcType=DATE} or #{rsbtStationDataVDTO.endDate,jdbcType=DATE} is null) ",
            "and b.is_deleted = '0' ",
            "group by a.parent_id,b.gen_num) aa where aa.parent_id = fsa_region.id " ,
            "and fsa_region.name in ",
            "<foreach collection='rsbtStationDataVDTO.areas' item='areas' open='(' separator=',' close=')'>",
            "#{areas}",
            "</foreach> ",
            "</script>"})
    List<RsbtStationDataVDTO> selectCommStatistic(@Param("rsbtStationDataVDTO") RsbtStationDataVDTO rsbtStationDataVDTO);

    /**
     * 新增信号统计
     * */
    @Select({"<script>",
            "select aa.total sumStation,fsa_region.name county,aa.gen_num || 'G' dataType from fsa_region,( " ,
            "select count(*) total,a.id,b.gen_num from RSBT_STATION t,fsa_region a,rsbt_station_appendix b where t.stat_area_code = a.code " ,
            "and t.guid = b.guid " ,
            "and (b.org_type = #{rsbtStationDataVDTO.orgType,jdbcType=VARCHAR} or #{rsbtStationDataVDTO.orgType,jdbcType=VARCHAR} is null) " ,
            "and (t.STAT_DATE_START &gt;= #{rsbtStationDataVDTO.startDate,jdbcType=DATE} or #{rsbtStationDataVDTO.startDate,jdbcType=DATE} is null) ",
            "and (t.STAT_DATE_START &lt;= #{rsbtStationDataVDTO.endDate,jdbcType=DATE} or #{rsbtStationDataVDTO.endDate,jdbcType=DATE} is null) ",
            "and (b.IS_SYNC = #{rsbtStationDataVDTO.isSync,jdbcType=VARCHAR} or #{rsbtStationDataVDTO.isSync,jdbcType=VARCHAR} is null) ",
            "group by a.id,b.gen_num) aa where aa.id = fsa_region.id " ,
            "and fsa_region.name in ",
            "<foreach collection='rsbtStationDataVDTO.areas' item='areas' open='(' separator=',' close=')'>",
            "#{areas}",
            "</foreach> ",
            "</script>"})
    List<RsbtStationDataVDTO> selectAreaCommStatistic(@Param("rsbtStationDataVDTO") RsbtStationDataVDTO rsbtStationDataVDTO);


//    /**
//     * 获取所有运营商
//     * */
//    @Select({"<script>",
//            "select distinct RSBT_STATION_APPENDIX.ORG_TYPE  ",
//            "from RSBT_STATION INNER JOIN RSBT_STATION_T ON RSBT_STATION.GUID = RSBT_STATION_T.GUID ",
//            "INNER JOIN RSBT_STATION_APPENDIX ON RSBT_STATION.GUID = RSBT_STATION_APPENDIX.GUID " +
//            "and RSBT_STATION_APPENDIX.COUNTY IN ",
//            "<foreach collection='rsbtStationDataVDTO.areas' item='areas' open='(' separator=',' close=')'>",
//            "#{areas}",
//            "</foreach>",
//            " AND (RSBT_STATION.STAT_DATE_START &gt;= #{rsbtStationDataVDTO.startDate,jdbcType=VARCHAR} or #{rsbtStationDataVDTO.startDate,jdbcType=VARCHAR} is null) ",
//            "and (RSBT_STATION.STAT_DATE_START  &lt;= #{rsbtStationDataVDTO.endDate,jdbcType=VARCHAR} or #{rsbtStationDataVDTO.endDate,jdbcType=VARCHAR} is null) ",
//            "</script>"})
//    List<String> findOpera(@Param("rsbtStationDataVDTO") RsbtStationDataVDTO rsbtStationDataVDTO);

    /**
     * 获取所有运营商
     * */
    @Select({"<script>",
            "select distinct( aa.org_type) from fsa_region,( " ,
                    "select a.parent_id,b.org_type from RSBT_STATION t,fsa_region a,rsbt_station_appendix b where t.stat_area_code = a.code " ,
                    "and t.guid = b.guid " ,
                    "and (b.org_type = #{rsbtStationDataVDTO.orgType,jdbcType=VARCHAR} or #{rsbtStationDataVDTO.orgType,jdbcType=VARCHAR} is null) " ,
            "and (b.is_sync = #{rsbtStationDataVDTO.isSync,jdbcType=VARCHAR} or #{rsbtStationDataVDTO.isSync,jdbcType=VARCHAR} is null) " ,
            " and (t.STAT_DATE_START &gt;= #{rsbtStationDataVDTO.startDate,jdbcType=VARCHAR} or #{rsbtStationDataVDTO.startDate,jdbcType=VARCHAR} is null) ",
            "and (t.STAT_DATE_START  &lt;= #{rsbtStationDataVDTO.endDate,jdbcType=VARCHAR} or #{rsbtStationDataVDTO.endDate,jdbcType=VARCHAR} is null) ",
            "and b.is_deleted = '0' ",
            "group by a.parent_id,b.org_type) aa where aa.parent_id = fsa_region.id " ,
                    "and fsa_region.name in " ,
                    "<foreach collection='rsbtStationDataVDTO.areas' item='areas' open='(' separator=',' close=')'>",
            "#{areas}",
            "</foreach> ",
            "</script>"})
    List<String> findOpera(@Param("rsbtStationDataVDTO") RsbtStationDataVDTO rsbtStationDataVDTO);

    /**
     * 按区域获取所有运营商
     * */
    @Select({"<script>",
            "select distinct( aa.org_type) from fsa_region,( " +
                    "select a.id,b.org_type from RSBT_STATION t,fsa_region a,rsbt_station_appendix b where t.stat_area_code = a.code " +
                    "and t.guid = b.guid " +
                    "and (b.org_type = #{rsbtStationDataVDTO.orgType,jdbcType=VARCHAR} or #{rsbtStationDataVDTO.orgType,jdbcType=VARCHAR} is null) " ,
            " and (t.STAT_DATE_START &gt;= #{rsbtStationDataVDTO.startDate,jdbcType=VARCHAR} or #{rsbtStationDataVDTO.startDate,jdbcType=VARCHAR} is null) ",
            "and (t.STAT_DATE_START  &lt;= #{rsbtStationDataVDTO.endDate,jdbcType=VARCHAR} or #{rsbtStationDataVDTO.endDate,jdbcType=VARCHAR} is null) ",
            "and (b.IS_SYNC = #{rsbtStationDataVDTO.isSync,jdbcType=VARCHAR} or #{rsbtStationDataVDTO.isSync,jdbcType=VARCHAR} is null) ",
            "group by a.id,b.org_type) aa where aa.id = fsa_region.id " +
                    "and fsa_region.name in " +
                    "<foreach collection='rsbtStationDataVDTO.areas' item='areas' open='(' separator=',' close=')'>",
            "#{areas}",
            "</foreach> ",
            "</script>"})
    List<String> findAreaOpera(@Param("rsbtStationDataVDTO") RsbtStationDataVDTO rsbtStationDataVDTO);

//    /**
//     * 新增运营商统计
//     * */
//    @Select({"<script>",
//            "select count(*) as sumStation,a.ORG_TYPE as dataType,a.COUNTY ",
//            "from (SELECT DISTINCT RSBT_STATION.GUID, RSBT_STATION.STAT_DATE_START, RSBT_STATION_APPENDIX.COUNTY AS COUNTY, RSBT_STATION_APPENDIX.ORG_TYPE ",
//            "FROM RSBT_STATION LEFT JOIN RSBT_STATION_T ON RSBT_STATION_T.GUID = RSBT_STATION.GUID" +
//                    " LEFT JOIN RSBT_STATION_APPENDIX ON RSBT_STATION.GUID = RSBT_STATION_APPENDIX.GUID ",
//            "WHERE RSBT_STATION_APPENDIX.COUNTY in ",
//            "<foreach collection='rsbtStationDataVDTO.areas' item='areas' open='(' separator=',' close=')'>",
//            "#{areas}",
//            "</foreach>",
//            " and (RSBT_STATION.STAT_DATE_START &gt;= #{rsbtStationDataVDTO.startDate,jdbcType=VARCHAR} or #{rsbtStationDataVDTO.startDate,jdbcType=VARCHAR} is null) ",
//            "and (RSBT_STATION.STAT_DATE_START &lt;= #{rsbtStationDataVDTO.endDate,jdbcType=VARCHAR} or #{rsbtStationDataVDTO.endDate,jdbcType=VARCHAR} is null) ) a ",
//            "group by COUNTY,a.ORG_TYPE",
//            "</script>"})
//    List<RsbtStationDataVDTO> selectOperaStatistic(@Param("rsbtStationDataVDTO") RsbtStationDataVDTO rsbtStationDataVDTO);

    /**
     * 新增运营商统计
     * */
    @Select({"<script>",
            "select aa.total sumStation,fsa_region.name county,aa.org_type dataType from fsa_region,( " ,
            "select count(*) total,a.parent_id,b.org_type from RSBT_STATION t,fsa_region a,rsbt_station_appendix b where t.stat_area_code = a.code " ,
            "and t.guid = b.guid " ,
            "and (b.org_type = #{rsbtStationDataVDTO.orgType,jdbcType=VARCHAR} or #{rsbtStationDataVDTO.orgType,jdbcType=VARCHAR} is null) " ,
            "and (b.is_sync = #{rsbtStationDataVDTO.isSync,jdbcType=VARCHAR} or #{rsbtStationDataVDTO.isSync,jdbcType=VARCHAR} is null) " ,
            "and (t.STAT_DATE_START &gt;= #{rsbtStationDataVDTO.startDate,jdbcType=DATE} or #{rsbtStationDataVDTO.startDate,jdbcType=DATE} is null) ",
            "and (t.STAT_DATE_START &lt;= #{rsbtStationDataVDTO.endDate,jdbcType=DATE} or #{rsbtStationDataVDTO.endDate,jdbcType=DATE} is null) ",
            "and b.is_deleted = '0' ",
            "group by a.parent_id,b.org_type) aa where aa.parent_id = fsa_region.id " ,
            "and fsa_region.name in ",
            "<foreach collection='rsbtStationDataVDTO.areas' item='areas' open='(' separator=',' close=')'>",
            "#{areas}",
            "</foreach> ",
            "</script>"})
    List<RsbtStationDataVDTO> selectOperaStatistic(@Param("rsbtStationDataVDTO") RsbtStationDataVDTO rsbtStationDataVDTO);

    /**
     * 新增运营商统计
     * */
    @Select({"<script>",
            "select aa.total sumStation,fsa_region.name county,aa.org_type dataType from fsa_region,( " ,
            "select count(*) total,a.id,b.org_type from RSBT_STATION t,fsa_region a,rsbt_station_appendix b where t.stat_area_code = a.code " ,
            "and t.guid = b.guid " ,
            "and (b.org_type = #{rsbtStationDataVDTO.orgType,jdbcType=VARCHAR} or #{rsbtStationDataVDTO.orgType,jdbcType=VARCHAR} is null) " ,
            "and (t.STAT_DATE_START &gt;= #{rsbtStationDataVDTO.startDate,jdbcType=DATE} or #{rsbtStationDataVDTO.startDate,jdbcType=DATE} is null) ",
            "and (t.STAT_DATE_START &lt;= #{rsbtStationDataVDTO.endDate,jdbcType=DATE} or #{rsbtStationDataVDTO.endDate,jdbcType=DATE} is null) ",
            "and (b.IS_SYNC = #{rsbtStationDataVDTO.isSync,jdbcType=VARCHAR} or #{rsbtStationDataVDTO.isSync,jdbcType=VARCHAR} is null) ",
            "group by a.id,b.org_type) aa where aa.id = fsa_region.id " ,
            "and fsa_region.name in ",
            "<foreach collection='rsbtStationDataVDTO.areas' item='areas' open='(' separator=',' close=')'>",
            "#{areas}",
            "</foreach> ",
            "</script>"})
    List<RsbtStationDataVDTO> selectAreaOperaStatistic(@Param("rsbtStationDataVDTO") RsbtStationDataVDTO rsbtStationDataVDTO);

}
