package com.caict.bsm.project.domain.business.mapper.datav;

import com.caict.bsm.project.system.data.mapper.BasicMapper;
import com.caict.bsm.project.system.model.dto.datav.StationHistoryDataVDTO;
import com.caict.bsm.project.system.model.dto.security.RsbtOrgDTO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/9/22
 */
@Repository
public interface RsbtStationHistoryDataVMapper extends BasicMapper<Object> {
    @Select("select distinct user_guid as type " +
            "from RSBT_STATION_BAK ")
    List<RsbtOrgDTO> findApprovedDataOrg();

    /**
     * 基站制式历史数据统计
     * */
    @Select("select aa.net_ts netType,aa.data_type dataType,count(*) sumHistory,ORG_TYPE as orgType " +
            "            from fsa_region, " +
            "            (select * from RSBT_STATION_BAK,fsa_region  " +
            "            where RSBT_STATION_BAK.County = fsa_region.name " +
            "            and (RSBT_STATION_BAK.COUNTY = #{stationHistoryDataVDTO.selectedArea,jdbcType=VARCHAR} or #{stationHistoryDataVDTO.selectedArea,jdbcType=VARCHAR} is null) " +
            "            and (RSBT_STATION_BAK.IS_SYNC = #{stationHistoryDataVDTO.isSync,jdbcType=VARCHAR} or #{stationHistoryDataVDTO.isSync,jdbcType=VARCHAR} is null) " +
            "            and (RSBT_STATION_BAK.BAK_DATE >= #{stationHistoryDataVDTO.sDateBegin,jdbcType=VARCHAR} or #{stationHistoryDataVDTO.sDateBegin,jdbcType=VARCHAR} is null) " +
            "            and (RSBT_STATION_BAK.BAK_DATE <= #{stationHistoryDataVDTO.eDateEnd,jdbcType=VARCHAR} or #{stationHistoryDataVDTO.eDateEnd,jdbcType=VARCHAR} is null) " +
            ") aa " +
            "            where fsa_region.id = aa.parent_id " +
            "            and (fsa_region.code = #{stationHistoryDataVDTO.regionId,jdbcType=VARCHAR} or #{stationHistoryDataVDTO.regionId,jdbcType=VARCHAR} is null) " +
            "            group by aa.net_ts,aa.ORG_TYPE,aa.data_type")
    List<StationHistoryDataVDTO> historyNetStatistic(@Param("stationHistoryDataVDTO") StationHistoryDataVDTO stationHistoryDataVDTO);

    /**
     * 按市基站制式历史数据统计
     * */
    @Select("select aa.net_ts netType,aa.data_type dataType,count(*) sumHistory,ORG_TYPE as orgType " +
            "            from fsa_region, " +
            "            (select * from RSBT_STATION_BAK,fsa_region  " +
            "            where RSBT_STATION_BAK.County = fsa_region.name " +
            "            and (RSBT_STATION_BAK.ORG_TYPE = #{stationHistoryDataVDTO.orgType,jdbcType=VARCHAR} or #{stationHistoryDataVDTO.orgType,jdbcType=VARCHAR} is null) " +
            "            and (RSBT_STATION_BAK.IS_SYNC = #{stationHistoryDataVDTO.isSync,jdbcType=VARCHAR} or #{stationHistoryDataVDTO.isSync,jdbcType=VARCHAR} is null) " +
            "            and (RSBT_STATION_BAK.BAK_DATE >= #{stationHistoryDataVDTO.sDateBegin,jdbcType=VARCHAR} or #{stationHistoryDataVDTO.sDateBegin,jdbcType=VARCHAR} is null) " +
            "            and (RSBT_STATION_BAK.BAK_DATE <= #{stationHistoryDataVDTO.eDateEnd,jdbcType=VARCHAR} or #{stationHistoryDataVDTO.eDateEnd,jdbcType=VARCHAR} is null) " +
            ") aa " +
            "            where fsa_region.id = aa.parent_id " +
            "            and (fsa_region.name = #{stationHistoryDataVDTO.selectedArea,jdbcType=VARCHAR} or #{stationHistoryDataVDTO.selectedArea,jdbcType=VARCHAR} is null) " +
            "            group by aa.net_ts,aa.ORG_TYPE,aa.data_type ")
    List<StationHistoryDataVDTO> historyAreaNetStatistic(@Param("stationHistoryDataVDTO") StationHistoryDataVDTO stationHistoryDataVDTO);

    /**
     * 基站代数数据统计
     * */
    @Select("select aa.gen_num || 'G' netType,aa.data_type dataType,count(*) sumHistory,ORG_TYPE as orgType " +
            "            from fsa_region, " +
            "            (select * from RSBT_STATION_BAK,fsa_region  " +
            "            where RSBT_STATION_BAK.County = fsa_region.name " +
            "            and (RSBT_STATION_BAK.COUNTY = #{stationHistoryDataVDTO.selectedArea,jdbcType=VARCHAR} or #{stationHistoryDataVDTO.selectedArea,jdbcType=VARCHAR} is null) " +
            "            and (RSBT_STATION_BAK.IS_SYNC = #{stationHistoryDataVDTO.isSync,jdbcType=VARCHAR} or #{stationHistoryDataVDTO.isSync,jdbcType=VARCHAR} is null) " +
            "            and (RSBT_STATION_BAK.BAK_DATE >= #{stationHistoryDataVDTO.sDateBegin,jdbcType=VARCHAR} or #{stationHistoryDataVDTO.sDateBegin,jdbcType=VARCHAR} is null) " +
            "            and (RSBT_STATION_BAK.BAK_DATE <= #{stationHistoryDataVDTO.eDateEnd,jdbcType=VARCHAR} or #{stationHistoryDataVDTO.eDateEnd,jdbcType=VARCHAR} is null) " +
            ") aa " +
            "            where fsa_region.id = aa.parent_id " +
            "            and (fsa_region.code = #{stationHistoryDataVDTO.regionId,jdbcType=VARCHAR} or #{stationHistoryDataVDTO.regionId,jdbcType=VARCHAR} is null) " +
            "            group by aa.gen_num,aa.ORG_TYPE,aa.data_type")
    List<StationHistoryDataVDTO> historyGenNumStatistic(@Param("stationHistoryDataVDTO") StationHistoryDataVDTO stationHistoryDataVDTO);

    /**
     * 按市基站代数数据统计
     * */
    @Select("select aa.gen_num || 'G' netType,aa.data_type dataType,count(*) sumHistory,ORG_TYPE as orgType " +
            "            from fsa_region, " +
            "            (select * from RSBT_STATION_BAK,fsa_region  " +
            "            where RSBT_STATION_BAK.County = fsa_region.name " +
            "            and (RSBT_STATION_BAK.ORG_TYPE = #{stationHistoryDataVDTO.orgType,jdbcType=VARCHAR} or #{stationHistoryDataVDTO.orgType,jdbcType=VARCHAR} is null) " +
            "            and (RSBT_STATION_BAK.IS_SYNC = #{stationHistoryDataVDTO.isSync,jdbcType=VARCHAR} or #{stationHistoryDataVDTO.isSync,jdbcType=VARCHAR} is null) " +
            "            and (RSBT_STATION_BAK.BAK_DATE >= #{stationHistoryDataVDTO.sDateBegin,jdbcType=VARCHAR} or #{stationHistoryDataVDTO.sDateBegin,jdbcType=VARCHAR} is null) " +
            "            and (RSBT_STATION_BAK.BAK_DATE <= #{stationHistoryDataVDTO.eDateEnd,jdbcType=VARCHAR} or #{stationHistoryDataVDTO.eDateEnd,jdbcType=VARCHAR} is null) " +
            ") aa " +
            "            where fsa_region.id = aa.parent_id " +
            "            and (fsa_region.name = #{stationHistoryDataVDTO.selectedArea,jdbcType=VARCHAR} or #{stationHistoryDataVDTO.selectedArea,jdbcType=VARCHAR} is null) " +
            "            group by aa.gen_num,aa.ORG_TYPE,aa.data_type ")
    List<StationHistoryDataVDTO> historyAreaGenNumStatistic(@Param("stationHistoryDataVDTO") StationHistoryDataVDTO stationHistoryDataVDTO);

    @Select("select to_char(bak_date,'yyyy') year,to_char(bak_date,'Q') season,COUNT(*) sumHistory from RSBT_STATION_BAK " +
            "where to_char(bak_date,'yyyy') >= #{yearBegin,jdbcType=VARCHAR}  " +
            "and to_char(bak_date,'yyyy') <= #{yearNow,jdbcType=VARCHAR}  " +
            "and (COUNTY = #{stationHistoryDataVDTO.selectedArea,jdbcType=VARCHAR} or #{stationHistoryDataVDTO.selectedArea,jdbcType=VARCHAR} is null) " +
            "and (NET_TS = #{stationHistoryDataVDTO.netType,jdbcType=VARCHAR} or #{stationHistoryDataVDTO.netType,jdbcType=VARCHAR} is null) " +
            "group by to_char(bak_date,'yyyy'),to_char(bak_date,'Q') " +
            "order by to_char(bak_date,'yyyy'),to_char(bak_date,'Q') ")
    List<StationHistoryDataVDTO> historyMonthStatistic(@Param("stationHistoryDataVDTO") StationHistoryDataVDTO stationHistoryDataVDTO, @Param("yearNow") String yearNow, @Param("yearBegin") String yearBegin);
}
