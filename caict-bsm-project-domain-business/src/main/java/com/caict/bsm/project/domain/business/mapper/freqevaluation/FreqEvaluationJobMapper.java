package com.caict.bsm.project.domain.business.mapper.freqevaluation;

import com.caict.bsm.project.system.data.mapper.BasicMapper;
import com.caict.bsm.project.system.model.dto.business.freqevaluation.FreqEvaluationJobDTO;
import com.caict.bsm.project.system.model.entity.business.freqevaluation.FreqEvaluationJob;
import com.caict.bsm.project.system.model.vo.freqevaluation.FreqEvaluationJobVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @description 省（自治区、直辖市）公众移动通信系统无线电频率使用率评价job
 * @date 2023年8月24日 11点13分
 */
@Repository
public interface FreqEvaluationJobMapper extends BasicMapper<FreqEvaluationJob> {

    /**
     * 根据条件查询
     *
     * @param vo vo
     * @return list
     */
    @Select("<script>" +
            "select * from (select a.*,ROWNUM rn from (select * from BSM_FREQ_EVALUATION_JOB j where 1 = 1 " +
            "   <if test='vo.type != null and vo.type != \"\"'>" +
            "       AND j.TYPE = #{vo.type,jdbcType=VARCHAR} </if> " +
            "   <if test='vo.jobName != null and vo.jobName != \"\"'>" +
            "       AND j.JOB_NAME = #{vo.jobName,jdbcType=VARCHAR} </if> " +
            "<if test='vo.startTime != null'>" +
            "    AND j.CREATE_DATE &gt;= #{vo.startTime,jdbcType=DATE}</if> " +
            "<if test='vo.endTime != null'>" +
            "    AND j.CREATE_DATE &lt;= #{vo.endTime,jdbcType=DATE}</if> " +
            " order by j.CREATE_DATE desc ) a " +
            "where ROWNUM &lt;= #{rowsEnd}) where rn &gt;= #{rowsStart}</script>")
    List<FreqEvaluationJobDTO> findAllByWhere(@Param("rowsStart") int rowsStart, @Param("rowsEnd") int rowsEnd, @Param("vo") FreqEvaluationJobVO vo);

    /**
     * 根据条件查询
     *
     * @param vo vo
     * @return int
     */
    @Select("<script>" +
            "select count(j.guid) from BSM_FREQ_EVALUATION_JOB j where 1 = 1 " +
            "   <if test='vo.type != null and vo.type != \"\"'>" +
            "       AND j.TYPE = #{vo.type,jdbcType=VARCHAR} </if> " +
            "   <if test='vo.jobName != null and vo.jobName != \"\"'>" +
            "       AND j.JOB_NAME = #{vo.jobName,jdbcType=VARCHAR} </if> " +
            "<if test='vo.startTime != null'>" +
            "    AND j.CREATE_DATE &gt;= #{vo.startTime,jdbcType=DATE}</if> " +
            "<if test='vo.endTime != null'>" +
            "    AND j.CREATE_DATE &lt;= #{vo.endTime,jdbcType=DATE}</if> " +
            " order by j.CREATE_DATE desc </script>")
    int selectCountWhere(@Param("vo") FreqEvaluationJobVO vo);
}
