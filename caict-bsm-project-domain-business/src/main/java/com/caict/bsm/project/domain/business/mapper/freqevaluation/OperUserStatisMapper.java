package com.caict.bsm.project.domain.business.mapper.freqevaluation;

import com.caict.bsm.project.system.data.mapper.BasicMapper;
import com.caict.bsm.project.system.model.dto.business.freqevaluation.OperUserStatisDTO;
import com.caict.bsm.project.system.model.entity.business.freqevaluation.OperUserStatis;
import com.caict.bsm.project.system.model.vo.freqevaluation.OperUserStatisVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @description 省（自治区、直辖市）公众移动通信系统无线电频率使用率评价结果
 * @date 2023年8月24日 11点13分
 */
@Repository
public interface OperUserStatisMapper extends BasicMapper<OperUserStatis> {

    /**
     * 查询数据
     *
     * @param vo vo
     * @return list
     */
    @Select("<script>select r.*, sr.NAME areaName from BSM_OPER_USER_STATIS r left join FSA_REGION sr on r.ORG_AREA_CODE = sr.CODE where 1=1 " +
            " and r.JOB_GUID = #{vo.jobGuid} " +
            "<if test='vo.orgType != null and vo.orgType != \"\"'> and r.ORG_TYPE = #{vo.orgType}</if>" +
            "<if test='vo.startDate != null and vo.startDate != \"\"'>" +
            " AND r.CREATE_DATE &gt;= to_date(#{vo.startDate,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')</if> " +
            "<if test='vo.endDate != null and vo.endDate != \"\"'>" +
            " AND r.CREATE_DATE &lt;= to_date(#{vo.endDate,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss')</if> " +
            " order by r.ORG_LEVEL </script>")
    List<OperUserStatisDTO> searchOperUserStatis(@Param("vo") OperUserStatisVO vo);

}
