package com.caict.bsm.project.domain.business.mapper.keystation;

import com.caict.bsm.project.system.data.mapper.BasicMapper;
import com.caict.bsm.project.system.model.dto.business.keystation.StationInterfereDTO;
import com.caict.bsm.project.system.model.dto.business.transfer.ApprovalScheduleLogDTO;
import com.caict.bsm.project.system.model.dto.business.transfer.TransportJobDTO;
import com.caict.bsm.project.system.model.entity.business.keystation.FisStation;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/9/30
 */
@Repository
public interface StationInterfereMapper extends BasicMapper<FisStation> {

    @Select("select TRANSPORT_JOB.*,APPROVAL_TRANSPORT_JOB.guid as appGuid,FSA_REGION.name as regionCode from TRANSPORT_JOB,APPROVAL_TRANSPORT_JOB,FSA_REGION " +
            "where TRANSPORT_JOB.guid = APPROVAL_TRANSPORT_JOB.job_guid " +
            "and APPROVAL_TRANSPORT_JOB.region_code = FSA_REGION.code " +
            "and APPROVAL_TRANSPORT_JOB.gen_num = '5' " +
            "and (TRANSPORT_JOB.job_name like concat(concat('%',#{transportJobDTO.jobName,jdbcType=VARCHAR}),'%') or #{transportJobDTO.jobName,jdbcType=VARCHAR} is null) " +
            "and (FSA_REGION.id = #{transportJobDTO.regionCode,jdbcType=VARCHAR} or #{transportJobDTO.regionCode,jdbcType=VARCHAR} is null) ")
    List<TransportJobDTO> findListByPage(@Param("transportJobDTO") TransportJobDTO transportJobDTO);

    @Select("select * from APPROVAL_SCHEDULE_LOG where app_guid = #{approvalScheduleLogDTO.appGuid} " +
            "order by is_handle desc")
    List<ApprovalScheduleLogDTO> findResultList(@Param("approvalScheduleLogDTO") ApprovalScheduleLogDTO approvalScheduleLogDTO);
}
