package com.caict.bsm.project.domain.business.mapper.license;


import com.caict.bsm.project.system.data.mapper.BasicMapper;
import com.caict.bsm.project.system.model.dto.business.license.LicenseDTO;
import com.caict.bsm.project.system.model.dto.business.license.LicensePdfDTO;
import com.caict.bsm.project.system.model.dto.business.license.LicenseStatisticDTO;
import com.caict.bsm.project.system.model.dto.business.station.ISectionDTO;
import com.caict.bsm.project.system.model.dto.es.RsbtLicenseEsDTO;
import com.caict.bsm.project.system.model.dto.security.UsersDTO;
import com.caict.bsm.project.system.model.entity.business.license.RsbtLicense;
import com.caict.bsm.project.system.model.entity.business.license.RsbtLicenseT;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/22
 */
@Repository
public interface LicenseMapper extends BasicMapper<RsbtLicense> {

    /**
     * 条件查询总数
     * */
    @Select("select count(*) from BSM_STATION_LICENSE " +
            "LEFT JOIN RSBT_STATION ON BSM_STATION_LICENSE.STATION_GUID = RSBT_STATION.STATION_GUID " +
            "LEFT JOIN RSBT_ORG ON RSBT_STATION.ORG_GUID = RSBT_ORG.ORG_GUID " +
            "LEFT JOIN FSA_USERS ON RSBT_ORG.ORG_USER = FSA_USERS.ID " +
            "LEFT JOIN BSM_APPLYTABLE_STATION ON BSM_APPLYTABLE_STATION.STATION_GUID = RSBT_STATION.STATION_GUID " +
            "LEFT JOIN BSM_APPLYTABLE ON BSM_APPLYTABLE.GUID = BSM_APPLYTABLE_STATION.APPLYTABLE_GUID " +
            "where BSM_STATION_LICENSE.IS_DELETED = 0 and BSM_STATION_LICENSE.LICENSE_CODE is not null " +
            "and (FSA_USERS.TYPE = #{licenseDTO.userType,jdbcType=VARCHAR} or #{licenseDTO.userType,jdbcType=VARCHAR} is null) " +
            "and (RSBT_ORG.ORG_NAME = #{licenseDTO.orgName,jdbcType=VARCHAR} or #{licenseDTO.orgName,jdbcType=VARCHAR} is null) " +
            "and (RSBT_STATION.STAT_NAME like concat(concat('%',#{licenseDTO.stationName,jdbcType=VARCHAR}),'%') or #{licenseDTO.stationName,jdbcType=VARCHAR} is null) " +
            "and (BSM_STATION_LICENSE.LICENSE_END_DATE <= #{licenseDTO.stationStartDate,jdbcType=VARCHAR} or #{licenseDTO.stationStartDate,jdbcType=VARCHAR} is null) " +
            "and (BSM_STATION_LICENSE.LICENSE_CODE like concat(concat('%',#{licenseDTO.licenseCode,jdbcType=VARCHAR}),'%') or #{licenseDTO.licenseCode,jdbcType=VARCHAR} is null) " +
            "and (RSBT_STATION.ST_C_CODE like concat(concat('%',#{licenseDTO.stationCode,jdbcType=VARCHAR}),'%') or #{licenseDTO.stationCode,jdbcType=VARCHAR} is null) " +
            "and (BSM_STATION_LICENSE.LICENSE_STATE = #{licenseDTO.licenseState,jdbcType=VARCHAR} or #{licenseDTO.licenseState,jdbcType=VARCHAR} is null) " +
            "and (RSBT_STATION.NET_TS = #{licenseDTO.netType,jdbcType=VARCHAR} or #{licenseDTO.netType,jdbcType=VARCHAR} is null) ")
    int selectCountWhere(@Param("licenseDTO") LicenseDTO licenseDTO);

    /**
     * 分页条件查询
     * */
    @Select("select RSBT_LICENSE.*,RSBT_STATION.GUID as stationGuid,RSBT_STATION.STAT_NAME AS stationName,RSBT_STATION_T.ST_C_CODE AS stationCode,RSBT_STATION.STAT_ADDR AS location, " +
            "RSBT_NET.NET_TS AS netType,RSBT_STATION.STAT_LG AS longitude,RSBT_STATION.STAT_LA AS latitude,RSBT_STATION.APP_CODE AS applyTableCode,RSBT_ORG.ORG_NAME AS orgName,RSBT_LICENSE_T.LICENSE_STATE AS LICENSESTATE,RSBT_LICENSE_T.IS_DOWNLOAD  " +
            "from RSBT_STATION " +
            "INNER JOIN RSBT_LICENSE ON RSBT_LICENSE.STATION_GUID = RSBT_STATION.GUID " +
            "INNER JOIN RSBT_STATION_T ON RSBT_LICENSE.STATION_GUID = RSBT_STATION_T.GUID " +
            "LEFT JOIN RSBT_NET ON RSBT_STATION.NET_GUID = RSBT_NET.GUID " +
            "LEFT JOIN RSBT_ORG ON RSBT_NET.ORG_GUID = RSBT_ORG.GUID " +
            "LEFT JOIN RSBT_ORG_APPENDIX ON RSBT_ORG.GUID = RSBT_ORG_APPENDIX.GUID " +
            "LEFT JOIN RSBT_APPLY ON RSBT_NET.GUID = RSBT_APPLY.NET_GUID " +
            "INNER JOIN RSBT_LICENSE_T ON RSBT_LICENSE.GUID = RSBT_LICENSE_T.GUID " +
            "INNER JOIN RSBT_STATION_APPENDIX ON RSBT_STATION.GUID = RSBT_STATION_APPENDIX.GUID " +
            "where (RSBT_ORG.ORG_NAME = #{licenseDTO.orgName,jdbcType=VARCHAR} or #{licenseDTO.orgName,jdbcType=VARCHAR} is null) " +
            "and (RSBT_STATION.STAT_NAME like concat(concat('%',#{licenseDTO.stationName,jdbcType=VARCHAR}),'%') or #{licenseDTO.stationName,jdbcType=VARCHAR} is null) " +
            "and (RSBT_LICENSE.APP_CODE like concat(concat('%',#{licenseDTO.applyTableCode,jdbcType=VARCHAR}),'%') or #{licenseDTO.applyTableCode,jdbcType=VARCHAR} is null) " +
            "and  RSBT_STATION.STAT_AREA_CODE in (select CODE from FSA_REGION start with ID = #{usersDTO.regionId,jdbcType=VARCHAR} connect by prior ID = PARENT_ID) " +
            "and (RSBT_LICENSE.License_Date_E <= #{licenseDTO.stationStartDate,jdbcType=DATE} or #{licenseDTO.stationStartDate,jdbcType=DATE} is null) " +
            "and (RSBT_LICENSE.LICENSE_CODE like concat(concat('%',#{licenseDTO.licenseCode,jdbcType=VARCHAR}),'%') or #{licenseDTO.licenseCode,jdbcType=VARCHAR} is null) " +
            "and (RSBT_STATION_T.ST_C_CODE like concat(concat('%',#{licenseDTO.stationCode,jdbcType=VARCHAR}),'%') or #{licenseDTO.stationCode,jdbcType=VARCHAR} is null) " +
            "and (RSBT_LICENSE_T.LICENSE_STATE = #{licenseDTO.licenseState,jdbcType=VARCHAR} or #{licenseDTO.licenseState,jdbcType=VARCHAR} is null) " +
            "and (RSBT_LICENSE_T.IS_DOWNLOAD = #{licenseDTO.isDownload,jdbcType=VARCHAR} or #{licenseDTO.isDownload,jdbcType=VARCHAR} is null) " +
            "and (RSBT_NET.NET_TS = #{licenseDTO.netType,jdbcType=VARCHAR} or #{licenseDTO.netType,jdbcType=VARCHAR} is null) " +
            "and (RSBT_ORG_APPENDIX.TYPE = #{usersDTO.type,jdbcType=VARCHAR} or #{usersDTO.type,jdbcType=VARCHAR} = 'wuwei') " +
            "and (RSBT_STATION_APPENDIX.IS_SYNC = #{licenseDTO.isSync,jdbcType=VARCHAR} or #{licenseDTO.isSync,jdbcType=VARCHAR} is null) " +
            " and (RSBT_STATION.STAT_LA >= #{licenseDTO.minlat,jdbcType=VARCHAR} or #{licenseDTO.minlat,jdbcType=VARCHAR} is null) " +
            "  and (RSBT_STATION.STAT_LA <= #{licenseDTO.maxlat,jdbcType=VARCHAR} or #{licenseDTO.maxlat,jdbcType=VARCHAR} is null) " +
            "  and (RSBT_STATION.STAT_LG >= #{licenseDTO.minlng,jdbcType=VARCHAR} or #{licenseDTO.minlng,jdbcType=VARCHAR} is null) " +
            "  and (RSBT_STATION.STAT_LG <= #{licenseDTO.maxlng,jdbcType=VARCHAR} or #{licenseDTO.maxlng,jdbcType=VARCHAR} is null) " +
            "order by RSBT_LICENSE.LICENSE_CODE ")
    List<LicenseDTO> findAllPageByWhere(@Param("licenseDTO") LicenseDTO licenseDTO,@Param("usersDTO") UsersDTO usersDTO);

    /**
     * 根据基站查询详情
     * */
    @Select("select RSBT_LICENSE.LICENSE_CODE as licenseCode,RSBT_STATION.STAT_NAME as stationName,RSBT_ORG.ORG_NAME as licensee, " +
            "RSBT_ORG.ORG_CODE as licenseeNo,RSBT_STATION.STAT_LG as longitude, " +
            "RSBT_STATION.STAT_LA as latitude,RSBT_STATION.STAT_ADDR as statAddr,RSBT_STATION_T.ST_C_CODE as stationCode, " +
            "RSBT_NET.NET_TS as stationType,RSBT_LICENSE.LICENSE_DATE_B as licenseStartDate, " +
            "RSBT_LICENSE.LICENSE_DATE_E as licenseEndDate, " +
            "(select FILE_NO from FSA_CHECK_RULE where FSA_CHECK_RULE.ORG_GUID = RSBT_net.GUID  " +
            "and FSA_CHECK_RULE.NET_TS = RSBT_NET.NET_TS and rownum = 1 ) as licenceNo,RSBT_STATION.GUID  " +
            "FROM RSBT_LICENSE,RSBT_STATION,RSBT_STATION_T,RSBT_ORG,RSBT_NET  " +
            "where RSBT_STATION.GUID = #{stationGuid} " +
            "and RSBT_STATION.GUID = RSBT_LICENSE.STATION_GUID " +
            "and RSBT_STATION.NET_GUID = RSBT_net.GUID   " +
            "and RSBT_NET.ORG_GUID = RSBT_ORG.GUID " +
            "and RSBT_STATION.GUID = RSBT_STATION_T.GUID")
    @Results({
            @Result(property = "iSectionDTOList", column = "STATION_GUID", many = @Many(select = "com.caict.bsm.project.domain.business.repository.license.LicenseMapper.getISectionDTOList"))}
    )
    LicensePdfDTO findOneFDFByStationGuid(@Param("stationGuid") String stationGuid);

    /**
     * 根据基站数组查询详情
     * */
    @Select({"<script>",
            "select RSBT_LICENSE.LICENSE_CODE as licenseCode,RSBT_STATION.STAT_NAME as stationName,RSBT_ORG.ORG_NAME as licensee,RSBT_ORG.ORG_CODE as licenseeNo,RSBT_STATION.STAT_LG as longitude," ,
                    "            RSBT_STATION.STAT_LA as latitude,RSBT_STATION.STAT_ADDR as statAddr,RSBT_STATION_T.ST_C_CODE as stationCode,RSBT_NET.NET_TS as stationType,RSBT_LICENSE.LICENSE_DATE_B as licenseStartDate," ,
                    "            RSBT_LICENSE.LICENSE_DATE_E as licenseEndDate,(select FILE_NO from FSA_CHECK_RULE where FSA_CHECK_RULE.ORG_GUID = RSBT_net.GUID and FSA_CHECK_RULE.NET_TS = RSBT_NET.NET_TS and rownum = 1 ) as licenceNo,RSBT_STATION.GUID " ,
                    "            FROM RSBT_LICENSE,RSBT_STATION,RSBT_STATION_T,RSBT_ORG,RSBT_NET " ,
                    "            where RSBT_STATION.GUID = RSBT_LICENSE.STATION_GUID " ,
                    "            and RSBT_NET.ORG_GUID = RSBT_ORG.GUID " ,
                    "            and RSBT_STATION.NET_GUID = RSBT_net.GUID " ,
                    "            and RSBT_STATION.GUID = RSBT_STATION_T.GUID ",
            "and RSBT_STATION.GUID in ",
            "<foreach collection='stationGuids' item='stationGuid' open='(' separator=',' close=')'>",
            "#{stationGuid}",
            "</foreach>",
            "</script>"})
    @Results({
            @Result(property = "stationGuid",column = "GUID"),
            @Result(property = "iSectionDTOList", column = "GUID",javaType = List.class, many = @Many(select = "getISectionDTOList"))}
    )
    List<LicensePdfDTO> findOneFDFByStationGuids(@Param("stationGuids") String[] stationGuids);

    /**
     * 根据基站查询详细扇区、设备、参数
     * */
    @Select("select RSBT_FREQ.FREQ_EFB || ' - ' || RSBT_FREQ.FREQ_EFE as freqEf, " +
            "RSBT_FREQ.FREQ_RFB || ' - ' || RSBT_FREQ.FREQ_RFE as freqRf,RSBT_EQU_T.ET_EQU_UPOW as equPow,(RSBT_FREQ.FREQ_EFE - RSBT_FREQ.FREQ_EFB) as freqBand,RSBT_EQU.EQU_AUTH as equAuth, " +
            "RSBT_ANTFEED.ANT_GAIN as antGain,RSBT_ANTFEED.ANT_POLE as antEpole,RSBT_ANTFEED.ANT_HIGHT as antHight  " +
            "from RSBT_ANTFEED,RSBT_EQU,RSBT_EQU_T,RSBT_FREQ,RSBT_EAF  " +
            "where RSBT_EAF.Equ_Guid = RSBT_EQU.guid " +
            "and RSBT_ANTFEED.guid = RSBT_EAF.Ant_Guid  " +
            "and Rsbt_Freq.guid = RSBT_EAF.Freq_Guid " +
            "and RSBT_ANTFEED.STATION_GUID = RSBT_EAF.STATION_GUID and RSBT_EQU.Station_Guid = RSBT_EAF.STATION_GUID and RSBT_FREQ.STATION_GUID = RSBT_EAF.STATION_GUID and RSBT_EAF.STATION_GUID = RSBT_EAF.STATION_GUID " +
            "and RSBT_EQU.guid = RSBT_EQU_T.guid " +
            "and RSBT_EAF.STATION_GUID = #{stationGuid}")
    List<ISectionDTO> getISectionDTOList(@Param("stationGuid") String stationGuid);

    /**
     * 查询出已发，未发，过期，停用的执照数量
     * @return
     */
    @Select("SELECT  " +
            "            SUM(CASE WHEN RSBT_LICENSE_T.LICENSE_STATE=1 THEN 1 ELSE 0 END) alreadyGrant, " +
            "            SUM(CASE WHEN RSBT_STATION_APPENDIX.IS_LICENSE='0' THEN 1 ELSE 0 END) notGrant, " +
            "            SUM(CASE WHEN RSBT_LICENSE_T.LICENSE_STATE=2 THEN 1 ELSE 0 END) overDue, " +
            "            SUM(CASE WHEN RSBT_LICENSE_T.LICENSE_STATE=3 THEN 1 ELSE 0 END) stopUse " +
            "            FROM RSBT_LICENSE,RSBT_LICENSE_T,RSBT_STATION_APPENDIX  " +
            "            WHERE RSBT_LICENSE.GUID=RSBT_LICENSE_T.GUID " +
            "            AND RSBT_LICENSE.STATION_GUID = RSBT_STATION_APPENDIX.GUID " +
            "            AND RSBT_STATION_APPENDIX.IS_SYNC = '1'")
    LicenseStatisticDTO getStatisticCount();


    @Select("select * from Rsbt_license where station_guid = #{stationGuid}")
    RsbtLicense selectByStationGuid(@Param("stationGuid") String stationGuid);

    @Update({"<script>",
            "update RSBT_LICENSE set app_code = #{appCode} ",
            "where RSBT_LICENSE.station_guid in ",
            "<foreach collection='stationGuids' item='stationGuid' open='(' separator=',' close=')'>",
            "#{stationGuid}",
            "</foreach>",
            "</script>"})
    int updateBatchByStationGuid(@Param("stationGuids") List<String> stationGuids,@Param("appCode")String appCode);

    @Update("UPDATE RSBT_LICENSE SET APP_CODE = #{newAppCode} WHERE APP_CODE = #{oldAppCode}")
    int updateAppCode(@Param("oldAppCode")String oldAppCode,@Param("newAppCode")String newAppCode);

    @Select("select RSBT_LICENSE_T.* from RSBT_LICENSE,RSBT_LICENSE_T WHERE RSBT_LICENSE.GUID = RSBT_LICENSE_T.GUID " +
            "AND RSBT_LICENSE.STATION_GUID = #{stationGuid}")
    RsbtLicenseT findLicenseTByStaGuid(@Param("stationGuid")String stationGuid);

    @Update({"<script>",
            "UPDATE RSBT_LICENSE_T SET IS_DOWNLOAD = #{status} ",
            "where RSBT_LICENSE_T.GUID in ( " +
                    "select RSBT_LICENSE_T.guid from RSBT_LICENSE,RSBT_LICENSE_T,rsbt_station where RSBT_LICENSE.guid = RSBT_LICENSE_T.guid " +
                    "and RSBT_LICENSE.station_guid = rsbt_station.guid " +
                    "and rsbt_station.guid in ",
            "<foreach collection='stationGuids' item='stationGuid' open='(' separator=',' close=')'>",
            "#{stationGuid}",
            "</foreach>)",
            "</script>"})
    int updateByStationGuids(@Param("stationGuids")String[] stationGuids,@Param("status")Long status);


    @Select("select RSBT_LICENSE.LICENSE_CODE as licenseCode,RSBT_STATION.STAT_NAME as stationName,RSBT_ORG.ORG_NAME as licensee,RSBT_ORG.ORG_CODE as licenseeNo,RSBT_STATION.STAT_LG as longitude," +
            "            RSBT_STATION.STAT_LA as latitude,RSBT_STATION.STAT_ADDR as statAddr,RSBT_STATION_T.ST_C_CODE as stationCode,RSBT_NET.NET_TS as stationType,RSBT_LICENSE.LICENSE_DATE_B as licenseStartDate," +
            "            RSBT_LICENSE.LICENSE_DATE_E as licenseEndDate,(select FILE_NO from FSA_CHECK_RULE where FSA_CHECK_RULE.ORG_GUID = RSBT_net.GUID and FSA_CHECK_RULE.NET_TS = RSBT_NET.NET_TS and rownum = 1 ) as licenceNo,RSBT_STATION.GUID " +
            "            FROM RSBT_LICENSE,RSBT_LICENSE_T,RSBT_STATION,RSBT_STATION_T,RSBT_STATION_APPENDIX,RSBT_ORG,RSBT_ORG_APPENDIX,RSBT_NET " +
            "            where RSBT_LICENSE.GUID = RSBT_LICENSE_T.GUID " +
            "            and RSBT_STATION.GUID = RSBT_LICENSE.STATION_GUID " +
            "            and RSBT_NET.ORG_GUID = RSBT_ORG.GUID " +
            "            and RSBT_STATION.NET_GUID = RSBT_net.GUID " +
            "            and RSBT_STATION.GUID = RSBT_STATION_T.GUID "+
            "            and RSBT_STATION.GUID = RSBT_STATION_APPENDIX.GUID "+
            "            and RSBT_ORG.GUID = RSBT_ORG_APPENDIX.GUID "+
            "and (RSBT_ORG.ORG_NAME = #{licenseDTO.orgName,jdbcType=VARCHAR} or #{licenseDTO.orgName,jdbcType=VARCHAR} is null) " +
            "and (RSBT_LICENSE.APP_CODE like concat(concat('%',#{licenseDTO.applyTableCode,jdbcType=VARCHAR}),'%') or #{licenseDTO.applyTableCode,jdbcType=VARCHAR} is null) " +
            "and (RSBT_STATION.STAT_NAME like concat(concat('%',#{licenseDTO.stationName,jdbcType=VARCHAR}),'%') or #{licenseDTO.stationName,jdbcType=VARCHAR} is null) " +
            "and  RSBT_STATION.STAT_AREA_CODE in (select CODE from FSA_REGION start with ID = #{usersDTO.regionId,jdbcType=VARCHAR} connect by prior ID = PARENT_ID) " +
            " and  (RSBT_STATION.STAT_AREA_CODE = #{licenseDTO.areaCode,jdbcType=VARCHAR} or #{licenseDTO.areaCode,jdbcType=VARCHAR} is null) " +
            "and (RSBT_LICENSE.License_Date_E <= #{licenseDTO.stationStartDate,jdbcType=DATE} or #{licenseDTO.stationStartDate,jdbcType=DATE} is null) " +
            "and (RSBT_LICENSE.LICENSE_CODE like concat(concat('%',#{licenseDTO.licenseCode,jdbcType=VARCHAR}),'%') or #{licenseDTO.licenseCode,jdbcType=VARCHAR} is null) " +
            "and (RSBT_STATION_T.ST_C_CODE like concat(concat('%',#{licenseDTO.stationCode,jdbcType=VARCHAR}),'%') or #{licenseDTO.stationCode,jdbcType=VARCHAR} is null) " +
            "and (RSBT_LICENSE_T.LICENSE_STATE = #{licenseDTO.licenseState,jdbcType=VARCHAR} or #{licenseDTO.licenseState,jdbcType=VARCHAR} is null) " +
            "and (RSBT_NET.NET_TS = #{licenseDTO.netType,jdbcType=VARCHAR} or #{licenseDTO.netType,jdbcType=VARCHAR} is null) " +
            "and (RSBT_ORG_APPENDIX.TYPE = #{usersDTO.type,jdbcType=VARCHAR} or #{usersDTO.type,jdbcType=VARCHAR} = 'wuwei') " +
            "and (RSBT_STATION_APPENDIX.IS_SYNC = #{licenseDTO.isSync,jdbcType=VARCHAR} or #{licenseDTO.isSync,jdbcType=VARCHAR} is null) " +
            " and (RSBT_STATION.STAT_LA >= #{licenseDTO.minlat,jdbcType=VARCHAR} or #{licenseDTO.minlat,jdbcType=VARCHAR} is null) " +
            "  and (RSBT_STATION.STAT_LA <= #{licenseDTO.maxlat,jdbcType=VARCHAR} or #{licenseDTO.maxlat,jdbcType=VARCHAR} is null) " +
            "  and (RSBT_STATION.STAT_LG >= #{licenseDTO.minlng,jdbcType=VARCHAR} or #{licenseDTO.minlng,jdbcType=VARCHAR} is null) " +
            "  and (RSBT_STATION.STAT_LG <= #{licenseDTO.maxlng,jdbcType=VARCHAR} or #{licenseDTO.maxlng,jdbcType=VARCHAR} is null) ")
    @Results({
            @Result(property = "stationGuid",column = "GUID"),
            @Result(property = "iSectionDTOList", column = "GUID",javaType = List.class, many = @Many(select = "getISectionDTOList"))}
    )
    List<LicensePdfDTO> findPrintLicenseByWhere(@Param("licenseDTO")LicenseDTO licenseDTO,@Param("usersDTO")UsersDTO usersDTO);

    @Select("select count(*) from RSBT_LICENSE ")
    int findCountPushToEs();

    @Select("select RSBT_LICENSE.guid,rsbt_station.GUID stationGuid,rsbt_station.APP_CODE applytablecode, " +
            "rsbt_station.STAT_NAME stationName,rsbt_station_appendix.ORG_TYPE orgName,RSBT_LICENSE.LICENSE_CODE, " +
            "       rsbt_station_T.st_c_code stationCode,RSBT_LICENSE.LICENSE_DATE_B,RSBT_LICENSE.LICENSE_DATE_E, " +
            "       RSBT_LICENSE.LICENSE_DATE,rsbt_station.STAT_ADDR location,RSBT_LICENSE_T.LICENSE_STATE,RSBT_LICENSE_T.IS_DOWNLOAD, " +
            "       rsbt_station.STAT_LA latitude,rsbt_station.STAT_LG longitude,RSBT_STATION_APPENDIX.COUNTY,RSBT_STATION_APPENDIX.TECH_TYPE netType " +
            "       from RSBT_LICENSE left join RSBT_LICENSE_T " +
            "on RSBT_LICENSE.GUID = RSBT_LICENSE_T.GUID " +
            "left join rsbt_station on RSBT_LICENSE.station_guid = rsbt_station.guid " +
            "left join rsbt_station_T on RSBT_LICENSE.station_guid = rsbt_station_t.guid " +
            "left join rsbt_station_appendix on RSBT_LICENSE.station_guid = rsbt_station_appendix.guid ")
    List<RsbtLicenseEsDTO> findByPage(@Param("startRow")int startRow, @Param("endRow")int endRow);
}
