package com.caict.bsm.project.domain.business.mapper.roadtest;

import com.caict.bsm.project.system.data.mapper.BasicMapper;
import com.caict.bsm.project.system.model.dto.roadtest.RoadTestFilesDTO;
import com.caict.bsm.project.system.model.entity.business.roadtest.RoadTestFiles;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * RoadTestFilesMapper.
 *
 * <AUTHOR>
 * @version jdk_8u291
 * @since 5/20/21 3:59 PM
 **/
@Repository
public interface RoadTestFilesMapper extends BasicMapper<RoadTestFiles> {

    /**
     * 查询数据
     *
     * @param roadTestFilesDTO
     * @return
     */
    @Select({"<script>select * from FSA_ROAD_FILES where 1=1" +
            "<if test='roadTestFilesDTO.fileNo!=null and roadTestFilesDTO.fileNo!= \"\"'>" +
            " and FSA_ROAD_FILES.FILE_NO like concat(concat('%',#{roadTestFilesDTO.fileNo,jdbcType=VARCHAR}),'%') " +
            "</if>" +
            "</script>"})
    List<RoadTestFilesDTO> findByRoadNo(@Param("roadTestFilesDTO") RoadTestFilesDTO roadTestFilesDTO);

    /**
     * 根据时间查询数据
     *
     * @param roadTestFiles
     */
    @Select("select * from FSA_ROAD_FILES where 1=1 and FSA_ROAD_FILES.UPLOAD_DATE = TO_DATE(#{roadTestFiles.uploadDateStr,jdbcType=DATE}, 'YYYY-MM-DD HH24:MI:SS')")
    RoadTestFiles selectOneByTime(@Param("roadTestFiles") RoadTestFiles roadTestFiles);
}
