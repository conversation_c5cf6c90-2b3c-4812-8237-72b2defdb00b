package com.caict.bsm.project.domain.business.mapper.sta;


import com.caict.bsm.project.system.data.mapper.BasicMapper;
import com.caict.bsm.project.system.model.dto.business.license.LicensePdfDTO;
import com.caict.bsm.project.system.model.dto.business.sta.StaLicenseDTO;
import com.caict.bsm.project.system.model.dto.business.sta.StaListTempDTO;
import com.caict.bsm.project.system.model.dto.business.station.ISectionDTO;
import com.caict.bsm.project.system.model.entity.business.sta.StaAntenna;
import com.caict.bsm.project.system.model.entity.business.sta.StaEqu;
import com.caict.bsm.project.system.model.entity.business.sta.StaFreq;
import com.caict.bsm.project.system.model.entity.business.sta.StaStation;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface StaMapper extends BasicMapper<StaStation> {
    @Select("SELECT * FROM (\n" +
            "SELECT A.*,ROWNUM RNUM FROM\n" +
            "(SELECT STA_STATION.GUID AS GUID,\n" +
            "STA_STATION.DISTR_CODE AS DISTRCODE,\n" +
            "STA_STATION.STA_TYPE AS STATYPE,\n" +
            "STA_STATION.PIPE_NUM AS PIPENUM,\n" +
            "STA_STATION.STA_NAME AS STANAME,\n" +
            "STA_STATION.IDEN_CODE AS IDENCODE,\n" +
            "STA_STATION.USER_NAME AS USERNAME,\n" +
            "STA_STATION.LOCATION AS LOCATION,\n" +
            "STA_STATION.LONGITUDE AS LONGITUDE,\n" +
            "STA_STATION.LATITUDE AS LATITUDE,\n" +
            "STA_STATION.DETAIL AS DETAIL,\n" +
            "STA_STATION.SPECIAL_CASE AS SPECIALCASE,\n" +
            "STA_STATION.APP_CODE AS APPCODE,\n" +
            "STA_STATION.STAT_APP_TYPE AS STATAPPTYPE,\n" +
            "STA_STATION.STAT_TDI AS STATTDI,\n" +
            "STA_STATION.STAT_WORK AS STATWORK,\n" +
            "STA_STATION.STAT_STATUS AS STATSTATUS,\n" +
            "STA_STATION.STAT_EQU_SUM AS STATEQUSUM,\n" +
            "STA_LICENSE.LINCESE_CODE AS LINCESECODE,\n" +
            "STA_LICENSE.GMT_CREATE AS GMTCREATE,\n" +
            "STA_LICENSE.LINCESE_END_DATE AS LINCESEENDDATE\n" +
            "FROM STA_STATION INNER JOIN STA_LICENSE ON STA_STATION.GUID = STA_LICENSE.STATION_GUID " +
            "AND (STA_STATION.STA_NAME LIKE concat(concat('%',#{stationName,jdbcType=VARCHAR}),'%') OR #{stationName,jdbcType=VARCHAR} is null) \n"+
            "AND (STA_STATION.IDEN_CODE LIKE concat(concat('%',#{pipeNum,jdbcType=VARCHAR}),'%') OR #{pipeNum,jdbcType=VARCHAR} is null) \n"+
            "AND (STA_LICENSE.LINCESE_CODE LIKE concat(concat('%',#{licenseCode,jdbcType=VARCHAR}),'%') OR #{licenseCode,jdbcType=VARCHAR} is null) \n"+
            "AND (STA_LICENSE.GMT_CREATE >= #{startDate,jdbcType=DATE} OR #{startDate,jdbcType=DATE} is null ) \n"+
            "AND (STA_LICENSE.GMT_CREATE <= #{endDate,jdbcType=DATE} OR #{endDate,jdbcType=DATE} is null) \n"+
            ") A WHERE ROWNUM <= #{endRows}) WHERE RNUM>=#{startRows}")
    List<StaLicenseDTO> getListVOByPage(@Param("startRows") int startRows, @Param("endRows") int endRows, @Param("stationName") String stationName, @Param("pipeNum") String pipeNum, @Param("licenseCode") String licenseCode, @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    @Select("SELECT COUNT(*) " +
            "FROM STA_STATION LEFT JOIN STA_LICENSE ON STA_STATION.GUID = STA_LICENSE.STATION_GUID " +
            "AND (STA_STATION.STA_NAME LIKE concat(concat('%',#{stationName,jdbcType=VARCHAR}),'%') OR #{stationName,jdbcType=VARCHAR} is null) \n"+
            "AND (STA_STATION.IDEN_CODE LIKE concat(concat('%',#{pipeNum,jdbcType=VARCHAR}),'%') OR #{pipeNum,jdbcType=VARCHAR} is null) \n"+
            "AND (STA_LICENSE.LINCESE_CODE LIKE concat(concat('%',#{licenseCode,jdbcType=VARCHAR}),'%') OR #{licenseCode,jdbcType=VARCHAR} is null) \n"+
            "AND (STA_LICENSE.GMT_CREATE >= #{startDate,jdbcType=DATE} OR #{startDate,jdbcType=DATE} is null) \n"+
            "AND (STA_LICENSE.GMT_CREATE <= #{endDate,jdbcType=DATE} OR #{endDate,jdbcType=DATE} is null) \n"
    )
    int getStaListNum(@Param("stationName") String stationName, @Param("pipeNum") String pipeNum, @Param("licenseCode") String licenseCode, @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    @Select("select s.guid as stationId,s.distr_code as distrCode,s.sta_type as staType,\n" +
            "s.pipe_num as pipeNum,s.sta_name as staName,s.iden_code as idenCode,s.user_name as userName,s.location as location,\n" +
            "s.longitude as longitude,s.latitude as latitude,s.detail as detail,s.special_case as specialCase,s.app_code as appCode,\n" +
            "s.stat_app_type as statAppType,s.stat_tdi as statTdi,s.stat_work as statWork,s.stat_status as statStatus,s.stat_equ_sum as statEquSum,\n" +
            "l.guid as licenseGuid,l.lincese_code as linceseCode,l.gmt_create as gmtCreate,l.gmt_modified as gmtModified,l.LINCESE_STATE as linceseState,\n" +
            "l.LINCESE_START_DATE as linceseStartDate,l.LINCESE_END_DATE as linceseEndDate,l.LICENSE_AUTH as licenseAuth,s.org_code as orgCode\n" +
            "from sta_station s,sta_license l\n" +
            "where s.guid=#{stationGuid}\n" +
            "and l.station_guid=s.guid")
    StaListTempDTO getList(@Param("stationGuid") String stationGuid);

    @Select("select f.guid as guid,f.station_guid as stationGuid,f.FREQ_EF as freqEf,f.FREQ_RF as  freqRf,f.FREQ_BAND as freqBand,\n" +
            "f.FT_FREQ_CSGN as ftFreqCsgn,f.FREQ_PASS as freqPass\n" +
            "from sta_freq f,sta_eaf r\n" +
            "where r.station_guid=#{stationGuid}\n" +
            "and f.guid=r.freq_guid")
    List<StaFreq> getFreq(@Param("stationGuid") String stationGuid);

    @Select("select e.guid as guid,e.station_guid as stationGuid,e.EQU_MODEL as equModel,e.EQU_AUTH as equAuth,e.EQU_MENU as equMenu,e.EQU_POW as equPow,e.EQU_TYPE as equType,\n" +
            "e.EQU_CODE as equCode,e.EQU_MB as equMb\n" +
            "from sta_equ e,sta_eaf r\n" +
            "where r.station_guid=#{stationGuid}\n" +
            "and e.guid=r.equ_guid")
    List<StaEqu> getEqu(@Param("stationGuid") String stationGuid);

    @Select("select a.guid as  guid,a.station_guid as stationGuid,a.ANT_GAIN as antGain,a.ANT_EPOLE as antEpole,a.ANT_HIGHT as antHight\n" +
            "from sta_antenna a,sta_eaf r\n" +
            "where r.station_guid=#{stationGuid}\n" +
            "and a.guid=r.ant_guid")
    List<StaAntenna> getAntenna(@Param("stationGuid") String stationGuid);

    @Delete("delete from sta_license l where l.station_guid=#{stationGuid}")
    int deleteLicense(@Param("stationGuid") String stationGuid);

    @Delete("delete from sta_freq f where f.station_guid=#{stationGuid}")
    int deleteFreq(@Param("stationGuid") String stationGuid);

    @Delete("delete from sta_equ e where e.station_guid=#{stationGuid}")
    int deleteEqu(@Param("stationGuid") String stationGuid);

    @Delete("delete from sta_antenna a where a.station_guid=#{stationGuid}")
    int deleteAntenna(@Param("stationGuid") String stationGuid);

    @Delete("delete from sta_station s where s.guid=#{stationGuid}")
    int deleteSta(@Param("stationGuid") String stationGuid);

    @Delete("delete from sta_eaf r where r.station_guid=#{stationGuid}")
    int deleteEaf(@Param("stationGuid") String stationGuid);


    @Select("select s.guid from sta_station s where s.iden_code=#{idenCode} ")
    String getStationId(@Param("idenCode") String idenCode);

    @Select("select 1 from sta_station s where s.iden_code=#{idenCode}")
    String selectIdenCode(@Param("idenCode") String idenCode);

    @Select("select 1 from STA_LICENSE l where l.lincese_code=#{linceseCode}")
    String selectLinceseCode(@Param("linceseCode") String linceseCode);

    @Insert("insert into sta_station(guid,org_code,distr_code,sta_type,pipe_num,sta_name,iden_code,user_name,location,longitude,latitude,detail,special_case,app_code,stat_app_type,stat_tdi,stat_work,stat_status,stat_equ_sum) " +
            "values(nvl(#{guid,jdbcType=VARCHAR},''),nvl(#{org_code,jdbcType=VARCHAR},''),nvl(#{distr_code,jdbcType=VARCHAR},''),nvl(#{sta_type,jdbcType=VARCHAR},'')," +
            "nvl(#{pipe_num,jdbcType=VARCHAR},''),nvl(#{sta_name,jdbcType=VARCHAR},''),nvl(#{iden_code,jdbcType=VARCHAR},''),nvl(#{user_name,jdbcType=VARCHAR},'')," +
            "nvl(#{location,jdbcType=VARCHAR},''),nvl(#{longitude,jdbcType=VARCHAR},''),nvl(#{latitude,jdbcType=VARCHAR},''),nvl(#{detail,jdbcType=VARCHAR},'')," +
            "nvl(#{special_case,jdbcType=VARCHAR},''),nvl(#{app_code,jdbcType=VARCHAR},''),nvl(#{stat_app_type,jdbcType=VARCHAR},''),nvl(#{stat_tdi,jdbcType=VARCHAR},'')," +
            "nvl(#{stat_work,jdbcType=VARCHAR},''),nvl(#{stat_status,jdbcType=VARCHAR},''),#{stat_equ_sum})")
    Boolean add1(@Param("guid") String guid, @Param("org_code") String org_code, @Param("distr_code") String distr_code,
                 @Param("sta_type") String sta_type, @Param("pipe_num") String pipe_num,
                 @Param("sta_name") String sta_name, @Param("iden_code") String iden_code,
                 @Param("user_name") String user_name, @Param("location") String location,
                 @Param("longitude") String longitude, @Param("latitude") String latitude,
                 @Param("detail") String detail, @Param("special_case") String special_case,
                 @Param("app_code") String app_code, @Param("stat_app_type") String stat_app_type,
                 @Param("stat_tdi") String stat_tdi, @Param("stat_work") String stat_work,
                 @Param("stat_status") String stat_status, @Param("stat_equ_sum") int stat_equ_sum);

    @Insert("insert into STA_LICENSE(GUID,STATION_GUID,LINCESE_CODE,GMT_CREATE,GMT_MODIFIED,LINCESE_STATE,LINCESE_START_DATE,LINCESE_END_DATE,LICENSE_AUTH)" +
            "values(#{GUID},#{STATION_GUID},#{LINCESE_CODE}," +
            "#{GMT_CREATE},#{GMT_MODIFIED},#{LINCESE_STATE}," +
            "#{LINCESE_START_DATE},#{LINCESE_END_DATE},#{LICENSE_AUTH})")
    Boolean add2(@Param("GUID") String guid, @Param("STATION_GUID") String stationGuid, @Param("LINCESE_CODE") String linceseCode,
                 @Param("GMT_CREATE") Date gmtCreate, @Param("GMT_MODIFIED") Date gmtModified, @Param("LINCESE_STATE") int linceseState,
                 @Param("LINCESE_START_DATE") Date linceseStartDate, @Param("LINCESE_END_DATE") Date linceseEndDate, @Param("LICENSE_AUTH") String licenseAuth);

    @Insert("insert into STA_FREQ(GUID,STATION_GUID,FREQ_EF,FREQ_RF,FREQ_BAND,FT_FREQ_CSGN,FREQ_PASS)" +
            "values(#{GUID},#{STATION_GUID},#{FREQ_EF}," +
            "#{FREQ_RF}," +
            "#{FREQ_BAND},#{FT_FREQ_CSGN},#{FREQ_PASS})")
    Boolean add3(@Param("GUID") String guid, @Param("STATION_GUID") String stationGuid, @Param("FREQ_EF") String freqEf,
                 @Param("FREQ_RF") String freqRf, @Param("FREQ_BAND") String freqBand,
                 @Param("FT_FREQ_CSGN") String ftFreqCsgn, @Param("FREQ_PASS") String freqPass);

    @Insert("insert into STA_EQU(GUID,STATION_GUID,EQU_MODEL,EQU_AUTH,EQU_MENU,EQU_POW,EQU_TYPE,EQU_CODE,EQU_MB)" +
            "values(#{GUID},#{STATION_GUID},#{EQU_MODEL}," +
            "#{EQU_AUTH},#{EQU_MENU},#{EQU_POW}," +
            "#{EQU_TYPE},#{EQU_CODE},#{EQU_MB})")
    Boolean add4(@Param("GUID") String guid, @Param("STATION_GUID") String stationGuid, @Param("EQU_MODEL") String equModel,
                 @Param("EQU_AUTH") String equAuth, @Param("EQU_MENU") String equMenu, @Param("EQU_POW") String equPow,
                 @Param("EQU_TYPE") String equType, @Param("EQU_CODE") String equCode, @Param("EQU_MB") String equMb);

    @Insert("insert into STA_ANTENNA(GUID,STATION_GUID,ANT_GAIN,ANT_EPOLE,ANT_HIGHT)" +
            "values(#{GUID},#{STATION_GUID},#{ANT_GAIN},#{ANT_EPOLE},#{ANT_HIGHT})")
    Boolean add5(@Param("GUID") String guid, @Param("STATION_GUID") String stationGuid, @Param("ANT_GAIN") Double antGain, @Param("ANT_EPOLE") String antEpole, @Param("ANT_HIGHT") Double antHight);

    @Insert("insert into sta_eaf(guid,station_guid,equ_guid,ant_guid,freq_guid)" +
            "values(#{guid},#{station_guid},#{equ_guid},#{ant_guid},#{freq_guid})")
    Boolean add6(@Param("guid") String guid, @Param("station_guid") String stationGuid, @Param("equ_guid") String equGuid, @Param("ant_guid") String antGuid, @Param("freq_guid") String freqGuid);

    @Select("select 1 from STA_FREQ f where f.guid=#{freqGuid}")
    String getFreqId(@Param("freqGuid") String freqGuid);

    @Delete("delete from sta_freq f where f.guid=#{freqGuid}")
    Boolean deleteF(@Param("freqGuid") String freqGuid);

    @Delete("delete from STA_EQU e where e.guid=#{equGuid}")
    Boolean deleteE(@Param("equGuid") String equGuid);

    @Delete("delete from STA_ANTENNA a where a.guid=#{AnGuid}")
    Boolean deleteA(@Param("AnGuid") String AnGuid);

    @Delete("delete from sta_eaf r where r.freq_guid=#{freqGuid} and r.equ_guid=#{equGuid} and r.ant_guid=#{AnGuid}")
    Boolean deleteEAF(@Param("freqGuid") String freqGuid, @Param("equGuid") String equGuid, @Param("AnGuid") String AnGuid);

    @Select("select guid from sta_freq f where f.station_guid=#{stationGuid}")
    List<String> getFreqIdlist(@Param("stationGuid") String stationGuid);

    @Select("select guid from STA_EQU e where e.station_guid=#{stationGuid}")
    List<String> getEquIdlist(@Param("stationGuid") String stationGuid);

    @Select("select guid from STA_ANTENNA e where e.station_guid=#{stationGuid}")
    List<String> getAntennaIdlist(@Param("stationGuid") String stationGuid);

    @Update("UPDATE sta_station SET org_code=nvl(#{org_code,jdbcType=VARCHAR},''),distr_code=nvl(#{distr_code,jdbcType=VARCHAR},''),sta_type=nvl(#{sta_type,jdbcType=VARCHAR},'')," +
            "pipe_num=nvl(#{pipe_num,jdbcType=VARCHAR},''),sta_name=nvl(#{sta_name,jdbcType=VARCHAR},''),iden_code=nvl(#{iden_code,jdbcType=VARCHAR},'')," +
            "user_name=nvl(#{user_name,jdbcType=VARCHAR},''),location=nvl(#{location,jdbcType=VARCHAR},''),longitude=nvl(#{longitude,jdbcType=VARCHAR},'')," +
            "latitude=nvl(#{latitude,jdbcType=VARCHAR},''),detail=nvl(#{detail,jdbcType=VARCHAR},''),special_case=nvl(#{special_case,jdbcType=VARCHAR},'')," +
            "app_code=nvl(#{app_code,jdbcType=VARCHAR},''),stat_app_type=nvl(#{stat_app_type,jdbcType=VARCHAR},''),stat_tdi=nvl(#{stat_tdi,jdbcType=VARCHAR},'')," +
            "stat_work=nvl(#{stat_work,jdbcType=VARCHAR},''),stat_status=nvl(#{stat_status,jdbcType=VARCHAR},'')," +
            "stat_equ_sum=#{stat_equ_sum}\n" +
            "WHERE guid = #{guid}")
    Boolean update1(@Param("guid") String guid, @Param("org_code") String org_code, @Param("distr_code") String distr_code,
                    @Param("sta_type") String sta_type, @Param("pipe_num") String pipe_num,
                    @Param("sta_name") String sta_name, @Param("iden_code") String iden_code,
                    @Param("user_name") String user_name, @Param("location") String location,
                    @Param("longitude") String longitude, @Param("latitude") String latitude,
                    @Param("detail") String detail, @Param("special_case") String special_case,
                    @Param("app_code") String app_code, @Param("stat_app_type") String stat_app_type,
                    @Param("stat_tdi") String stat_tdi, @Param("stat_work") String stat_work,
                    @Param("stat_status") String stat_status, @Param("stat_equ_sum") int stat_equ_sum);

    @Update("update STA_LICENSE set LINCESE_CODE=#{LINCESE_CODE},GMT_CREATE=#{GMT_CREATE,jdbcType=DATE},GMT_MODIFIED=#{GMT_MODIFIED,jdbcType=DATE}," +
            "LINCESE_STATE=#{LINCESE_STATE},LINCESE_START_DATE=#{LINCESE_START_DATE,jdbcType=DATE},LINCESE_END_DATE=#{LINCESE_END_DATE,jdbcType=DATE},LICENSE_AUTH=nvl(#{LICENSE_AUTH,jdbcType=VARCHAR},'') " +
            "where GUID=#{GUID}")
    Boolean update2(@Param("GUID") String guid, @Param("LINCESE_CODE") String linceseCode,
                    @Param("GMT_CREATE") Date gmtCreate, @Param("GMT_MODIFIED") Date gmtModified, @Param("LINCESE_STATE") int linceseState,
                    @Param("LINCESE_START_DATE") Date linceseStartDate, @Param("LINCESE_END_DATE") Date linceseEndDate, @Param("LICENSE_AUTH") String licenseAuth);

    @Update("update STA_FREQ set FREQ_EF=#{FREQ_EF},FREQ_RF=#{FREQ_RF}," +
            "FREQ_BAND=#{FREQ_BAND},FT_FREQ_CSGN=#{FT_FREQ_CSGN},FREQ_PASS=#{FREQ_PASS}" +
            "where GUID=#{GUID}")
    Boolean update3(@Param("GUID") String guid, @Param("FREQ_EF") String freqEf, @Param("FREQ_RF") String freqRf,
                    @Param("FREQ_BAND") String freqBand, @Param("FT_FREQ_CSGN") String ftFreqCsgn, @Param("FREQ_PASS") String freqPass);

    @Update("update STA_EQU set EQU_MODEL=#{EQU_MODEL},EQU_AUTH=#{EQU_AUTH}," +
            "EQU_MENU=#{EQU_MENU},EQU_POW=#{EQU_POW},EQU_TYPE=#{EQU_TYPE},EQU_CODE=#{EQU_CODE},EQU_MB=#{EQU_MB}" +
            "where GUID=#{GUID}")
    Boolean update4(@Param("GUID") String guid, @Param("EQU_MODEL") String equModel,
                    @Param("EQU_AUTH") String equAuth, @Param("EQU_MENU") String equMenu, @Param("EQU_POW") String equPow,
                    @Param("EQU_TYPE") String equType, @Param("EQU_CODE") String equCode, @Param("EQU_MB") String equMb);

    @Update("update STA_ANTENNA set ANT_GAIN=#{ANT_GAIN},ANT_EPOLE=#{ANT_EPOLE},ANT_HIGHT=#{ANT_HIGHT}" +
            "where GUID=#{GUID}")
    Boolean update5(@Param("GUID") String guid, @Param("ANT_GAIN") Long antGain, @Param("ANT_EPOLE") String antEpole, @Param("ANT_HIGHT") Long antHight);

    @Select("select l.lincese_code as licenseCode,s.sta_name as stationName,s.user_name as licensee,s.org_code as licenseeNo,s.longitude as longitude," +
            "s.latitude as latitude,s.location as statAddr,s.iden_code as stationCode,s.STA_TYPE as stationType,l.lincese_start_date as licenseStartDate," +
            "l.lincese_end_date as licenseEndDate,s.org_code as licenceNo,s.guid as stationGuid, " +
            "s.special_case as specialCase " +
            "FROM sta_license l,sta_station s " +
            "where s.guid=#{stationGuid} " +
            "and l.station_guid=s.guid "
    )
    /*@Results({
            @Result(property = "iSectionDTOList", column = "guid", javaType=List.class, many = @Many(select = "com.caict.bsm.project.domain.business.mapper.sta.staMapper.getISectionDTOList"))}
    )*/
    LicensePdfDTO getLicense(@Param("stationGuid") String stationGuid);

    /*@Select("select   f.freq_pass as freqPass,f.freq_ef as freqEf,f.freq_rf as freqRf,  e.equ_pow as equPow,f.freq_band as freqBand,     \n" +
            "e.equ_auth as equAuth,a.ant_gain as antGain ,a.ant_epole as antEpole,a.ant_hight as antHight\n" +
            "from sta_freq f,sta_equ e,sta_antenna a,sta_eaf r\n" +
            "where r.station_guid=#{stationGuid}\n" +
            "and r.equ_guid=e.guid\n" +
            "and r.ant_guid=a.guid\n" +
            "and r.freq_guid=f.guid")
    List<ISectionDTO> getStaVO(@Param("stationGuid") String stationGuid);*/

    /**
     * 根据基站查询详细扇区、设备、参数
     * */
    @Select("select distinct f.freq_ef as freqEf," +
            "f.freq_rf as freqRf,e.EQU_POW as equPow,f.freq_band as freqBand,e.EQU_AUTH as equAuth," +
            "a.ANT_GAIN as antGain,a.ANT_EPOLE as antEpole,a.ANT_HIGHT as antHight " +
            "from sta_freq f,sta_equ e,sta_antenna a,sta_eaf r\n" +
            "where r.station_guid=#{stationGuid}\n" +
            "and r.equ_guid=e.guid\n" +
            "and r.ant_guid=a.guid\n" +
            "and r.freq_guid=f.guid")
    List<ISectionDTO> getISectionDTOList(@Param("stationGuid") String stationGuid);

    /**
     * 根据基站数组查询详情
     * */
    @Select({"<script>",
            "select l.lincese_code as licenseCode,s.sta_name as stationName,s.user_name as licensee,s.org_code as licenseeNo,s.longitude as longitude,",
                    "s.latitude as latitude,s.location as statAddr,s.iden_code as stationCode,s.STA_TYPE as stationType,l.lincese_start_date as licenseStartDate,",
                    "l.lincese_end_date as licenseEndDate,s.org_code as licenceNo,s.guid as stationGuid, " ,
                    "s.special_case as specialCase " ,
                    "FROM sta_license l,sta_station s " ,
                    "where l.station_guid=s.guid ",
            "and s.guid in ",
            "<foreach collection='stationGuidList' item='guid' open='(' separator=',' close=')'>",
            "#{guid}",
            "</foreach>",
            "</script>"})
    /*@Results({
            @Result(property = "stationGuid",column = "STATION_GUID"),
            @Result(property = "iSectionDTOList", column = "STATION_GUID", many = @Many(select = "com.caict.bsm.project.domain.business.mapper.sta.staMapper.getISectionDTOList"))}
    )*/
    List<LicensePdfDTO> findOneFDFByStationGuids(@Param("stationGuidList") List<String> stationIdlist);
}
