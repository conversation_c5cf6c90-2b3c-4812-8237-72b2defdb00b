package com.caict.bsm.project.domain.business.mapper.station;

import com.caict.bsm.project.system.data.mapper.BasicMapper;
import com.caict.bsm.project.system.model.entity.business.station.RsbtAntfeed;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

/**
 * Created by dengsy on 2020-04-22.
 */
@Repository
public interface RsbtAntfeedMapper extends BasicMapper<RsbtAntfeed> {

    /**
     * 根据基站id和扇区id查询
     * */
    @Select("select * from RSBT_ANTFEED LEFT JOIN RSBT_ANTFEED_T ON RSBT_ANTFEED.GUID = RSBT_ANTFEED_T.GUID" +
            " WHERE RSBT_ANTFEED.STATION_GUID = #{stationGuid} and RSBT_ANTFEED_T.AT_CCODE = #{cellId}")
    RsbtAntfeed findOneByStationCell(@Param("stationGuid") String stationGuid, @Param("cellId") String cellId);

    @Update("update rsbt_antfeed_appendix set is_deleted = '1' where guid in (select guid from rsbt_antfeed where rsbt_equ.station_guid = #{stationGuid}) " +
            "and AT_CCode = #{cellCode}" )
    int updateByStationGuid(@Param("stationGuid")String stationGuid,@Param("cellCode")String cellCode);
}
