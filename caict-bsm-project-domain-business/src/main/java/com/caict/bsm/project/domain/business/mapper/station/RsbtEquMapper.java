package com.caict.bsm.project.domain.business.mapper.station;

import com.caict.bsm.project.system.data.mapper.BasicMapper;
import com.caict.bsm.project.system.model.entity.business.station.RsbtEqu;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

/**
 * Created by dengsy on 2020-04-22.
 */
@Repository
public interface RsbtEquMapper extends BasicMapper<RsbtEqu> {

    /**
     * 根据基站id和扇区id查询
     * */
    @Select("select * from RSBT_EQU LEFT JOIN RSBT_EQU_T ON RSBT_EQU.GUID = RSBT_EQU_T.GUID\n" +
            "where RSBT_EQU.STATION_GUID = #{stationGuid} and RSBT_EQU_T.ET_EQU_CCODE = #{cellId}")
    RsbtEqu findOneByStationSection(@Param("stationGuid") String stationGuid, @Param("cellId") String cellId);

    @Update("update rsbt_equ_appendix set is_deleted = '1' where guid in (select guid from rsbt_equ where rsbt_equ.station_guid = #{stationGuid}) " +
            "and ET_EQU_CCode = #{cellCode}" )
    int updateByStationGuid(@Param("stationGuid")String stationGuid,@Param("cellCode")String cellCode);
}
