package com.caict.bsm.project.domain.business.mapper.station;


import com.caict.bsm.project.system.data.mapper.BasicMapper;
import com.caict.bsm.project.system.model.entity.business.station.RsbtFreq;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

/**
 * Created by dengsy on 2020-04-22.
 */
@Repository
public interface RsbtFreqMapper extends BasicMapper<RsbtFreq> {

    /**
     * 根据基站guid和扇区id查询
     * */
    @Select("select * from RSBT_FREQ LEFT JOIN RSBT_FREQ_T ON RSBT_FREQ.GUID = RSBT_FREQ_T.GUID" +
            "  where RSBT_FREQ.STATION_GUID = #{stationGuid} and RSBT_FREQ_T.FT_FREQ_CCODE = #{cellId}")
    RsbtFreq findOneByStationCell(@Param("stationGuid") String stationGuid, @Param("cellId") String cellId);

    @Update("update rsbt_freq_appendix set is_deleted = '1' where guid in (select guid from rsbt_freq where rsbt_freq.station_guid = #{stationGuid}) " +
            "and FT_FREQ_CCode = #{cellCode}" )
    int updateByStationGuid(@Param("stationGuid")String stationGuid,@Param("cellCode")String cellCode);
}
