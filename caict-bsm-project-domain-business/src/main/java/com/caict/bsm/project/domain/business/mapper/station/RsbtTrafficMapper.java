package com.caict.bsm.project.domain.business.mapper.station;


import com.caict.bsm.project.system.data.mapper.BasicMapper;
import com.caict.bsm.project.system.model.entity.business.station.RsbtTraffic;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

/**
 * Created by dengsy on 2020-04-22.
 */
@Repository
public interface RsbtTrafficMapper extends BasicMapper<RsbtTraffic> {

    /**
     * 根据基站guid和扇区id查询
     * */
    @Select("select * from RSBT_TRAFFIC where STATION_GUID = #{stationGuid} and SECTION_CODE = #{cellId} and ROWNUM = 1")
    RsbtTraffic findOneByStationCell(@Param("stationGuid") String stationGuid, @Param("cellId") String cellId);
}
