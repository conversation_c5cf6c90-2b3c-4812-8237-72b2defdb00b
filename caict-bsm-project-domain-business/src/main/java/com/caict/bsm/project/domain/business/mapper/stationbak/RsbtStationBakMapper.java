package com.caict.bsm.project.domain.business.mapper.stationbak;

import com.caict.bsm.project.system.data.mapper.BasicMapper;
import com.caict.bsm.project.system.model.dto.business.station.StationBakDTO;
import com.caict.bsm.project.system.model.dto.business.transfer.ApprovalScheduleLogDTO;
import com.caict.bsm.project.system.model.dto.business.transfer.ApprovalTransportJobDTO;
import com.caict.bsm.project.system.model.dto.business.transfer.TransportRawBtsDTO;
import com.caict.bsm.project.system.model.entity.business.stationbak.RsbtStationBak;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by dengsy on 2020-04-22.
 */
@Repository
public interface RsbtStationBakMapper extends BasicMapper<RsbtStationBak> {

    @Select({"<script>",
            "select RSBT_STATION_BAK.*,RSBT_STATION_appendix.Attribute_Station,RSBT_STATION_appendix.expand_station from RSBT_STATION_BAK left join " +
                    "            RSBT_STATION_appendix on RSBT_STATION_BAK.Station_Guid = RSBT_STATION_appendix.guid " ,
            "WHERE RSBT_STATION_BAK.APP_GUID = #{appGuid} " +
                    "AND RSBT_STATION_BAK.GUID IN ",
            "<foreach collection='guids' item='guid' open='(' separator=',' close=')'>",
            "#{guid} ",
            "</foreach>",
            "</script>"})
    List<StationBakDTO> findByGuids(@Param("guids") List<String> guids,@Param("appGuid") String appGuid);

    @Update({"<script>",
            "update RSBT_STATION_BAK set is_sync = '1' " ,
            "WHERE RSBT_STATION_BAK.APP_GUID = #{appGuid} " +
                    "AND RSBT_STATION_BAK.STATION_GUID IN ",
            "<foreach collection='guids' item='guid' open='(' separator=',' close=')'>",
            "#{guid} ",
            "</foreach>",
            "</script>"})
    int updateIsSync(@Param("guids")List<String> guids,@Param("appGuid")String appGuid);

    @Select("select * from (" +
            "select RSBT_STATION_BAK.*,RSBT_STATION_appendix.Attribute_Station,RSBT_STATION_appendix.expand_station,rownum as rn from RSBT_STATION_BAK left join" +
            "            RSBT_STATION_appendix on RSBT_STATION_BAK.Station_Guid = RSBT_STATION_appendix.guid " +
            "where RSBT_STATION_BAK.APP_GUID = #{appGuid} " +
            "and (RSBT_STATION_BAK.DATA_STATUS = #{dataStatus,jdbcType=VARCHAR} or #{dataStatus,jdbcType=VARCHAR} is null) " +
            "and (RSBT_STATION_BAK.DATA_TYPE = #{dataType,jdbcType=VARCHAR} or #{dataType,jdbcType=VARCHAR} is null) " +
            "and (RSBT_STATION_BAK.GEN_NUM = #{genNum,jdbcType=VARCHAR} or #{genNum,jdbcType=VARCHAR} is null) " +
            "AND (RSBT_STATION_BAK.STAT_NAME LIKE concat(concat('%',#{statName,jdbcType=VARCHAR}),'%') OR #{statName,jdbcType=VARCHAR} is null) "+
            "AND (RSBT_STATION_BAK.STAT_ADDR LIKE concat(concat('%',#{statAddr,jdbcType=VARCHAR}),'%') OR #{statAddr,jdbcType=VARCHAR} is null) "+
            "and RSBT_STATION_BAK.IS_SYNC != '1' " +
            "and rownum <= #{endRows}) r where r.rn > #{startRows}")
    List<StationBakDTO> findByPage(@Param("appGuid")String appGuid,@Param("dataStatus")String dataStatus,@Param("dataType")String dataType,
                                    @Param("genNum")String genNum,@Param("startRows")int startRows,@Param("endRows")int endRows,
                                    @Param("statName")String statName,@Param("statAddr")String statAddr);

    @Select("select count(*) from RSBT_STATION_BAK " +
            "where APP_GUID = #{appGuid} " +
            "and (DATA_STATUS = #{dataStatus,jdbcType=VARCHAR} or #{dataStatus,jdbcType=VARCHAR} is null) " +
            "and (DATA_TYPE = #{dataType,jdbcType=VARCHAR} or #{dataType,jdbcType=VARCHAR} is null) " +
            "and (GEN_NUM = #{genNum,jdbcType=VARCHAR} or #{genNum,jdbcType=VARCHAR} is null) " +
            "AND (STAT_NAME LIKE concat(concat('%',#{statName,jdbcType=VARCHAR}),'%') OR #{statName,jdbcType=VARCHAR} is null) "+
            "AND (STAT_ADDR LIKE concat(concat('%',#{statAddr,jdbcType=VARCHAR}),'%') OR #{statAddr,jdbcType=VARCHAR} is null) "+
            "and IS_SYNC != '1' ")
    int findCount(@Param("appGuid")String appGuid,@Param("dataStatus")String dataStatus,@Param("dataType")String dataType,@Param("genNum")String genNum,
                  @Param("statName")String statName,@Param("statAddr")String statAddr);

    @Select("select count(*) from RSBT_STATION_BAK " +
            "where APP_GUID = #{appGuid} " +
            "and IS_SYNC != '1' " +
            "and (is_giveup != '2' or is_giveup is null) ")
    int findCountByAppGuid(@Param("appGuid")String appGuid);

    @Select("select * from RSBT_STATION_BAK WHERE app_guid = #{appGuid}")
    List<RsbtStationBak> findByAppGuid(@Param("appGuid")String appGuid);

    @Update("update RSBT_STATION_BAK set app_code = #{stationBak.appCode,jdbcType=VARCHAR},stat_tdi = #{stationBak.statTdi,jdbcType=VARCHAR},is_sync = #{stationBak.isSync,jdbcType=VARCHAR} " +
            "where guid = #{stationBak.guid,jdbcType=VARCHAR} and app_guid = #{stationBak.appGuid,jdbcType=VARCHAR}")
    int updateByGuidAppGuid(@Param("stationBak")RsbtStationBak stationBak);

    @Update("UPDATE RSBT_STATION_BAK SET IS_GIVEUP = '2' WHERE APP_GUID = #{appGuid} AND IS_SYNC != '1' ")
    int updateGiveUp(@Param("appGuid")String appGuid);

    @Select("select approval_schedule_log.* from rsbt_station_bak,approval_schedule_log where rsbt_station_bak.app_guid = #{appGuid} " +
            "and rsbt_station_bak.st_c_code = approval_schedule_log.bts_id " +
            "and rsbt_station_bak.is_giveup = '2'")
    List<ApprovalScheduleLogDTO> findGiveUp(@Param("appGuid")String appGuid);

    @Select("select rsbt_station_bak.* from rsbt_station_bak where rsbt_station_bak.app_guid = #{approvalTransportJobDTO.guid,jdbcType=VARCHAR} " +
            "and rsbt_station_bak.is_giveup = '2'")
    List<StationBakDTO> findGiveUpDataList(@Param("approvalTransportJobDTO") ApprovalTransportJobDTO approvalTransportJobDTO);

    @Select("select * from RSBT_STATION_BAK WHERE app_guid = #{appGuid} and is_giveup = '2' ")
    List<RsbtStationBak> findNotPassData(@Param("appGuid")String appGuid);

    @Select("select * from RSBT_STATION_BAK WHERE app_guid = #{appGuid} and st_c_code = #{stCCode} ")
    RsbtStationBak findByCondition(@Param("appGuid")String appGuid,@Param("stCCode")String stCCode);

}
