package com.caict.bsm.project.domain.business.mapper.transfer;


import com.caict.bsm.project.system.data.mapper.BasicMapper;
import com.caict.bsm.project.system.model.dto.business.transfer.ApprovalScheduleLogDTO;
import com.caict.bsm.project.system.model.entity.business.transfer.ApprovalScheduleLog;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by dengsy on 2020-04-22.
 */
@Repository
public interface ApprovalScheduleLogMapper extends BasicMapper<ApprovalScheduleLog> {

    /**
     * 根据appGuid、数据类型查询总数
     * */
    @Select("select count(*) from APPROVAL_SCHEDULE_LOG where APP_GUID = #{appGuid} " +
            "and (DATA_TYPE = #{dataType,jdbcType=VARCHAR} or #{dataType,jdbcType=VARCHAR} is null)")
    int selectCountByAppGuidDateType(@Param("appGuid")String appGuid,@Param("dataType")String dataType);

    /**
     * 根据appGuid、数据类型分页查询
     * */
    @Select("select * from APPROVAL_SCHEDULE_LOG " +
            "where JOB_GUID = #{appGuid,jdbcType=VARCHAR} " +
            "and (DATA_TYPE = #{dataType,jdbcType=VARCHAR} or #{dataType,jdbcType=VARCHAR} is null) " +
            "and (GEN_NUM = #{genNum,jdbcType=VARCHAR} or #{genNum,jdbcType=VARCHAR} is null) " +
            "order by APPROVAL_SCHEDULE_LOG.GUID ")
    List<ApprovalScheduleLogDTO> findAllPageByAppGuidDateType(@Param("appGuid")String appGuid, @Param("dataType")String dataType, @Param("genNum")String genNum);


    /**
     * 根据guid查询详情
     * */
    @Select("select * from APPROVAL_SCHEDULE_LOG where GUID = #{guid}")
    ApprovalScheduleLogDTO findOneByGuid(@Param("guid")String guid);


    @Select("select * from APPROVAL_SCHEDULE_LOG where APP_GUID = #{appGuid}")
    List<ApprovalScheduleLog> findByAppGuid(@Param("appGuid")String appGuid);
}
