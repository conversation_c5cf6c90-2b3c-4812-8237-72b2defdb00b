package com.caict.bsm.project.domain.business.mapper.transfer;


import com.caict.bsm.project.system.data.mapper.BasicMapper;
import com.caict.bsm.project.system.model.dto.business.station.StationScheduleDataDTO;
import com.caict.bsm.project.system.model.dto.business.transfer.ApprovalScheduleDTO;
import com.caict.bsm.project.system.model.entity.business.transfer.ApprovalRawBts;
import com.caict.bsm.project.system.model.entity.business.transfer.ApprovalSchedule;
import com.caict.bsm.project.system.model.entity.business.transfer.ApprovalScheduleLog;
import com.caict.bsm.project.system.model.entity.business.transfer.CoordinateScheduleTemp;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by dengsy on 2020-04-22.
 */
@Repository
public interface ApprovalScheduleMapper extends BasicMapper<ApprovalSchedule> {

    /**
     * 根据appGuid删除全部
     * */
    @Delete("delete APPROVAL_SCHEDULE where APP_GUID = #{appGuid}")
    int deleteAllByAppGuid(@Param("appGuid")String appGuid);

    /**
     * 根据guid查询详情
     * */
    @Select("select * from APPROVAL_SCHEDULE where GUID = #{guid}")
    ApprovalScheduleDTO findOneByGuid(@Param("guid")String guid);

    /**
     * 根据appGuid插入审核日志
     * */
    @Insert("insert into APPROVAL_SCHEDULE_LOG(GUID,IS_APPROVED,APP_GUID,JOB_GUID,UPDATE_TIME,IS_VALID,CELL_NAME,CELL_ID,BTS_NAME,BTS_ID,TECH_TYPE,LOCATION,LONGITUDE," +
            "LATITUDE,SEND_START_FREQ,SEND_END_FREQ,ACC_START_FREQ,ACC_END_FREQ,MAX_EMISSIVE_POWER,HEIGHT,DATA_TYPE,COUNTY,IS_HANDLE,USER_GUID,VENDOR_NAME,DEVICE_MODEL,MODEL_CODE,ANTENNA_GAIN,GEN_NUM," +
            "ANTENNA_MODEL,ANTENNA_FACTORY,POLARIZATION_MODE,ANTENNA_AZIMUTH,FEEDER_LOSS,ALTITUDE,ORG_TYPE,SET_YEAR,SET_MONTH,EXPAND_STATION,ATTRIBUTE_STATION,AT_RANG,AT_EANG,ST_SERV_R,BTS_DATA_TYPE) " +
            "select APPROVAL_SCHEDULE.GUID,#{isApproved} as IS_APPROVED,APPROVAL_SCHEDULE.APP_GUID,APPROVAL_SCHEDULE.JOB_GUID,sysdate as UPDATE_TIME,APPROVAL_SCHEDULE.IS_VALID,APPROVAL_SCHEDULE.CELL_NAME,APPROVAL_SCHEDULE.CELL_ID,APPROVAL_SCHEDULE.BTS_NAME,APPROVAL_SCHEDULE.BTS_ID,APPROVAL_SCHEDULE.TECH_TYPE,APPROVAL_SCHEDULE.LOCATION,APPROVAL_SCHEDULE.LONGITUDE," +
            "APPROVAL_SCHEDULE.LATITUDE,APPROVAL_SCHEDULE.SEND_START_FREQ,APPROVAL_SCHEDULE.SEND_END_FREQ,APPROVAL_SCHEDULE.ACC_START_FREQ,APPROVAL_SCHEDULE.ACC_END_FREQ,APPROVAL_SCHEDULE.MAX_EMISSIVE_POWER,APPROVAL_SCHEDULE.HEIGHT,APPROVAL_SCHEDULE.DATA_TYPE,APPROVAL_SCHEDULE.COUNTY,APPROVAL_SCHEDULE.IS_HANDLE,APPROVAL_SCHEDULE.USER_GUID,APPROVAL_SCHEDULE.VENDOR_NAME,APPROVAL_SCHEDULE.DEVICE_MODEL,APPROVAL_SCHEDULE.MODEL_CODE,APPROVAL_SCHEDULE.ANTENNA_GAIN,APPROVAL_SCHEDULE.GEN_NUM," +
            "APPROVAL_SCHEDULE.ANTENNA_MODEL,APPROVAL_SCHEDULE.ANTENNA_FACTORY,APPROVAL_SCHEDULE.POLARIZATION_MODE,APPROVAL_SCHEDULE.ANTENNA_AZIMUTH,APPROVAL_SCHEDULE.FEEDER_LOSS,APPROVAL_SCHEDULE.ALTITUDE,APPROVAL_SCHEDULE.ORG_TYPE,APPROVAL_SCHEDULE.SET_YEAR,APPROVAL_SCHEDULE.SET_MONTH,APPROVAL_SCHEDULE.EXPAND_STATION,APPROVAL_SCHEDULE.ATTRIBUTE_STATION,APPROVAL_SCHEDULE.AT_RANG,APPROVAL_SCHEDULE.AT_EANG,APPROVAL_SCHEDULE.ST_SERV_R,BTS_DATA_TYPE " +
            "from APPROVAL_SCHEDULE,dual where APPROVAL_SCHEDULE.APP_GUID = #{appGuid}")
    int InsertApprovalScheduleLogByAppGuid(@Param("appGuid")String appGuid,@Param("isApproved")Long isApproved);

    /**
     * 根据appGuid查询返回日志记录
     * */
    @Select("select APPROVAL_SCHEDULE.*,sysdate as updateTime,#{isApproved} as isApproved from APPROVAL_SCHEDULE,dual where APPROVAL_SCHEDULE.APP_GUID = #{appGuid}")
    List<ApprovalScheduleLog> findAllApprovalScheduleLogByAppGuid(@Param("appGuid")String appGuid,@Param("isApproved")Long isApproved);

    /**
     * 根据appGuid、类型审核数据数量
     * */
    @Select("select count(1)" +
            "from (select distinct BTS_ID,BTS_NAME,TECH_TYPE,LOCATION,COUNTY,ALTITUDE,LATITUDE,LONGITUDE,BTS_DATA_TYPE,IS_HANDLE,USER_GUID,JOB_GUID,CODE,GEN_NUM,IS_VALID " +
            "from APPROVAL_SCHEDULE,FSA_REGION " +
            "where APPROVAL_SCHEDULE.COUNTY = FSA_REGION.NAME and APP_GUID = #{appGuid} and BTS_DATA_TYPE = #{dataType} " +
            "order by BTS_ID) a")
    int selectCountStationScheduleDataDTOPageByJobIdDataType(@Param("appGuid")String appGuid,@Param("dataType")String dateType);

    /**
     * 根据appGuid、类型审核变更数据
     * */
    @Select("select a.BTS_ID as btsId,a.BTS_NAME as btsName,a.TECH_TYPE as techType,a.LOCATION as location,a.COUNTY as county,a.ALTITUDE as altitude,a.LATITUDE " +
            "as latitude,a.LONGITUDE as longitude,a.DATA_TYPE as dataType,a.IS_HANDLE as isHandle,a.USER_GUID as userGuid,a.JOB_GUID as jobGuid,a.guid  " +
            "as stationGuid,CODE as regionCode,GEN_NUM as genNum,IS_VALID as isValid,a.EXPAND_STATION,a.ATTRIBUTE_STATION,a.ST_SERV_R,  " +
            "a.ORG_CODE from (select distinct rst.guid, ast.BTS_ID,ast.BTS_NAME,ast.TECH_TYPE,ast.LOCATION,ast.COUNTY,ast.ALTITUDE,ast.LATITUDE,  " +
            "ast.LONGITUDE,ast.DATA_TYPE,IS_HANDLE,ast.USER_GUID,ast.JOB_GUID,fr.CODE,ast.GEN_NUM,ast.IS_VALID,ast.EXPAND_STATION,ast.ATTRIBUTE_STATION,  " +
            "ast.ST_SERV_R,ro.ORG_CODE from APPROVAL_SCHEDULE ast,FSA_REGION fr,RSBT_STATION_T rst,RSBT_STATION_APPENDIX rsa,RSBT_ORG_APPENDIX roa, RSBT_ORG ro  " +
            "where ast.COUNTY = fr.NAME and ro.GUID = roa.GUID and roa.TYPE = rsa.ORG_TYPE and roa.STATUS = 1 and ast.BTS_ID = rst.ST_C_CODE " +
            "and  rst.guid = rsa.guid " +
            "and ast.APP_GUID = #{appGuid} and ast.DATA_TYPE = #{dataType} order by ast.BTS_ID) a ")
    List<StationScheduleDataDTO> findStationScheduleDataDTOPageByJobIdUpdate(@Param("appGuid")String appGuid,@Param("dataType")String dateType);

    /**
     * 根据appGuid、类型审核数据
     * */
    @Select("select a.BTS_ID as btsId,a.BTS_NAME as btsName,a.TECH_TYPE as techType,a.LOCATION as location,a.COUNTY as county,a.ALTITUDE as altitude,a.LATITUDE as latitude,a.LONGITUDE as longitude,a.DATA_TYPE as dataType," +
            "a.IS_HANDLE as isHandle,a.USER_GUID as userGuid,a.JOB_GUID as jobGuid,sys_guid() as stationGuid,CODE as regionCode,GEN_NUM as genNum,a.EXPAND_STATION,a.ATTRIBUTE_STATION,a.ST_SERV_R " +
            "from (select distinct BTS_ID,BTS_NAME,TECH_TYPE,LOCATION,COUNTY,ALTITUDE,LATITUDE,LONGITUDE,DATA_TYPE,IS_HANDLE,USER_GUID,JOB_GUID,CODE,GEN_NUM,EXPAND_STATION,ATTRIBUTE_STATION,ST_SERV_R " +
            "from APPROVAL_SCHEDULE,FSA_REGION " +
            "where APPROVAL_SCHEDULE.COUNTY = FSA_REGION.NAME and APP_GUID = #{appGuid} and DATA_TYPE = #{dataType} " +
            "order by BTS_ID) a")
    List<StationScheduleDataDTO> findStationScheduleDataDTOPageByJobIdAdd(@Param("appGuid")String appGuid,@Param("dataType")String dateType);

    @Select("select a.BTS_ID as btsId,a.BTS_NAME as btsName,a.TECH_TYPE as techType,a.LOCATION as location,a.COUNTY as county, " +
            " a.ALTITUDE as altitude,a.LATITUDE as latitude,a.LONGITUDE as longitude,a.BTS_DATA_TYPE as dataType,a.DATA_TYPE as cellDataType, " +
            " a.IS_HANDLE as isHandle,a.USER_GUID as userGuid,a.JOB_GUID as jobGuid,sys_guid() as stationGuid,CODE as regionCode,GEN_NUM as genNum,a.EXPAND_STATION,a.ATTRIBUTE_STATION,a.ST_SERV_R,a.SET_YEAR,a.SET_MONTH " +
            " from (select BTS_ID,BTS_NAME,TECH_TYPE,LOCATION,COUNTY,ALTITUDE,LATITUDE,LONGITUDE,BTS_DATA_TYPE,IS_HANDLE,DATA_TYPE,USER_GUID,JOB_GUID,CODE,GEN_NUM,EXPAND_STATION,ATTRIBUTE_STATION,ST_SERV_R,ST_SCENE,SET_YEAR,SET_MONTH" +
            " from APPROVAL_SCHEDULE,FSA_REGION where APPROVAL_SCHEDULE.COUNTY = FSA_REGION.NAME and APP_GUID = #{appGuid} and BTS_DATA_TYPE = #{btsDataType} and (DATA_TYPE =  #{dataType1} or DATA_TYPE =  #{dataType2} or DATA_TYPE = #{dataType3})) a " +
            " group by BTS_ID,BTS_NAME,TECH_TYPE,LOCATION,COUNTY,ALTITUDE,LATITUDE,LONGITUDE,BTS_DATA_TYPE,IS_HANDLE,DATA_TYPE,USER_GUID,JOB_GUID,CODE,GEN_NUM,EXPAND_STATION,ATTRIBUTE_STATION,ST_SERV_R,ST_SCENE,SET_YEAR,SET_MONTH " +
            " order by a.BTS_ID, a.DATA_TYPE ")
    List<StationScheduleDataDTO> findStationScheduleDataDTOPageByJobIdDataTypeAdd(@Param("appGuid")String appGuid,@Param("btsDataType")String btsDataType,@Param("dataType1")String dataType1,@Param("dataType2")String dataType2,@Param("dataType3")String dataType3);

    @Select("select a.BTS_ID as btsId,a.BTS_NAME as btsName,a.TECH_TYPE as techType,a.LOCATION as location,a.COUNTY as county,a.ALTITUDE as altitude,a.LATITUDE " +
            "as latitude,a.LONGITUDE as longitude,a.IS_HANDLE as isHandle,a.USER_GUID as userGuid, a.BTS_DATA_TYPE as dataType, a.JOB_GUID as jobGuid,a.guid  " +
            "as stationGuid,CODE as regionCode,GEN_NUM as genNum,IS_VALID as isValid,a.EXPAND_STATION,a.ATTRIBUTE_STATION,a.ST_SERV_R,  " +
            "a.ORG_CODE,a.ST_SCENE,a.SET_YEAR,a.SET_MONTH " +
            "from (select distinct rst.guid, ast.BTS_ID,ast.BTS_NAME,ast.TECH_TYPE,ast.LOCATION,ast.COUNTY,ast.ALTITUDE,ast.LATITUDE,  " +
            "ast.LONGITUDE,ast.BTS_DATA_TYPE,IS_HANDLE,ast.USER_GUID,ast.JOB_GUID,fr.CODE,ast.GEN_NUM,ast.IS_VALID,ast.EXPAND_STATION,ast.ATTRIBUTE_STATION,ast.SET_YEAR,ast.SET_MONTH,  " +
            "ast.ST_SERV_R,ast.ST_SCENE ,ro.ORG_CODE " +
            "from APPROVAL_SCHEDULE ast,FSA_REGION fr,RSBT_STATION_T rst,RSBT_STATION_APPENDIX rsa,RSBT_ORG_APPENDIX roa, RSBT_ORG ro  " +
            "where ast.COUNTY = fr.NAME and ro.GUID = roa.GUID and roa.TYPE = rsa.ORG_TYPE and roa.STATUS = 1 and ast.BTS_ID = rst.ST_C_CODE " +
            "and  rst.guid = rsa.guid " +
            "and ast.APP_GUID = #{appGuid} and ast.BTS_DATA_TYPE = #{btsDataType} and (DATA_TYPE =  #{dataType1} or DATA_TYPE =  #{dataType2} or DATA_TYPE = #{dataType3}) order by ast.BTS_ID,ast.BTS_DATA_TYPE) a ")
    List<StationScheduleDataDTO> findStationScheduleDataDTOPageByJobIdDataTypeUpdate(@Param("appGuid")String appGuid,@Param("btsDataType")String btsDataType,@Param("dataType1")String dataType1,@Param("dataType2")String dataType2,@Param("dataType3")String dataType3);

    /**
     * 根据appGuid查询返回干扰协调数据
     * */
    @Select("select APPROVAL_RAW_BTS.*,#{jobGuid} as jobGuid from APPROVAL_RAW_BTS where APP_GUID = #{appGuid}")
    List<CoordinateScheduleTemp> findAllCoordinateScheduleByAppGuid(@Param("jobGuid")String jobGuid, @Param("appGuid")String appGuid);

    /**
     * 根据appGuid查询ApprovalRawBts
     * */
    @Select("select * from APPROVAL_RAW_BTS where APP_GUID = #{appGuid} ")
    List<ApprovalRawBts> findAllByAppGuid(@Param("appGuid")String appGuid);

    /**
     * 根据jobGuid查询总数
     * */
    @Select("select count(1) from APPROVAL_SCHEDULE where JOB_GUID = #{jobGuid}")
    int selectCountByJob(@Param("jobGuid")String jobGuid);

    @Select("SELECT MAX(IS_VALID) FROM APPROVAL_SCHEDULE WHERE BTS_ID = #{btsId} " +
            "AND USER_GUID = #{userGuid} " +
            "AND TECH_TYPE = #{techType}")
    String selectIsValidByBtsId(@Param("btsId")String btsId,@Param("userGuid")String userGuid,@Param("techType")String techType);

    /**
     * 添加审核待办数据
     * */
    @Insert("insert into APPROVAL_SCHEDULE(GUID,APP_GUID,JOB_GUID,APPLICATION_CODE,IS_VALID,CELL_NAME,CELL_ID,BTS_NAME,BTS_ID,TECH_TYPE,LOCATION,LONGITUDE,LATITUDE,SEND_START_FREQ,SEND_END_FREQ,ACC_START_FREQ,ACC_END_FREQ,MAX_EMISSIVE_POWER," +
            "HEIGHT,DATA_TYPE,COUNTY,IS_HANDLE,USER_GUID,VENDOR_NAME,DEVICE_MODEL,MODEL_CODE,ANTENNA_GAIN,GEN_NUM,ANTENNA_MODEL,ANTENNA_FACTORY,POLARIZATION_MODE,ANTENNA_AZIMUTH,FEEDER_LOSS,ALTITUDE,SET_YEAR,SET_MONTH,ORG_TYPE,EXPAND_STATION,ATTRIBUTE_STATION,AT_RANG,AT_EANG,ST_SERV_R,ANALYSIS_STATUS,EXPIRE_DATE,BTS_DATA_TYPE,TRF_USER,TRF_DATE,TRF_DATA,ST_SCENE) " +
            "select APPROVAL_RAW_BTS.GUID,APPROVAL_RAW_BTS.APP_GUID,APPROVAL_TRANSPORT_JOB.JOB_GUID,APPROVAL_TRANSPORT_JOB.APP_CODE,APPROVAL_RAW_BTS.IS_VALID,APPROVAL_RAW_BTS.CELL_NAME,APPROVAL_RAW_BTS.CELL_ID,APPROVAL_RAW_BTS.BTS_NAME,APPROVAL_RAW_BTS.BTS_ID,APPROVAL_RAW_BTS.TECH_TYPE,APPROVAL_RAW_BTS.LOCATION,APPROVAL_RAW_BTS.LONGITUDE,APPROVAL_RAW_BTS.LATITUDE,APPROVAL_RAW_BTS.SEND_START_FREQ,APPROVAL_RAW_BTS.SEND_END_FREQ,APPROVAL_RAW_BTS.ACC_START_FREQ,APPROVAL_RAW_BTS.ACC_END_FREQ,APPROVAL_RAW_BTS.MAX_EMISSIVE_POWER," +
            "APPROVAL_RAW_BTS.HEIGHT,APPROVAL_RAW_BTS.DATA_TYPE,APPROVAL_RAW_BTS.COUNTY,'0',APPROVAL_RAW_BTS.USER_GUID,APPROVAL_RAW_BTS.VENDOR_NAME,APPROVAL_RAW_BTS.DEVICE_MODEL,APPROVAL_RAW_BTS.MODEL_CODE,APPROVAL_RAW_BTS.ANTENNA_GAIN,APPROVAL_RAW_BTS.GEN_NUM,APPROVAL_RAW_BTS.ANTENNA_MODEL,APPROVAL_RAW_BTS.ANTENNA_FACTORY,APPROVAL_RAW_BTS.POLARIZATION_MODE,APPROVAL_RAW_BTS.ANTENNA_AZIMUTH,APPROVAL_RAW_BTS.FEEDER_LOSS,APPROVAL_RAW_BTS.ALTITUDE,APPROVAL_RAW_BTS.SET_YEAR,APPROVAL_RAW_BTS.SET_MONTH,APPROVAL_RAW_BTS.ORG_TYPE," +
            "APPROVAL_RAW_BTS.EXPAND_STATION,APPROVAL_RAW_BTS.ATTRIBUTE_STATION,APPROVAL_RAW_BTS.AT_RANG,APPROVAL_RAW_BTS.AT_EANG,APPROVAL_RAW_BTS.ST_SERV_R,'1',EXPIRE_DATE,APPROVAL_RAW_BTS.BTS_DATA_TYPE,TRF_USER,TRF_DATE,TRF_DATA,ST_SCENE " +
            "from APPROVAL_RAW_BTS,APPROVAL_TRANSPORT_JOB " +
            "where APPROVAL_RAW_BTS.APP_GUID = APPROVAL_TRANSPORT_JOB.GUID " +
            "and APPROVAL_RAW_BTS.APP_GUID = #{appGuid}")
    int insertByTransportRawBts(@Param("appGuid")String appGuid);

    /**
     * 根据job删除
     * */
    @Delete("delete APPROVAL_SCHEDULE where JOB_GUID = #{jobGuid}")
    int deleteAllByJob(@Param("jobGuid")String jobGuid);
}
