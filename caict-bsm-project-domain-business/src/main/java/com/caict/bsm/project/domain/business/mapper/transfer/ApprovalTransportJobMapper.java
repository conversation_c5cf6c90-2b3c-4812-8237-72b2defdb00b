package com.caict.bsm.project.domain.business.mapper.transfer;

import com.caict.bsm.project.system.data.mapper.BasicMapper;
import com.caict.bsm.project.system.model.dto.business.transfer.ApprovalTransportJobDTO;
import com.caict.bsm.project.system.model.dto.business.transfer.TransportJobDTO;
import com.caict.bsm.project.system.model.dto.security.UsersDTO;
import com.caict.bsm.project.system.model.entity.business.transfer.ApprovalTransportJob;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * Created by dengsy on 2020-04-22.
 */
@Repository
public interface ApprovalTransportJobMapper extends BasicMapper<ApprovalTransportJob> {

    /**
     * 根据jobGuid查询未提交的数据
     * */
    @Select("select * from APPROVAL_TRANSPORT_JOB where JOB_GUID = #{jobGuid} and DATA_TYPE = #{dataType} and GEN_NUM = #{genNum} " +
            "and REGION_CODE = #{regionCode} and TECH_TYPE = #{techType} and IS_APPROVED = 0")
    ApprovalTransportJobDTO findOneByJobGuidUploadRegion(@Param("jobGuid")String jobGuid, @Param("dataType")String dataType, @Param("genNum")String genNum,@Param("regionCode")String regionCode,@Param("techType")String techType);

    /**
     * 根据jobGuid查询未提交的数据
     * */
    @Select("select * from APPROVAL_TRANSPORT_JOB where JOB_GUID = #{jobGuid} and DATA_TYPE = #{dataType} and GEN_NUM = #{genNum} and IS_APPROVED = 0")
    ApprovalTransportJobDTO findOneByJobGuidUpload(@Param("jobGuid")String jobGuid, @Param("dataType")String dataType, @Param("genNum")String genNum);

    /**
     * 查询对比任务总数
     * */
    @Select("select count(*) from APPROVAL_TRANSPORT_JOB,FSA_USERS where APPROVAL_TRANSPORT_JOB.USER_GUID = FSA_USERS.ID  " +
            "and (APPROVAL_TRANSPORT_JOB.IS_COMPARE = #{isCompare,jdbcType=VARCHAR} or #{isCompare,jdbcType=VARCHAR} is null) " +
            "and (APPROVAL_TRANSPORT_JOB.GEN_NUM = #{genNum,jdbcType=VARCHAR} or #{genNum,jdbcType=VARCHAR} is null) " +
            "and (APPROVAL_TRANSPORT_JOB.GMT_CREATE >= #{appDateStart,jdbcType=VARCHAR} or #{appDateStart,jdbcType=VARCHAR} is null) " +
            "and (FSA_USERS.TYPE = #{userType,jdbcType=VARCHAR} or #{userType,jdbcType=VARCHAR} is null) " +
            "and (APPROVAL_TRANSPORT_JOB.GMT_CREATE <= #{appDateEnd,jdbcType=VARCHAR} or #{appDateStart,jdbcType=VARCHAR} is null) " )
    int selectCountCompareJobVOListByPage(@Param("isCompare")String isCompare,@Param("appDateStart")Date appDateStart,@Param("appDateEnd")Date appDateEnd,@Param("genNum")String genNum,@Param("userType") String userType);

    /**
     * 分页查询对比任务
     * */
    @Select("select APPROVAL_TRANSPORT_JOB.*,FSA_USERS.TYPE as userType from APPROVAL_TRANSPORT_JOB,FSA_USERS where APPROVAL_TRANSPORT_JOB.USER_GUID = FSA_USERS.ID " +
            "and (APPROVAL_TRANSPORT_JOB.IS_COMPARE = #{isCompare,jdbcType=VARCHAR} or #{isCompare,jdbcType=VARCHAR} is null) " +
            "and (APPROVAL_TRANSPORT_JOB.GMT_CREATE >= #{appDateStart,jdbcType=VARCHAR} or #{appDateStart,jdbcType=VARCHAR} is null) " +
            "and (APPROVAL_TRANSPORT_JOB.GMT_CREATE <= #{appDateEnd,jdbcType=VARCHAR} or #{appDateEnd,jdbcType=VARCHAR} is null) " +
            "and (APPROVAL_TRANSPORT_JOB.GEN_NUM = #{genNum,jdbcType=VARCHAR} or #{genNum,jdbcType=VARCHAR} is null) " +
            "and (FSA_USERS.TYPE = #{userType,jdbcType=VARCHAR} or #{userType,jdbcType=VARCHAR} is null) " +
            "order by APPROVAL_TRANSPORT_JOB.GMT_CREATE DESC ")
    List<ApprovalTransportJobDTO> findPageCompareJobVOListByPage(@Param("isCompare")String isCompare,@Param("appDateStart")Date appDateStart,
                                                                 @Param("appDateEnd") Date appDateEnd,@Param("genNum") String genNum,
                                                                 @Param("userType") String userType);

    /**
     * 查询异常数据总数
     * */
    @Select("select count(*) from APPROVAL_TRANSPORT_JOB,TRANSPORT_FILE,FSA_USERS " +
            "where APPROVAL_TRANSPORT_JOB.GUID = TRANSPORT_FILE.JOB_GUID and APPROVAL_TRANSPORT_JOB.USER_GUID = FSA_USERS.ID " +
            "and (APPROVAL_TRANSPORT_JOB.OP_USER_GUID = #{userId,jdbcType=VARCHAR} or #{userId,jdbcType=VARCHAR} is null) " +
            "and (FSA_USERS.TYPE = #{userType,jdbcType=VARCHAR} or #{userType,jdbcType=VARCHAR} is null) " +
            "and TRANSPORT_FILE.FILE_STATE = #{fileState} " )
    int selectCountApprovalTransportJoVOLog(@Param("userType")String userType,@Param("userId")String userId,@Param("fileState")Long fileState);

    /**
     * 分页查询异常数据总数
     * */
    @Select("select APPROVAL_TRANSPORT_JOB.*,TRANSPORT_FILE.GUID as fileGuid,TRANSPORT_FILE.FILE_LOCAL_NAME as fileName,TRANSPORT_FILE.FILE_STATE as fileState " +
            "from APPROVAL_TRANSPORT_JOB,TRANSPORT_FILE,FSA_USERS " +
            "where APPROVAL_TRANSPORT_JOB.GUID = TRANSPORT_FILE.JOB_GUID and APPROVAL_TRANSPORT_JOB.USER_GUID = FSA_USERS.ID " +
            "and (APPROVAL_TRANSPORT_JOB.OP_USER_GUID = #{userId,jdbcType=VARCHAR} or #{userId,jdbcType=VARCHAR} is null) " +
            "and (FSA_USERS.TYPE = #{userType,jdbcType=VARCHAR} or #{userType,jdbcType=VARCHAR} is null) " +
            "and TRANSPORT_FILE.FILE_STATE = #{fileState} " +
            "order by APPROVAL_TRANSPORT_JOB.GMT_CREATE desc ")
    List<ApprovalTransportJobDTO> findApprovalTransportJobVOLogPage(@Param("userType")String userType,@Param("userId")String userId,@Param("fileState")Long fileState);

    /**
     * 查询待办任务总数
     * */
    @Select("select count(*) from APPROVAL_TRANSPORT_JOB where USER_GUID in (select ID from FSA_USERS where (TYPE = #{approvalTransportJobDTO.userType,jdbcType=VARCHAR}) or #{approvalTransportJobDTO.userType,jdbcType=VARCHAR} is null) " +
            "and (APPROVAL_TRANSPORT_JOB.DATA_TYPE = #{approvalTransportJobDTO.dataType,jdbcType=VARCHAR} or #{approvalTransportJobDTO.dataType,jdbcType=VARCHAR} is null) " +
            "and (APPROVAL_TRANSPORT_JOB.GEN_NUM = #{approvalTransportJobDTO.genNum,jdbcType=VARCHAR} or #{approvalTransportJobDTO.genNum,jdbcType=VARCHAR} is null) "
    )
    int selectCountByWhere(@Param("approvalTransportJobDTO")ApprovalTransportJobDTO approvalTransportJobDTO);

    /**
     * 分页查询待办任务
     * */
    @Select("select APPROVAL_TRANSPORT_JOB.*,TRANSPORT_JOB.JOB_NAME as jobName from APPROVAL_TRANSPORT_JOB,TRANSPORT_JOB " +
            "where APPROVAL_TRANSPORT_JOB.JOB_GUID = TRANSPORT_JOB.GUID " +
            "and APPROVAL_TRANSPORT_JOB.USER_GUID in (select ID from FSA_USERS where TYPE = #{approvalTransportJobDTO.userType,jdbcType=VARCHAR} or #{approvalTransportJobDTO.userType,jdbcType=VARCHAR} is null) " +
            "and (APPROVAL_TRANSPORT_JOB.DATA_TYPE = #{approvalTransportJobDTO.dataType,jdbcType=VARCHAR} or #{approvalTransportJobDTO.dataType,jdbcType=VARCHAR} is null) " +
            "and (APPROVAL_TRANSPORT_JOB.GEN_NUM = #{approvalTransportJobDTO.genNum,jdbcType=VARCHAR} or #{approvalTransportJobDTO.genNum,jdbcType=VARCHAR} is null) " +
            "order by APPROVAL_TRANSPORT_JOB.GMT_CREATE desc ")
    List<ApprovalTransportJobDTO> findAllPageByWhere(@Param("approvalTransportJobDTO")ApprovalTransportJobDTO approvalTransportJobDTO);

    /**
     * 查看审核列表历史记录总数
     * */
    @Select("select count(*) from APPROVAL_TRANSPORT_JOB,TRANSPORT_JOB,FSA_USERS where TRANSPORT_JOB.GUID = APPROVAL_TRANSPORT_JOB.JOB_GUID and APPROVAL_TRANSPORT_JOB.USER_GUID = FSA_USERS.ID  " +
            "and (APPROVAL_TRANSPORT_JOB.USER_GUID = #{approvalTransportJobDTO.userGuid,jdbcType=VARCHAR} or #{approvalTransportJobDTO.userGuid,jdbcType=VARCHAR} is null) " +
            "and (APPROVAL_TRANSPORT_JOB.GMT_CREATE >= #{approvalTransportJobDTO.appDateStart,jdbcType=VARCHAR} or #{approvalTransportJobDTO.appDateStart,jdbcType=VARCHAR} is null) " +
            "and (APPROVAL_TRANSPORT_JOB.GMT_CREATE <= #{approvalTransportJobDTO.appDateEnd,jdbcType=VARCHAR} or #{approvalTransportJobDTO.appDateStart,jdbcType=VARCHAR} is null) " +
            "and (TRANSPORT_JOB.JOB_NAME like concat(concat('%',#{approvalTransportJobDTO.jobName,jdbcType=VARCHAR}),'%') or #{approvalTransportJobDTO.jobName,jdbcType=VARCHAR} is null) " +
            "and (APPROVAL_TRANSPORT_JOB.IS_APPROVED = #{approvalTransportJobDTO.isApproved,jdbcType=VARCHAR} or #{approvalTransportJobDTO.isApproved,jdbcType=VARCHAR} is null) " +
            "and (APPROVAL_TRANSPORT_JOB.IS_COMPARE = #{approvalTransportJobDTO.isCompare,jdbcType=VARCHAR} or #{approvalTransportJobDTO.isCompare,jdbcType=VARCHAR} is null) " +
            "and (FSA_USERS.TYPE = #{approvalTransportJobDTO.userType,jdbcType=VARCHAR} or #{approvalTransportJobDTO.userType,jdbcType=VARCHAR} is null) ")
    int selectCountAppTransportJob(@Param("approvalTransportJobDTO")ApprovalTransportJobDTO approvalTransportJobDTO);

    /**
     * 分页查看审核列表历史记录
     * */
    @Select("select APPROVAL_TRANSPORT_JOB.*,TRANSPORT_JOB.JOB_NAME as jobName,FSA_USERS.TYPE as userType " +
            "from APPROVAL_TRANSPORT_JOB,TRANSPORT_JOB,FSA_USERS " +
            "where TRANSPORT_JOB.GUID = APPROVAL_TRANSPORT_JOB.JOB_GUID and APPROVAL_TRANSPORT_JOB.USER_GUID = FSA_USERS.ID " +
            "and (APPROVAL_TRANSPORT_JOB.USER_GUID = #{approvalTransportJobDTO.userGuid,jdbcType=VARCHAR} or #{approvalTransportJobDTO.userGuid,jdbcType=VARCHAR} is null) " +
            "and (APPROVAL_TRANSPORT_JOB.GMT_CREATE >= #{approvalTransportJobDTO.appDateStart,jdbcType=VARCHAR} or #{approvalTransportJobDTO.appDateStart,jdbcType=VARCHAR} is null) " +
            "and (APPROVAL_TRANSPORT_JOB.GMT_CREATE <= #{approvalTransportJobDTO.appDateEnd,jdbcType=VARCHAR} or #{approvalTransportJobDTO.appDateStart,jdbcType=VARCHAR} is null) " +
            "and (TRANSPORT_JOB.JOB_NAME like concat(concat('%',#{approvalTransportJobDTO.jobName,jdbcType=VARCHAR}),'%') or #{approvalTransportJobDTO.jobName,jdbcType=VARCHAR} is null) " +
            "and (APPROVAL_TRANSPORT_JOB.IS_APPROVED = #{approvalTransportJobDTO.isApproved,jdbcType=VARCHAR} or #{approvalTransportJobDTO.isApproved,jdbcType=VARCHAR} is null) " +
            "and (APPROVAL_TRANSPORT_JOB.IS_COMPARE = #{approvalTransportJobDTO.isCompare,jdbcType=VARCHAR} or #{approvalTransportJobDTO.isCompare,jdbcType=VARCHAR} is null) " +
            "and (APPROVAL_TRANSPORT_JOB.DATA_TYPE = #{approvalTransportJobDTO.dataType,jdbcType=VARCHAR} or #{approvalTransportJobDTO.dataType,jdbcType=VARCHAR} is null) " +
            "and (APPROVAL_TRANSPORT_JOB.GEN_NUM = #{approvalTransportJobDTO.genNum,jdbcType=VARCHAR} or #{approvalTransportJobDTO.genNum,jdbcType=VARCHAR} is null) " +
            "and (FSA_USERS.TYPE = #{approvalTransportJobDTO.userType,jdbcType=VARCHAR} or #{approvalTransportJobDTO.userType,jdbcType=VARCHAR} is null) " +
            "order by APPROVAL_TRANSPORT_JOB.GMT_CREATE desc ")
    List<ApprovalTransportJobDTO> findAllPageAppTransportJob(@Param("approvalTransportJobDTO")ApprovalTransportJobDTO approvalTransportJobDTO);

    @Select("select count(1) from APPROVAL_TRANSPORT_JOB where JOB_GUID = #{jobId} and IS_COMPARE != #{isCompare}")
    int findNotComplete(@Param("jobId")String jobId,@Param("isCompare")String isCompare);

    @Select("select APPROVAL_TRANSPORT_JOB.*,FSA_USERS.TYPE AS userType " +
            "from APPROVAL_TRANSPORT_JOB,FSA_USERS " +
            "where APPROVAL_TRANSPORT_JOB.USER_GUID = FSA_USERS.ID " +
            "and (FSA_USERS.TYPE = #{usersDTO.type,jdbcType=VARCHAR} or #{usersDTO.type,jdbcType=VARCHAR} = 'wuwei') " +
            "and (FSA_USERS.TYPE = #{approvalTransportJobDTO.userType,jdbcType=VARCHAR} or #{approvalTransportJobDTO.userType,jdbcType=VARCHAR} is null) " +
            "and (APPROVAL_TRANSPORT_JOB.GMT_CREATE >= #{approvalTransportJobDTO.appDateStart,jdbcType=VARCHAR} or #{approvalTransportJobDTO.appDateStart,jdbcType=VARCHAR} is null) " +
            "and (APPROVAL_TRANSPORT_JOB.GMT_CREATE <= #{approvalTransportJobDTO.appDateEnd,jdbcType=VARCHAR} or #{approvalTransportJobDTO.appDateStart,jdbcType=VARCHAR} is null) " +
            "and (APPROVAL_TRANSPORT_JOB.IS_COMPARE = #{approvalTransportJobDTO.isCompare,jdbcType=VARCHAR} or #{approvalTransportJobDTO.isCompare,jdbcType=VARCHAR} is null) " +
            "and (APPROVAL_TRANSPORT_JOB.DATA_TYPE = #{approvalTransportJobDTO.dataType,jdbcType=VARCHAR} or #{approvalTransportJobDTO.dataType,jdbcType=VARCHAR} is null) " +
            "and (APPROVAL_TRANSPORT_JOB.GEN_NUM = #{approvalTransportJobDTO.genNum,jdbcType=VARCHAR} or #{approvalTransportJobDTO.genNum,jdbcType=VARCHAR} is null) " +
            "and APPROVAL_TRANSPORT_JOB.REGION_CODE in (select code from FSA_REGION start with ID = #{usersDTO.regionId,jdbcType=VARCHAR} connect by prior ID = PARENT_ID) " +
            "order by APPROVAL_TRANSPORT_JOB.GMT_CREATE desc ")
    List<ApprovalTransportJobDTO> findListByPage(@Param("approvalTransportJobDTO")ApprovalTransportJobDTO approvalTransportJobDTO, @Param("usersDTO")UsersDTO usersDTO);

    @Select("select count(*) from APPROVAL_TRANSPORT_JOB where job_guid = #{jobGuid} and is_compare not in ('18','19')")
    int judgeComplete(@Param("jobGuid")String jobGuid);

    @Select("select * from APPROVAL_TRANSPORT_JOB where job_guid = #{jobGuid} and region_code = #{regionCode}")
    List<ApprovalTransportJob> findByJobIdAndRegionCode(@Param("jobGuid")String jobGuid,@Param("regionCode")String regionCode);

    @Select("select count(*) from APPROVAL_TRANSPORT_JOB where region_code = #{regionCode} and USER_GUID = #{userGuid} and is_compare not in ('18','19')")
    int judgeCompleteByRegion(@Param("regionCode")String regionCode,@Param("userGuid")String userGuid);

    /**
     * 待办事项job branch查询
     *
     * @param usersDTO                usersDTO
     * @param approvalTransportJobDTO approvalTransportJobDTO
     * @return list
     */
    @Select("select APPROVAL_TRANSPORT_JOB.*,FSA_USERS.TYPE AS userType from APPROVAL_TRANSPORT_JOB,FSA_USERS where APPROVAL_TRANSPORT_JOB.USER_GUID = FSA_USERS.ID " +
            "and APPROVAL_TRANSPORT_JOB.JOB_GUID =  #{approvalTransportJobDTO.jobGuid,jdbcType=VARCHAR} " +
            "and (APPROVAL_TRANSPORT_JOB.GMT_CREATE >= #{approvalTransportJobDTO.appDateStart,jdbcType=VARCHAR} or #{approvalTransportJobDTO.appDateStart,jdbcType=VARCHAR} is null) " +
            "and (APPROVAL_TRANSPORT_JOB.GMT_CREATE <= #{approvalTransportJobDTO.appDateEnd,jdbcType=VARCHAR} or #{approvalTransportJobDTO.appDateStart,jdbcType=VARCHAR} is null) " +
            "and (APPROVAL_TRANSPORT_JOB.IS_COMPARE = #{approvalTransportJobDTO.isCompare,jdbcType=VARCHAR} or #{approvalTransportJobDTO.isCompare,jdbcType=VARCHAR} is null) " +
            "and (APPROVAL_TRANSPORT_JOB.DATA_TYPE = #{approvalTransportJobDTO.dataType,jdbcType=VARCHAR} or #{approvalTransportJobDTO.dataType,jdbcType=VARCHAR} is null) " +
            "and (APPROVAL_TRANSPORT_JOB.GEN_NUM = #{approvalTransportJobDTO.genNum,jdbcType=VARCHAR} or #{approvalTransportJobDTO.genNum,jdbcType=VARCHAR} is null) " +
            "and APPROVAL_TRANSPORT_JOB.REGION_CODE in (select code from FSA_REGION start with ID = #{usersDTO.regionId,jdbcType=VARCHAR} connect by prior ID = PARENT_ID) " +
            "order by APPROVAL_TRANSPORT_JOB.GMT_CREATE desc ")
    List<ApprovalTransportJobDTO> findDetailListByPage(@Param("approvalTransportJobDTO")ApprovalTransportJobDTO approvalTransportJobDTO, @Param("usersDTO")UsersDTO usersDTO);

    /**
     * 待办事项job查询
     *
     * @param areaCode code
     * @param userType userType
     * @return list
     */
    @Select("select distinct j.guid, j.JOB_NAME, j.GMT_CREATE, u.TYPE as userType, j.IS_COMPARE as jobStatus from TRANSPORT_JOB j left join APPROVAL_TRANSPORT_JOB b " +
            "on b.JOB_GUID = j.GUID left join FSA_USERS u on u.ID = j.USER_GUID where 1=1 " +
            "and (b.REGION_CODE = #{areaCode,jdbcType=VARCHAR} or #{areaCode,jdbcType=VARCHAR} is null) " +
            "and (u.TYPE = #{userType,jdbcType=VARCHAR} or #{userType,jdbcType=VARCHAR} is null) order by j.GMT_CREATE desc ")
    List<TransportJobDTO> findJobListByPage(@Param("areaCode") String areaCode, @Param("userType") String userType);

    @Select("select * from APPROVAL_TRANSPORT_JOB where job_guid = #{jobId} " +
            "and (op_user = #{opStatus,jdbcType=VARCHAR} or #{opStatus,jdbcType=VARCHAR} is null ) ")
    List<ApprovalTransportJob> findNotPassList(@Param("jobId")String jobId,@Param("opStatus") String opStatus);

    /**
     * 查询当前任务中有跟上次审核不通过的任务相同的增量、代数、地区的任务
     */
    @Select("select * from APPROVAL_TRANSPORT_JOB where job_guid = #{jobId} and data_type = #{dataType} and gen_num = #{genNum} and region_code = #{regionCode} and tech_type = #{techType} ")
    ApprovalTransportJob findByDetail(@Param("jobId")String jobId,@Param("dataType")String dataType,@Param("genNum")String genNum,@Param("regionCode")String regionCode,@Param("techType")String techType);

    @Insert("INSERT INTO APPROVAL_SCHEDULE_LOG (GUID, IS_APPROVED, APP_GUID, JOB_GUID, APPLICATION_CODE, IS_VALID, CELL_NAME, CELL_ID, BTS_NAME, BTS_ID, TECH_TYPE, " +
            "LOCATION, LONGITUDE, LATITUDE, SEND_START_FREQ, SEND_END_FREQ, ACC_START_FREQ, ACC_END_FREQ, MAX_EMISSIVE_POWER, HEIGHT, DATA_TYPE, COUNTY, IS_HANDLE, " +
            "USER_GUID, VENDOR_NAME, DEVICE_MODEL, MODEL_CODE, ANTENNA_GAIN, GEN_NUM, ANTENNA_MODEL, ANTENNA_FACTORY, POLARIZATION_MODE, ANTENNA_AZIMUTH, FEEDER_LOSS, " +
            "ALTITUDE, UPDATE_TIME, ORG_TYPE, SET_YEAR, SET_MONTH, EXPAND_STATION, ATTRIBUTE_STATION, AT_RANG, AT_EANG, ST_SERV_R, BTS_DATA_TYPE) ( " +
            "select SYS_GUID(), IS_APPROVED, #{newAppGuid}, JOB_GUID, APPLICATION_CODE, IS_VALID, CELL_NAME, CELL_ID, BTS_NAME, BTS_ID, TECH_TYPE, " +
            "LOCATION, LONGITUDE, LATITUDE, SEND_START_FREQ, SEND_END_FREQ, ACC_START_FREQ, ACC_END_FREQ, MAX_EMISSIVE_POWER, HEIGHT, DATA_TYPE, COUNTY, IS_HANDLE, " +
            "USER_GUID, VENDOR_NAME, DEVICE_MODEL, MODEL_CODE, ANTENNA_GAIN, GEN_NUM, ANTENNA_MODEL, ANTENNA_FACTORY, POLARIZATION_MODE, ANTENNA_AZIMUTH, FEEDER_LOSS, " +
            "ALTITUDE, UPDATE_TIME, ORG_TYPE, SET_YEAR, SET_MONTH, EXPAND_STATION, ATTRIBUTE_STATION, AT_RANG, AT_EANG, ST_SERV_R, BTS_DATA_TYPE " +
            "from APPROVAL_SCHEDULE_LOG where APP_GUID = #{oldAppGuid} and BTS_ID in (select distinct ST_C_CODE from RSBT_STATION_BAK where APP_GUID = #{oldAppGuid} and IS_GIVEUP = 2))")
    void insertApprovalScheduleLog(@Param("newAppGuid") String newAppGuid, @Param("oldAppGuid") String oldAppGuid);

    @Select("select * from APPROVAL_TRANSPORT_JOB where job_guid = #{jobGuid} and is_compare = #{isCompare}")
    List<ApprovalTransportJob> findByJobGuidAndIsCompare(@Param("jobGuid")String jobGuid,@Param("isCompare")String isCompare);

    @Select("select count(*) from APPROVAL_TRANSPORT_JOB  " +
            "where APPROVAL_TRANSPORT_JOB.Job_Guid = (select job_guid from APPROVAL_TRANSPORT_JOB " +
            "where guid = #{appGuid}) " +
            "AND APPROVAL_TRANSPORT_JOB.is_compare NOT IN ('18','19')")
    int findByJobId(@Param("appGuid")String appGuid);

}
