package com.caict.bsm.project.domain.business.mapper.transfer;

import com.caict.bsm.project.system.data.mapper.BasicMapper;
import com.caict.bsm.project.system.model.dto.business.transfer.LogTransportJobDTO;
import com.caict.bsm.project.system.model.entity.business.transfer.LogTransportJob;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by dengsy on 2020-04-22.
 */
@Repository
public interface LogTransportJobMapper extends BasicMapper<LogTransportJob> {

    /**
     * 根据日志类型和文件id查询
     */
    @Select("select LOG_TRANSPORT_JOB.*,TRANSPORT_FILE.FILE_LOCAL_NAME as fileName from LOG_TRANSPORT_JOB,TRANSPORT_FILE where LOG_TRANSPORT_JOB.FILE_GUID = TRANSPORT_FILE.GUID and LOG_TYPE = #{logType} and FILE_GUID = #{fileGuid}")
    List<LogTransportJobDTO> findAllByLogTypeFileId(@Param("logType") Long logType, @Param("fileGuid") String fileGuid);

    /**
     * 下载校验后错误的日志
     *
     * @param fileGuid
     * @return
     */
    @Select("select LOG_DETAIL, BTS_ID, CELL_ID from LOG_TRANSPORT_JOB where GUID in (select distinct max(GUID) from LOG_TRANSPORT_JOB where FILE_GUID = #{fileGuid} group by LOG_DETAIL)")
    List<LogTransportJob> findErrorDataByFileId(@Param("fileGuid") String fileGuid);

    /**
     * 根据fileId删除日志信息
     */
    @Delete("delete from LOG_TRANSPORT_JOB where FILE_GUID = #{fileId}")
    void deleteByFileId(@Param("fileId") String fileId);

    /**
     * 根据日志类型和FileId查询总条数
     */
    @Select("select count(1) from LOG_TRANSPORT_JOB where LOG_TYPE = #{logType} and FILE_GUID = #{fileId}")
    int selectCountByTypeFileId(@Param("logType") Long logType, @Param("fileId") String fileId);
}
