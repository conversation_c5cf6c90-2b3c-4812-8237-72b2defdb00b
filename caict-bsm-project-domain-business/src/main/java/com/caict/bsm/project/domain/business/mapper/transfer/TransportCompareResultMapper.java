package com.caict.bsm.project.domain.business.mapper.transfer;


import com.caict.bsm.project.system.data.mapper.BasicMapper;
import com.caict.bsm.project.system.model.dto.business.transfer.TransportCompareResultDTO;
import com.caict.bsm.project.system.model.entity.business.transfer.TransportCompareResult;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by dengsy on 2020-04-22.
 */
@Repository
public interface TransportCompareResultMapper extends BasicMapper<TransportCompareResult> {

    @Select("select count(*) from TRANSPORT_COMPARE_RESULT t where JOB_GUID = #{jobId} and RESULT_TYPE = '1' " +
            "union all " +
            "select count(*) from TRANSPORT_COMPARE_RESULT t where JOB_GUID = #{jobId} and RESULT_TYPE = '2' " +
            "union all " +
            "select count(*) from TRANSPORT_COMPARE_RESULT t where JOB_GUID = #{jobId} and RESULT_TYPE = '3' " +
            "union all " +
            "select count(*) from TRANSPORT_COMPARE_RESULT t where JOB_GUID = #{jobId} and RESULT_TYPE = '4'")
    List<Integer> getCount(@Param("jobId") String jobId);

    /**
     * 查询详情附带发现审核后的数据
     * */
    @Select("select * from TRANSPORT_COMPARE_RESULT where GUID = #{guid}")
    @Results({
            @Result(property = "importDateid",column = "IMPORT_DATAID"),
            @Result(property = "transportRawBtsDTO",column = "IMPORT_DATAID",one = @One(select = "com.caict.bsm.project.domain.business.mapper.transfer.TransportRawBtsMapper.findOneByGuid")),
            @Result(property = "existDataid",column = "EXIST_DATAID"),
            @Result(property = "approvalScheduleLogDTO",column = "EXIST_DATAID",one = @One(select = "com.caict.bsm.project.domain.business.mapper.transfer.ApprovalScheduleLogMapper.findOneByGuid"))
    })
    TransportCompareResultDTO findOneDetail(@Param("guid")String guid);

    /**
     * 查询详情附带发现审核前数据
     * */
    @Select("select * from TRANSPORT_COMPARE_RESULT where GUID = #{guid}")
    @Results({
            @Result(property = "importDateid",column = "IMPORT_DATAID"),
            @Result(property = "transportRawBtsDTO",column = "IMPORT_DATAID",one = @One(select = "com.caict.bsm.project.domain.business.mapper.transfer.TransportRawBtsMapper.findOneByGuid")),
            @Result(property = "existDataid",column = "EXIST_DATAID"),
            @Result(property = "approvalScheduleDTO",column = "EXIST_DATAID",one = @One(select = "com.caict.bsm.project.domain.business.mapper.transfer.ApprovalScheduleMapper.findOneByGuid"))
    })
    TransportCompareResultDTO findDetail(@Param("guid")String guid);

    /**
     * 根据appGuid查询总数
     * */
    @Select("select count(*) from TRANSPORT_COMPARE_RESULT where APP_GUID = #{appGuid} and (RESULT_TYPE = #{resultType,jdbcType=VARCHAR} or #{resultType,jdbcType=VARCHAR} is null)")
    int selectCountByAppGuid(@Param("appGuid")String appGuid,@Param("resultType")String resultType);

    /**
     * 根据appGuid分页查询查询（审核后）
     * */
    @Select("select TRANSPORT_COMPARE_RESULT.*,APPROVAL_SCHEDULE_LOG.CELL_NAME as cellName,APPROVAL_SCHEDULE_LOG.CELL_ID as cellId,APPROVAL_SCHEDULE_LOG.BTS_NAME as btsName,APPROVAL_SCHEDULE_LOG.BTS_ID as btsId " +
            "from TRANSPORT_COMPARE_RESULT,APPROVAL_SCHEDULE_LOG " +
            "where TRANSPORT_COMPARE_RESULT.EXIST_DATAID = APPROVAL_SCHEDULE_LOG.GUID " +
            "and TRANSPORT_COMPARE_RESULT.APP_GUID = #{appGuid} and (RESULT_TYPE = #{resultType,jdbcType=VARCHAR} or #{resultType,jdbcType=VARCHAR} is null) " +
            "order by TRANSPORT_COMPARE_RESULT.GUID ")
    List<TransportCompareResultDTO> findAllPageByAppGuid(@Param("appGuid")String appGuid,@Param("resultType")String resultType);

    /**
     * 根据appGuid分页查询查询（审核前)
     * */
    @Select("select TRANSPORT_COMPARE_RESULT.*,APPROVAL_SCHEDULE.CELL_NAME as cellName,APPROVAL_SCHEDULE.CELL_ID as cellId,APPROVAL_SCHEDULE.BTS_NAME as btsName,APPROVAL_SCHEDULE.BTS_ID as btsId " +
            "from TRANSPORT_COMPARE_RESULT,APPROVAL_SCHEDULE " +
            "where TRANSPORT_COMPARE_RESULT.EXIST_DATAID = APPROVAL_SCHEDULE.GUID " +
            "and TRANSPORT_COMPARE_RESULT.APP_GUID = #{appGuid} and (RESULT_TYPE = #{resultType,jdbcType=VARCHAR} or #{resultType,jdbcType=VARCHAR} is null) " +
            "order by TRANSPORT_COMPARE_RESULT.GUID ")
    List<TransportCompareResultDTO> findAllPageScheduleByAppGuid(@Param("appGuid")String appGuid,@Param("resultType")String resultType);

    /**
     * 根据类型查询数量
     * */
    @Select("select count(1) from TRANSPORT_COMPARE_RESULT where APP_GUID = #{appGuid} and RESULT_TYPE = #{resultType}")
    int selectCountByResultType(@Param("appGuid")String appGuid,@Param("resultType")String resultType);

    /**
     * 根据类型查询不属于的数量
     * */
    @Select("select count(1) from TRANSPORT_COMPARE_RESULT where APP_GUID = #{appGuid} and RESULT_TYPE != #{resultType}")
    int selectCountByNotResultType(@Param("appGuid")String appGuid,@Param("resultType")String resultType);

    /**
     * 添加对比数据
     * */
    @Insert("insert into TRANSPORT_COMPARE_RESULT(GUID,JOB_GUID,IMPORT_DATAID,EXIST_DATAID,RESULT_TYPE,GMT_CREATE,USER_GUID,APP_GUID) " +
            "select APPROVAL_RAW_BTS.GUID,APPROVAL_TRANSPORT_JOB.JOB_GUID,APPROVAL_RAW_BTS.GUID as IMPORT_DATAID,APPROVAL_RAW_BTS.GUID as EXIST_DATAID,APPROVAL_RAW_BTS.DATA_TYPE as RESULT_TYPE,sysdate as GMT_CREATE,APPROVAL_RAW_BTS.USER_GUID,APPROVAL_RAW_BTS.APP_GUID " +
            "from APPROVAL_RAW_BTS,APPROVAL_TRANSPORT_JOB,dual " +
            "where APPROVAL_RAW_BTS.APP_GUID = APPROVAL_TRANSPORT_JOB.GUID and APPROVAL_RAW_BTS.APP_GUID = #{appGuid}")
    int insertByAppGuid(@Param("appGuid")String appGuid);
}
