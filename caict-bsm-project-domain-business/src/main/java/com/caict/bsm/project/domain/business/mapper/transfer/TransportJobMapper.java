package com.caict.bsm.project.domain.business.mapper.transfer;

import com.caict.bsm.project.system.data.mapper.BasicMapper;
import com.caict.bsm.project.system.model.dto.business.transfer.TransportJobDTO;
import com.caict.bsm.project.system.model.dto.business.transfer.TransportRawBtsDTO;
import com.caict.bsm.project.system.model.dto.business.transfer_in.TransportJobInDTO;
import com.caict.bsm.project.system.model.dto.security.UsersDTO;
import com.caict.bsm.project.system.model.entity.business.transfer.TransportJob;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by dengsy on 2020-04-22.
 */
@Repository
public interface TransportJobMapper extends BasicMapper<TransportJob> {

    /**
     * 根据用户类型、状态和流程查询数量
     * */
    @Select({"<script>",
            "select count(*) from TRANSPORT_JOB  ",
            "where JOB_STATE = #{jobState} ",
            "and USER_GUID = #{userId} ",
            "and IS_COMPARE in ",
            "<foreach collection='compares' item='compare' open='(' separator=',' close=')'>",
            "#{compare}",
            "</foreach>",
            "</script>"})
    int selectCountByTypeStateCompare(@Param("userId")String userId, @Param("jobState")Long jobState, @Param("compares")List<String> compares);

    /**
     * 根据用户类型、任务名称查询
     * */
    @Select("select * from TRANSPORT_JOB where USER_GUID in(select ID from FSA_USERS where TYPE = #{userType}) and JOB_NAME = #{jobName}")
    TransportJobDTO findOneByJobName(@Param("userType")String userType, @Param("jobName")String jobName);

    /**
     * 根据userType和任务流程状态查询不是同一个Job的任务
     * */
    @Select("select * from TRANSPORT_JOB where USER_GUID in (select ID from FSA_USERS where TYPE = #{userType}) and IS_COMPARE = #{isCompare} and GUID != #{jobId}")
    TransportJobDTO findOneByUserTypeAndCompareNotJobId(@Param("userType")String userType,@Param("isCompare")String isCompare,@Param("jobId")String jobId);

    /**
     * 条件查询总数
     * */
    @Select("select count(*) from TRANSPORT_JOB " +
            "where USER_GUID = #{userId,jdbcType=VARCHAR} " +
            "and (IS_COMPARE = #{transportJobDTO.isCompare,jdbcType=VARCHAR} or #{transportJobDTO.isCompare,jdbcType=VARCHAR} is null) " +
            "and (JOB_NAME like concat(concat('%',#{transportJobDTO.jobName,jdbcType=VARCHAR}),'%') or #{transportJobDTO.jobName,jdbcType=VARCHAR} is null) " +
            "and (GMT_CREATE >= #{transportJobDTO.jobDateStart,jdbcType=VARCHAR} or #{transportJobDTO.jobDateStart,jdbcType=VARCHAR} is null) " +
            "and (GMT_CREATE <= #{transportJobDTO.jobDateEnd,jdbcType=VARCHAR} or #{transportJobDTO.jobDateEnd,jdbcType=VARCHAR} is null) ")
    int selectAllCount(@Param("transportJobDTO") TransportJobDTO transportJobDTO,@Param("userId")String userId);

    /**
     * 条件分页查询
     * */
    @Select("select TRANSPORT_JOB.*,TRANSPORT_JOB.IS_COMPARE AS jobStatus,FSA_USERS.TYPE AS USERTYPE from TRANSPORT_JOB,FSA_USERS " +
            "where TRANSPORT_JOB.USER_GUID = FSA_USERS.ID " +
            "and (TRANSPORT_JOB.USER_GUID = #{userId,jdbcType=VARCHAR} or #{userId,jdbcType=VARCHAR} is null)" +
            "and (FSA_USERS.TYPE = #{transportJobDTO.userType,jdbcType=VARCHAR} or #{transportJobDTO.userType,jdbcType=VARCHAR} is null) " +
            "and (TRANSPORT_JOB.JOB_STATE = #{transportJobDTO.jobState,jdbcType=VARCHAR} or #{transportJobDTO.jobState,jdbcType=VARCHAR} is null) " +
            "and (TRANSPORT_JOB.IS_COMPARE = #{transportJobDTO.isCompare,jdbcType=VARCHAR} or #{transportJobDTO.isCompare,jdbcType=VARCHAR} is null) " +
            "and (TRANSPORT_JOB.JOB_NAME like concat(concat('%',#{transportJobDTO.jobName,jdbcType=VARCHAR}),'%') or #{transportJobDTO.jobName,jdbcType=VARCHAR} is null) " +
            "and (TRANSPORT_JOB.GMT_CREATE >= #{transportJobDTO.jobDateStart,jdbcType=DATE} or #{transportJobDTO.jobDateStart,jdbcType=DATE} is null) " +
            "and (TRANSPORT_JOB.GMT_CREATE <= #{transportJobDTO.jobDateEnd,jdbcType=DATE} or #{transportJobDTO.jobDateEnd,jdbcType=DATE} is null) " +
            "order by TRANSPORT_JOB.GMT_CREATE DESC")
    List<TransportJobDTO> findAllPage(@Param("transportJobDTO") TransportJobDTO transportJobDTO,@Param("userId")String userId);

    /**
     * 根据事件名称查询
     * */
    @Select("select * from TRANSPORT_JOB where (JOB_NAME like concat(concat('%',#{jobName,jdbcType=VARCHAR}),'%') or #{jobName,jdbcType=VARCHAR} is null) " +
            "and JOB_STATE = #{jobState} and USER_GUID = #{userId}")
    List<TransportJobDTO> findAllLikeByJobNameAndJobState(@Param("jobName")String jobName,@Param("jobState")Long jobState,@Param("userId")String userId);

    /**
     * 查询任务文件异常总数
     * */
    @Select("select count(*) from TRANSPORT_JOB,TRANSPORT_FILE " +
            "where TRANSPORT_JOB.GUID = TRANSPORT_FILE.JOB_GUID " +
            "and (TRANSPORT_JOB.JOB_NAME like concat(concat('%',#{jobName,jdbcType=VARCHAR}),'%') or #{jobName,jdbcType=VARCHAR} is null)" +
            "and TRANSPORT_JOB.USER_GUID = #{userId} and TRANSPORT_FILE.FILE_STATE = #{fileState} " +
            "order by TRANSPORT_JOB.GMT_CREATE desc ")
    int selectCountTransportJobVOLogCsv(@Param("jobName")String jobName,@Param("userId")String userId,@Param("fileState")Long fileState);

    /**
     * 分页查询任务文件异常列表
     * */
    @Select("select TRANSPORT_JOB.*,TRANSPORT_FILE.GUID as fileGuid,TRANSPORT_FILE.FILE_LOCAL_NAME as fileName,TRANSPORT_FILE.FILE_STATE as fileState " +
            "from TRANSPORT_JOB,TRANSPORT_FILE " +
            "where TRANSPORT_JOB.GUID = TRANSPORT_FILE.JOB_GUID " +
            "and (TRANSPORT_JOB.JOB_NAME like concat(concat('%',#{jobName,jdbcType=VARCHAR}),'%') or #{jobName,jdbcType=VARCHAR} is null) " +
            "and TRANSPORT_JOB.USER_GUID = #{userId} and TRANSPORT_FILE.FILE_STATE = #{fileState} " +
            "order by TRANSPORT_JOB.GMT_CREATE desc ")
    List<TransportJobDTO> selectTransportJobVOLogCsv(@Param("jobName")String jobName, @Param("userId")String userId,@Param("fileState")Long fileState);

    /**
     * 根据业务流程状态查询总数
     * */
    @Select("select count(*) from TRANSPORT_JOB where IS_COMPARE = #{isCompare}")
    int selectCountByIsCompareOrderGmtModified(@Param("isCompare")String isCompare);

    /**
     * 根据业务流程状态分页查询
     * */
    @Select("select * from TRANSPORT_JOB " +
            "where IS_COMPARE = #{isCompare} " +
            "order by TRANSPORT_JOB.GMT_MODIFIED ")
    List<TransportJobDTO> findAllPageByIsCompareOrderGmtModified(@Param("isCompare")String isCompare);

    /**
     * 查询详情
     * */
    @Select("select * from TRANSPORT_JOB where GUID = #{jobGuid}")
    @Results({
            @Result(property = "guid",column = "GUID"),
            @Result(property = "transportFileDTOList",column = "GUID",many = @Many(select = "com.caict.bsm.project.domain.business.mapper.transfer.TransportFileMapper.findOperatorsAllByJobGuid")),
            @Result(property = "transportFileAttachedDTOList",column = "GUID",many = @Many(select = "com.caict.bsm.project.domain.business.mapper.transfer.TransportFileMapper.findEnclosureAllByJobGuid"))
    })
    TransportJobDTO findOneByGuid(@Param("jobGuid")String jobGuid);

    @Select("select * from TRANSPORT_JOB WHERE IS_COMPARE != '10'")
    List<TransportJob> findJob();


    /**
     * 条件分页查询
     * */
    @Select("select TRANSPORT_JOB.*,FSA_USERS.TYPE AS USERTYPE from TRANSPORT_JOB,FSA_USERS " +
            "where TRANSPORT_JOB.USER_GUID = FSA_USERS.ID " +
            "and (TRANSPORT_JOB.IS_COMPARE = #{transportJobInDTO.isCompare,jdbcType=VARCHAR} or #{transportJobInDTO.isCompare,jdbcType=VARCHAR} is null) " +
            "and (TRANSPORT_JOB.JOB_NAME like concat(concat('%',#{transportJobInDTO.jobName,jdbcType=VARCHAR}),'%') or #{transportJobInDTO.jobName,jdbcType=VARCHAR} is null) " +
            "and (TRANSPORT_JOB.GMT_CREATE >= #{transportJobInDTO.appDateStart,jdbcType=VARCHAR} or #{transportJobInDTO.appDateStart,jdbcType=VARCHAR} is null) " +
            "and (TRANSPORT_JOB.GMT_CREATE <= #{transportJobInDTO.appDateEnd,jdbcType=VARCHAR} or #{transportJobInDTO.appDateEnd,jdbcType=VARCHAR} is null) " +
            "and (FSA_USERS.TYPE = #{transportJobInDTO.userType,jdbcType=VARCHAR} or #{transportJobInDTO.userType,jdbcType=VARCHAR} is null) " +
            "and (FSA_USERS.REGION_ID = #{usersDTO.regionId,jdbcType=VARCHAR} or #{usersDTO.regionId,jdbcType=VARCHAR} is null) " +
            "order by TRANSPORT_JOB.GMT_CREATE DESC")
    List<TransportJobInDTO> findInAllPage(@Param("transportJobInDTO") TransportJobInDTO transportJobInDTO, @Param("usersDTO")UsersDTO usersDTO);

    /**
     * 根据用户查询全部
     * */
    @Select("select * from TRANSPORT_JOB where USER_GUID = #{userId} order by JOB_DATE desc")
    List<TransportJobDTO> findAllByUser(@Param("userId")String userId);

    /**
     * 根据用户查询全部
     * */
    @Select("<script>" +
            "select TRANSPORT_RAW_BTS.* from TRANSPORT_JOB,TRANSPORT_RAW_BTS where TRANSPORT_JOB.GUID = TRANSPORT_RAW_BTS.JOB_GUID " +
            "and TRANSPORT_JOB.guid = #{guid} " +
            "<if test='regionCode!=null and regionCode!= \"\"'>" +
            "and TRANSPORT_RAW_BTS.REGION_CODE = #{regionCode} " +
            "</if>" +
            "and TRANSPORT_RAW_BTS.DATA_TYPE != #{dataType} " +
            "</script>")
    List<TransportRawBtsDTO> findIncrease(@Param("guid")String guid,@Param("regionCode")String regionCode,@Param("dataType")String dataType);

    /**
     * 根据branch地区code查询
     *
     * @param areaCode areaCode
     * @return list
     */
    @Select("select distinct j.guid, j.JOB_NAME, j.GMT_CREATE, u.TYPE as userType, b.REGION_CODE, j.IS_COMPARE as jobStatus from TRANSPORT_JOB j left join TRANSPORT_JOB_BRANCH b " +
            "on b.JOB_GUID = j.GUID left join FSA_USERS u on u.ID = j.USER_GUID where b.REGION_CODE = #{areaCode} order by j.GMT_CREATE desc ")
    List<TransportJobDTO> findJobPageByBranchCode(@Param("areaCode") String areaCode);

    @Update("update TRANSPORT_JOB set is_deleted = #{isDeal} where guid = #{jobId}")
    int updateIsDeleted(@Param("jobId")String jobId,@Param("isDeal")String isDeal);

    @Select("select * from ( " +
            "select * from TRANSPORT_JOB a,fsa_users b where a.user_guid = b.id  " +
            "            and a.is_compare = '10' order by a.GMT_CREATE ) where rownum = 1")
    TransportJob findByStatusOrderByDate();

    @Select("select apply_job_in.app_code from transport_job " +
            "left join approval_transport_job " +
            "on transport_job.guid = approval_transport_job.job_guid " +
            "left join apply_job_in on approval_transport_job.guid = apply_job_in.app_guid " +
            "where apply_job_in.is_compare = '2'")
    List<String> findAppCodeList(@Param("jobGuid")String jobGuid);

    @Select("UPDATE TRANSPORT_JOB set is_compare = #{status} " +
            "where GUID = #{guid}")
    void updateCompleteByGuid(@Param("guid")String guid,@Param("status")String status);

}
