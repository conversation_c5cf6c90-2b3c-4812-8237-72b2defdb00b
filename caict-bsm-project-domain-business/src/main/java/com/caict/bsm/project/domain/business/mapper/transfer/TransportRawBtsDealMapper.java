package com.caict.bsm.project.domain.business.mapper.transfer;


import com.caict.bsm.project.system.data.mapper.BasicMapper;
import com.caict.bsm.project.system.model.dto.business.transfer.TransportRawBtsDealDTO;
import com.caict.bsm.project.system.model.dto.business.transfer.TransportRawBtsDealShowDTO;
import com.caict.bsm.project.system.model.entity.business.transfer.TransportRawBtsDeal;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by dengsy on 2020-04-22.
 */
@Repository
public interface TransportRawBtsDealMapper extends BasicMapper<TransportRawBtsDeal> {

    /**
     * 根据job删除
     * */
    @Delete("delete from TRANSPORT_RAW_BTS_DEAL where JOB_GUID = #{jobId}")
    int deleteByJobId(@Param("jobId") String jobId);

    @Delete("delete from TRANSPORT_RAW_BTS_DEAL where JOB_GUID = #{jobId} AND DATA_TYPE = #{dataType} AND GEN_NUM = #{genNum}")
    int deleteByJobIdDataTypeGenNum(@Param("jobId")String jobId,@Param("dataType")String dataType,@Param("genNum")String genNum);

    @Update("update TRANSPORT_RAW_BTS_DEAL set is_confirm = '1' where job_guid = #{jobId} and bts_data_type = #{dataType} and gen_num = #{genNum} ")
    void updateConfirm(@Param("jobId")String jobId,@Param("dataType")String dataType,@Param("genNum")String genNum);

    /**
     * 根据job流程状态用户查询总数
     * */
    @Select("select count(*) from TRANSPORT_RAW_BTS_DEAL,TRANSPORT_JOB_BRANCH " +
            "where TRANSPORT_RAW_BTS_DEAL.JOB_GUID = TRANSPORT_JOB_BRANCH.JOB_GUID and TRANSPORT_JOB_BRANCH.IS_COMPARE in('3','4') " +
            "and TRANSPORT_JOB_BRANCH.USER_GUID = #{userId} " +
            "AND TRANSPORT_RAW_BTS_DEAL.DATA_TYPE = TRANSPORT_JOB_BRANCH.DATA_TYPE " +
            "AND TRANSPORT_RAW_BTS_DEAL.GEN_NUM = TRANSPORT_JOB_BRANCH.GEN_NUM " +
            "and (TRANSPORT_RAW_BTS_DEAL.CELL_NAME like concat(concat('%',#{transportRawBtsDealDTO.cellName,jdbcType=VARCHAR}),'%') or #{transportRawBtsDealDTO.cellName,jdbcType=VARCHAR} is null) " +
            "and (TRANSPORT_RAW_BTS_DEAL.CELL_ID = #{transportRawBtsDealDTO.cellId,jdbcType=VARCHAR} or #{transportRawBtsDealDTO.cellId,jdbcType=VARCHAR} is null) " +
            "and (TRANSPORT_RAW_BTS_DEAL.DATA_TYPE = #{transportRawBtsDealDTO.dataType,jdbcType=VARCHAR} or #{transportRawBtsDealDTO.dataType,jdbcType=VARCHAR} is null) " +
            "and (TRANSPORT_RAW_BTS_DEAL.TECH_TYPE = #{transportRawBtsDealDTO.techType,jdbcType=VARCHAR} or #{transportRawBtsDealDTO.techType,jdbcType=VARCHAR} is null) " +
            "and (TRANSPORT_RAW_BTS_DEAL.GEN_NUM = #{transportRawBtsDealDTO.genNum,jdbcType=VARCHAR} or #{transportRawBtsDealDTO.genNum,jdbcType=VARCHAR} is null) ")
    int selectAllCount(@Param("userId")String userId,@Param("transportRawBtsDealDTO") TransportRawBtsDealDTO transportRawBtsDealDTO);

    /**
     * 根据job流程状态用户分页查询
     * */
    @Select("select TRANSPORT_RAW_BTS_DEAL_LOG.* from TRANSPORT_RAW_BTS_DEAL_LOG " +
            "where TRANSPORT_RAW_BTS_DEAL_LOG.JOB_GUID = #{transportRawBtsDealDTO.guid} " +
            "and TRANSPORT_RAW_BTS_DEAL_LOG.IS_VALID != '4' " +
            "and (TRANSPORT_RAW_BTS_DEAL_LOG.CELL_NAME like concat(concat('%',#{transportRawBtsDealDTO.cellName,jdbcType=VARCHAR}),'%') or #{transportRawBtsDealDTO.cellName,jdbcType=VARCHAR} is null) " +
            "and (TRANSPORT_RAW_BTS_DEAL_LOG.CELL_ID = #{transportRawBtsDealDTO.cellId,jdbcType=VARCHAR} or #{transportRawBtsDealDTO.cellId,jdbcType=VARCHAR} is null) " +
            "and (TRANSPORT_RAW_BTS_DEAL_LOG.DATA_TYPE = #{transportRawBtsDealDTO.dataType,jdbcType=VARCHAR} or #{transportRawBtsDealDTO.dataType,jdbcType=VARCHAR} is null) " +
            "and (TRANSPORT_RAW_BTS_DEAL_LOG.TECH_TYPE = #{transportRawBtsDealDTO.techType,jdbcType=VARCHAR} or #{transportRawBtsDealDTO.techType,jdbcType=VARCHAR} is null) " +
            "and (TRANSPORT_RAW_BTS_DEAL_LOG.GEN_NUM = #{transportRawBtsDealDTO.genNum,jdbcType=VARCHAR} or #{transportRawBtsDealDTO.genNum,jdbcType=VARCHAR} is null) " +
            "and (TRANSPORT_RAW_BTS_DEAL_LOG.JOB_GUID = #{transportRawBtsDealDTO.jobGuid,jdbcType=VARCHAR} or #{transportRawBtsDealDTO.jobGuid,jdbcType=VARCHAR} is null) "+
            "order by TRANSPORT_RAW_BTS_DEAL_LOG.CELL_ID")
    List<TransportRawBtsDealDTO> findAllPageOld(@Param("userId")String userId,@Param("transportRawBtsDealDTO")TransportRawBtsDealDTO transportRawBtsDealDTO);

    /**
     * 根据job流程状态用户分页查询
     *
     * @param userId
     * @param dealDto
     * @param dataType1
     * @param dataType2
     * @return list
     */
    @Select("select a.BTS_ID, a.BTS_NAME, a.TECH_TYPE,a.GEN_NUM, a.LOCATION, a.LATITUDE, a.LONGITUDE, a.JOB_GUID,a.BTS_DATA_TYPE,a.DATA_TYPE from TRANSPORT_RAW_BTS_DEAL a,TRANSPORT_JOB_BRANCH b" +
            " where a.JOB_GUID = b.JOB_GUID AND b.IS_COMPARE in ('3','4') AND a.BTS_DATA_TYPE = b.DATA_TYPE AND a.GEN_NUM = b.GEN_NUM" +
            " AND (b.USER_GUID = #{userId,jdbcType=VARCHAR} OR #{userId,jdbcType=VARCHAR} is null)" +
            " AND (a.JOB_GUID = #{dealDto.jobGuid,jdbcType=VARCHAR} OR #{dealDto.jobGuid,jdbcType=VARCHAR} is null)" +
            " AND (a.BTS_ID LIKE concat(concat('%',#{dealDto.btsId,jdbcType=VARCHAR}),'%') OR #{dealDto.btsId,jdbcType=VARCHAR} is null)" +
            " AND (a.BTS_NAME LIKE concat(concat('%',#{dealDto.btsName,jdbcType=VARCHAR}),'%') OR #{dealDto.btsName,jdbcType=VARCHAR} is null)" +
            " AND (a.TECH_TYPE = #{dealDto.techType,jdbcType=VARCHAR} or #{dealDto.techType,jdbcType=VARCHAR} is null)" +
            " AND (a.BTS_DATA_TYPE = #{dealDto.dataType,jdbcType=VARCHAR} or #{dealDto.dataType,jdbcType=VARCHAR} is null)" +
            " AND (a.GEN_NUM = #{dealDto.genNum,jdbcType=VARCHAR} or #{dealDto.genNum,jdbcType=VARCHAR} is null) " +
            " AND ((a.DATA_TYPE = #{dataType1,jdbcType=VARCHAR} or #{dataType1,jdbcType=VARCHAR} is null) " +
            " or (a.DATA_TYPE = #{dataType2,jdbcType=VARCHAR} or #{dataType2,jdbcType=VARCHAR} is null) " +
            " or (a.DATA_TYPE = #{dataType3,jdbcType=VARCHAR} or #{dataType3,jdbcType=VARCHAR} is null)) " +
            " group by a.BTS_ID, a.BTS_NAME, a.TECH_TYPE,a.GEN_NUM, a.LOCATION, a.LATITUDE, a.LONGITUDE, a.JOB_GUID,a.BTS_DATA_TYPE,a.DATA_TYPE order by a.BTS_ID,a.DATA_TYPE")
    List<TransportRawBtsDealDTO> findAllPage(@Param("userId")String userId,@Param("dealDto")TransportRawBtsDealDTO dealDto,@Param("dataType1")String dataType1, @Param("dataType2")String dataType2, @Param("dataType3")String dataType3);

    /**
     * 下载查询总数
     * */
    @Select({"<script>",
            "select count(*) from TRANSPORT_RAW_BTS_DEAL_LOG ",
            "WHERE (TRANSPORT_JOB.USER_GUID = #{userId,jdbcType=VARCHAR} OR #{userId,jdbcType=VARCHAR} is null) ",
            "and TRANSPORT_RAW_BTS_DEAL_LOG.IS_VALID != '4' " +
            "AND (TRANSPORT_RAW_BTS_DEAL_LOG.JOB_GUID = #{transportRawBtsDealDTO.jobGuid,jdbcType=VARCHAR} OR #{transportRawBtsDealDTO.jobGuid,jdbcType=VARCHAR} is null) ",
            "AND (TRANSPORT_RAW_BTS_DEAL_LOG.CELL_ID = #{transportRawBtsDealDTO.cellId,jdbcType=VARCHAR} or #{transportRawBtsDealDTO.cellId,jdbcType=VARCHAR} is null) ",
            "AND (TRANSPORT_RAW_BTS_DEAL_LOG.CELL_NAME LIKE concat(concat('%',#{transportRawBtsDealDTO.cellName,jdbcType=VARCHAR}),'%') OR #{transportRawBtsDealDTO.cellName,jdbcType=VARCHAR} is null) ",
            "AND (TRANSPORT_RAW_BTS_DEAL_LOG.BTS_ID LIKE concat(concat('%',#{transportRawBtsDealDTO.btsId,jdbcType=VARCHAR}),'%') OR #{transportRawBtsDealDTO.btsId,jdbcType=VARCHAR} is null) ",
            "AND (TRANSPORT_RAW_BTS_DEAL_LOG.BTS_NAME LIKE concat(concat('%',#{transportRawBtsDealDTO.btsName,jdbcType=VARCHAR}),'%') OR #{transportRawBtsDealDTO.btsName,jdbcType=VARCHAR} is null) ",
            "AND (TRANSPORT_RAW_BTS_DEAL_LOG.DATA_TYPE LIKE concat(concat('%',#{transportRawBtsDealDTO.dataType,jdbcType=VARCHAR}),'%') OR #{transportRawBtsDealDTO.dataType,jdbcType=VARCHAR} is null) ",
            "AND (TRANSPORT_RAW_BTS_DEAL_LOG.TECH_TYPE = #{transportRawBtsDealDTO.techType,jdbcType=VARCHAR} or #{transportRawBtsDealDTO.techType,jdbcType=VARCHAR} is null) ",
            "AND (TRANSPORT_RAW_BTS_DEAL_LOG.GEN_NUM = #{transportRawBtsDealDTO.genNum,jdbcType=VARCHAR} or #{transportRawBtsDealDTO.genNum,jdbcType=VARCHAR} is null) ",
            "<when test='transportRawBtsDealDTO.guids != null and transportRawBtsDealDTO.guids.length > 0'> ",
            "AND TRANSPORT_RAW_BTS_DEAL_LOG.GUID IN ",
            "<foreach collection='transportRawBtsDealDTO.guids' item='guid' open='(' separator=',' close=')'>",
            "#{guid} ",
            "</foreach>",
            "</when>",
            "</script>"})
    int selectCountPage(@Param("userId")String userId,@Param("transportRawBtsDealDTO")TransportRawBtsDealDTO transportRawBtsDealDTO);

    /**
     * 分页下载查询
     * */
    @Select({"<script>",
            "select TRANSPORT_RAW_BTS_DEAL_LOG. * from TRANSPORT_RAW_BTS_DEAL_LOG ",
            "WHERE (TRANSPORT_RAW_BTS_DEAL_LOG.USER_GUID = #{userId,jdbcType=VARCHAR} OR #{userId,jdbcType=VARCHAR} is null) ",
            "and TRANSPORT_RAW_BTS_DEAL_LOG.IS_VALID != '4' " +
            "AND (TRANSPORT_RAW_BTS_DEAL_LOG.JOB_GUID = #{transportRawBtsDealDTO.jobGuid,jdbcType=VARCHAR} OR #{transportRawBtsDealDTO.jobGuid,jdbcType=VARCHAR} is null) ",
            "AND (TRANSPORT_RAW_BTS_DEAL_LOG.CELL_ID = #{transportRawBtsDealDTO.cellId,jdbcType=VARCHAR} or #{transportRawBtsDealDTO.cellId,jdbcType=VARCHAR} is null) ",
            "AND (TRANSPORT_RAW_BTS_DEAL_LOG.CELL_NAME LIKE concat(concat('%',#{transportRawBtsDealDTO.cellName,jdbcType=VARCHAR}),'%') OR #{transportRawBtsDealDTO.cellName,jdbcType=VARCHAR} is null) ",
            "AND (TRANSPORT_RAW_BTS_DEAL_LOG.BTS_ID LIKE concat(concat('%',#{transportRawBtsDealDTO.btsId,jdbcType=VARCHAR}),'%') OR #{transportRawBtsDealDTO.btsId,jdbcType=VARCHAR} is null) ",
            "AND (TRANSPORT_RAW_BTS_DEAL_LOG.BTS_NAME LIKE concat(concat('%',#{transportRawBtsDealDTO.btsName,jdbcType=VARCHAR}),'%') OR #{transportRawBtsDealDTO.btsName,jdbcType=VARCHAR} is null) ",
            //"AND (TRANSPORT_RAW_BTS_DEAL.DATA_TYPE LIKE concat(concat('%',#{transportRawBtsDealDTO.dataType,jdbcType=VARCHAR}),'%') OR #{transportRawBtsDealDTO.dataType,jdbcType=VARCHAR} is null) ",
            "AND (TRANSPORT_RAW_BTS_DEAL_LOG.TECH_TYPE = #{transportRawBtsDealDTO.techType,jdbcType=VARCHAR} or #{transportRawBtsDealDTO.techType,jdbcType=VARCHAR} is null) ",
            "AND (TRANSPORT_RAW_BTS_DEAL_LOG.GEN_NUM = #{genNum,jdbcType=VARCHAR} or #{genNum,jdbcType=VARCHAR} is null) ",
            "AND (TRANSPORT_RAW_BTS_DEAL_LOG.DATA_TYPE = #{dataType,jdbcType=VARCHAR} or #{dataType,jdbcType=VARCHAR} is null) ",
            "<when test='transportRawBtsDealDTO.guids != null and transportRawBtsDealDTO.guids.length > 0'> ",
            "AND TRANSPORT_RAW_BTS_DEAL_LOG.GUID IN ",
            "<foreach collection='transportRawBtsDealDTO.guids' item='guid' open='(' separator=',' close=')'>",
            "#{guid} ",
            "</foreach>",
            "</when>",
            " ORDER BY TRANSPORT_RAW_BTS_DEAL_LOG.CELL_ID ",
            "</script>"})
    List<TransportRawBtsDealDTO> selectPageOld(@Param("userId")String userId,@Param("transportRawBtsDealDTO")TransportRawBtsDealDTO transportRawBtsDealDTO,
                                            @Param("dataType")String dataType,@Param("genNum")String genNum);

    /**
     * 查询下载的数据
     *
     * @param userId
     * @param dealDto
     * @param btsDataType
     * @param genNum
     * @param dataType1
     * @param dataType2
     * @return list
     */
    @Select("select a.BTS_ID, a.BTS_NAME, a.TECH_TYPE, a.LOCATION, a.LATITUDE, a.LONGITUDE, a.JOB_GUID from TRANSPORT_RAW_BTS_DEAL a,TRANSPORT_JOB_BRANCH b" +
            " where a.JOB_GUID = b.JOB_GUID AND b.IS_COMPARE in ('3','4') AND a.BTS_DATA_TYPE = b.DATA_TYPE AND a.GEN_NUM = b.GEN_NUM" +
            " AND (b.USER_GUID = #{userId,jdbcType=VARCHAR} OR #{userId,jdbcType=VARCHAR} is null)" +
            " AND (a.JOB_GUID = #{dealDto.jobGuid,jdbcType=VARCHAR} OR #{dealDto.jobGuid,jdbcType=VARCHAR} is null)" +
            " AND (a.BTS_ID LIKE concat(concat('%',#{dealDto.btsId,jdbcType=VARCHAR}),'%') OR #{dealDto.btsId,jdbcType=VARCHAR} is null)" +
            " AND (a.BTS_NAME LIKE concat(concat('%',#{dealDto.btsName,jdbcType=VARCHAR}),'%') OR #{dealDto.btsName,jdbcType=VARCHAR} is null)" +
            " AND (a.TECH_TYPE = #{dealDto.techType,jdbcType=VARCHAR} or #{dealDto.techType,jdbcType=VARCHAR} is null)" +
            " AND (a.BTS_DATA_TYPE = #{btsDataType,jdbcType=VARCHAR} or #{btsDataType,jdbcType=VARCHAR} is null)" +
            " AND (a.GEN_NUM = #{genNum,jdbcType=VARCHAR} or #{genNum,jdbcType=VARCHAR} is null) AND (a.DATA_TYPE = #{dataType1} or a.DATA_TYPE = #{dataType2} or a.DATA_TYPE = #{dataType3}) " +
            " group by a.BTS_ID, a.BTS_NAME, a.TECH_TYPE, a.LOCATION, a.LATITUDE, a.LONGITUDE, a.JOB_GUID order by a.BTS_ID")
    List<TransportRawBtsDealDTO> selectPage(@Param("userId")String userId,@Param("dealDto")TransportRawBtsDealDTO dealDto,
                                            @Param("btsDataType")String btsDataType,@Param("genNum")String genNum, @Param("dataType1")String dataType1, @Param("dataType2")String dataType2, @Param("dataType3")String dataType3);

    /**
     * 根据流程类型查询详细数量
     * */
    @Select({"<script>",
            "select TRANSPORT_JOB.GUID as jobGuid,FSA_USERS.TYPE,TRANSPORT_JOB.IS_COMPARE as compare, ",
            "(select count(*) from TRANSPORT_RAW_BTS_DEAL where TRANSPORT_RAW_BTS_DEAL.data_type = '1' and TRANSPORT_RAW_BTS_DEAL.JOB_GUID = TRANSPORT_JOB.GUID and TRANSPORT_RAW_BTS_DEAL.is_confirm is null) as transportRawBtsDealNew, ",
            "(select count(*) from TRANSPORT_RAW_BTS_DEAL where TRANSPORT_RAW_BTS_DEAL.data_type = '2' and TRANSPORT_RAW_BTS_DEAL.JOB_GUID = TRANSPORT_JOB.GUID and TRANSPORT_RAW_BTS_DEAL.is_confirm is null) as transportRawBtsDealUpdate, ",
            "(select count(*) from TRANSPORT_RAW_BTS_DEAL where TRANSPORT_RAW_BTS_DEAL.data_type = '3' and TRANSPORT_RAW_BTS_DEAL.JOB_GUID = TRANSPORT_JOB.GUID and TRANSPORT_RAW_BTS_DEAL.is_confirm is null) as transportRawBtsDealDelete ",
            "from TRANSPORT_JOB,FSA_USERS  ",
            "where TRANSPORT_JOB.USER_GUID = FSA_USERS.ID ",
            "and TRANSPORT_JOB.IS_COMPARE in ",
            "<foreach collection='compares' item='compare' open='(' separator=',' close=')'>",
            "#{compare}",
            "</foreach>",
            "</script>"})
    List<TransportRawBtsDealShowDTO> findAllTransportRawBtsDealShowDTOOld(@Param("compares")List<String> compares);

    /**
     * 根据流程类型查询详细数量
     * */
    @Select({"<script>",
            "select TRANSPORT_JOB.GUID as jobGuid,FSA_USERS.TYPE,TRANSPORT_JOB.IS_COMPARE as compare, " +
                    "(select count(*) from(select BTS_ID from TRANSPORT_RAW_BTS_DEAL,TRANSPORT_JOB " +
                    "where TRANSPORT_RAW_BTS_DEAL.BTS_DATA_TYPE = '1' and TRANSPORT_RAW_BTS_DEAL.JOB_GUID = TRANSPORT_JOB.GUID " +
                    "  and TRANSPORT_RAW_BTS_DEAL.is_confirm is null group by BTS_ID)) as transportRawBtsDealNew, " +
                    "(select count(*) from(select BTS_ID from TRANSPORT_RAW_BTS_DEAL,TRANSPORT_JOB " +
                    "where TRANSPORT_RAW_BTS_DEAL.BTS_DATA_TYPE = '2' and TRANSPORT_RAW_BTS_DEAL.JOB_GUID = TRANSPORT_JOB.GUID " +
                    "  and TRANSPORT_RAW_BTS_DEAL.is_confirm is null group by BTS_ID)) as transportRawBtsDealUpdate, " +
                    "(select count(*) from(select BTS_ID from TRANSPORT_RAW_BTS_DEAL,TRANSPORT_JOB " +
                    "where TRANSPORT_RAW_BTS_DEAL.BTS_DATA_TYPE = '3' and TRANSPORT_RAW_BTS_DEAL.JOB_GUID = TRANSPORT_JOB.GUID " +
                    "  and TRANSPORT_RAW_BTS_DEAL.is_confirm is null group by BTS_ID)) as transportRawBtsDealDelete ",
            "from TRANSPORT_JOB,FSA_USERS where TRANSPORT_JOB.USER_GUID = FSA_USERS.ID and TRANSPORT_JOB.IS_COMPARE in ",
            "<foreach collection='compares' item='compare' open='(' separator=',' close=')'>",
            "#{compare}",
            "</foreach>",
            "</script>"})
    List<TransportRawBtsDealShowDTO> findAllTransportRawBtsDealShowDTO(@Param("compares")List<String> compares);

    /**
     * 根据job、类型查询总数
     * */
    @Select("select count(*) from TRANSPORT_RAW_BTS_DEAL where JOB_GUID = #{jobGuid} and DATA_TYPE = #{dataType} and GEN_NUM = #{genNum}")
    int selectCountByJobDataType(@Param("jobGuid")String jobGuid,@Param("dataType")String dataType,@Param("genNum")String genNum);

    /**
     * 根据Job、类型分页查询
     */
    @Select("select TRANSPORT_RAW_BTS_DEAL.*,case when LOG_TRANSPORT_JOB.LOG_DETAIL is null THEN '校验通过' else LOG_TRANSPORT_JOB.LOG_DETAIL end as logDetail " +
            "from TRANSPORT_RAW_BTS_DEAL " +
            "left join LOG_TRANSPORT_JOB on TRANSPORT_RAW_BTS_DEAL.JOB_GUID = LOG_TRANSPORT_JOB.JOB_GUID and TRANSPORT_RAW_BTS_DEAL.BTS_ID = LOG_TRANSPORT_JOB.BTS_ID and TRANSPORT_RAW_BTS_DEAL.CELL_ID = LOG_TRANSPORT_JOB.CELL_ID " +
            "where TRANSPORT_RAW_BTS_DEAL.JOB_GUID = #{jobGuid} and TRANSPORT_RAW_BTS_DEAL.DATA_TYPE = #{dataType} " +
            "and TRANSPORT_RAW_BTS_DEAL.GEN_NUM = #{genNum} " +
            "and TRANSPORT_RAW_BTS_DEAL.is_confirm is null " +
            "order by TRANSPORT_RAW_BTS_DEAL.JOB_GUID,TRANSPORT_RAW_BTS_DEAL.CELL_ID")
    List<TransportRawBtsDealDTO> findAllPageByJobDataTypeOld(@Param("jobGuid") String jobGuid, @Param("dataType") String dataType, @Param("genNum") String genNum);

    /**
     * 根据Job、类型分页查询
     */
    @Select("select TRANSPORT_RAW_BTS_DEAL.BTS_ID, BTS_NAME, TECH_TYPE, LOCATION,LATITUDE, LONGITUDE, TRANSPORT_RAW_BTS_DEAL.JOB_GUID" +
            " from TRANSPORT_RAW_BTS_DEAL left join LOG_TRANSPORT_JOB on TRANSPORT_RAW_BTS_DEAL.JOB_GUID = LOG_TRANSPORT_JOB.JOB_GUID" +
            " and TRANSPORT_RAW_BTS_DEAL.BTS_ID = LOG_TRANSPORT_JOB.BTS_ID where TRANSPORT_RAW_BTS_DEAL.JOB_GUID = #{jobGuid}" +
            " and TRANSPORT_RAW_BTS_DEAL.BTS_DATA_TYPE = #{btsIdDataType} and (DATA_TYPE = #{dataType1} or DATA_TYPE = #{dataType2} or DATA_TYPE = #{dataType3}) " +
            " and TRANSPORT_RAW_BTS_DEAL.GEN_NUM = #{genNum} and TRANSPORT_RAW_BTS_DEAL.is_confirm is null group by " +
            " TRANSPORT_RAW_BTS_DEAL.BTS_ID, BTS_NAME, TECH_TYPE, LOCATION, LATITUDE, LONGITUDE, TRANSPORT_RAW_BTS_DEAL.JOB_GUID " +
            " order by TRANSPORT_RAW_BTS_DEAL.BTS_ID ")
    List<TransportRawBtsDealDTO> findAllPageByJobDataType(@Param("jobGuid") String jobGuid, @Param("btsIdDataType") String btsIdDataType, @Param("genNum") String genNum, @Param("dataType1") String dataType1, @Param("dataType2") String dataType2, @Param("dataType3") String dataType3);

    /**
     * 审核时根据Job、类型分页查询
     */
    @Select("select TRANSPORT_RAW_BTS_DEAL.*,case when LOG_TRANSPORT_JOB.LOG_DETAIL is null THEN '校验通过' else LOG_TRANSPORT_JOB.LOG_DETAIL end as logDetail " +
            "from TRANSPORT_RAW_BTS_DEAL " +
            "left join LOG_TRANSPORT_JOB on TRANSPORT_RAW_BTS_DEAL.JOB_GUID = LOG_TRANSPORT_JOB.JOB_GUID and TRANSPORT_RAW_BTS_DEAL.BTS_ID = LOG_TRANSPORT_JOB.BTS_ID and TRANSPORT_RAW_BTS_DEAL.CELL_ID = LOG_TRANSPORT_JOB.CELL_ID " +
            "where TRANSPORT_RAW_BTS_DEAL.JOB_GUID = #{jobGuid} and TRANSPORT_RAW_BTS_DEAL.DATA_TYPE = #{dataType} " +
            "and TRANSPORT_RAW_BTS_DEAL.GEN_NUM = #{genNum} " +
            "and TRANSPORT_RAW_BTS_DEAL.is_confirm = '1' " +
            "order by TRANSPORT_RAW_BTS_DEAL.JOB_GUID,TRANSPORT_RAW_BTS_DEAL.CELL_ID")
    List<TransportRawBtsDealDTO> findCheckPageByJobDataTypeOld(@Param("jobGuid") String jobGuid, @Param("dataType") String dataType, @Param("genNum") String genNum);

    /**
     * 审核时根据Job、类型分页查询
     */
    @Select("select TRANSPORT_RAW_BTS_DEAL.BTS_ID, BTS_NAME, TECH_TYPE, LOCATION,LATITUDE, LONGITUDE, TRANSPORT_RAW_BTS_DEAL.JOB_GUID" +
            " from TRANSPORT_RAW_BTS_DEAL left join LOG_TRANSPORT_JOB on TRANSPORT_RAW_BTS_DEAL.JOB_GUID = LOG_TRANSPORT_JOB.JOB_GUID" +
            " and TRANSPORT_RAW_BTS_DEAL.BTS_ID = LOG_TRANSPORT_JOB.BTS_ID where TRANSPORT_RAW_BTS_DEAL.JOB_GUID = #{jobGuid}" +
            " and TRANSPORT_RAW_BTS_DEAL.BTS_DATA_TYPE = #{btsIdDataType} and (DATA_TYPE = #{dataType1} or DATA_TYPE = #{dataType2} or DATA_TYPE = #{dataType3}) " +
            " and TRANSPORT_RAW_BTS_DEAL.GEN_NUM = #{genNum} and TRANSPORT_RAW_BTS_DEAL.is_confirm ='1' group by " +
            " TRANSPORT_RAW_BTS_DEAL.BTS_ID, BTS_NAME, TECH_TYPE, LOCATION, LATITUDE, LONGITUDE, TRANSPORT_RAW_BTS_DEAL.JOB_GUID " +
            " order by TRANSPORT_RAW_BTS_DEAL.BTS_ID ")
    List<TransportRawBtsDealDTO> findCheckPageByJobDataType(@Param("jobGuid") String jobGuid, @Param("btsIdDataType") String btsIdDataType, @Param("genNum") String genNum, @Param("dataType1") String dataType1, @Param("dataType2") String dataType2, @Param("dataType3") String dataType3);


    /**
     * 生成下载数据
     * */
    @Insert("<script>" +
            "insert into TRANSPORT_RAW_BTS_DEAL(GUID,JOB_GUID,IS_VALID,CELL_NAME,CELL_ID,BTS_NAME,BTS_ID,TECH_TYPE,LOCATION,LONGITUDE,LATITUDE,SEND_START_FREQ,SEND_END_FREQ,ACC_START_FREQ,ACC_END_FREQ,MAX_EMISSIVE_POWER," +
            "HEIGHT,DATA_TYPE,COUNTY,IS_HANDLE,UPLOAD_DATE,IS_DOWNLOAD,USER_GUID,VENDOR_NAME,DEVICE_MODEL,MODEL_CODE,ANTENNA_GAIN,GEN_NUM,ANTENNA_MODEL,ANTENNA_FACTORY,POLARIZATION_MODE,ANTENNA_AZIMUTH,FEEDER_LOSS,ALTITUDE,SET_YEAR,SET_MONTH,ORG_TYPE,EXPAND_STATION,ATTRIBUTE_STATION,AT_RANG,AT_EANG,ST_SERV_R,REGION_CODE,TRF_USER,TRF_DATE,TRF_DATA,ST_SCENE) " +
            "select GUID,JOB_GUID,IS_VALID,CELL_NAME,CELL_ID,BTS_NAME,BTS_ID,TECH_TYPE,LOCATION,LONGITUDE,LATITUDE,SEND_START_FREQ,SEND_END_FREQ,ACC_START_FREQ,ACC_END_FREQ,MAX_EMISSIVE_POWER," +
            "HEIGHT,DATA_TYPE,COUNTY,'1',UPLOAD_DATE,'0',USER_GUID,VENDOR_NAME,DEVICE_MODEL,MODEL_CODE,ANTENNA_GAIN,GEN_NUM,ANTENNA_MODEL,ANTENNA_FACTORY,POLARIZATION_MODE,ANTENNA_AZIMUTH,FEEDER_LOSS,ALTITUDE,SET_YEAR,SET_MONTH,ORG_TYPE," +
            "EXPAND_STATION,ATTRIBUTE_STATION,AT_RANG,AT_EANG,ST_SERV_R,REGION_CODE,TRF_USER,TRF_DATE,TRF_DATA,ST_SCENE " +
            "from TRANSPORT_RAW_BTS " +
            "where job_guid = #{jobGuid} " +
            "<if test='regionCode!=null and regionCode!= \"\"'>" +
            "and REGION_CODE = #{regionCode} " +
            "</if>" +
            "and IS_VALID != 4 " +
            "and DATA_TYPE != 4 " +
            "</script>")
    int insertByTransportRawBts(@Param("jobGuid")String jobGuid,@Param("regionCode")String regionCode);

    /**
     * 根据btsId更新数据
     *
     * @param jobId jobId
     * @param btsIds list
     */
    @Update("<script>update TRANSPORT_RAW_BTS_DEAL set BTS_DATA_TYPE = #{dataType} where JOB_GUID = #{jobId} " +
            "<if test='regionCode!=null and regionCode!= \"\"'>" +
            "and REGION_CODE = #{regionCode} " +
            "</if>" +
            " and BTS_ID in " +
            "<foreach collection='btsIds' item='btsId' open='(' separator=',' close=')'>" +
            " #{btsId} " +
            "</foreach>" +
            "</script>")
    void updateByBtsIds(@Param("dataType") String dataType, @Param("jobId") String jobId, @Param("btsIds") List<String> btsIds,@Param("regionCode")String regionCode);

    /**
     * 根据jobId、btsId查询数据
     *
     * @param jobId jobId
     * @param btsId btsId
     * @return list
     */
    @Select("select * from TRANSPORT_RAW_BTS_DEAL where JOB_GUID = #{jobId} and BTS_ID = #{btsId} ORDER BY DATA_TYPE asc ")
    List<TransportRawBtsDealDTO> findAllCellByJobBtsId(@Param("jobId")String jobId,@Param("btsId")String btsId);

}
