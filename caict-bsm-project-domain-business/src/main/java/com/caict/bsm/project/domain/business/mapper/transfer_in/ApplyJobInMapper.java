package com.caict.bsm.project.domain.business.mapper.transfer_in;

import com.caict.bsm.project.system.data.mapper.BasicMapper;
import com.caict.bsm.project.system.model.dto.business.transfer.ApprovalTransportJobDTO;
import com.caict.bsm.project.system.model.dto.business.transfer_in.ApplyJobInDTO;
import com.caict.bsm.project.system.model.entity.business.transfer_in.ApplyJobIn;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by yanchengpeng on 2021/5/30.
 */
@Repository
public interface ApplyJobInMapper extends BasicMapper<ApplyJobIn> {

    @Update("update APPLY_JOB_IN set IS_COMPARE = #{status} " +
            "where app_code = #{appCode} ")
    int updateByAppCode(@Param("appGuid") String appGuid, @Param("appCode") String appCode, @Param("status") String status);

    @Select("select * from APPLY_JOB_IN " +
            "WHERE APP_GUID = #{guid}")
    List<ApplyJobInDTO> findDetailByPage(@Param("guid")String guid);

    @Select("select * from APPLY_JOB_IN where app_code = #{appCode}")
    ApplyJobInDTO selectByAppCode(@Param("appCode")String appCode);

    @Update("UPDATE APPLY_JOB_IN SET app_code = #{newAppCode} where app_code = #{oldAppCode}")
    int updateAppCode(@Param("oldAppCode")String oldAppCode,@Param("newAppCode")String newAppCode);

    @Select("select * from APPLY_JOB_IN where app_guid = #{appGuid}")
    List<ApplyJobInDTO> findByAppGuid(@Param("appGuid") String appGuid);
}
