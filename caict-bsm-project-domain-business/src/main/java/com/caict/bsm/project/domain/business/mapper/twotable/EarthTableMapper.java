package com.caict.bsm.project.domain.business.mapper.twotable;

import com.caict.bsm.project.system.data.mapper.BasicMapper;
import com.caict.bsm.project.system.model.dto.twotable.EarthTableDTO;
import com.caict.bsm.project.system.model.entity.business.twotable.EarthTable;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/12/28
 */
@Repository
public interface EarthTableMapper extends BasicMapper<EarthTable> {

    @Select("select * from FSA_EARTH_STATION " +
            "where TABLE_TYPE = #{earthTableDTO.tableType,jdbcType=VARCHAR} " +
            "and (FSA_EARTH_STATION.ID_CODE = #{earthTableDTO.idCode,jdbcType=VARCHAR} or #{earthTableDTO.idCode,jdbcType=VARCHAR} is null) ")
    List<EarthTableDTO> findEarthByPage(@Param("earthTableDTO") EarthTableDTO earthTableDTO);

    @Delete("delete from FSA_EARTH_STATION ")
    int deleteAll();
}
