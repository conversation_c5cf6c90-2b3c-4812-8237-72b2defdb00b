package com.caict.bsm.project.domain.business.mapper.twotable;

import com.caict.bsm.project.system.data.mapper.BasicMapper;
import com.caict.bsm.project.system.model.dto.twotable.ProgressTableDTO;
import com.caict.bsm.project.system.model.entity.business.twotable.ProgressTable;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/12/28
 */
@Repository
public interface ProgressTableMapper extends BasicMapper<ProgressTable> {

    @Select("select * from FSA_PROGRESS " +
            "WHERE 1=1 " +
            "and (FSA_PROGRESS.INDEX_NUM = #{progressTableDTO.indexNum,jdbcType=VARCHAR} or #{progressTableDTO.indexNum,jdbcType=VARCHAR} is null) ")
    List<ProgressTableDTO> findProcessByPage(@Param("progressTableDTO") ProgressTableDTO progressTableDTO);

    @Delete("delete from FSA_PROGRESS ")
    int deleteAll();
}
