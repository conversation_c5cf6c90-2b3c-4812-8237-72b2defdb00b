package com.caict.bsm.project.domain.business.mapper.twotable;

import com.caict.bsm.project.system.data.mapper.BasicMapper;
import com.caict.bsm.project.system.model.dto.twotable.ReformTableDTO;
import com.caict.bsm.project.system.model.entity.business.twotable.ReformTable;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/12/28
 */
@Repository
public interface ReformTableMapper extends BasicMapper<ReformTable> {

    @Select("select * from FSA_REFORM " +
            "WHERE 1=1 " +
            "and (FSA_REFORM.INDEX_NUM = #{reformTableDTO.indexNum,jdbcType=VARCHAR} or #{reformTableDTO.indexNum,jdbcType=VARCHAR} is null) ")
    List<ReformTableDTO> findReformByPage(@Param("reformTableDTO") ReformTableDTO reformTableDTO);

    @Delete("delete from FSA_REFORM ")
    int deleteAll();
}
