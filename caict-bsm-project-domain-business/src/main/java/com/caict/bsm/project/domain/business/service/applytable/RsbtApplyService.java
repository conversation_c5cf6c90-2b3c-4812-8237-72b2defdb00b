package com.caict.bsm.project.domain.business.service.applytable;

import com.caict.bsm.project.domain.business.mapper.applytable.RsbtApplyMapper;
import com.caict.bsm.project.system.data.service.BasicService;
import com.caict.bsm.project.system.model.dto.business.applytable.RsbtApplyDTO;
import com.caict.bsm.project.system.model.dto.business.applytable.table2.Table2ApplyTableDTO;
import com.caict.bsm.project.system.model.dto.business.techtable.*;
import com.caict.bsm.project.system.model.entity.business.applytable.RsbtApply;
import com.caict.bsm.project.system.utils.util.VerificationCode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * Created by dengsy on 2020-04-28.
 */
@Service
public class RsbtApplyService extends BasicService<RsbtApply> {

    @Autowired
    private RsbtApplyMapper bsmApplyTableMapper;

    /**
     * 保存（如果Id为空或者为“”则为添加，否则修改）
     * */
    public String save(RsbtApply bsmApplytable){
        if(!"".equals(bsmApplytable.getGuid()) && bsmApplytable.getGuid() != null){
            if(bsmApplyTableMapper.update(bsmApplytable) > 0){
                return bsmApplytable.getGuid()+"";
            }
        }else {
            String id = VerificationCode.myUUID();
            bsmApplytable.setGuid(id);
            if(bsmApplyTableMapper.insert(bsmApplytable) > 0){
                return id;
            }
        }
        return null;
    }

    /**
     * 删除（根据id）
     * */
    public int delete(String guid){
        RsbtApply bsmApplytable = new RsbtApply();
        bsmApplytable.setGuid(guid);
        return bsmApplyTableMapper.delete(bsmApplytable);
    }

    /**
     * 查询全部
     * */
    public List<RsbtApply> findAll(){
        RsbtApply bsmApplytable = new RsbtApply();
        return bsmApplyTableMapper.findAll(bsmApplytable);
    }

    /**
     * 根据id查询
     * */
    public RsbtApply findOne(String guid){
        RsbtApply bsmApplytable = new RsbtApply();
        bsmApplytable.setGuid(guid);
        return bsmApplyTableMapper.findOne(bsmApplytable);
    }

    public RsbtApply selectOneByOrgIdAndStationCount(String orgId, String netType, String stationCount){
        return bsmApplyTableMapper.selectOneByOrgIdAndStationCount(orgId,netType,stationCount);
    }

    public RsbtApply findOneByCode(String appCode){
        return bsmApplyTableMapper.findOneByCode(appCode);
    }

    public RsbtApply selectOneOrderByCode(){
        return bsmApplyTableMapper.selectOneOrderByCode();
    }

    /**
     * 条件查询总数
     * */
    public int selectCountByWhere(RsbtApplyDTO bsmApplytableDTO){
        return bsmApplyTableMapper.selectCountByWhere(bsmApplytableDTO);
    }

    /**
     * 分页条件查询
     * */
    public List<RsbtApplyDTO> findAllByWhere(RsbtApplyDTO bsmApplytableDTO){
        return bsmApplyTableMapper.findAllByWhere(bsmApplytableDTO);
    }

    /**
     * 内网分页条件查询
     * */
    public List<RsbtApplyDTO> findAllInByWhere(RsbtApplyDTO bsmApplytableDTO){
        return bsmApplyTableMapper.findAllInByWhere(bsmApplytableDTO);
    }

    /**
     * 根据guid查询详情DTO
     * */
    public RsbtApplyDTO findOneByGuid(String guid){
        return bsmApplyTableMapper.findOneByGuid(guid);
    }

    public Table11CommonDTO findTechTable(RsbtApplyDTO applyDTO){
        Table11CommonDTO table11CommonDTO = new Table11CommonDTO();
        RsbtApply apply = findOneByCode(applyDTO.getAppCode());

        Table11StationDataDTO station = bsmApplyTableMapper.findStation(applyDTO.getAppCode(), applyDTO.getTechNum());
        if (station!=null){
            if (apply!=null){
                station.setTechType(apply.getAppSubType());
            }
            table11CommonDTO.setStationData(station);
            table11CommonDTO.setStatAppType(station.getStatAppType());
            table11CommonDTO.setMemo(station.getMemo());
            table11CommonDTO.setStatTdi(station.getStatTdi());

            List<Table11SectorDataDTO> sectorDatas = bsmApplyTableMapper.findSectors(station.getGuid());
            if (sectorDatas!=null){
                table11CommonDTO.setSectorDatas(sectorDatas);
                station.setStCSum(sectorDatas.size());
                for (Table11SectorDataDTO table11SectorDataDTO:sectorDatas) {
                    if (table11SectorDataDTO.getEquData()!=null){
                        table11SectorDataDTO.getEquData().setEtEquSum(1);
                    }
                }
            }
            List<Table11AntFeedDataDTO> antFeeds = bsmApplyTableMapper.findAntFeed(station.getGuid());
            if (antFeeds!=null){
                table11CommonDTO.setAntFeedDatas(antFeeds);
            }
        }
        return table11CommonDTO;
    }

    public List<RsbtApply> selectExistApplytable(Set<String> appSet){
        return bsmApplyTableMapper.selectExistApplytable(appSet);
    }

    public int saves(List<RsbtApply> list){
        return bsmApplyTableMapper.insertBatch(list);
    }

    public int updateApplyStationCount(){
        return bsmApplyTableMapper.updateApplyStationCount();
    }

    public int queryApplyTableCount(){
        return bsmApplyTableMapper.queryApplyTableCount();
    }
}
