package com.caict.bsm.project.domain.business.service.area;

import com.caict.bsm.project.domain.business.mapper.area.FsaRegionMapper;
import com.caict.bsm.project.system.data.service.BasicService;
import com.caict.bsm.project.system.model.entity.business.area.FsaRegion;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by yanchengpeng on 2021/3/25.
 */
@Service
public class FsaRegionService extends BasicService<FsaRegion>{

    @Autowired
    private FsaRegionMapper fsaRegionMapper;

    /**
     * 查询下属地区
     */
    public List<FsaRegion> findAreaByRegionId(String regionId){
        return fsaRegionMapper.findAreaByRegionId(regionId);
    }

    public FsaRegion findOneByRegionCode(String regionCode){
        return fsaRegionMapper.findOneByRegionCode(regionCode);
    }

    public void updateByCode(String regionCode,String appCode){
        fsaRegionMapper.updateByCode(regionCode,appCode);
    }
}
