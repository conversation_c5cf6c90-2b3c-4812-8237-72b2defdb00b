package com.caict.bsm.project.domain.business.service.datav;

import com.caict.bsm.project.domain.business.mapper.datav.ApprovedDataVMapper;
import com.caict.bsm.project.system.data.service.BasicService;
import com.caict.bsm.project.system.model.dto.datav.ApprovedDataVDTO;
import com.caict.bsm.project.system.model.dto.security.RsbtOrgDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ApprovedDataVService extends BasicService<Object> {

    @Autowired
    private ApprovedDataVMapper approvedDataVMapper;

    /**
     * 查询所有运营商
     * */
    public List<RsbtOrgDTO> findApprovedDataOrg(){
        return approvedDataVMapper.findApprovedDataOrg();
    }

    /**
     * 信号交互数据统计
     * */
    public List<ApprovedDataVDTO> ApprovedDataGenNumStatistic(String orgCode, String dataType){
        return approvedDataVMapper.ApprovedDataGenNumStatistic(orgCode,dataType);
    }

    /**
     * 制式交互数据统计
     * */
    public List<ApprovedDataVDTO> ApprovedDataNetNumStatistic(String orgType, String dataType){
        return approvedDataVMapper.ApprovedDataNetNumStatistic(orgType,dataType);
    }

    /**
     * 分类获取各运营商各代数的基站总数
     */
    public int getStationCountByGen(String orgCode,String dataType){
        return approvedDataVMapper.getStationCountByGen(orgCode,dataType);
    }

    /**
     * 分类获取各运营商各制式的基站总数
     */
    public int getStationCountByNet(String orgType,String dataType){
        return approvedDataVMapper.getStationCountByNet(orgType,dataType);
    }
}
