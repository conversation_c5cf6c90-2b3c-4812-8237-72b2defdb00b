package com.caict.bsm.project.domain.business.service.datav;

import com.caict.bsm.project.domain.business.mapper.datav.LicenseDataVMapper;
import com.caict.bsm.project.system.data.service.BasicService;
import com.caict.bsm.project.system.model.dto.datav.LicenseDataVDTO;
import com.caict.bsm.project.system.model.dto.security.RsbtOrgDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class LicenseDataVService extends BasicService<Object> {

    @Autowired
    private LicenseDataVMapper licenseDataVMapper;

    /**
     * 查询所有运营商
     * */
    public List<RsbtOrgDTO> findLicenseOrg(){
        return licenseDataVMapper.findLicenseOrg();
    }

    /**
     * 电子执照统计
     * */
    public List<LicenseDataVDTO> licenseStatistic(LicenseDataVDTO licenseDataVDTO){
        return licenseDataVMapper.licenseStatistic(licenseDataVDTO);
    }

    /**
     * 电子执照统计
     * */
    public List<LicenseDataVDTO> licenseAreaStatistic(LicenseDataVDTO licenseDataVDTO){
        return licenseDataVMapper.licenseAreaStatistic(licenseDataVDTO);
    }
}
