package com.caict.bsm.project.domain.business.service.datav;

import com.caict.bsm.project.domain.business.mapper.datav.RsbtStationDataVMapper;
import com.caict.bsm.project.system.data.service.BasicService;
import com.caict.bsm.project.system.model.dto.datav.RsbtStationDataVDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class RsbtStationDataVService extends BasicService<Object> {

    @Autowired
    private RsbtStationDataVMapper rsbtStationDataVMapper;

    /**
     * 基站数据状态统计
     * */
    public List<RsbtStationDataVDTO> selectStationStatistics(RsbtStationDataVDTO rsbtStationDataVDTO){
        return rsbtStationDataVMapper.selectStationStatistics(rsbtStationDataVDTO);
    }

    /**
     * 按市统计基站数据状态统计
     * */
    public List<RsbtStationDataVDTO> selectAreaStationStatistics(RsbtStationDataVDTO rsbtStationDataVDTO){
        return rsbtStationDataVMapper.selectAreaStationStatistics(rsbtStationDataVDTO);
    }

    /**
     * 基站数据制式统计
     * */
    public List<RsbtStationDataVDTO> selectStationNetSum(RsbtStationDataVDTO rsbtStationDataVDTO){
        return rsbtStationDataVMapper.selectStationNetSum(rsbtStationDataVDTO);
    }

    /**
     * 基站数据制式统计
     * */
    public List<RsbtStationDataVDTO> selectAreaStationNetSum(RsbtStationDataVDTO rsbtStationDataVDTO){
        return rsbtStationDataVMapper.selectAreaStationNetSum(rsbtStationDataVDTO);
    }

    /**
     * 获取所有制式
     * */
    public List<String> findDataType(RsbtStationDataVDTO rsbtStationDataVDTO){
        return rsbtStationDataVMapper.findDataType(rsbtStationDataVDTO);
    }

    /**
     * 按区域获取所有制式
     * */
    public List<String> findAreaDataType(RsbtStationDataVDTO rsbtStationDataVDTO){
        return rsbtStationDataVMapper.findAreaDataType(rsbtStationDataVDTO);
    }


    /**
     * 新增制式统计
     * */
    public List<RsbtStationDataVDTO> selectSingleStastic(RsbtStationDataVDTO rsbtStationDataVDTO){
        return rsbtStationDataVMapper.selectSingleStastic(rsbtStationDataVDTO);
    }

    /**
     * 新增制式统计
     * */
    public List<RsbtStationDataVDTO> selectAreaSingleStastic(RsbtStationDataVDTO rsbtStationDataVDTO){
        return rsbtStationDataVMapper.selectAreaSingleStastic(rsbtStationDataVDTO);
    }


    /**
     * 获取所有信号
     * */
    public List<String> findGenNum(RsbtStationDataVDTO rsbtStationDataVDTO){
        return rsbtStationDataVMapper.findGenNum(rsbtStationDataVDTO);
    }

    /**
     * 按区域获取所有信号
     * */
    public List<String> findAreaGenNum(RsbtStationDataVDTO rsbtStationDataVDTO){
        return rsbtStationDataVMapper.findAreaGenNum(rsbtStationDataVDTO);
    }


    /**
     * 新增信号统计
     * */
    public List<RsbtStationDataVDTO> selectCommStatistic(RsbtStationDataVDTO rsbtStationDataVDTO){
        return rsbtStationDataVMapper.selectCommStatistic(rsbtStationDataVDTO);
    }

    /**
     * 新增信号统计
     * */
    public List<RsbtStationDataVDTO> selectAreaCommStatistic(RsbtStationDataVDTO rsbtStationDataVDTO){
        return rsbtStationDataVMapper.selectAreaCommStatistic(rsbtStationDataVDTO);
    }


    /**
     * 获取所有运营商
     * */
    public List<String> findOpera(RsbtStationDataVDTO rsbtStationDataVDTO){
        return rsbtStationDataVMapper.findOpera(rsbtStationDataVDTO);
    }

    /**
     * 按区域获取所有运营商
     * */
    public List<String> findAreaOpera(RsbtStationDataVDTO rsbtStationDataVDTO){
        return rsbtStationDataVMapper.findAreaOpera(rsbtStationDataVDTO);
    }


    /**
     * 新增运营商统计
     * */
    public List<RsbtStationDataVDTO> selectOperaStatistic(RsbtStationDataVDTO rsbtStationDataVDTO){
        return rsbtStationDataVMapper.selectOperaStatistic(rsbtStationDataVDTO);
    }

    /**
     * 新增运营商统计
     * */
    public List<RsbtStationDataVDTO> selectAreaOperaStatistic(RsbtStationDataVDTO rsbtStationDataVDTO){
        return rsbtStationDataVMapper.selectAreaOperaStatistic(rsbtStationDataVDTO);
    }

}
