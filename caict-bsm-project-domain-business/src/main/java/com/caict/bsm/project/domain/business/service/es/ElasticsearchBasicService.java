package com.caict.bsm.project.domain.business.service.es;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.caict.bsm.project.system.data.util.PageHandle;
import com.caict.bsm.project.system.utils.service.RedisService;
import com.caict.bsm.project.system.utils.util.JSONResult;
import com.caict.bsm.project.system.utils.util.MyDate;
import com.github.pagehelper.PageInfo;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.core.TimeValue;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.reindex.BulkByScrollResponse;
import org.elasticsearch.index.reindex.UpdateByQueryRequest;
import org.elasticsearch.script.Script;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.BucketOrder;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.sort.FieldSortBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;

import java.io.IOException;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by dengsy on 2021-8-13.
 * Elasticsearch公共service
 *
 * T：返回类型
 * P：查询条件
 * */
public class ElasticsearchBasicService<T,P> {

    private static final Logger LOG = LoggerFactory.getLogger(ElasticsearchBasicService.class);

    private Class<T> clazz;

    @Autowired
    private ElasticsearchRestTemplate elasticsearchRestTemplate;
    @Autowired
    private RestHighLevelClient restHighLevelClient;
    @Autowired
    private RedisService redisService;

    //构造方法
    public ElasticsearchBasicService(){
        //获取泛型类型
        Type type = getClass().getGenericSuperclass();
        if (type instanceof ParameterizedType){
            // 强制转化“参数化类型”
            ParameterizedType parameterizedType = (ParameterizedType) type;
            // 参数化类型中可能有多个泛型参数
            Type[] types = parameterizedType.getActualTypeArguments();
            // 获取数据的第一个元素
            clazz = (Class<T>) types[0];
        }
    }

    /**
     * 批量数据存入redis队列
     * */
    public void putRedis(String key,List<T> list){
        if (list != null) {
            int size = list.size();
            if (size > 50000) {
                for (int i = 0; i < size; i += 50000){
                    List<T> collect = list.stream().skip(i).limit(50000).collect(Collectors.toList());

                    LOG.info(key + "写入数量：" + collect.size() + " 时间： " + MyDate.dateTimeToString(new Date()));
                    redisService.lpush(key, JSONResult.getSuccessJson(collect));
                }
            }else {
                LOG.info(key + "写入数量：" + list.size() + " 时间： " + MyDate.dateTimeToString(new Date()));
                redisService.lpush(key,JSONResult.getSuccessJson(list));
            }
        }
    }

    /**
     * 批量数据读取redis队列
     * */
    public List<T> loadRedis(String key){
        JSONObject jsonObject = JSONObject.parseObject(redisService.rpop(key));
        if (jsonObject != null) return JSONArray.parseArray(jsonObject.getString("data"),clazz);
        return null;
    }

    /**
     * ES 保存
     * */
    public T save(T entity){
        return elasticsearchRestTemplate.save(entity);
    }

    /**
     * ES 批量保存
     * */
    public Iterable<T> save(List<T> list){
        return elasticsearchRestTemplate.save(list);
    }

    /**
     * ES 条件查询
     * */
    public List<T> searchHitsList(P searchVO, BoolQueryBuilder boolQueryBuilder){
        //排序
        FieldSortBuilder sortBuilder = new FieldSortBuilder("id").order(SortOrder.ASC);

        //构建查询条件
        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
//        queryBuilder.withQuery(boolQueryBuilder).withSort(sortBuilder).build();

        SearchHits<T> hits = elasticsearchRestTemplate.search(queryBuilder.build(),clazz);

        List list = new ArrayList<T>();
        hits.forEach(es -> {list.add(es.getContent());});

        return list;
    }

    /**
     * ES 分页条件查询
     * boolQueryBuilder：构建的查询条件
     * */
    public PageInfo<T> searchHitsPage(P searchVO, BoolQueryBuilder boolQueryBuilder){
        PageHandle pageHandle = new PageHandle(searchVO,"es");

        //排序
//        FieldSortBuilder sortBuilder = new FieldSortBuilder("guid.keyword").order(SortOrder.ASC);

        //分页条件
        PageRequest pageRequest = PageRequest.of(pageHandle.getPageNum() - 1,pageHandle.getPageSize());

        //构建查询条件
        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
        queryBuilder.withQuery(boolQueryBuilder).withPageable(pageRequest).build();

        SearchHits<T> hits = elasticsearchRestTemplate.search(queryBuilder.build(),clazz);

        List list = new ArrayList<T>();
        hits.forEach(es -> {list.add(es.getContent());});

        PageInfo pageInfo = pageHandle.buildPage(list);
        pageInfo.setTotal(count(boolQueryBuilder));

        return pageInfo;
    }

    /**
     * 查询条件下总数
     * boolQueryBuilder：构建的查询条件
     * */
    public long count(BoolQueryBuilder boolQueryBuilder){
        //构建查询条件
        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
        queryBuilder.withQuery(boolQueryBuilder).build();

        return elasticsearchRestTemplate.count(queryBuilder.build(),clazz);
    }

    /**
     * ES 条件统计
     * boolQueryBuilder：构建的查询条件
     * keyWord：聚合列名
     * */
    public SearchHits searchHitsCount(BoolQueryBuilder boolQueryBuilder, String keyWord){
        //构建聚合查询条件(默认返回100条）
        TermsAggregationBuilder stateAgg = AggregationBuilders.terms("count").field(keyWord).order(BucketOrder.count(false)).size(100);
        //构建查询条件
        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
        queryBuilder.withQuery(boolQueryBuilder).addAggregation(stateAgg).build();

        SearchHits<T> hits = elasticsearchRestTemplate.search(queryBuilder.build(),clazz);
        return hits;
    }

    /**
     * 设置日期段
     * boolQueryBuilder：构建的查询条件
     * */
    public void setFileDate(BoolQueryBuilder boolQueryBuilder, String keyName, String start, String end){
        //日期段
//        var startDate = MyDate.stringToDateFormatter(start,"yyyy-MM-dd");
//        var endDate = MyDate.stringToDateFormatter(end,"yyyy-MM-dd");
//        boolQueryBuilder.must(QueryBuilders.rangeQuery(keyName).gte(MyDate.dateToStringFormatter(startDate,"yyyyMMdd")).lte(MyDate.dateToStringFormatter(endDate,"yyyyMMdd")));
    }

    /**
     * 条件修改
     * boolQueryBuilder：构建的查询条件
     * script：更新内容
     * */
    public long updateByQuery(BoolQueryBuilder boolQueryBuilder, Script script){
        try {
            Document document = clazz.getAnnotation(Document.class);
            String index = document.indexName();
            //构建查询条件
            UpdateByQueryRequest request = new UpdateByQueryRequest(index);
            //版本冲突
            request.setConflicts("proceed");
            //查询内容
            request.setQuery(boolQueryBuilder);
            //批次大小
            request.setBatchSize(50000);
            //使用滚动参数来控制“搜索上下文”存活的时间
            request.setScroll(TimeValue.timeValueMinutes(10));
            //刷新索引
            request.setRefresh(true);
            //更新的内容
            request.setScript(script);
            BulkByScrollResponse response = restHighLevelClient.updateByQuery(request, RequestOptions.DEFAULT);
            long status = response.getStatus().getUpdated();
            return status;
        }catch (IOException ioException){
            LOG.error("ES条件修改异常： " + ioException.getMessage());
            return -2;
        }
    }
}
