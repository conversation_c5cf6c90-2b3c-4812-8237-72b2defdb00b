package com.caict.bsm.project.domain.business.service.es;

import com.caict.bsm.project.domain.business.repository.RsbtLicenseMainRepository;
import com.caict.bsm.project.domain.business.service.license.LicenseService;
import com.caict.bsm.project.system.model.dto.business.license.LicenseDTO;
import com.caict.bsm.project.system.model.dto.es.RsbtLicenseEsDTO;
import com.caict.bsm.project.system.model.dto.es.RsbtStationEsDTO;
import com.caict.bsm.project.system.model.dto.security.UsersDTO;
import com.caict.bsm.project.system.model.es.RsbtLicenseMainES;
import com.caict.bsm.project.system.model.es.RsbtLicenseMainVO;
import com.caict.bsm.project.system.model.es.RsbtStationMainES;
import com.caict.bsm.project.system.utils.util.DateUtils;
import com.caict.bsm.project.system.utils.util.ThreadPoolUtil;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.script.Script;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: ycp
 * @createTime: 2023/09/26 9:49
 * @company: 成渝（成都）信息通信研究院
 * @description:
 */
@Service
public class RsbtLicenseMainWebService extends ElasticsearchBasicService<RsbtLicenseMainES, RsbtLicenseMainVO> {

    private static final Logger LOG = LoggerFactory.getLogger(RsbtLicenseMainWebService.class);

    @Autowired
    private RsbtLicenseMainRepository rsbtLicenseMainRepository;
    @Autowired
    private LicenseService licenseService;

    /**
     * 批量添加
     * */
    public void saveBatch(List<RsbtLicenseMainES> list){
        if (list != null){
            int size = list.size();

            LOG.info("开启区域协查数据elastic操作，数量：" + size);
            if (size > 50000) {
                for (int i = 0; i < size; i += 50000){
                    List<RsbtLicenseMainES> collect = list.stream().skip(i).limit(50000).collect(Collectors.toList());
                    rsbtLicenseMainRepository.saveAll(collect);
                }
            }else {
                rsbtLicenseMainRepository.saveAll(list);
            }
            LOG.info("区域协查数据elastic操作结束");
        }
    }

    public PageInfo<RsbtLicenseMainES> findPage(LicenseDTO licenseDTO, UsersDTO usersDTO){
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        RsbtLicenseMainVO rsbtLicenseMainVO = new RsbtLicenseMainVO();
        rsbtLicenseMainVO.setPage(licenseDTO.getPage());
        rsbtLicenseMainVO.setRows(licenseDTO.getRows());
        if (!"wuwei".equals(usersDTO.getType())){
            boolQueryBuilder.must(QueryBuilders.matchQuery("org_name",usersDTO.getType()));
        }
        if (licenseDTO.getCodeList()!=null) boolQueryBuilder.must(QueryBuilders.termsQuery("county.keyword",licenseDTO.getCodeList()));
        if (StringUtils.isNoneBlank(licenseDTO.getOrgName())) boolQueryBuilder.must(QueryBuilders.matchQuery("org_name",licenseDTO.getOrgName()));
        if (StringUtils.isNoneBlank(licenseDTO.getNetType())) boolQueryBuilder.must(QueryBuilders.matchQuery("net_type",licenseDTO.getNetType()));
        if (StringUtils.isNoneBlank(licenseDTO.getLicenseCode())) boolQueryBuilder.must(QueryBuilders.wildcardQuery("license_code","*"+licenseDTO.getLicenseCode()+"*"));
        if (StringUtils.isNoneBlank(licenseDTO.getLicenseState())) boolQueryBuilder.must(QueryBuilders.matchQuery("license_state",licenseDTO.getLicenseState()));
        if (StringUtils.isNoneBlank(licenseDTO.getIsDownload())) boolQueryBuilder.must(QueryBuilders.matchQuery("is_download",licenseDTO.getIsDownload()));
        if (StringUtils.isNoneBlank(licenseDTO.getStationName())) boolQueryBuilder.must(QueryBuilders.wildcardQuery("station_name.keyword","*"+licenseDTO.getStationName()+"*"));
        if (StringUtils.isNoneBlank(licenseDTO.getStationCode())) boolQueryBuilder.must(QueryBuilders.wildcardQuery("station_code.keyword","*"+licenseDTO.getStationCode()+"*"));
        if (StringUtils.isNoneBlank(licenseDTO.getApplyTableCode())) boolQueryBuilder.must(QueryBuilders.wildcardQuery("apply_table_code.keyword","*"+licenseDTO.getApplyTableCode()+"*"));
        if (licenseDTO.getStationStartDate()!=null) {
            Date stationStartDate = licenseDTO.getStationStartDate();
            boolQueryBuilder.must(QueryBuilders.rangeQuery("license_date_e").lte(stationStartDate));
        }
        //范围查询
        if (licenseDTO.getMaxlat()!=null && licenseDTO.getMinlat()!=null) boolQueryBuilder.must(QueryBuilders.rangeQuery("latitude").gte(licenseDTO.getMinlat()).lte(licenseDTO.getMaxlat()));
        if (licenseDTO.getMaxlng()!=null && licenseDTO.getMinlng()!=null) boolQueryBuilder.must(QueryBuilders.rangeQuery("longitude").gte(licenseDTO.getMinlng()).lte(licenseDTO.getMaxlng()));

        return this.searchHitsPage(rsbtLicenseMainVO, boolQueryBuilder);
    }

    /**
     * 修改状态为已生成
     */
    public void updateEs(String guid){
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.must(QueryBuilders.matchQuery("station_guid", guid));
        Script script = new Script("ctx._source.is_download='1';");
        this.updateByQuery(boolQueryBuilder, script);
    }

    public void delete(RsbtLicenseMainES rsbtLicenseMainES){
        rsbtLicenseMainRepository.delete(rsbtLicenseMainES);
    }

    public void pushLicenseToEs(){
        //查询出已审核通过的基站信息
        //先查询出总数量
        int pushCount = licenseService.findCountPushToEs();
        if (pushCount>0){
            ThreadPoolUtil.getThread().execute(()-> push(pushCount));
        }
    }

    public void push(int count){
        System.out.println("开始查询数据。。。");
        List<RsbtLicenseEsDTO> rsbtLicenseEsDTOList = licenseService.findByPage(0,1);
        System.out.println("查询数据完成，总数："+rsbtLicenseEsDTOList.size());
        List<RsbtLicenseMainES> rsbtLicenseMainESList = new ArrayList<>();
        for (RsbtLicenseEsDTO rsbtLicenseEsDTO:rsbtLicenseEsDTOList){
            RsbtLicenseMainES rsbtLicenseMainES = new RsbtLicenseMainES();
            BeanUtils.copyProperties(rsbtLicenseEsDTO,rsbtLicenseMainES);
            rsbtLicenseMainES.setLicenseDate(DateUtils.dateToLocalDateTime(rsbtLicenseEsDTO.getLicenseDate()));
            rsbtLicenseMainES.setLicenseDateB(DateUtils.dateToLocalDateTime(rsbtLicenseEsDTO.getLicenseDateB()));
            rsbtLicenseMainES.setLicenseDateE(DateUtils.dateToLocalDateTime(rsbtLicenseEsDTO.getLicenseDateE()));
            rsbtLicenseMainESList.add(rsbtLicenseMainES);
        }
        this.saveBatch(rsbtLicenseMainESList);
    }

}
