package com.caict.bsm.project.domain.business.service.es;

import com.caict.bsm.project.domain.business.repository.RsbtStationMainRepository;
import com.caict.bsm.project.domain.business.service.station.RsbtStationService;
import com.caict.bsm.project.system.model.dto.business.station.StationDTO;
import com.caict.bsm.project.system.model.dto.es.RsbtStationEsDTO;
import com.caict.bsm.project.system.model.dto.security.UsersDTO;
import com.caict.bsm.project.system.model.es.RsbtStationMainES;
import com.caict.bsm.project.system.model.es.RsbtStationMainVO;
import com.caict.bsm.project.system.utils.util.ThreadPoolUtil;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.script.Script;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: ycp
 * @createTime: 2023/09/26 9:49
 * @company: 成渝（成都）信息通信研究院
 * @description:
 */
@Service
public class RsbtStationMainWebService extends ElasticsearchBasicService<RsbtStationMainES, RsbtStationMainVO> {

    private static final Logger LOG = LoggerFactory.getLogger(RsbtStationMainWebService.class);

    @Autowired
    private RsbtStationMainRepository rsbtStationMainRepository;
    @Autowired
    private RsbtStationService rsbtStationService;

    /**
     * 批量添加
     * */
    public void saveBatch(List<RsbtStationMainES> list){
        if (list != null){
            int size = list.size();

            LOG.info("开启区域协查数据elastic操作，数量：" + size);
            if (size > 50000) {
                for (int i = 0; i < size; i += 50000){
                    List<RsbtStationMainES> collect = list.stream().skip(i).limit(50000).collect(Collectors.toList());
                    rsbtStationMainRepository.saveAll(collect);
                }
            }else {
                rsbtStationMainRepository.saveAll(list);
            }
            LOG.info("区域协查数据elastic操作结束");
        }
    }

    public PageInfo<RsbtStationMainES> findPage(StationDTO stationDTO, UsersDTO usersDTO){
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        RsbtStationMainVO regionalInvestigationESSearchVO = new RsbtStationMainVO();
        regionalInvestigationESSearchVO.setPage(stationDTO.getPage());
        regionalInvestigationESSearchVO.setRows(stationDTO.getRows());

        if (stationDTO.getCodeList()!=null) boolQueryBuilder.must(QueryBuilders.termsQuery("county.keyword",stationDTO.getCodeList()));

        if (StringUtils.isNoneBlank(stationDTO.getOrgName())) boolQueryBuilder.must(QueryBuilders.matchQuery("org_type",stationDTO.getOrgName()));
        if (StringUtils.isNoneBlank(stationDTO.getGenNum())) boolQueryBuilder.must(QueryBuilders.matchQuery("net_type",stationDTO.getGenNum()+"G"));
        if (StringUtils.isNoneBlank(stationDTO.getExpandStation())) boolQueryBuilder.must(QueryBuilders.matchQuery("expand_station",stationDTO.getExpandStation()));
        if (StringUtils.isNoneBlank(stationDTO.getAttributeStation())) boolQueryBuilder.must(QueryBuilders.matchQuery("attribute_station",stationDTO.getAttributeStation()));
        if (StringUtils.isNoneBlank(stationDTO.getIsDeleted())) boolQueryBuilder.must(QueryBuilders.matchQuery("station_state",stationDTO.getIsDeleted()));
        if (StringUtils.isNoneBlank(stationDTO.getIsLicense())) boolQueryBuilder.must(QueryBuilders.matchQuery("is_license",stationDTO.getIsLicense()));
        if (StringUtils.isNoneBlank(stationDTO.getStationName())) boolQueryBuilder.must(QueryBuilders.wildcardQuery("station_name.keyword","*"+stationDTO.getStationName()+"*"));
        if (StringUtils.isNoneBlank(stationDTO.getStationCode())) boolQueryBuilder.must(QueryBuilders.wildcardQuery("station_code.keyword","*"+stationDTO.getStationCode()+"*"));
        //范围查询
        if (stationDTO.getMaxlat()!=null && stationDTO.getMinlat()!=null) boolQueryBuilder.must(QueryBuilders.rangeQuery("latitude").gte(stationDTO.getMinlat()).lte(stationDTO.getMaxlat()));
        if (stationDTO.getMaxlng()!=null && stationDTO.getMinlng()!=null) boolQueryBuilder.must(QueryBuilders.rangeQuery("longitude").gte(stationDTO.getMinlng()).lte(stationDTO.getMaxlng()));
        if (!"wuwei".equals(usersDTO.getType())){
            boolQueryBuilder.must(QueryBuilders.matchQuery("org_type",usersDTO.getType()));
        }
        PageInfo<RsbtStationMainES> regionalInvestigationESPageInfo = this.searchHitsPage(regionalInvestigationESSearchVO, boolQueryBuilder);
        return regionalInvestigationESPageInfo;
    }

    public void updateEs(String guid){
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.must(QueryBuilders.matchQuery("guid", guid));
        Script script = new Script("ctx._source.is_sync='1';");
        this.updateByQuery(boolQueryBuilder, script);
    }

    public void updateEsLicenseState(String guid){
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.must(QueryBuilders.matchQuery("guid", guid));
        Script script = new Script("ctx._source.is_license='1';");
        this.updateByQuery(boolQueryBuilder, script);
    }

    public void delete(RsbtStationMainES rsbtStationMainES){
        rsbtStationMainRepository.delete(rsbtStationMainES);
    }

    public void pushStationToEs(){
        //查询出已审核通过的基站信息
        //先查询出总数量
        int pushCount = rsbtStationService.findCountPushToEs();
        if (pushCount>0){
            ThreadPoolUtil.getThread().execute(()-> push(pushCount));
        }
    }

//    public void push(int count){
//        System.out.println("开始推送基站数据至es。。。");
//        int countPage = count / 10;
//        if (count % 10 > 0) {
//            countPage += 1;
//        }
//        for (int i = 1; i <= countPage; i++) {
//            List<RsbtStationEsDTO> rsbtStationEsDTOS = rsbtStationService.findByPage((i - 1) * 10, i * 10);
//            List<RsbtStationMainES> rsbtStationMainESList = new ArrayList<>();
//            for (RsbtStationEsDTO rsbtStationEsDTO:rsbtStationEsDTOS){
//                RsbtStationMainES rsbtStationMainES = new RsbtStationMainES();
//                BeanUtils.copyProperties(rsbtStationEsDTO,rsbtStationMainES);
//                rsbtStationMainES.setStationId(rsbtStationEsDTO.getGuid());
//                rsbtStationMainES.setIsSync("1");
//
//                rsbtStationMainESList.add(rsbtStationMainES);
//            }
//            rsbtStationMainRepository.saveAll(rsbtStationMainESList);
//            System.out.println("推送基站数据，第"+i+"页至es完成");
//        }
//        System.out.println("推送基站数据至es完成");
//    }

    public void push(int count){
        System.out.println("开始查询数据。。。");
        List<RsbtStationEsDTO> rsbtStationEsDTOS = rsbtStationService.findByPage(0,1);
        System.out.println("查询数据完成，总数："+rsbtStationEsDTOS.size());
        List<RsbtStationMainES> rsbtStationMainESList = new ArrayList<>();
        for (RsbtStationEsDTO rsbtStationEsDTO:rsbtStationEsDTOS){
            RsbtStationMainES rsbtStationMainES = new RsbtStationMainES();
            BeanUtils.copyProperties(rsbtStationEsDTO,rsbtStationMainES);
            rsbtStationMainES.setStationId(rsbtStationEsDTO.getGuid());
            rsbtStationMainES.setIsSync("1");

            rsbtStationMainESList.add(rsbtStationMainES);
        }
        this.saveBatch(rsbtStationMainESList);
        System.out.println("推送基站数据至es完成");

    }

    //分页结果对象转换
//    protected PageInfo<RegionalInvestigationESDTO> pageToDto(PageInfo<RegionalInvestigationES> esPage){
//        var esList = esPage.getList();
//        var list = esToDtoList(esList);
//
//        PageInfo<RegionalInvestigationESDTO> page = new PageInfo<>();
//        BeanUtils.copyProperties(esPage,page);
//        page.setList(list);
//
//        return page;
//    }


}
