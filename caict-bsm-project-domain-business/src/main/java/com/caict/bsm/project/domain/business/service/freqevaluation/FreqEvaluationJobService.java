package com.caict.bsm.project.domain.business.service.freqevaluation;

import com.caict.bsm.project.domain.business.mapper.freqevaluation.FreqEvaluationJobMapper;
import com.caict.bsm.project.system.model.dto.business.freqevaluation.FreqEvaluationJobDTO;
import com.caict.bsm.project.system.model.entity.business.freqevaluation.FreqEvaluationJob;
import com.caict.bsm.project.system.model.vo.freqevaluation.FreqEvaluationJobVO;
import com.caict.bsm.project.system.utils.util.VerificationCode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description 省（自治区、直辖市）公众移动通信系统无线电频率使用率评价job
 * @date 2023年8月24日 11点13分
 */
@Service
public class FreqEvaluationJobService {

    @Autowired
    private FreqEvaluationJobMapper mapper;


    /**
     * 保存数据
     *
     * @param job job
     * @return str
     */
    public String save(FreqEvaluationJob job) {
        if (!"".equals(job.getGuid()) && job.getGuid() != null) {
            if (mapper.update(job) > 0) {
                return job.getGuid() + "";
            }
        } else {
            String id = VerificationCode.myUUID();
            job.setGuid(id);
            if (mapper.insert(job) > 0) {
                return id;
            }
        }
        return null;
    }

    /**
     * 删除
     *
     * @param jobGuid jobGuid
     * @return int
     */
    public int delete(String jobGuid) {
        return mapper.delete(jobGuid);
    }

    /**
     * 删除
     *
     * @param jobGuid jobGuid
     * @return int
     */
    public FreqEvaluationJob findById(String jobGuid) {
        return mapper.findOne(jobGuid);
    }

    /**
     * 根据条件查询
     *
     * @param vo vo
     * @return list
     */
    public List<FreqEvaluationJobDTO> findAllByWhere(int rowsStart, int rowsEnd, FreqEvaluationJobVO vo) {
        return mapper.findAllByWhere(rowsStart, rowsEnd, vo);
    }

    /**
     * 根据条件查询
     *
     * @param vo vo
     * @return int
     */
    public int selectCountWhere(FreqEvaluationJobVO vo) {
        return mapper.selectCountWhere(vo);
    }
}
