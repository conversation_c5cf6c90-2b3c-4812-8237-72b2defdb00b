package com.caict.bsm.project.domain.business.service.freqevaluation;

import com.caict.bsm.project.domain.business.mapper.freqevaluation.FreqEvaluationMapper;
import com.caict.bsm.project.system.model.dto.business.freqevaluation.FreqEvaluationDTO;
import com.caict.bsm.project.system.model.dto.business.freqevaluation.FreqEvaluationProvDTO;
import com.caict.bsm.project.system.model.vo.freqevaluation.FreqEvaluationProvVO;
import com.caict.bsm.project.system.model.vo.freqevaluation.FreqEvaluationVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description 省（自治区、直辖市）公众移动通信系统无线电频率使用率评价结果
 * @date 2023年8月24日 11点13分
 */
@Service
public class FreqEvaluationService {

    @Autowired
    private FreqEvaluationMapper mapper;

    /**
     * 查询数据
     *
     * @param param param
     * @return list
     */
    public List<FreqEvaluationDTO> searchFREQEvaluation(FreqEvaluationVO param) {
        return mapper.searchFREQEvaluation(param);
    }

    /**
     * 查询数据
     *
     * @param param param
     * @return list
     */
    public List<FreqEvaluationProvDTO> searchFREQEvaluationProv(FreqEvaluationProvVO param) {
        return mapper.searchFREQEvaluationProv(param);
    }

    /**
     * 查询市数据数量
     *
     * @param vo vo
     * @return int
     */
    public int searchFREQEvaluationNum(FreqEvaluationVO vo) {
        return mapper.searchFREQEvaluationNum(vo);
    }
}
