package com.caict.bsm.project.domain.business.service.freqevaluation;

import com.caict.bsm.project.domain.business.mapper.freqevaluation.OperUserStatisMapper;
import com.caict.bsm.project.system.model.dto.business.freqevaluation.OperUserStatisDTO;
import com.caict.bsm.project.system.model.vo.freqevaluation.OperUserStatisVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description 省（自治区、直辖市）公众移动通信系统无线电频率使用率评价结果
 * @date 2023年8月24日 11点13分
 */
@Service
public class OperUserStatisService {

    @Autowired
    private OperUserStatisMapper mapper;

    /**
     * 查询数据
     *
     * @param vo vo
     * @return list
     */
    public List<OperUserStatisDTO> searchOperUserStatis(OperUserStatisVO vo) {
        return mapper.searchOperUserStatis(vo);
    }
}
