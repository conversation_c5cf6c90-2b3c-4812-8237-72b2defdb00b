package com.caict.bsm.project.domain.business.service.license;

import com.caict.bsm.project.domain.business.mapper.license.LicenseMapper;
import com.caict.bsm.project.system.data.service.BasicService;
import com.caict.bsm.project.system.model.dto.business.license.LicenseDTO;
import com.caict.bsm.project.system.model.dto.business.license.LicensePdfDTO;
import com.caict.bsm.project.system.model.dto.business.license.LicenseStatisticDTO;
import com.caict.bsm.project.system.model.dto.business.station.ISectionDTO;
import com.caict.bsm.project.system.model.dto.es.RsbtLicenseEsDTO;
import com.caict.bsm.project.system.model.dto.security.UsersDTO;
import com.caict.bsm.project.system.model.entity.business.license.RsbtLicense;
import com.caict.bsm.project.system.model.entity.business.license.RsbtLicenseT;
import com.caict.bsm.project.system.model.entity.business.station.RsbtStationAppendix;
import com.caict.bsm.project.system.utils.util.VerificationCode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/22
 */
@Service
public class LicenseService extends BasicService<RsbtLicense> {
    @Autowired
    private LicenseMapper licenseMapper;

    /**
     * 保存（如果Id为空或者为“”则为添加，否则修改）
     * */
    public String save(RsbtLicense bsmStationLicense){
        if(!"".equals(bsmStationLicense.getGuid()) && bsmStationLicense.getGuid() != null){
            if(licenseMapper.update(bsmStationLicense) > 0){
                return bsmStationLicense.getGuid()+"";
            }
        }else {
            String id = VerificationCode.myUUID();
            bsmStationLicense.setGuid(id);
            if(licenseMapper.insert(bsmStationLicense) > 0){
                return id;
            }
        }
        return null;
    }

    /**
     * 删除（根据id）
     * */
    public int delete(String guid){
        RsbtLicense bsmStationLicense = new RsbtLicense();
        bsmStationLicense.setGuid(guid);
        return licenseMapper.delete(bsmStationLicense);
    }

    /**
     * 查询全部
     * */
    public List<RsbtLicense> findAll(){
        RsbtLicense bsmStationLicense = new RsbtLicense();
        return licenseMapper.findAll(bsmStationLicense);
    }

    /**
     * 根据id查询
     * */
    public RsbtLicense findOne(String guid){
        RsbtLicense bsmStationLicense = new RsbtLicense();
        bsmStationLicense.setGuid(guid);
        return licenseMapper.findOne(bsmStationLicense);
    }

//    public List<LicenseDTO> getLicenseVOByPager(int startRows,int endRows,String orgName,String licenseCode,
//                                                String companyName,String stationName,String applytableCode,
//                                                String StationCode,Long licenseState,String netType,
//                                                Date expirationDate){
//        return licenseMapper.getLicenseVOByPager(startRows,endRows,orgName,licenseCode,companyName,stationName,applytableCode,StationCode,licenseState,netType,expirationDate);
//    }
//
//    public int getLicenseNum(String orgName,String licenseCode,
//                             String companyName,String stationName,String applytableCode,
//                             String StationCode,Long licenseState,String netType,
//                             Date expirationDate){
//        return licenseMapper.getLicenseNum(orgName,licenseCode,companyName,stationName,applytableCode,StationCode,licenseState,netType,expirationDate);
//    }
//
//    public List<LicenseDTO> getLicenseVO(String stationId){
//        return licenseMapper.getLicenseVO(stationId);
//    }
//
//    public List<LicensePdfDTO> getLicense(String stationId){
//        return licenseMapper.getLicense(stationId);
//    }
//
//    public List<String> getAntPole(String stationId){
//        return licenseMapper.getAntPole(stationId);
//    }
//
//    public List<String> getStationId(String linceseCode,String statName,Date linceseEndDate,
//                                     String applytableCode,String stCode,Long linceseState,
//                                     String orgName,String orgUser){
//        return licenseMapper.getStationId(linceseCode,statName,linceseEndDate,applytableCode,stCode,linceseState,orgName,orgUser);
//    }

    /**
     * 条件查询总数
     * */
    public int selectCountWhere(LicenseDTO licenseDTO){
        return licenseMapper.selectCountWhere(licenseDTO);
    }

    /**
     * 分页条件查询
     * */
    public List<LicenseDTO> findAllPageByWhere(LicenseDTO licenseDTO, UsersDTO usersDTO){
        return licenseMapper.findAllPageByWhere(licenseDTO,usersDTO);
    }

    /**
     * 根据基站查询详情
     * */
    public LicensePdfDTO findOneFDFByStationGuid(String stationGuid){
        return licenseMapper.findOneFDFByStationGuid(stationGuid);
    }

    /**
     * 根据基站数组查询详情
     * */
    public List<LicensePdfDTO> findOneFDFByStationGuids(String[] stationGuids){
        return licenseMapper.findOneFDFByStationGuids(stationGuids);
    }

    /**
     * 查询执照统计数据
     * @return
     */
    public LicenseStatisticDTO getStatisticCount(){
        return licenseMapper.getStatisticCount();
    }

    /**
     * 根据基站id查询执照
     */
    public RsbtLicense selectByStationGuid(String stationGuid){
        return licenseMapper.selectByStationGuid(stationGuid);
    }

    public int updateBatchByStationGuid(List<String> stationGuids,String appCode){
        return licenseMapper.updateBatchByStationGuid(stationGuids,appCode);
    }

    public RsbtLicenseT findLicenseTByStaGuid(String stationGuid){
        return licenseMapper.findLicenseTByStaGuid(stationGuid);
    }

    /**
     * 查看执照数据
     */
    public List<ISectionDTO> getISectionDTOList(String stationGuid){
        return licenseMapper.getISectionDTOList(stationGuid);
    }

    public int updateAppCode(String oldAppCode,String newAppCode){
        return licenseMapper.updateAppCode(oldAppCode,newAppCode);
    }

    public int updateByStationGuids(String[] stationGuids,Long status){
        return licenseMapper.updateByStationGuids(stationGuids,status);
    }

    public List<LicensePdfDTO> findPrintLicenseByWhere(LicenseDTO licenseDTO,UsersDTO usersDTO){
        return licenseMapper.findPrintLicenseByWhere(licenseDTO,usersDTO);
    }

    public int findCountPushToEs(){
        return licenseMapper.findCountPushToEs();
    }

    public List<RsbtLicenseEsDTO> findByPage(int startRow, int endRow){
        return licenseMapper.findByPage(startRow,endRow);
    }
}
