package com.caict.bsm.project.domain.business.service.license;

import com.caict.bsm.project.domain.business.mapper.license.LicenseTMapper;
import com.caict.bsm.project.system.data.service.BasicService;
import com.caict.bsm.project.system.model.entity.business.license.RsbtLicenseT;
import com.caict.bsm.project.system.utils.util.VerificationCode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2020/9/11
 */
@Service
public class LicenseTService extends BasicService<RsbtLicenseT> {

    @Autowired
    private LicenseTMapper licenseTMapper;
    /**
     * 保存（如果Id为空或者为“”则为添加，否则修改）
     * */
    public String save(RsbtLicenseT licenseT){
        if(!"".equals(licenseT.getGuid()) && licenseT.getGuid() != null){
            if(licenseTMapper.update(licenseT) > 0){
                return licenseT.getGuid()+"";
            }
        }else {
            String id = VerificationCode.myUUID();
            licenseT.setGuid(id);
            if(licenseTMapper.insert(licenseT) > 0){
                return id;
            }
        }
        return null;
    }

    public RsbtLicenseT findOne(String guid){
        RsbtLicenseT licenseT = new RsbtLicenseT();
        licenseT.setGuid(guid);
        return licenseTMapper.findOne(licenseT);
    }
}
