package com.caict.bsm.project.domain.business.service.sta;

import com.caict.bsm.project.domain.business.mapper.sta.StaMapper;
import com.caict.bsm.project.system.data.service.BasicService;
import com.caict.bsm.project.system.model.dto.business.license.LicensePdfDTO;
import com.caict.bsm.project.system.model.dto.business.sta.StaLicenseDTO;
import com.caict.bsm.project.system.model.dto.business.sta.StaListTempDTO;
import com.caict.bsm.project.system.model.dto.business.station.ISectionDTO;
import com.caict.bsm.project.system.model.entity.business.sta.StaAntenna;
import com.caict.bsm.project.system.model.entity.business.sta.StaEqu;
import com.caict.bsm.project.system.model.entity.business.sta.StaFreq;
import com.caict.bsm.project.system.model.entity.business.sta.StaStation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/5/8
 */
@Service
public class StaStationService extends BasicService<StaStation>{
    @Autowired
    private StaMapper staMapper;

    public List<StaLicenseDTO> getListVOByPage(int startRows, int endRows, String stationName, String pipeNum, String licenseCode, Date startDate, Date endDate){
        return staMapper.getListVOByPage(startRows,endRows,stationName,pipeNum,licenseCode,startDate,endDate);
    }

    public int getStaListNum(String stationName,String pipeNum,String licenseCode,Date startDate,Date endDate){
        return staMapper.getStaListNum(stationName,pipeNum,licenseCode,startDate,endDate);
    }

    public StaListTempDTO getList(String stationGuid){
        return staMapper.getList(stationGuid);
    }

    public List<StaAntenna> getAntenna(String stationGuid){
        return staMapper.getAntenna(stationGuid);
    }

    public List<StaEqu> getEqu(String stationGuid){
        return staMapper.getEqu(stationGuid);
    }

    public List<StaFreq> getFreq(String stationGuid){
        return staMapper.getFreq(stationGuid);
    }

    public List<StaStation> selectStaions(String stationGuid){
        StaStation staStation = new StaStation();
        staStation.setGuid(stationGuid);
        return staMapper.findAll(staStation);
    }

    public int deleteLicense(String stationGuid){
        return staMapper.deleteLicense(stationGuid);
    }

    public int deleteFreq(String stationGuid){
        return staMapper.deleteFreq(stationGuid);
    }

    public int deleteEqu(String stationGuid){
        return staMapper.deleteEqu(stationGuid);
    }

    public int deleteAntenna(String stationGuid){
        return staMapper.deleteAntenna(stationGuid);
    }

    public int deleteSta(String stationGuid){
        return staMapper.deleteSta(stationGuid);
    }

    public int deleteEaf(String stationGuid){
        return staMapper.deleteEaf(stationGuid);
    }

    public String getStationId(String idenCode){
        return staMapper.getStationId(idenCode);
    }

    public Boolean add1(String guid,String org_code,String distr_code, String sta_type,String pipe_num, String sta_name, String iden_code,
                        String user_name, String location, String longitude, String latitude, String detail, String special_case, String app_code, String stat_app_type,
                        String stat_tdi,String stat_work, String stat_status,int stat_equ_sum){
        return staMapper.add1(guid,org_code,distr_code, sta_type,pipe_num, sta_name, iden_code,
                user_name, location, longitude, latitude, detail, special_case, app_code, stat_app_type,
                stat_tdi,stat_work, stat_status,stat_equ_sum);
    }

    public Boolean add2(String guid, String stationGuid, String linceseCode,
                        Date gmtCreate, Date gmtModified,int linceseState,
                        Date linceseStartDate,Date linceseEndDate,String licenseAuth){
        return staMapper.add2(guid,stationGuid,linceseCode,gmtCreate,gmtModified,linceseState,linceseStartDate,linceseEndDate,licenseAuth);
    }

    public Boolean add3(String guid, String stationGuid,String freqEf,String freqRf,String freqBand,
                        String ftFreqCsgn, String freqPass){
        freqEf = (freqEf == null? "":freqEf);
        freqRf = (freqRf == null? "":freqRf);
        freqBand = (freqBand == null? "":freqBand);
        ftFreqCsgn = (ftFreqCsgn == null? "":ftFreqCsgn);
        freqPass = (freqPass == null? "":freqPass);
        return staMapper.add3(guid, stationGuid,freqEf,freqRf,freqBand,
                ftFreqCsgn, freqPass);
    }

    public Boolean add4(String guid, String stationGuid, String equModel,String equAuth,String equMenu,String equPow,
                        String equType,String equCode,String equMb){
        equModel = (equModel == null? "":equModel);
        equAuth = (equAuth == null? "":equAuth);
        equMenu = (equMenu == null? "":equMenu);
        equPow = (equPow == null? "":equPow);
        equType = (equType == null? "":equType);
        equCode = (equCode == null? "":equCode);
        equMb = (equMb == null? "":equMb);
        return staMapper.add4(guid, stationGuid, equModel,equAuth,equMenu,equPow,
                equType,equCode,equMb);
    }

    public Boolean add5(String guid, String stationGuid, Double antGain,String antEpole,Double antHight){
        antGain = antGain == null? 0:antGain;
        antEpole = antEpole == null? "":antEpole;
        antHight = antHight==null? 0:antHight;
        return staMapper.add5(guid,stationGuid,antGain,antEpole,antHight);
    }

    public Boolean add6(String guid,String stationGuid,String equGuid,String antGuid,String freqGuid){
        return staMapper.add6(guid,stationGuid,equGuid,antGuid,freqGuid);
    }

    public List<String> getFreqIdlist(String stationId){
        return staMapper.getFreqIdlist(stationId);
    }

    public Boolean update1(String guid, String org_code,String distr_code,
                           String sta_type, String pipe_num,
                           String sta_name, String iden_code,
                            String user_name, String location,
                           String longitude,  String latitude,
                            String detail, String special_case,
                           String app_code,  String stat_app_type,
                           String stat_tdi,  String stat_work,
                           String stat_status, int stat_equ_sum){
        return staMapper.update1(guid, org_code,distr_code,
                sta_type, pipe_num,
                sta_name, iden_code,
                user_name, location,
                longitude,  latitude,
                detail, special_case,
                app_code,  stat_app_type,
                stat_tdi,  stat_work,
                stat_status,stat_equ_sum);
    }

    public Boolean update2(String guid, String linceseCode,Date gmtCreate,Date gmtModified,int linceseState,
                           Date linceseStartDate,Date linceseEndDate, String licenseAuth){
        return staMapper.update2(guid, linceseCode, gmtCreate,gmtModified,linceseState,linceseStartDate,linceseEndDate,licenseAuth);
    }

    public String selectIdenCode(String idenCode){
        return staMapper.selectIdenCode(idenCode);
    }

    public String selectLinceseCode(String linceseCode){
        return staMapper.selectLinceseCode(linceseCode);
    }

    public LicensePdfDTO getLicense(String stationId){
        return staMapper.getLicense(stationId);
    }

    public List<ISectionDTO> getISectionDTOList(String stationId){
        return staMapper.getISectionDTOList(stationId);
    }

    public List<LicensePdfDTO> findOneFDFByStationGuids(List<String> stationIdlist){
        return staMapper.findOneFDFByStationGuids(stationIdlist);
    }
}
