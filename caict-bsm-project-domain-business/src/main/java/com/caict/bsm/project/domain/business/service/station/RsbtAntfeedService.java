package com.caict.bsm.project.domain.business.service.station;

import com.caict.bsm.project.domain.business.mapper.station.RsbtAntfeedMapper;
import com.caict.bsm.project.system.data.service.BasicService;
import com.caict.bsm.project.system.model.entity.business.station.RsbtAntfeed;
import com.caict.bsm.project.system.utils.util.VerificationCode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by dengsy on 2020-04-22.
 */
@Service
public class RsbtAntfeedService extends BasicService<RsbtAntfeed> {

    @Autowired
    private RsbtAntfeedMapper rsbtAntfeedMapper;

    /**
     * 根据基站id和扇区id查询
     * */
    public RsbtAntfeed findOneByStationCell(String stationGuid,String cellId){
        return rsbtAntfeedMapper.findOneByStationCell(stationGuid,cellId);
    }

    public int updateByStationGuid(String stationGuid,String cellCode){
        return rsbtAntfeedMapper.updateByStationGuid(stationGuid,cellCode);
    }
}
