package com.caict.bsm.project.domain.business.service.station;

import com.caict.bsm.project.domain.business.mapper.station.RsbtEquMapper;
import com.caict.bsm.project.system.data.service.BasicService;
import com.caict.bsm.project.system.model.entity.business.station.RsbtEqu;
import com.caict.bsm.project.system.utils.util.VerificationCode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by dengsy on 2020-04-22.
 */
@Service
public class RsbtEquService extends BasicService<RsbtEqu> {

    @Autowired
    private RsbtEquMapper rsbtEquMapper;

    /**
     * 根据基站id和扇区id查询
     * */
    public RsbtEqu findOneByStationSection(String stationGuid,String cellId){
        return rsbtEquMapper.findOneByStationSection(stationGuid,cellId);
    }

    public int updateByStationGuid(String stationGuid,String cellCode){
        return rsbtEquMapper.updateByStationGuid(stationGuid,cellCode);
    }
}
