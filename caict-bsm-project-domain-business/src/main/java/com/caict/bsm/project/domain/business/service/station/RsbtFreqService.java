package com.caict.bsm.project.domain.business.service.station;

import com.caict.bsm.project.domain.business.mapper.station.RsbtFreqMapper;
import com.caict.bsm.project.system.data.service.BasicService;
import com.caict.bsm.project.system.model.entity.business.station.RsbtFreq;
import com.caict.bsm.project.system.utils.util.VerificationCode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by dengsy on 2020-04-22.
 */
@Service
public class RsbtFreqService extends BasicService<RsbtFreq> {

    @Autowired
    private RsbtFreqMapper rsbtFreqMapper;

    /**
     * 根据基站guid和扇区id查询
     * */
    public RsbtFreq findOneByStationCell(String stationGuid,String cellId){
        return rsbtFreqMapper.findOneByStationCell(stationGuid,cellId);
    }

    public int updateByStationGuid(String stationGuid,String cellCode){
        return rsbtFreqMapper.updateByStationGuid(stationGuid,cellCode);
    }
}
