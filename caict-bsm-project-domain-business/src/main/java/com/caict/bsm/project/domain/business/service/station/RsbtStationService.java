package com.caict.bsm.project.domain.business.service.station;

import com.caict.bsm.project.domain.business.mapper.station.RsbtStationMapper;
import com.caict.bsm.project.domain.business.service.es.RsbtStationMainWebService;
import com.caict.bsm.project.system.data.service.BasicService;
import com.caict.bsm.project.system.model.dto.business.common.EchartsResDTO;
import com.caict.bsm.project.system.model.dto.business.station.*;
import com.caict.bsm.project.system.model.dto.es.RsbtStationEsDTO;
import com.caict.bsm.project.system.model.dto.security.UsersDTO;
import com.caict.bsm.project.system.model.entity.business.station.RsbtStation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/4/24
 */
@Service
public class RsbtStationService extends BasicService<RsbtStation> {

    @Autowired
    private RsbtStationMapper rsbtStationMapper;
    @Autowired
    private RsbtStationMainWebService rsbtStationMainWebService;

    /**
     * 获取没有入申请表的所有站点
     * */
    public List<StationDTO> selectAllNotApplyTable(){
        return rsbtStationMapper.selectAllNotApplyTable();
    }

    /**
     * 查询未生成执照的基站
     * */
    public List<StationLicenseDTO> selectAllNotLicense(){
        return rsbtStationMapper.selectAllNotLicense();
    }

    /**
     * 查询fast30公里以内的基站
     * */
    public List<StationFastDTO> findAllByFast(UsersDTO usersDTO){
        return rsbtStationMapper.findAllByFast(usersDTO);
    }

    /**
     * 更新执照状态为发布执照
     */
    public void updateStationLicenseStatus(List<StationLicenseDTO> list){
        List<String> guidList = list.stream().map(RsbtStation::getGuid).collect(Collectors.toList());
        int len = 1000;

        int size = guidList.size();
        int count = (size + len -1 ) / len;
        for(int i=0;i < count;i++){
            List<String> subGuidList = guidList.subList(i * len, Math.min((i + 1) * len, size));
            rsbtStationMapper.updateStationLicenseStatus(subGuidList);
        }
        //修改es中执照生成状态
        System.out.println("开始修改执照状态。。。。。。。。");
        for (StationLicenseDTO stationLicenseDTO:list) {
            rsbtStationMainWebService.updateEsLicenseState(stationLicenseDTO.getGuid());
        }
    }
    /**
     * 根据基站识别号下载
     * */
    public RsbtStation querySattionByBts(String bts,String techType,String orgCode){
        return rsbtStationMapper.querySattionByBts(bts,techType,orgCode);
    }

    /**
     * 条件查询总数
     * */
    public int selectCountByWhere(StationDTO stationDTO){
        return rsbtStationMapper.selectCountByWhere(stationDTO);
    }

    /**
     * 分页条件查询
     * */
//    public List<StationDTO> findAllByWhere(int rowsStart,int rowsEnd,StationDTO stationDTO){
//        return rsbtStationMapper.findAllByWhere(rowsStart,rowsEnd,stationDTO);
//    }
    public List<StationDTO> findAllByWhere(StationDTO stationDTO, UsersDTO usersDTO){
        return rsbtStationMapper.findAllByWhere(stationDTO,usersDTO);
    }

    /**
     * 查询详情DTO
     * */
    public StationDTO findOneByGuid(String guid){
        return rsbtStationMapper.findOneByGuid(guid);
    }


    public List<RsbtStation> selectExistStation(Set<String> stationSet){
        return rsbtStationMapper.selectExistStation(stationSet);
    }

    public int saves(List<RsbtStation> list){
        return rsbtStationMapper.insertBatch(list);
    }

    /**
     * 统计
     */
    public int queryStationCount(){
        return rsbtStationMapper.queryStationCount();
    }

    public List<EchartsResDTO> queryStationCountyNumber(){
        return rsbtStationMapper.queryStationCountyNumber();
    }

    public int queryNewStaByThisMonth(){
        return rsbtStationMapper.queryNewStaByThisMonth();
    }

    public List<StaInfoDTO> queryNewInsertStation(){
        return rsbtStationMapper.queryNewInsertStation();
    }

    /**
     * 查询区域排名（根据基站数排名)
     */
    public List<RegionSortDTO> selectRegionSortDTO(){
        List<RegionSortDTO> list = rsbtStationMapper.selectRegionSortDTO();
        for (int i=0;i<list.size();i++){
            String regionName = list.get(i).getRegionName();
            List<OrgStationNumSortDTO> orgStationNumSortDTOS = rsbtStationMapper.selectOrgSortDTO(regionName);
            for (OrgStationNumSortDTO org:orgStationNumSortDTOS) {
                checkoutOrg(org.getOrgName(),org);
            }
            list.get(i).setOrgStationNumSortDTOList(orgStationNumSortDTOS);
        }
        return list;
    }

    /**
     * 校验组织名称
     */
    public static void checkoutOrg(String orgName,OrgStationNumSortDTO orgStationNumSortDTO){
        if (orgName.contains("中国移动")) orgStationNumSortDTO.setOrgName("中国移动");
        else if (orgName.contains("中国电信")) orgStationNumSortDTO.setOrgName("中国电信");
        else if (orgName.contains("中国联合")) orgStationNumSortDTO.setOrgName("中国联通");
    }

    public List<RegionSortDTO> searchTwo(List<RegionSortDTO> list){
        Collections.sort(list, new Comparator<RegionSortDTO>() {
            @Override
            public int compare(RegionSortDTO o1, RegionSortDTO o2) {
                return -o1.getRegionStationNum().compareTo(o2.getRegionStationNum());
            }
        });
        return list;
    }

    /**
     * 查询台站分布
     * @return
     */
    public List<RegionSortDTO> getDistributionStatistics(){
        return rsbtStationMapper.getDistributionStatistics();
    }

    /**
     * 查询运营商对应的基站数
     */
    public List<OrgStationNumSortDTO> selectOrgStationNum(){
        List<OrgStationNumSortDTO> list = rsbtStationMapper.selectOrgStationNum();
        for (OrgStationNumSortDTO org:list) {
            checkoutOrg(org.getOrgName(),org);
        }
        return list;
    }

    /**
     * 降序排列
     * @param list
     * @return
     */
    public List<OrgStationNumSortDTO> search(List<OrgStationNumSortDTO> list){
        Collections.sort(list, new Comparator<OrgStationNumSortDTO>() {
            @Override
            public int compare(OrgStationNumSortDTO o1, OrgStationNumSortDTO o2) {
                return -o1.getStationNumber().compareTo(o2.getStationNumber());
            }
        });
        return list;
    }

    public List<StationDTO> queryStationByRange(Double lowLng, Double lowLat, Double highLng, Double highLat){
        return rsbtStationMapper.queryStationByRange(lowLng, lowLat, highLng, highLat);
    }

    public List<RsbtStation> findListByAppCodePage(String appCode){
        return rsbtStationMapper.findListByAppCodePage(appCode);
    }

    public int updateAppCode(String oldAppCode,String newAppCode){
        return rsbtStationMapper.updateAppCode(oldAppCode,newAppCode);
    }

    public RsbtStation selectByStationGuid(String guid){
        return rsbtStationMapper.selectByStationGuid(guid);
    }

    /**
     * 是否区域内查询
     */
    public List<StationDTO> queryAreaRange(StationDTO stationDTO,UsersDTO usersDTO){
        return rsbtStationMapper.queryAreaRange(stationDTO,usersDTO);
    }

    public RsbtStation findByStCode(String staCode){
        return rsbtStationMapper.findByStCode(staCode);
    }

    public int judgeDel(String stationGuid){
        return rsbtStationMapper.judgeDel(stationGuid);
    }

    public int findCountPushToEs(){
        return rsbtStationMapper.findCountPushToEs();
    }

    public List<RsbtStationEsDTO> findByPage(int startRow, int endRow){
        return rsbtStationMapper.findByPage(startRow,endRow);
    }
}
