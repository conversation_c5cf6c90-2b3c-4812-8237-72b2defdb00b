package com.caict.bsm.project.domain.business.service.station;

import com.caict.bsm.project.domain.business.mapper.station.RsbtTrafficMapper;
import com.caict.bsm.project.system.data.service.BasicService;
import com.caict.bsm.project.system.model.entity.business.station.RsbtTraffic;
import com.caict.bsm.project.system.utils.util.VerificationCode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * Created by dengsy on 2020-04-22.
 */
@Service
public class RsbtTrafficService extends BasicService<RsbtTraffic> {

    @Autowired
    private RsbtTrafficMapper rsbtTrafficMapper;

    /**
     * 保存（如果Id为空或者为“”则为添加，否则修改）
     */
    public String save(RsbtTraffic rsbtTraffic) {
        if (!"".equals(rsbtTraffic.getTrafficGuid()) && rsbtTraffic.getTrafficGuid() != null) {
            if (rsbtTrafficMapper.update(rsbtTraffic) > 0) {
                return rsbtTraffic.getTrafficGuid() + "";
            }
        } else {
            String id = VerificationCode.idGet("rf", 4);
            rsbtTraffic.setTrafficGuid(id);
            if (rsbtTrafficMapper.insert(rsbtTraffic) > 0) {
                return id;
            }
        }
        return null;
    }

    /**
     * 删除（根据id）
     */
    public int delete(String guid) {
        RsbtTraffic rsbtTraffic = new RsbtTraffic();
        rsbtTraffic.setTrafficGuid(guid);
        return rsbtTrafficMapper.delete(rsbtTraffic);
    }

    /**
     * 查询全部
     */
    public List<RsbtTraffic> findAll() {
        RsbtTraffic rsbtTraffic = new RsbtTraffic();
        return rsbtTrafficMapper.findAll(rsbtTraffic);
    }

    /**
     * 根据id查询
     */
    public RsbtTraffic findOne(String guid) {
        RsbtTraffic rsbtTraffic = new RsbtTraffic();
        rsbtTraffic.setTrafficGuid(guid);
        return rsbtTrafficMapper.findOne(rsbtTraffic);
    }

    /**
     * 条件查询
     */
    public List<RsbtTraffic> findAllWhere(RsbtTraffic rsbtTraffic) {
        return rsbtTrafficMapper.findAll(rsbtTraffic);
    }

    /**
     * 根据基站guid和扇区id查询
     */
    public RsbtTraffic findOneByStationCell(String stationGuid, String cellId) {
        return rsbtTrafficMapper.findOneByStationCell(stationGuid, cellId);
    }

    public int saves(List<RsbtTraffic> list) {
        return rsbtTrafficMapper.insertBatch(list);
    }

    public int batchDel(List<RsbtTraffic> list) {
        List<Object> ids = Collections.synchronizedList(new ArrayList<>());
        list.parallelStream().forEach(rsbtTraffic -> ids.add(rsbtTraffic.getTrafficGuid()));
        return rsbtTrafficMapper.deleteBatch(list);
    }
}
