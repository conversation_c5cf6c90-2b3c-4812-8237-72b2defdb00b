package com.caict.bsm.project.domain.business.service.stationbak;

import com.caict.bsm.project.domain.business.mapper.stationbak.RsbtStationBakMapper;
import com.caict.bsm.project.system.data.service.BasicService;
import com.caict.bsm.project.system.model.dto.business.station.StationBakDTO;
import com.caict.bsm.project.system.model.dto.business.transfer.ApprovalScheduleLogDTO;
import com.caict.bsm.project.system.model.dto.business.transfer.ApprovalTransportJobDTO;
import com.caict.bsm.project.system.model.entity.business.stationbak.RsbtStationBak;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/24
 */
@Service
public class RsbtStationBakService extends BasicService<RsbtStationBak> {

    @Autowired
    private RsbtStationBakMapper rsbtStationBakMapper;

    public List<StationBakDTO> findByGuids(List<String> guids,String appGuid){
        return rsbtStationBakMapper.findByGuids(guids,appGuid);
    }

    public List<StationBakDTO> findByPage(String appGuid,String dataStatus,String dataType,String genNum,int startRows,int endRows,String statName,String statAddr){
        return rsbtStationBakMapper.findByPage(appGuid,dataStatus,dataType,genNum,startRows,endRows,statName,statAddr);
    }

    public List<RsbtStationBak> findByAppGuid(String appGuid){
        return rsbtStationBakMapper.findByAppGuid(appGuid);
    }

    public int updateIsSync(List<String> guids,String appGuid){
        return rsbtStationBakMapper.updateIsSync(guids,appGuid);
    }

    public int findCount(String appGuid,String dataStatus,String dataType,String genNum,String statName,String statAddr){
        return rsbtStationBakMapper.findCount(appGuid,dataStatus,dataType,genNum,statName,statAddr);
    }

    public int findCountByAppGuid(String appGuid){
        return rsbtStationBakMapper.findCountByAppGuid(appGuid);
    }

    public int updateByGuidAppGuid(RsbtStationBak stationBak){
        return rsbtStationBakMapper.updateByGuidAppGuid(stationBak);
    }

    public int updateGiveUp(String appGuid){
        return rsbtStationBakMapper.updateGiveUp(appGuid);
    }

    public List<ApprovalScheduleLogDTO> findGiveUp(String appGuid){
        return rsbtStationBakMapper.findGiveUp(appGuid);
    }

    public List<StationBakDTO> findGiveUpDataList(ApprovalTransportJobDTO approvalTransportJobDTO){
        return rsbtStationBakMapper.findGiveUpDataList(approvalTransportJobDTO);
    }

    public List<RsbtStationBak> findNotPassData(String appGuid){
        return rsbtStationBakMapper.findNotPassData(appGuid);
    }

    public RsbtStationBak findByCondition(String appGuid,String stCCode){
        return rsbtStationBakMapper.findByCondition(appGuid,stCCode);
    }
}
