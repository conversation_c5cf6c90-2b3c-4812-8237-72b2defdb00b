package com.caict.bsm.project.domain.business.service.transfer;

import com.caict.bsm.project.domain.business.mapper.transfer.ApprovalRawBtsMapper;
import com.caict.bsm.project.system.data.service.BasicService;
import com.caict.bsm.project.system.model.entity.business.transfer.ApprovalRawBts;
import com.caict.bsm.project.system.utils.util.VerificationCode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ApprovalRawBtsService extends BasicService<ApprovalRawBts> {

    @Autowired
    private ApprovalRawBtsMapper approvalRawBtsMapper;

    /**
     * 根据fileId删除
     * */
    public int deleteAllByFileId(String fileId){
        return approvalRawBtsMapper.deleteAllByFileId(fileId);
    }

    /**
     * 根据appGuid查询总数
     * */
    public int selectCountAllByAppGuid(String appGuid){
        return approvalRawBtsMapper.selectCountAllByAppGuid(appGuid);
    }

    /**
     * 根据fileId查询总数
     * */
    public int selectCountByFileId(String fileId){
        return approvalRawBtsMapper.selectCountByFileId(fileId);
    }

    /**
     * 根据appGuid查询
     * */
    public List<ApprovalRawBts> findAllByAppGuid(String appGuid){
        return approvalRawBtsMapper.findAllByAppGuid(appGuid);
    }

    /**
     * 3.0自动生成待审核数据
     * */
    public int insertBySchedule(String jobGuid,String userGuid,String dataType,String genNum,String appGuid,String regionCode,String techType){
        return approvalRawBtsMapper.insertBySchedule(jobGuid,userGuid,dataType,genNum,appGuid,regionCode,techType);
    }
}
