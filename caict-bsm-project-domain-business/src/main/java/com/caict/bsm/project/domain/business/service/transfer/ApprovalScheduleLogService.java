package com.caict.bsm.project.domain.business.service.transfer;

import com.caict.bsm.project.domain.business.mapper.transfer.ApprovalScheduleLogMapper;
import com.caict.bsm.project.system.data.service.BasicService;
import com.caict.bsm.project.system.model.dto.business.transfer.ApprovalScheduleLogDTO;
import com.caict.bsm.project.system.model.entity.business.transfer.ApprovalScheduleLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by dengsy on 2020-04-22.
 */
@Service
public class ApprovalScheduleLogService extends BasicService<ApprovalScheduleLog> {

    @Autowired
    private ApprovalScheduleLogMapper approvalScheduleLogMapper;

    /**
     * 根据appGuid、数据类型查询总数
     * */
    public int selectCountByAppGuidDateType(String appGuid,String dataType){
        return approvalScheduleLogMapper.selectCountByAppGuidDateType(appGuid,dataType);
    }

    /**
     * 根据appGuid、数据类型分页查询
     * */
    public List<ApprovalScheduleLogDTO> findAllPageByAppGuidDateType( String appGuid, String dataType,String genNum){
        return approvalScheduleLogMapper.findAllPageByAppGuidDateType(appGuid,dataType,genNum);
    }

    public List<ApprovalScheduleLog> findByAppGuid(String appGuid){
        return approvalScheduleLogMapper.findByAppGuid(appGuid);
    }
}
