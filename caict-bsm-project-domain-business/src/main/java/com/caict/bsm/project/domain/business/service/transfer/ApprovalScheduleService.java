package com.caict.bsm.project.domain.business.service.transfer;

import com.caict.bsm.project.domain.business.mapper.transfer.ApprovalScheduleMapper;
import com.caict.bsm.project.system.data.service.BasicService;
import com.caict.bsm.project.system.model.dto.business.station.StationScheduleDataDTO;
import com.caict.bsm.project.system.model.entity.business.transfer.ApprovalRawBts;
import com.caict.bsm.project.system.model.entity.business.transfer.ApprovalSchedule;
import com.caict.bsm.project.system.model.entity.business.transfer.ApprovalScheduleLog;
import com.caict.bsm.project.system.model.entity.business.transfer.CoordinateScheduleTemp;
import com.caict.bsm.project.system.utils.util.VerificationCode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.TreeSet;
import java.util.stream.Collectors;

/**
 * Created by dengsy on 2020-04-22.
 */
@Service
public class ApprovalScheduleService extends BasicService<ApprovalSchedule> {

    @Autowired
    private ApprovalScheduleMapper approvalScheduleMapper;

    /**
     * 根据appGuid删除全部
     * */
    public int deleteAllByAppGuid(String appGuid){
        return approvalScheduleMapper.deleteAllByAppGuid(appGuid);
    }

    /**
     * 根据appGuid插入审核日志
     * */
    public int InsertApprovalScheduleLogByAppGuid(String appGuid, Long isApproved){
        return approvalScheduleMapper.InsertApprovalScheduleLogByAppGuid(appGuid,isApproved);
    }

    /**
     * 根据appGuid查询返回日志记录
     * */
    public  List<ApprovalScheduleLog> findAllApprovalScheduleLogByAppGuid(String appGuid, Long isApproved){
        return approvalScheduleMapper.findAllApprovalScheduleLogByAppGuid(appGuid,isApproved);
    }

    /**
     * 根据appGuid、类型审核数据数量
     * */
    public int selectCountStationScheduleDataDTOPageByJobIdDataType(String appGuid,String dateType){
        return approvalScheduleMapper.selectCountStationScheduleDataDTOPageByJobIdDataType(appGuid,dateType);
    }

    /**
     * 根据appGuid、类型审核数据
     * */
    public List<StationScheduleDataDTO> findStationScheduleDataDTOPageByJobIdDataType(String appGuid,String dataType){
//        if (dateType.equals("1")) {
//            return approvalScheduleMapper.findStationScheduleDataDTOPageByJobIdAdd(appGuid, dateType);
//        } else {
//            return approvalScheduleMapper.findStationScheduleDataDTOPageByJobIdUpdate(appGuid, dateType);
//        }
        List<StationScheduleDataDTO> transportScheduleListRedis;
        if (dataType.equals("3")) {
            transportScheduleListRedis = approvalScheduleMapper.findStationScheduleDataDTOPageByJobIdDataTypeUpdate(appGuid,dataType,"3", "3", "3");
        } else if (dataType.equals("2")) {
            transportScheduleListRedis = approvalScheduleMapper.findStationScheduleDataDTOPageByJobIdDataTypeUpdate(appGuid,dataType,"1", "2", "3");
            // 去重
            if (transportScheduleListRedis.size() > 1) {
                transportScheduleListRedis = transportScheduleListRedis.stream().collect(Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(StationScheduleDataDTO::getBtsId))), ArrayList::new));
            }
        } else {
            transportScheduleListRedis = approvalScheduleMapper.findStationScheduleDataDTOPageByJobIdDataTypeAdd(appGuid,dataType,"1", "2", "3");
            if (transportScheduleListRedis.size() > 1) {
                transportScheduleListRedis = transportScheduleListRedis.stream().collect(Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(StationScheduleDataDTO::getBtsId))), ArrayList::new));
            }
        }
        return transportScheduleListRedis;
    }

    /**
     * 根据appGuid查询返回干扰协调数据
     * */
    public List<CoordinateScheduleTemp> findAllCoordinateScheduleByAppGuid(String jobGuid, String appGuid){
        return approvalScheduleMapper.findAllCoordinateScheduleByAppGuid(jobGuid,appGuid);
    }

    /**
     * 根据appGuid查询ApprovalRawBts
     * */
    public List<ApprovalRawBts> findAllByAppGuid(String appGuid){
        return approvalScheduleMapper.findAllByAppGuid(appGuid);
    }

    /**
     * 根据jobGuid查询总数
     * */
    public int selectCountByJob(String jobGuid){
        return approvalScheduleMapper.selectCountByJob(jobGuid);
    }

    public String selectIsValidByBtsId(String btsId,String userGuid,String techType){
        return approvalScheduleMapper.selectIsValidByBtsId(btsId,userGuid,techType);
    }

    /**
     * 添加审核待办数据
     * */
    public int insertByTransportRawBts(String appGuid){
        return approvalScheduleMapper.insertByTransportRawBts(appGuid);
    }

    /**
     * 根据job删除
     * */
    public int deleteAllByJob(String jobGuid){
        return approvalScheduleMapper.deleteAllByJob(jobGuid);
    }
}
