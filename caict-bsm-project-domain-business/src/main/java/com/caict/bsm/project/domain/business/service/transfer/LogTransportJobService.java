package com.caict.bsm.project.domain.business.service.transfer;

import com.caict.bsm.project.domain.business.mapper.transfer.LogTransportJobMapper;
import com.caict.bsm.project.system.data.service.BasicService;
import com.caict.bsm.project.system.model.contrust.DBBoolConst;
import com.caict.bsm.project.system.model.dto.business.transfer.LogTransportJobDTO;
import com.caict.bsm.project.system.model.entity.business.transfer.LogTransportJob;
import com.caict.bsm.project.system.utils.util.VerificationCode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * Created by dengsy on 2020-04-22.
 */
@Service
public class LogTransportJobService extends BasicService<LogTransportJob> {

    @Autowired
    private LogTransportJobMapper logTransportJobMapper;

    /**
     * 根据日志类型和文件id查询
     */
    public List<LogTransportJobDTO> findAllByLogTypeFileId(Long logType, String fileGuid) {
        return logTransportJobMapper.findAllByLogTypeFileId(logType, fileGuid);
    }

    /**
     * 下载校验后错误的日志
     *
     * @param fileGuid
     * @return
     */
    public List<LogTransportJob> findErrorDataByFileId(String fileGuid) {
        return logTransportJobMapper.findErrorDataByFileId(fileGuid);
    }

    /**
     * 设置Log数据
     */
    public LogTransportJob setLogTransportJob(String jobId, Long type, String brief, String detail, String fileGuid) {
        LogTransportJob logTransportJob = new LogTransportJob();
        logTransportJob.setIsDeleted(DBBoolConst.FALSE);
        logTransportJob.setGmtCreate(new Date());
        logTransportJob.setGmtModified(new Date());

        logTransportJob.setJobGuid(jobId);
        logTransportJob.setLogType(type);
        logTransportJob.setLogShort(brief);
        logTransportJob.setLogDetail(detail);
        logTransportJob.setFileGuid(fileGuid);
        return logTransportJob;
    }

    /**
     * 根据fileId删除错误日志
     */
    public void deleteByFileId(String fileId) {
        logTransportJobMapper.deleteByFileId(fileId);
    }

    /**
     * 根据日志类型和FileId查询总条数
     */
    public int selectCountByTypeFileId(Long logType, String fileId) {
        return logTransportJobMapper.selectCountByTypeFileId(logType, fileId);
    }
}
