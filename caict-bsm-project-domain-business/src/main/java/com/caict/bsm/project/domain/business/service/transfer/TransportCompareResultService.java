package com.caict.bsm.project.domain.business.service.transfer;

import com.caict.bsm.project.domain.business.mapper.transfer.TransportCompareResultMapper;
import com.caict.bsm.project.system.data.service.BasicService;
import com.caict.bsm.project.system.model.dto.business.transfer.TransportCompareResultDTO;
import com.caict.bsm.project.system.model.entity.business.transfer.TransportCompareResult;
import com.caict.bsm.project.system.utils.util.VerificationCode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by dengsy on 2020-04-22.
 */
@Service
public class TransportCompareResultService extends BasicService<TransportCompareResult> {

    @Autowired
    private TransportCompareResultMapper transportCompareResultMapper;

    /**
     * 返回各比对结果数据的条数
     * */
    public List<Integer> getCount(String jobId){
        return transportCompareResultMapper.getCount(jobId);
    }

    /**
     * 查询详情附带发现审核后的数据
     * */
    public TransportCompareResultDTO findOneDetail(String guid){
        return transportCompareResultMapper.findOneDetail(guid);
    }

    /**
     * 查询详情附带发现审核前的数据
     * */
    public TransportCompareResultDTO findDetail(String guid){
        return transportCompareResultMapper.findDetail(guid);
    }

    /**
     * 根据appGuid查询总数
     * */
    public int selectCountByAppGuid(String appGuid,String resultType){
        return transportCompareResultMapper.selectCountByAppGuid(appGuid,resultType);
    }

    /**
     * 根据appGuid分页查询查询
     * */
    public List<TransportCompareResultDTO> findAllPageByAppGuid(String appGuid,String resultType){
        return transportCompareResultMapper.findAllPageByAppGuid(appGuid,resultType);
    }

    /**
     * 根据appGuid分页查询查询（审核前)
     * */
    public List<TransportCompareResultDTO> findAllPageScheduleByAppGuid(String appGuid,String resultType){
        return transportCompareResultMapper.findAllPageScheduleByAppGuid(appGuid,resultType);
    }

    /**
     * 根据类型查询数量
     * */
    public int selectCountByResultType(String appGuid,String resultType){
        return transportCompareResultMapper.selectCountByResultType(appGuid,resultType);
    }

    /**
     * 根据类型查询不属于的数量
     * */
    public int selectCountByNotResultType(String appGuid,String resultType){
        return transportCompareResultMapper.selectCountByNotResultType(appGuid,resultType);
    }

    /**
     * 添加对比数据
     * */
    public int insertByAppGuid(String appGuid){
        return transportCompareResultMapper.insertByAppGuid(appGuid);
    }
}
