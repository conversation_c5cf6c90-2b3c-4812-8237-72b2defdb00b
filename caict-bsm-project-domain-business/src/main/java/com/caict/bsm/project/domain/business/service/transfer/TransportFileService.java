package com.caict.bsm.project.domain.business.service.transfer;

import com.caict.bsm.project.domain.business.mapper.transfer.TransportFileMapper;
import com.caict.bsm.project.system.data.service.BasicService;
import com.caict.bsm.project.system.model.dto.business.transfer.TransportFileDTO;
import com.caict.bsm.project.system.model.entity.business.transfer.TransportFile;
import com.caict.bsm.project.system.utils.util.VerificationCode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by dengsy on 2020-04-22.
 */
@Service
public class TransportFileService extends BasicService<TransportFile> {

    @Autowired
    private TransportFileMapper transportFileMapper;

    /**
     * 根据Job、文件名查询数量
     * */
    public int selectCountByJobFileName(String jobId,String fileName){
        return transportFileMapper.selectCountByJobFileName(jobId,fileName);
    }

    public List<TransportFileDTO> findByJobGuid(String jobGuid, String dataType, String genNum, String fileType){
        return transportFileMapper.findByJobGuid(jobGuid,dataType,genNum,fileType);
    }

    public List<TransportFileDTO> findByGuidType(String jobGuid,String fileType){
        return transportFileMapper.findByGuidType(jobGuid,fileType);
    }

    /**
     * 根据id查询
     * */
    public TransportFile findOne(String guid){
        TransportFile transportFile = new TransportFile();
        transportFile.setGuid(guid);
        return transportFileMapper.findOne(transportFile);
    }
}
