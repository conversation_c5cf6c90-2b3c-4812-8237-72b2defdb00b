package com.caict.bsm.project.domain.business.service.transfer;

import com.caict.bsm.project.domain.business.mapper.transfer.TransportJobBranchMapper;
import com.caict.bsm.project.system.data.service.BasicService;
import com.caict.bsm.project.system.model.dto.business.applytable.BsmApplyTableTemplateDTO;
import com.caict.bsm.project.system.model.dto.business.applytable.BsmApplyTemplateDTO;
import com.caict.bsm.project.system.model.dto.business.station.StationBakDTO;
import com.caict.bsm.project.system.model.dto.business.station.StationDTO;
import com.caict.bsm.project.system.model.dto.business.transfer.TransportJobBranchDTO;
import com.caict.bsm.project.system.model.dto.business.transfer_in.PackageDataDTO;
import com.caict.bsm.project.system.model.dto.business.transfer_in.SchedualDTO;
import com.caict.bsm.project.system.model.dto.business.transfer_in.TransportJobInBranchDTO;
import com.caict.bsm.project.system.model.dto.security.UsersDTO;
import com.caict.bsm.project.system.model.entity.business.transfer.TransportJobBranch;
import com.caict.bsm.project.system.model.entity.business.transfer.TransportRawBtsDealLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/6/15
 */
@Service
public class TransportJobBranchService extends BasicService<TransportJobBranch> {

    @Autowired
    private TransportJobBranchMapper transportJobBranchMapper;

    public int selectAllCount(String jobGuid){
        return transportJobBranchMapper.selectAllCount(jobGuid);
    }

    public List<TransportJobBranchDTO> findAllBranch(TransportJobBranchDTO transportJobBranchDTO){
        return transportJobBranchMapper.findAllBranch(transportJobBranchDTO);
    }

    public int updateByComfirm(String jobGuid,String dataType,String genNum,String isCompare){
        return transportJobBranchMapper.updateByComfirm(jobGuid,dataType,genNum,isCompare);
    }

    public int updateByComfirmAuto(String jobGuid,String isCompare){
        return transportJobBranchMapper.updateByComfirmAuto(jobGuid,isCompare);
    }

    /**
     * 判断此job下是否已经全部确认完
     */
    public int judgeAllConfirm(String jobGuid){
        return transportJobBranchMapper.judgeAllConfirm(jobGuid);
    }

    /**
     *根据jobid删除
     */
    public int deleteByJobId(String jobId){
        return transportJobBranchMapper.deleteByJobId(jobId);
    }

    public void updateByJobId(String jobId,String dataType,String genNum,String isCompare){
        transportJobBranchMapper.updateByJobId(jobId,dataType,genNum,isCompare);
    }

    /**
     * 查询用户下所有申请表
     * */
    public List<TransportJobBranchDTO> findAllByUser(String userId){
        return transportJobBranchMapper.findAllByUser(userId);
    }

    /**
     * 根据任务、运营商、类型、制式查询
     * */
    public TransportJobBranchDTO findOneByJobUserTypeGen(String jobGuid,String userGuid,String dataType,String genNum,String regionCode,String techType){
        return transportJobBranchMapper.findOneByJobUserTypeGen(jobGuid,userGuid,dataType,genNum,regionCode,techType);
    }

    /**
     * 根据任务、运营商、类型、制式查询
     * */
    public List<TransportJobBranchDTO> findOneByJobUserTypeGen1(String jobGuid,String techType,String userGuid,String dataType,String genNum,String regionCode){
        return transportJobBranchMapper.findOneByJobUserTypeGen1(jobGuid,techType,userGuid,dataType,genNum,regionCode);
    }

    public TransportJobBranch findByAppCode(String appCode){
        return transportJobBranchMapper.findByAppCode(appCode);
    }

    public List<TransportJobInBranchDTO> findListByPage(TransportJobInBranchDTO transportJobInBranchDTO, UsersDTO usersDTO){
        return transportJobBranchMapper.findListByPage(transportJobInBranchDTO,usersDTO);
    }

    public TransportJobInBranchDTO findDetail(String guid){
        return transportJobBranchMapper.findDetail(guid);
    }

    public List<SchedualDTO> findSchedual(String regionId){
        return transportJobBranchMapper.findSchedual(regionId);
    }

    /*public List<TransportJobBranch> findListByJobGuid(String jobGuid){
        return transportJobBranchMapper.findListByJobGuid(jobGuid);
    }*/

    public List<StationBakDTO> findStationListByGuid(TransportJobInBranchDTO transportJobInBranchDTO){
        return transportJobBranchMapper.findStationListByGuid(transportJobInBranchDTO);
    }

    public List<StationDTO> findHistoryByGuid(TransportJobInBranchDTO transportJobInBranchDTO){
        return transportJobBranchMapper.findHistoryByGuid(transportJobInBranchDTO.getGuid());
    }

    /**
     * 外网端查询申请表
     * */
    public BsmApplyTemplateDTO findBsmApplyTemplateByGuid(String guid){
        return transportJobBranchMapper.findBsmApplyTemplateByGuid(guid);
    }

    /**
     * 外网端查询申请表
     * */
    public TransportJobInBranchDTO findBsmApplyTemplateByAppGuid(String guid){
        return transportJobBranchMapper.findBsmApplyTemplateByAppGuid(guid);
    }

    /**
     * 内网查询用户下所有申请表
     * */
    public List<TransportJobBranchDTO> findAllApply(TransportJobInBranchDTO transportJobInBranchDTO){
        return transportJobBranchMapper.findAllApply(transportJobInBranchDTO);
    }

    /**
     * 外网端查询申请表技术资料信息
     * */
    public List<BsmApplyTableTemplateDTO> findBsmApplyTableTemplateByJobUserDataType(String jobGuid, String dataType,String code,String genNum){
        return transportJobBranchMapper.findBsmApplyTableTemplateByJobUserDataType(jobGuid,dataType,code,genNum);
    }

    /**
     * 运营商查看分任务详情
     */
    public List<TransportRawBtsDealLog> findAllDetailByUser(String guid, String userGuid){
        return transportJobBranchMapper.findAllDetailByUser(guid,userGuid);
    }

    /**
     * 运营商查看分任务详情
     */
    public TransportJobInBranchDTO selectByNewAppCode(String appCode){
        return transportJobBranchMapper.selectByNewAppCode(appCode);
    }

    /**
     * 根据job和状态查询数量
     * */
    public int selectCountByJobState(String jobGuid,String state){
        return transportJobBranchMapper.selectCountByJobState(jobGuid,state);
    }

    public List<PackageDataDTO> findSchedualPackage(){
        return transportJobBranchMapper.findSchedualPackage();
    }

    public List<PackageDataDTO> findSchedualPackage2(){
        return transportJobBranchMapper.findSchedualPackage2();
    }
}
