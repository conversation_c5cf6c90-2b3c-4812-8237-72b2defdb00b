package com.caict.bsm.project.domain.business.service.transfer;

import com.caict.bsm.project.domain.business.mapper.transfer.TransportJobBranchTempMapper;
import com.caict.bsm.project.system.model.entity.business.transfer.TransportJobBranchTemp;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description TransportJobBranchTempService
 * @date 2023/4/27 17:13
 */

@Service
public class TransportJobBranchTempService {

    @Autowired
    private TransportJobBranchTempMapper transportJobBranchTempMapper;

    public int insertBatch(List<TransportJobBranchTemp> transportJobBranchTemps){
        return transportJobBranchTempMapper.insertBatch(transportJobBranchTemps);
    }

    public List<TransportJobBranchTemp> findAllBranch(String jobGuid){
        return transportJobBranchTempMapper.findAllBranch(jobGuid);
    }

    public List<TransportJobBranchTemp> findGroupByRegion(){
        return transportJobBranchTempMapper.findGroupByRegion();
    }

    public int updateIsCompareByJobGuidRegion(String jobGuid,String regionCode,String status){
        return transportJobBranchTempMapper.updateIsCompareByJobGuidRegion(jobGuid,regionCode,status);
    }
}
