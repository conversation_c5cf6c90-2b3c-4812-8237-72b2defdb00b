package com.caict.bsm.project.domain.business.service.transfer;

import com.caict.bsm.project.domain.business.mapper.transfer.TransportJobMapper;

import com.caict.bsm.project.system.data.service.BasicService;
import com.caict.bsm.project.system.model.dto.business.transfer.TransportJobDTO;
import com.caict.bsm.project.system.model.dto.business.transfer.TransportRawBtsDTO;
import com.caict.bsm.project.system.model.dto.business.transfer_in.TransportJobInDTO;
import com.caict.bsm.project.system.model.dto.security.UsersDTO;
import com.caict.bsm.project.system.model.entity.business.transfer.TransportJob;

import com.caict.bsm.project.system.utils.util.VerificationCode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by dengsy on 2020-04-22.
 */
@Service
public class TransportJobService extends BasicService<TransportJob> {

    @Autowired
    private TransportJobMapper transportJobMapper;

    /**
     * 根据用户类型、状态和流程查询数量
     * */
    public int selectCountByTypeStateCompare(String userId,Long jobState,List<String> compares){
        return transportJobMapper.selectCountByTypeStateCompare(userId,jobState,compares);
    }

    /**
     * 根据用户类型、任务名称查询
     * */
    public TransportJobDTO findOneByJobName(String userType, String jobName){
        return transportJobMapper.findOneByJobName(userType,jobName);
    }

    /**
     * 根据userType和任务流程状态查询不是同一个Job的任务
     * */
    public TransportJobDTO findOneByUserTypeAndCompareNotJobId(String userType,String isCompare,String jobId){
        return transportJobMapper.findOneByUserTypeAndCompareNotJobId(userType,isCompare,jobId);
    }

    /**
     * 条件查询总数
     * */
    public int selectAllCount(TransportJobDTO transportJobDTO,String userId){
        return transportJobMapper.selectAllCount(transportJobDTO,userId);
    }

    /**
     * 条件分页查询
     * */
    public List<TransportJobDTO> findAllPage(TransportJobDTO transportJobDTO,String userId){
        return transportJobMapper.findAllPage(transportJobDTO,userId);
    }

    /**
     * 根据事件名称查询
     * */
    public List<TransportJobDTO> findAllLikeByJobNameAndJobState(String jobName,Long jobState,String userId){
        return transportJobMapper.findAllLikeByJobNameAndJobState(jobName,jobState,userId);
    }

    /**
     * 查询任务文件异常总数
     * */
    public int selectCountTransportJobVOLogCsv(String jobName,String userId,Long fileState){
        return transportJobMapper.selectCountTransportJobVOLogCsv(jobName,userId,fileState);
    }

    /**
     * 分页查询任务文件异常列表
     * */
    public List<TransportJobDTO> selectTransportJobVOLogCsv(String jobName,String userId,Long fileState){
        return transportJobMapper.selectTransportJobVOLogCsv(jobName,userId,fileState);
    }

    /**
     * 根据业务流程状态查询总数
     * */
    public int selectCountByIsCompareOrderGmtModified(String isCompare){
        return transportJobMapper.selectCountByIsCompareOrderGmtModified(isCompare);
    }

    /**
     * 根据业务流程状态分页查询
     * */
    public List<TransportJobDTO> findAllPageByIsCompareOrderGmtModified(String isCompare){
        return transportJobMapper.findAllPageByIsCompareOrderGmtModified(isCompare);
    }

    /**
     * 查询详情
     * */
    public TransportJobDTO findOneByGuid(String jobGuid){
        return transportJobMapper.findOneByGuid(jobGuid);
    }

    public List<TransportJob> findJob(){
        return transportJobMapper.findJob();
    }

    /**
     * 条件分页查询
     * */
    public List<TransportJobInDTO> findInAllPage(TransportJobInDTO transportJobInDTO, UsersDTO usersDTO){
        return transportJobMapper.findInAllPage(transportJobInDTO,usersDTO);
    }

    /**
     * 根据用户查询全部
     * */
    public List<TransportJobDTO> findAllByUser(String userId){
        return transportJobMapper.findAllByUser(userId);
    }

    /**
     * 根据用户查询全部
     * */
    public List<TransportRawBtsDTO> findIncrease(String guid,String regionCode,String dataType){
        return transportJobMapper.findIncrease(guid,regionCode,dataType);
    }

    /**
     * 根据branch地区code查询
     *
     * @param areaCode areaCode
     * @return list
     */
    public List<TransportJobDTO> findJobPageByBranchCode(String areaCode) {
        return transportJobMapper.findJobPageByBranchCode(areaCode);
    }

    public int updateIsDeleted(String jobId,String isDeal){
        return transportJobMapper.updateIsDeleted(jobId,isDeal);
    }

    public TransportJob findByStatusOrderByDate(){
        return transportJobMapper.findByStatusOrderByDate();
    }

    public List<String> findAppCodeList(String jobGuid){
        return transportJobMapper.findAppCodeList(jobGuid);
    }

    /**
     * 根据id查询
     * */
    public TransportJob findOne(String guid){
        TransportJob transportJob = new TransportJob();
        transportJob.setGuid(guid);
        return transportJobMapper.findOne(transportJob);
    }

    public void updateCompleteByGuid(String guid,String status){
        transportJobMapper.updateCompleteByGuid(guid,status);
    }
}
