package com.caict.bsm.project.domain.business.service.transfer;

import com.caict.bsm.project.domain.business.mapper.transfer.TransportRawBtsDealMapper;
import com.caict.bsm.project.system.data.service.BasicService;
import com.caict.bsm.project.system.model.dto.business.transfer.TransportRawBtsDealDTO;
import com.caict.bsm.project.system.model.dto.business.transfer.TransportRawBtsDealShowDTO;
import com.caict.bsm.project.system.model.entity.business.transfer.TransportRawBtsDeal;
import com.caict.bsm.project.system.utils.util.VerificationCode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Iterator;
import java.util.List;

/**
 * Created by dengsy on 2020-04-22.
 */
@Service
public class TransportRawBtsDealService extends BasicService<TransportRawBtsDeal> {

    @Autowired
    private TransportRawBtsDealMapper transportRawBtsDealMapper;

    /**
     * 根据job删除
     * */
    public int deleteByJobId(String jobId){
        return transportRawBtsDealMapper.deleteByJobId(jobId);
    }

    /**
     * 根据jobid、dataType、genNum删除
     * */
    public int deleteByJobIdDataTypeGenNum(String jobId,String dataType,String genNum){
        return transportRawBtsDealMapper.deleteByJobIdDataTypeGenNum(jobId,dataType,genNum);
    }

    public void updateConfirm(String jobId,String dataType,String genNum){
        transportRawBtsDealMapper.updateConfirm(jobId,dataType,genNum);
    }

    /**
     * 根据job流程状态用户查询总数
     * */
    public int selectAllCount(String userId, TransportRawBtsDealDTO transportRawBtsDealDTO){
        return transportRawBtsDealMapper.selectAllCount(userId,transportRawBtsDealDTO);
    }

    /**
     * 根据job流程状态用户分页查询
     * */
    public List<TransportRawBtsDealDTO> findAllPage(String userId,TransportRawBtsDealDTO transportRawBtsDealDTO, String dataType1, String dataType2, String dataType3){
        return transportRawBtsDealMapper.findAllPage(userId,transportRawBtsDealDTO,dataType1,dataType2,dataType3);
    }

    /**
     * 下载查询总数
     * */
    public int selectCountPage(String userId,TransportRawBtsDealDTO transportRawBtsDealDTO){
        return transportRawBtsDealMapper.selectCountPage(userId,transportRawBtsDealDTO);
    }

    /**
     * 下载查询
     */
    public List<TransportRawBtsDealDTO> selectPage(String userId, TransportRawBtsDealDTO transportRawBtsDealDTO, String btsDataType, String genNum, String dataType1, String dataType2, String dataType3) {
        return transportRawBtsDealMapper.selectPage(userId, transportRawBtsDealDTO, btsDataType, genNum, dataType1, dataType2, dataType3);
    }

    /**
     * 根据流程类型查询详细数量
     * */
    public List<TransportRawBtsDealShowDTO> findAllTransportRawBtsDealShowDTO(List<String> compares){
        List<TransportRawBtsDealShowDTO> allTransportRawBtsDealShowDTO = transportRawBtsDealMapper.findAllTransportRawBtsDealShowDTO(compares);
        Iterator<TransportRawBtsDealShowDTO> it = allTransportRawBtsDealShowDTO.iterator();
        while(it.hasNext()){
            TransportRawBtsDealShowDTO transportRawBtsDealShowDTO = it.next();
            if(transportRawBtsDealShowDTO.getTransportRawBtsDealNew() == 0 && transportRawBtsDealShowDTO.getTransportRawBtsDealUpdate()==0 && transportRawBtsDealShowDTO.getTransportRawBtsDealDelete()==0){
                it.remove();
            }
        }
        return allTransportRawBtsDealShowDTO;
    }

    /**
     * 根据job、类型查询总数
     * */
    public int selectCountByJobDataType(String jobGuid,String dataType,String genNum){
        return transportRawBtsDealMapper.selectCountByJobDataType(jobGuid,dataType,genNum);
    }

    /**
     * 数据确认时根据Job、类型分页查询
     * */
    public List<TransportRawBtsDealDTO> findAllPageByJobDataType(String jobGuid,String btsIdDataType,String genNum, String dataType1, String dataType2, String dataType3) {
        return transportRawBtsDealMapper.findAllPageByJobDataType(jobGuid,btsIdDataType,genNum,dataType1, dataType2, dataType3);
    }

    /**
     * 数据审核时根据Job、类型分页查询
     * */
    public List<TransportRawBtsDealDTO> findCheckPageByJobDataType(String jobGuid,String btsIdDataType,String genNum, String dataType1, String dataType2, String dataType3) {
        return transportRawBtsDealMapper.findCheckPageByJobDataType(jobGuid,btsIdDataType,genNum,dataType1, dataType2, dataType3);
    }

    /**
     * 生成下载数据
     * */
    public int insertByTransportRawBts(String jobGuid,String regionCode){
        return transportRawBtsDealMapper.insertByTransportRawBts(jobGuid,regionCode);
    }

    /**
     * 根据btsId更新数据
     *
     * @param jobId jobId
     * @param btsIds list
     */
    public void updateByBtsIds(String dataType, String jobId, List<String> btsIds,String regionCode){
        transportRawBtsDealMapper.updateByBtsIds(dataType, jobId, btsIds,regionCode);
    }

    /**
     * 根据jobId、btsId查询数据
     *
     * @param jobId jobId
     * @param btsId btsId
     * @return list
     */
    public List<TransportRawBtsDealDTO> findAllCellByJobBtsId(String jobId, String btsId) {
        return transportRawBtsDealMapper.findAllCellByJobBtsId(jobId, btsId);
    }
}
