package com.caict.bsm.project.domain.business.service.transfer;

import com.caict.bsm.project.domain.business.mapper.transfer.TransportRawBtsMapper;
import com.caict.bsm.project.system.data.service.BasicService;
import com.caict.bsm.project.system.model.dto.business.transfer.TransportRawBtsDTO;
import com.caict.bsm.project.system.model.entity.business.dataconver.AsyncRawBts;
import com.caict.bsm.project.system.model.entity.business.transfer.TransportJobBranchTemp;
import com.caict.bsm.project.system.model.entity.business.transfer.TransportRawBts;
import com.caict.bsm.project.system.utils.util.VerificationCode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by dengsy on 2020-04-22.
 */
@Service
public class TransportRawBtsService extends BasicService<TransportRawBts> {

    @Autowired
    private TransportRawBtsMapper transportRawBtsMapper;

    /**
     * 根据fileId删除数据
     * */
    public int deleteAllByFileId(String fileId){
        return transportRawBtsMapper.deleteAllByFileId(fileId);
    }

    /**
     * 根据jobId查询总数
     * */
    public int selectCountByJobId(String jobId){
        return transportRawBtsMapper.selectCountByJobId(jobId);
    }

    /**
     * 根据jobId分页查询
     * */
    public List<TransportRawBtsDTO> findAllPageByJobId(String jobId){
        return transportRawBtsMapper.findAllPageByJobId(jobId);
    }

    /**
     * 数据库基础数据反向生成
     * */
    public List<TransportRawBtsDTO> setTransportRawBtsDTOs(String orgName,String netTs,int rownum){
        return transportRawBtsMapper.setTransportRawBtsDTOs(orgName,netTs,rownum);
    }

    /**
     * 根据fileId查询总数
     * */
    public int selectCountByFileId(String fileId){
        return transportRawBtsMapper.selectCountByFileId(fileId);
    }

    /**
     * 查询基站基础数据不一致的基站识别号
     * */
    public List<TransportRawBtsDTO> findAllBtsId(String jobGuid,String fileGuid){
        return transportRawBtsMapper.findAllBtsId(jobGuid,fileGuid);
    }

    /**
     * 查询基站基础数据不一致的基站识别号
     * */
    public List<TransportRawBtsDTO> findAllBtsIdExpend(String jobGuid,String fileGuid){
        return transportRawBtsMapper.findAllBtsIdExpend(jobGuid,fileGuid);
    }

    /**
     * 根据基站批量修改状态
     * */
    public int updateIsValid(String jobGuid,String fileGuid,long isValid){
        return transportRawBtsMapper.updateIsValid(jobGuid,fileGuid,isValid);
    }

    /**
     * 批量处理直放站，修改bts_id为cell_id
     * */
    public int updateBtsFromCell(String jobGuid,String fileGuid){
        return transportRawBtsMapper.updateBtsFromCell(jobGuid,fileGuid);
    }

    /**
     * 识别新增（方法待优化）
     * */
    public int updateBtsDataTypeAdd(String jobGuid,String regionCode){
        return transportRawBtsMapper.updateBtsDataTypeAdd(jobGuid,regionCode);
    }

    /**
     * 识别变更和不变（方法待优化）
     * */
    public int updateBtsDataTypeUpdate(String jobGuid,String regionCode){
        return transportRawBtsMapper.updateBtsDataTypeUpdate(jobGuid,regionCode);
    }

    /**
     * 识别延续和不变（方法待优化）
     * */
    public int updateBtsDataTypeContinue(String jobGuid,String regionCode){
        return transportRawBtsMapper.updateBtsDataTypeContinue(jobGuid,regionCode);
    }

    /**
     * 运营商增量提交数据时进行数据类型更改
     * */
    public int updateBtsDataTypeSingleAdd(String jobGuid,String regionCode){
        return transportRawBtsMapper.updateBtsDataTypeSingleAdd(jobGuid,regionCode);
    }

    /**
     * 运营商增量提交数据时进行数据类型更改
     * */
    public int updateBtsDataTypeSingleUpdate(String jobGuid,String regionCode){
        return transportRawBtsMapper.updateBtsDataTypeSingleUpdate(jobGuid,regionCode);
    }

    /**
     * 运营商增量提交数据时进行数据类型注销
     * */
    public int updateBtsDataTypeSingleDel(String jobGuid,String regionCode){
        return transportRawBtsMapper.updateBtsDataTypeSingleDel(jobGuid,regionCode);
    }

    /**
     * 根据Job删除
     * */
    public int deleteAllByJob(String jobGuid,String regionCode){
        return transportRawBtsMapper.deleteAllByJob(jobGuid,regionCode);
    }

    public List<TransportRawBts> findByJobIdAndRegion(String jobGuid,String regionCode){
        return transportRawBtsMapper.findByJobIdAndRegion(jobGuid,regionCode);
    }

    /**
     * 查询基站基础数据不一致的基站识别号
     *
     * @param jobGuid jobGuid
     * @return list
     */
    public List<TransportRawBtsDTO> findAllBtsId1(String jobGuid){
        return transportRawBtsMapper.findAllBtsId1(jobGuid);
    }

    /**
     * 根据cellid查询数据
     *
     * @param orgType orgType
     * @return list
     */
    public  List<AsyncRawBts> findLayuanByOrgType(String orgType) {
        return transportRawBtsMapper.findLayuanByOrgType(orgType);
    }

    /**
     * 更新数据
     *
     * @param transportRawBts transportRawBts
     */
    public void update1(TransportRawBts transportRawBts){
        transportRawBtsMapper.update(transportRawBts);
    }

    /**
     * 根据btsids查询所有
     *
     * @param jobId jobId
     * @param btsIds btsId
     * @return list
     */
    public List<TransportRawBts> findAllByBtsIds(String[] btsIds, String jobId){
        return transportRawBtsMapper.findAllByBtsIds(btsIds, jobId);
    }

    /**
     * 将表数据按地区分类
     *
     * @param jobId jobId
     * @return list
     */
    public List<TransportJobBranchTemp> selectDistinctRegionByJobid(String jobId) {
        return transportRawBtsMapper.selectDistinctRegionByJobid(jobId);
    }
}
