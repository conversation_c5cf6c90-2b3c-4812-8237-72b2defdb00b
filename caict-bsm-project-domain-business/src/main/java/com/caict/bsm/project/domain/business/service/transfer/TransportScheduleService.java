package com.caict.bsm.project.domain.business.service.transfer;

import com.caict.bsm.project.domain.business.mapper.transfer.TransportScheduleMapper;
import com.caict.bsm.project.system.data.service.BasicService;
import com.caict.bsm.project.system.model.dto.business.station.StationScheduleDataDTO;
import com.caict.bsm.project.system.model.dto.business.transfer.TransportJobBranchDTO;
import com.caict.bsm.project.system.model.dto.business.transfer.TransportScheduleDTO;
import com.caict.bsm.project.system.model.dto.business.transfer.TransportScheduleShowDTO;
import com.caict.bsm.project.system.model.entity.business.transfer.TransportSchedule;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Iterator;
import java.util.List;

/**
 * Created by dengsy on 2020-04-22.
 */
@Service
public class TransportScheduleService extends BasicService<TransportSchedule> {

    @Autowired
    private TransportScheduleMapper transportScheduleMapper;

    /**
     * 据jobId删除
     * */
    public int deleteByJobId(String jobId){
        return transportScheduleMapper.deleteByJobId(jobId);
    }

    /**
     * 根据jobId删除
     * */
    public int deleteByJobIdDataTypeGenNum(String jobId,String dataType,String genNum){
        return transportScheduleMapper.deleteByJobIdDataTypeGenNum(jobId,dataType,genNum);
    }

    /**
     * 根据流程类型查询代办数量
     * */
    public List<TransportScheduleShowDTO> findAllTransportSchedule(List<String> compares, String isHandle){
        List<TransportScheduleShowDTO> allTransportSchedule = transportScheduleMapper.findAllTransportSchedule(compares, isHandle);
        Iterator<TransportScheduleShowDTO> it = allTransportSchedule.iterator();
        while(it.hasNext()){
            TransportScheduleShowDTO transportSchedule = it.next();
            if(transportSchedule.getScheduleNew() == 0 && transportSchedule.getScheduleUpdate()==0 && transportSchedule.getScheduleDelete()==0){
                it.remove();
            }
        }
        return allTransportSchedule;
    }

    /**
     * 根据jobId、类型查询总数
     * */
    public int selectCountByJobIdDataType(String jobId,String dataType,String isHandle){
        return transportScheduleMapper.selectCountByJobIdDataType(jobId,dataType,isHandle);
    }

    /**
     * 根据jobId、类型分页查询
     * */
    public  List<TransportScheduleDTO> findAllPageByJobIdDataType(String jobId, String dataType, String isHandle){
        return transportScheduleMapper.findAllPageByJobIdDataType(jobId,dataType,isHandle);
    }

    /**
     * 根据jobId、类型分页查询审核数据
     * */
    public List<StationScheduleDataDTO> findStationScheduleDataDTOPageByJobIdDataType(int rowsStart, int rowsEnd, String jobId, String dataType){
        return transportScheduleMapper.findStationScheduleDataDTOPageByJobIdDataType(rowsStart,rowsEnd,jobId,dataType);
    }

    /**
     * 根据jobGuid查询总数
     * */
    public int selectCountAllByJob(String jobGuid,String isHandle){
        return transportScheduleMapper.selectCountAllByJob(jobGuid,isHandle);
    }

    public void updateConfirm(String jobId,String dataType,String genNum){
        transportScheduleMapper.updateConfirm(jobId,dataType,genNum);
    }

    /**
     * 统计申请表基站数量和扇区数量
     * */
    public List<TransportJobBranchDTO> selectDistinctDataTypeAndGenNum(String jobId,String regionCode){
        return transportScheduleMapper.selectDistinctDataTypeAndGenNum(jobId,regionCode);
    }

    /**
     * 根据jobGuid查询数量
     * */
    public int selectCountByJobGuid(String jobGuid){
        return transportScheduleMapper.selectCountByJobGuid(jobGuid);
    }

    /**
     * 根据扇区id，基站识别id和userId查询
     * */
    public TransportSchedule findOneByCellBtsUser(String cellId,String btsId,String techType,String dataType){
        return transportScheduleMapper.findOneByCellBtsUser(cellId,btsId,techType,dataType);
    }

    /**
     * 根据任务、用户、类型、制式删除
     * */
    public int deleteByJobUserTypeGen(String jobGuid,String userGuid,String dataType,String genNum){
        return transportScheduleMapper.deleteByJobUserTypeGen(jobGuid,userGuid,dataType,genNum);
    }

    /**
     * 生成待办
     * */
    public int insertByTransportRawBts(String jobId,String regionCode){
        return transportScheduleMapper.insertByTransportRawBts(jobId,regionCode);
    }

    /**
     * 根据app删除全部
     * */
    public int deleteAllByAppGuid(String appGuid){
        return transportScheduleMapper.deleteAllByAppGuid(appGuid);
    }

    public int selectCountByJobGuidAndDataType(String jobGuid,String dataType){
        return transportScheduleMapper.selectCountByJobGuidAndDataType(jobGuid,dataType);
    }

    /**
     * 查询同一个btsId下dataType不一致的基站ID和dataType
     *
     * @return list
     */
    public List<TransportSchedule> selectDisAccordBtsIds() {
        return transportScheduleMapper.selectDisAccordBtsIds();
    }

    /**
     * 查询同一个btsId下dataType一致的基站ID
     *
     * @return list
     */
    public List<TransportSchedule> selectAccordBtsIdsAndDataType() {
        return transportScheduleMapper.selectAccordBtsIdsAndDataType();
    }

    /**
     * 查询同一个btsId下dataType一致的基站ID
     *
     * @return list
     */
    public List<TransportSchedule> selectAccordBtsIdCellIdAndDataType() {
        return transportScheduleMapper.selectAccordBtsIdCellIdAndDataType();
    }

    /**
     * 根据btsId更新数据
     *
     * @param jobId jobId
     * @param btsIds list
     */
    public void updateByBtsIds(String dataType, String jobId, List<String> btsIds,String regionCode){
        transportScheduleMapper.updateByBtsIds(dataType, jobId, btsIds,regionCode);
    }
}
