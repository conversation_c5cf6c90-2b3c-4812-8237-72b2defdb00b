package com.caict.bsm.project.domain.business.service.transfer_in;

import com.caict.bsm.project.domain.business.mapper.transfer_in.ApplyJobInMapper;
import com.caict.bsm.project.system.data.service.BasicService;
import com.caict.bsm.project.system.model.dto.business.transfer.ApprovalTransportJobDTO;
import com.caict.bsm.project.system.model.dto.business.transfer_in.ApplyJobInDTO;
import com.caict.bsm.project.system.model.entity.business.transfer_in.ApplyJobIn;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by yanchengpeng on 2021/5/30.
 */
@Service
public class ApplyJobInService extends BasicService<ApplyJobIn> {

    @Autowired
    private ApplyJobInMapper applyJobInMapper;

    public int updateByAppCode(String appGuid,String appCode,String status){
        return applyJobInMapper.updateByAppCode(appGuid,appCode,status);
    }

    public List<ApplyJobInDTO> findDetailByPage(ApprovalTransportJobDTO approvalTransportJobDTO){
        return applyJobInMapper.findDetailByPage(approvalTransportJobDTO.getGuid());
    }

    public ApplyJobInDTO selectByAppCode(String appCode){
        return applyJobInMapper.selectByAppCode(appCode);
    }

    public int updateAppCode(String oldAppCode,String newAppCode){
        return applyJobInMapper.updateAppCode(oldAppCode,newAppCode);
    }

    public List<ApplyJobInDTO> findByAppGuid(String appGuid){
        return applyJobInMapper.findByAppGuid(appGuid);
    }
}
