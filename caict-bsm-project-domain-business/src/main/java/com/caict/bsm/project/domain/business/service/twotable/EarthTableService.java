package com.caict.bsm.project.domain.business.service.twotable;

import com.caict.bsm.project.domain.business.mapper.twotable.EarthTableMapper;
import com.caict.bsm.project.system.data.service.BasicService;
import com.caict.bsm.project.system.model.dto.twotable.EarthTableDTO;
import com.caict.bsm.project.system.model.entity.business.twotable.EarthTable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/12/28
 */
@Service
public class EarthTableService extends BasicService<EarthTable> {

    @Autowired
    private EarthTableMapper earthTableMapper;

    public List<EarthTableDTO> findEarthByPage(EarthTableDTO earthTableDTO){
        return earthTableMapper.findEarthByPage(earthTableDTO);
    }

    public int deleteAll(){
        return earthTableMapper.deleteAll();
    }
}
