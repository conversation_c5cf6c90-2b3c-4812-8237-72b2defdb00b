package com.caict.bsm.project.domain.business.service.twotable;

import com.caict.bsm.project.domain.business.mapper.twotable.ProgressTableMapper;
import com.caict.bsm.project.system.data.service.BasicService;
import com.caict.bsm.project.system.model.dto.twotable.ProgressTableDTO;
import com.caict.bsm.project.system.model.entity.business.twotable.ProgressTable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/12/28
 */
@Service
public class ProgressTableService extends BasicService<ProgressTable> {

    @Autowired
    private ProgressTableMapper progressTableMapper;

    public List<ProgressTableDTO> findProcessByPage(ProgressTableDTO progressTableDTO){
        return progressTableMapper.findProcessByPage(progressTableDTO);
    }

    public int deleteAll(){
        return progressTableMapper.deleteAll();
    }
}
