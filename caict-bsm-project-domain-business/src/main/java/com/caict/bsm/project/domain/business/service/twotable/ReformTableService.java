package com.caict.bsm.project.domain.business.service.twotable;

import com.caict.bsm.project.domain.business.mapper.twotable.ReformTableMapper;
import com.caict.bsm.project.system.data.service.BasicService;
import com.caict.bsm.project.system.model.dto.twotable.ReformTableDTO;
import com.caict.bsm.project.system.model.entity.business.twotable.ReformTable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/12/28
 */
@Service
public class ReformTableService extends BasicService<ReformTable> {

    @Autowired
    private ReformTableMapper reformTableMapper;

    public int batchInsert(List<ReformTable> reformTables){
        return reformTableMapper.insertBatch(reformTables);
    }

    public List<ReformTableDTO> findReformByPage(ReformTableDTO reformTableDTO){
        return reformTableMapper.findReformByPage(reformTableDTO);
    }

    public int deleteAll(){
        return reformTableMapper.deleteAll();
    }
}
