package com.caict.bsm.project.domain.security.mapper;

import com.caict.bsm.project.system.data.mapper.BasicMapper;
import com.caict.bsm.project.system.model.dto.security.UsersDTO;
import com.caict.bsm.project.system.model.entity.security.Login;
import org.apache.ibatis.annotations.*;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Repository;

/**
 * Created by dengsy on 2019-10-24.
 */
@Repository
public interface LoginMapper extends BasicMapper<Login> {

    @Select("select * from FSA_LOGIN where LOGIN_NAME = #{login_name} and PASSWORD = #{password}")
    Login getLoginById(Login login);

    @Select("select FSA_USERS.*," +
            "FSA_LOGIN.LOGIN_NAME,FSA_LOGIN.LAST_DATE,FSA_LOGIN.LAST_WAY,FSA_LOGIN.LAST_IP,FSA_LOGIN.USER_ID " +
            "from FSA_LOGIN,FSA_USERS where FSA_LOGIN.user_id = FSA_USERS.ID and FSA_LOGIN.LOGIN_NAME = #{loginName} and FSA_LOGIN.PASSWORD = #{password}")
    @Results({
            @Result(property = "userId",column = "USER_ID"),
            @Result(property = "rsbtOrgDTO",column = "USER_ID",one = @One(select = "com.caict.bsm.project.domain.security.mapper.RsbtOrgMapper.findOneDetailsByUsers")),
            @Result(property = "roleDTO",column = "USER_ID",one = @One(select = "com.caict.bsm.project.domain.security.mapper.RoleUsersMapper.findRoleByUser"))
    })
    UsersDTO login(@Param("loginName") String loginName, @Param("password") String password);

    /**
     * 根据主键登录
     * */
    @Select("select FSA_USERS.*," +
            "FSA_LOGIN.LOGIN_NAME,FSA_LOGIN.LAST_DATE,FSA_LOGIN.LAST_WAY,FSA_LOGIN.LAST_IP,FSA_LOGIN.USER_ID " +
            "from FSA_LOGIN,FSA_USERS where FSA_LOGIN.USER_ID = FSA_USERS.ID and FSA_LOGIN.LOGIN_NAME = #{userName}")
    @Results({
            @Result(property = "userId",column = "USER_ID"),
            @Result(property = "roleDTO",column = "USER_ID",one = @One(select = "com.caict.bsm.project.domain.security.mapper.RoleUsersMapper.findRoleByUser")),
            @Result(property = "rsbtOrgDTO",column = "USER_ID",one = @One(select = "com.caict.bsm.project.domain.security.mapper.RsbtOrgMapper.findOneDetailsByUsers"))

    })
    UsersDTO loginOnByUserName(@Param("userName")String userName);

    @Update("update FSA_LOGIN set PASSWORD = #{password} where LOGIN_NAME = #{loginName}")
    int updatePasswordByLogin(@Param("loginName") String loginName, @Param("password") String password);

    @Select("select * from FSA_LOGIN where USER_ID = #{userId}")
    Login findAllByUserId(@Param("userId") String userId);

    @Select("select * from FSA_LOGIN where login_name = #{loginName}")
    Login findAllByUserName(@Param("loginName") String loginName);

    /**
     * 根据用户删除
     * */
    @Delete("delete from FSA_LOGIN where USER_ID = #{userId}")
    int deleteByUser(@Param("userId")String userId);
}
