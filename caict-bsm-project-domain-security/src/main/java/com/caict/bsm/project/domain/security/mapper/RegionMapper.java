package com.caict.bsm.project.domain.security.mapper;

import com.caict.bsm.project.system.data.mapper.BasicMapper;
import com.caict.bsm.project.system.model.dto.security.RegionDTO;
import com.caict.bsm.project.system.model.entity.security.Region;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by dengsy on 2019-05-06.
 */
@Repository
public interface RegionMapper extends BasicMapper<Region> {

    @Select("select * from FSA_REGION")
    List<RegionDTO> findAllDto();

    @Select("select * from FSA_REGION where parent_id = #{parentId}")
    List<RegionDTO> findAllByParentId(@Param("parentId") int parentId);

    @Select("select * from FSA_REGION where CODE = #{code}")
    RegionDTO findOneByCode(@Param("code")String code);

    @Select("select * from FSA_REGION where ID = #{id}")
    RegionDTO findOneById(@Param("id")String id);

    @Select("select NAME from FSA_REGION where CODE = #{code}")
    String findNameByCode(@Param("code")String code);

    @Select("select code from FSA_REGION where name like concat(concat('%',#{countyName,jdbcType=VARCHAR}),'%')")
    String findByName(@Param("countyName")String countyName);

    /**
     * 根据id查询自身和下级的所有数据
     * */
    @Select("select * from FSA_REGION where id = #{parentId} or parent_id = #{parentId}")
    List<RegionDTO> findAllByIdOrParentId(@Param("parentId") int parentId);

    /**
     * 根据组织查询所有区
     * */
    @Select("select FSA_REGION.* " +
            "from FSA_REGION " +
            "where parent_id in " +
            "(" +
            "select id from FSA_REGION where parent_id = (select id from FSA_REGION,RSBT_ORG where FSA_REGION.CODE = RSBT_ORG.ORG_AREA_CODE and RSBT_ORG.GUID = #{orgGuid}) " +
            ")")
    List<RegionDTO> findAllByOrg(@Param("orgGuid")String orgGuid);

    @Select("select * from FSA_REGION " +
            "WHERE parent_id = (select id from FSA_REGION where code = #{regionId}) ")
    List<RegionDTO> findAreaByRegionId(@Param("regionId") String regionId);

    @Select("select * from FSA_REGION " +
            "WHERE parent_id = (select id from FSA_REGION where name = #{name}) ")
    List<RegionDTO> findAreaByProvince(@Param("name") String name);

    @Select("select name from FSA_REGION " +
            "WHERE parent_id = #{regionId} ")
    List<String> findCodeArea(@Param("regionId") String regionId);
}
