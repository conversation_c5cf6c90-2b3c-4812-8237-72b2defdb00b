package com.caict.bsm.project.domain.security.mapper;

import com.caict.bsm.project.system.data.mapper.BasicMapper;
import com.caict.bsm.project.system.model.dto.business.rule.FsaCheckRuleDTO;
import com.caict.bsm.project.system.model.dto.security.RsbtOrgDTO;
import com.caict.bsm.project.system.model.entity.security.RsbtOrg;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by dengsy on 2019-05-06.
 */
@Repository
public interface RsbtOrgMapper extends BasicMapper<RsbtOrg> {

    /**
    /**
     * 查询组织机构详情
     * */
    @Select("select RSBT_ORG.*," +
            "RSBT_ORG_APPENDIX.GMT_CREATE,RSBT_ORG_APPENDIX.GMT_MODIFIED,RSBT_ORG_APPENDIX.STATUS,RSBT_ORG_APPENDIX.IS_SYNC,RSBT_ORG_APPENDIX.TYPE,RSBT_ORG_APPENDIX.PARENT_ID,RSBT_ORG_APPENDIX.REGION_DETAILS " +
            "from RSBT_ORG,RSBT_ORG_APPENDIX where RSBT_ORG.GUID = RSBT_ORG_APPENDIX.GUID and RSBT_ORG.GUID = #{id}")
    @Results({
            @Result(property = "guid",column = "GUID"),
            @Result(property = "usersDTOs",column = "GUID",many = @Many(select = "com.caict.bsm.project.domain.security.mapper.RsbtOrgUsersMapper.findAllUsersByOrg")),
            @Result(property = "orgAreaCode",column = "ORG_AREA_CODE"),
            @Result(property = "regionDTO",column = "REGION_ID",one = @One(select = "com.caict.bsm.project.domain.security.mapper.RegionMapper.findOneByCode"))
    })
    RsbtOrgDTO findOneDetails(@Param("id")String id);

    /**
     * 根据用户查询详情
     * */
    @Select("select RSBT_ORG.*," +
            "RSBT_ORG_APPENDIX.GMT_CREATE,RSBT_ORG_APPENDIX.GMT_MODIFIED,RSBT_ORG_APPENDIX.STATUS,RSBT_ORG_APPENDIX.IS_SYNC,RSBT_ORG_APPENDIX.TYPE,RSBT_ORG_APPENDIX.PARENT_ID,RSBT_ORG_APPENDIX.REGION_DETAILS " +
            "from RSBT_ORG,RSBT_ORG_APPENDIX,RSBT_ORG_USERS " +
            "where RSBT_ORG.GUID = RSBT_ORG_APPENDIX.GUID and RSBT_ORG.GUID = RSBT_ORG_USERS.ORG_GUID and RSBT_ORG_USERS.USERS_ID = #{usersIds}")
    @Results({
            @Result(property = "guid",column = "GUID"),
            @Result(property = "usersDTOs",column = "GUID",many = @Many(select = "com.caict.bsm.project.domain.security.mapper.RsbtOrgUsersMapper.findAllUsersByOrg")),
            @Result(property = "orgAreaCode",column = "ORG_AREA_CODE"),
            @Result(property = "regionDTO",column = "ORG_AREA_CODE",one = @One(select = "com.caict.bsm.project.domain.security.mapper.RegionMapper.findOneByCode"))
    })
    RsbtOrgDTO findOneDetailsByUsers(@Param("usersIds")String usersIds);

    /**
     * 根据父级查询全部
     * */
    @Select("select RSBT_ORG.*, " +
            "RSBT_ORG_APPENDIX.GMT_CREATE,RSBT_ORG_APPENDIX.GMT_MODIFIED,RSBT_ORG_APPENDIX.STATUS,RSBT_ORG_APPENDIX.IS_SYNC,RSBT_ORG_APPENDIX.TYPE,RSBT_ORG_APPENDIX.PARENT_ID,RSBT_ORG_APPENDIX.REGION_DETAILS " +
            "from RSBT_ORG,RSBT_ORG_APPENDIX where RSBT_ORG.GUID = RSBT_ORG_APPENDIX.GUID and ORG_SUP_CODE = #{orgSupCode}")
    List<RsbtOrgDTO> findAllBySupCode(@Param("orgSupCode")String orgSupCode);

    /**
     * 条件查询全部DTO
     * */
    @Select("select RSBT_ORG.*, " +
            "RSBT_ORG_APPENDIX.GMT_CREATE,RSBT_ORG_APPENDIX.GMT_MODIFIED,RSBT_ORG_APPENDIX.STATUS,RSBT_ORG_APPENDIX.IS_SYNC,RSBT_ORG_APPENDIX.TYPE,RSBT_ORG_APPENDIX.PARENT_ID,RSBT_ORG_APPENDIX.REGION_DETAILS " +
            "from RSBT_ORG,RSBT_ORG_APPENDIX where RSBT_ORG.GUID = RSBT_ORG_APPENDIX.GUID and RSBT_ORG_APPENDIX.STATUS != 0")
    List<RsbtOrgDTO> findAllDtoByWhere(@Param("rsbtOrgDTO")RsbtOrgDTO rsbtOrgDTO);

    @Select("SELECT\n" +
            "RSBT_ORG.GUID AS ORGID,\n" +
            "FROM\n" +
            "RSBT_ORG\n" +
            "LEFT JOIN RSBT_ORG_APPENDIX ON RSBT_ORG_APPENDIX.GUID = RSBT_ORG.GUID\n" +
            "AND RSBT_ORG_APPENDIX.IS_DELETED = 0 \n" +
            "AND RSBT_ORG.ORG_NAME = #{companyKeyword}")
    List<String> getIdListByName(@Param("companyKeyword") String companyKeyword);


    /**
     * 根据状态查询全部
     * */
    @Select("select RSBT_ORG.*, " +
            "RSBT_ORG_APPENDIX.GMT_CREATE,RSBT_ORG_APPENDIX.GMT_MODIFIED,RSBT_ORG_APPENDIX.STATUS,RSBT_ORG_APPENDIX.IS_SYNC,RSBT_ORG_APPENDIX.TYPE,RSBT_ORG_APPENDIX.PARENT_ID,RSBT_ORG_APPENDIX.REGION_DETAILS " +
            "from RSBT_ORG,RSBT_ORG_APPENDIX " +
            "where RSBT_ORG.GUID = RSBT_ORG_APPENDIX.GUID " +
            "and RSBT_ORG_APPENDIX.STATUS = #{isDeleted}")
    List<RsbtOrgDTO> findAllByIsDeleted(@Param("isDeleted")String isDeleted);

    @Select("select RSBT_ORG.*, " +
            "RSBT_ORG_APPENDIX.GMT_CREATE,RSBT_ORG_APPENDIX.GMT_MODIFIED,RSBT_ORG_APPENDIX.STATUS,RSBT_ORG_APPENDIX.IS_SYNC,RSBT_ORG_APPENDIX.TYPE,RSBT_ORG_APPENDIX.PARENT_ID,RSBT_ORG_APPENDIX.REGION_DETAILS " +
            "from RSBT_ORG,RSBT_ORG_APPENDIX " +
            "where RSBT_ORG.GUID = RSBT_ORG_APPENDIX.GUID " +
            "and RSBT_ORG_APPENDIX.type != 'wuwei'" +
            "and RSBT_ORG_APPENDIX.STATUS ='1' ")
    List<RsbtOrgDTO> findAllToWeb();

    /**
     * 根据状态查询全部规则
     * */
    @Select("select RSBT_ORG.GUID, RSBT_ORG.ORG_NAME, RSBT_ORG_APPENDIX.TYPE as orgType from RSBT_ORG,RSBT_ORG_APPENDIX " +
            "where RSBT_ORG.GUID = RSBT_ORG_APPENDIX.GUID " +
            "and RSBT_ORG_APPENDIX.STATUS = '0' ")
    @Results({
            @Result(property = "orgGuid",column = "GUID"),
            @Result(property = "fsaCheckRuleDTOList",column = "GUID",many = @Many(select = "com.caict.bsm.project.domain.business.mapper.rule.FsaCheckRuleMapper.findAllByOrgGuid"))
    })
    List<FsaCheckRuleDTO> findAllCheckRuleByIsDeleted();

    /**
     * 根据状态查询制式
     * */
    @Select("select RSBT_ORG.GUID, RSBT_ORG.ORG_NAME, RSBT_ORG_APPENDIX.TYPE as orgType from RSBT_ORG,RSBT_ORG_APPENDIX " +
            "where RSBT_ORG.GUID = RSBT_ORG_APPENDIX.GUID " +
            "and RSBT_ORG_APPENDIX.STATUS = '0' ")
    @Results({
            @Result(property = "orgGuid",column = "GUID"),
            @Result(property = "fsaCheckRuleDTOList",column = "GUID",many = @Many(select = "com.caict.bsm.project.domain.business.mapper.rule.FsaCheckRuleMapper.findDistinctByOrgGuid"))
    })
    List<FsaCheckRuleDTO> findDistinctCheckRuleByIsDeleted();

    @Select("select * from RSBT_ORG where ORG_NAME = #{orgName} ")
    RsbtOrgDTO findOneByOrgName(@Param("orgName") String orgName);


    @Select("SELECT RSBT_ORG.*,RSBT_ORG_APPENDIX.TYPE AS userType," +
            "RSBT_ORG_APPENDIX.GMT_CREATE,RSBT_ORG_APPENDIX.GMT_MODIFIED,RSBT_ORG_APPENDIX.STATUS,RSBT_ORG_APPENDIX.IS_SYNC,RSBT_ORG_APPENDIX.TYPE,RSBT_ORG_APPENDIX.PARENT_ID,RSBT_ORG_APPENDIX.REGION_DETAILS " +
            "from RSBT_ORG,RSBT_ORG_APPENDIX,RSBT_ORG_USERS " +
            "where RSBT_ORG.GUID = RSBT_ORG_APPENDIX.GUID and RSBT_ORG.GUID = RSBT_ORG_USERS.ORG_GUID " +
            "and RSBT_ORG_APPENDIX.STATUS = '1' AND RSBT_ORG_USERS.USERS_ID = #{userId}")
    RsbtOrgDTO findRsbtOrgByUserId(@Param("userId") String userId);

    @Select("select count(*) from RSBT_ORG ")
    int selectOrgCount();

    @Select("select * from RSBT_ORG where GUID = #{guid}")
    RsbtOrgDTO findOneByGuid(@Param("guid")String guid);

    @Select("select RSBT_ORG.guid from RSBT_ORG,RSBT_ORG_APPENDIX where RSBT_ORG.GUID = RSBT_ORG_APPENDIX.GUID " +
            "AND RSBT_ORG.ORG_AREA_CODE = #{areaCode} " +
            "AND RSBT_ORG_APPENDIX.TYPE = #{orgType} ")
    String findByAreaCode(@Param("areaCode")String areaCode,@Param("orgType")String orgType);
}
