package com.caict.bsm.project.domain.security.mapper;

import com.caict.bsm.project.system.data.mapper.BasicMapper;
import com.caict.bsm.project.system.model.dto.security.UsersDTO;
import com.caict.bsm.project.system.model.entity.security.Users;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by dengsy on 2019-10-24.
 */
@Repository
public interface UsersMapper extends BasicMapper<Users> {

    @Select("select FSA_USERS.* from FSA_USERS,FSA_ROLE_USERS where FSA_USERS.ID = FSA_ROLE_USERS.USERS_ID and FSA_ROLE_USERS.ROLE_ID = #{roleId}")
    List<UsersDTO> findAllUsersByRoleId(@Param("roleId") String roleId);

    @Select("select * from FSA_USERS")
    @Results({
        @Result(property = "userId",column = "ID"),
        @Result(property = "roleDTO",column = "ID",one = @One(select = "com.caict.fsa.project.domain.security.mapper.RoleMapper.findRoleByUsersId"))
    })
    List<UsersDTO> findAllUsersDTO();

    @Select("select * from FSA_USERS where ID = #{usersId}")
    @Results({
            @Result(property = "userId",column = "ID"),
            @Result(property = "roleDTO",column = "ID",one = @One(select = "com.caict.bsm.project.domain.security.mapper.RoleMapper.findRoleByUsersId")),
            @Result(property = "rsbtOrgDTO",column = "ID",one = @One(select = "com.caict.bsm.project.domain.security.mapper.RsbtOrgMapper.findRsbtOrgByUserId")),
    })
    UsersDTO findOneUsersDTO(@Param("usersId") String usersId);

    /**
     * 查询总数
     * */
    @Select("select count(*) from FSA_USERS")
    int selectAllCount();

    /**
     * 分页查询全部
     * */
    @Select("select FSA_USERS.*," +
            "(select FSA_ROLE.NAME from FSA_ROLE,FSA_ROLE_USERS where FSA_USERS.ID = FSA_ROLE_USERS.USERS_ID and FSA_ROLE_USERS.ROLE_ID = FSA_ROLE.ID and rownum=1) as roleName," +
            "(select RSBT_ORG.ORG_NAME from RSBT_ORG,RSBT_ORG_USERS where FSA_USERS.ID = RSBT_ORG_USERS.USERS_ID and RSBT_ORG_USERS.ORG_GUID = RSBT_ORG.GUID and rownum=1) as orgName " +
            "from FSA_USERS where FSA_USERS.ID != '1' order by FSA_USERS.ID " )
    List<UsersDTO> findAllWherePage(@Param("usersDTO")UsersDTO usersDTO);
}
