package com.caict.bsm.project.domain.security.service;

import com.alibaba.fastjson.JSONObject;
import com.caict.bsm.project.domain.security.mapper.LoginMapper;
import com.caict.bsm.project.domain.security.mapper.UsersMapper;
import com.caict.bsm.project.system.data.service.BasicService;
import com.caict.bsm.project.system.model.dto.security.RegionDTO;
import com.caict.bsm.project.system.model.dto.security.RsbtOrgDTO;
import com.caict.bsm.project.system.model.dto.security.UsersDTO;
import com.caict.bsm.project.system.model.entity.security.Login;
import com.caict.bsm.project.system.model.entity.security.RoleUsers;
import com.caict.bsm.project.system.model.entity.security.RsbtOrgUsers;
import com.caict.bsm.project.system.model.entity.security.Users;
import com.caict.bsm.project.system.model.entity.security.login_aspx.accept.Envelope;
import com.caict.bsm.project.system.model.entity.security.login_aspx.accept.GetUserResult;
import com.caict.bsm.project.system.model.entity.security.login_aspx.accept.UserInfo;
import com.caict.bsm.project.system.model.entity.security.login_aspx.send.*;
import com.caict.bsm.project.system.utils.service.RedisService;
import com.caict.bsm.project.system.utils.util.MD5Utils;
import com.caict.bsm.project.system.utils.util.SendXmlUtil;
import com.caict.bsm.project.system.utils.util.VerificationCode;
import com.caict.bsm.project.system.utils.util.XmlUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * Created by dengsy on 2019-10-24.
 */
@Service
public class AuthService extends BasicService {

    @Autowired
    private LoginMapper loginMapper;
    @Autowired
    private LoginService loginService;
    @Autowired
    private UsersService usersService;
    @Autowired
    private RsbtOrgService rsbtOrgService;
    @Autowired
    private RoleUsersService roleUsersService;
    @Autowired
    private RsbtOrgUsersService orgUsersService;
    @Autowired
    private RegionService regionService;

    @Autowired(required = false)
    private RedisService redisService;

    @Value("${caict.myPasswordKey}")
    private String myPasswordKey;

    /**
     * 生成token
     * 暂时使用uuid作为源token，后期替换auth
     * */
    public String getToken(){
        String token = UUID.randomUUID().toString().replace("-", "");
        return token;
    }

    /**
     * 登陆实现
     * */
    public UsersDTO login(String loginName, String password){
        UsersDTO usersDTO = loginMapper.login(loginName,password);
        return usersDTO;
    }


//    /**
//     * 主键登录
//     * */
//    public UsersDTO login(String userId){
//        UsersDTO usersDTO = loginMapper.loginOnByUserId(userId);
//        return usersDTO;
//    }

    /**
     * 主键登录
     * */
    public UsersDTO loginByUserName(String userName){
        UsersDTO usersDTO = loginMapper.loginOnByUserName(userName);
        return usersDTO;
    }

    /**
     * 登录
     * 返回token和userId
     * */
    public Map<String,String> loginOn(String loginName,String password){
        UsersDTO usersDTO = login(loginName,password);
        if (usersDTO != null) return loginOn(usersDTO);
        return null;
    }

    /**
     * 主键登录
     * 一体化平台登录
     * 返回token和userId
     * */
    public Map<String,String> loginOn(String userName){
        try {
            //在库中查询此人员信息
//            UsersDTO usersDTO = login(userId);
            UsersDTO usersDTO = loginByUserName(userName);
            if (usersDTO!=null){
                return loginOn(usersDTO);
            }else {
                //如查询无此人员信息，则同步一次一体化平台信息

                String sendXml = creatXml();
                if (sendXml!=null) {
                    String returnXml = SendXmlUtil.sendRequest(sendXml);
                    if (returnXml != null) {
                        Envelope envelope = (Envelope) XmlUtil.xmlToBean(returnXml, Envelope.class);
                        if (envelope != null) {
                            //将获得的人员信息存入人员信息表中
                            List<UserInfo> userInfo = envelope.getResponseBody().getGetUsersResponse().getUserInfo();
                            List<Login> loginList = new ArrayList<>();
                            List<Users> usersList = new ArrayList<>();
                            List<RoleUsers> roleUsersList = new ArrayList<>();
                            List<RsbtOrgUsers> orgUsersList = new ArrayList<>();
                            for (UserInfo userDetail: userInfo){
                                //查询数据库中是否有该人员信息，如果没有就放入list中插入数据库
//                                Login allByUserId = loginMapper.findAllByUserId(userDetail.getUserID());
                                Login allByUserId = loginMapper.findAllByUserName(userDetail.getUserName());
                                if(allByUserId==null){
                                    Login loginDB = new Login();
                                    Users user = new Users();
                                    RoleUsers roleUsers = new RoleUsers();
                                    String id = VerificationCode.myUUID();
                                    roleUsers.setId(VerificationCode.myUUID());
                                    roleUsers.setUsersId(id);

                                    loginDB.setId(VerificationCode.myUUID());
                                    loginDB.setUserId(id);
                                    loginDB.setLoginName(userDetail.getUserCode());
                                    loginDB.setPassword("1111");
//                                    loginDB.setPassword(MD5Utils.getInstance().encode(userDetail.getUserPassword(), myPasswordKey));

                                    user.setId(id);
                                    user.setSex(userDetail.getSex());
                                    user.setName(userDetail.getUserName());
                                    user.setEmail(userDetail.getLocalEmail());
                                    user.setMobile(userDetail.getMobile());
                                    user.setType("wuwei");
                                    user.setWechart("yitihua");
                                    loginDB.setType("wuwei");
                                    String region = userDetail.getOrgInfo().getOrgCode();
                                    String regionId = "";
                                    if (region!=null && !"".equals(region)){
                                        regionId = region.substring(0, 6);
                                        if("522200".equals(regionId)){
                                            regionId = "520600";
                                        }else if ("522400".equals(regionId)){
                                            regionId = "520500";
                                        }
                                    }else {
                                        regionId = "520000";
                                        roleUsers.setRoleId("06333af0-f600-41b4-97f8-68e96bb753aa");
                                    }
                                    if ("520000".equals(regionId)){
                                        roleUsers.setRoleId("06333af0-f600-41b4-97f8-68e96bb753aa");
                                    }else {
                                        roleUsers.setRoleId("9c83f9be-e01a-4c9c-b51a-1032a192e6f5");
                                    }

                                    String orgGuid = rsbtOrgService.findByAreaCode(regionId,"wuwei");
                                    RsbtOrgUsers rsbtOrgUsers = new RsbtOrgUsers();
                                    if (orgGuid!=null && !"".equals(orgGuid)){
                                        rsbtOrgUsers.setId(VerificationCode.myUUID());
                                        rsbtOrgUsers.setUsersId(id);
                                        rsbtOrgUsers.setOrgGuid(orgGuid);
                                    }

                                    RegionDTO regionDTO = regionService.findOneByCode(regionId);
                                    if (regionDTO!=null){
                                        user.setRegionId(regionDTO.getId());
                                    }


                                    loginList.add(loginDB);
                                    usersList.add(user);

                                    roleUsersList.add(roleUsers);
                                    orgUsersList.add(rsbtOrgUsers);
                                }
                            }
                            if (usersList.size()>0){
                                usersService.insertBatch(usersList);
                                loginService.insertBatch(loginList);
                                roleUsersService.insertBatch(roleUsersList);
                                orgUsersService.insertBatch(orgUsersList);
                            }
                        }
                    }
                }
                UsersDTO usersDTO2 = loginMapper.loginOnByUserName(userName);
                return loginOn(usersDTO2);
            }
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }

    //登录处理
    protected Map<String,String> loginOn(UsersDTO usersDTO){
        Map<String,String> map = new HashMap<String,String>();
        String token = getToken();
        String key = "Caict:"+token;
        map.put("token",token);
        map.put("roleType","");
        map.put("userType","");
        if (usersDTO.getRoleDTO() != null && usersDTO.getRoleDTO().getType() != null) map.put("roleType",usersDTO.getRoleDTO().getType());
        if (usersDTO.getType() != null) map.put("userType",usersDTO.getType());
        if (usersDTO.getRsbtOrgDTO() != null && usersDTO.getRsbtOrgDTO().getRegionDTO() != null) {
            map.put("regionId",usersDTO.getRsbtOrgDTO().getRegionDTO().getId());
            usersDTO.setRegionId(usersDTO.getRsbtOrgDTO().getRegionDTO().getId());
        }
        setLogin(key,JSONObject.toJSONString(usersDTO));
        return map;
    }

    private UsersDTO createUserDTO(Login login) {
        UsersDTO usersDTO = new UsersDTO();
        usersDTO.setUserId(login.getUserId());
        usersDTO.setLoginName(login.getLoginName());
        usersDTO.setType(login.getType());
        return usersDTO;
    }

    /**
     * 使用token存储用户信息到redis，设置过期时间
     * */
    public void setLogin(String key,String jsonString){
        redisService.setEx(key,60*30,jsonString);
    }

    /**
     * 登出
     * */
    public void loginOff(String token){
        String key = "Caict:"+token;
        if(redisService.get(key) != null){
            redisService.delete(key);
        }
    }

    /**
     * 根据token获取用户登陆信息
     * */
    public String getLoginUsersDTO(String token){
        String key = "Caict:"+token;
        return redisService.get(key);
    }

    /**
     * 根据登录账号修改密码
     * */
    public int updatePasswordByLogin(String loginName,String password){
        return loginMapper.updatePasswordByLogin(loginName,password);
    }

    /**
     * 根据userId查询
     * */
    public Login findOneByUserId(String userId){
        return loginMapper.findAllByUserId(userId);
    }

    /**
     * 组建xml报文
     */
    public static String creatXml(){
        RespEnvelope respEnvelope = new RespEnvelope();

        RequestHeader header = new RequestHeader();

        RequestBody body = new RequestBody();

        MonitorHeader monitorHeader = new MonitorHeader();
        monitorHeader.setTransId("");
        monitorHeader.setBizKey("");
        monitorHeader.setPsCode("PS-520000-01-0002-0001");
        monitorHeader.setBsCode("BS-520000-01-0002-0001");
        monitorHeader.setAppCode("520000-01-0151");
        monitorHeader.setAppName("");
        monitorHeader.setPlatFormCode("520000-01-0001");
        monitorHeader.setPlatFormName("");
        monitorHeader.setResCode1("");
        monitorHeader.setResCode2("");
        RequstBodySon requstBodySon = new RequstBodySon();
        header.setMonitorHeader(monitorHeader);
        body.setRequstBodySon(requstBodySon);
        respEnvelope.setRequestBody(body);
        respEnvelope.setRequestHeader(header);

        return XmlUtil.convertToXml(respEnvelope);
    }

    public static void main(String[] args) {
        String sendXml = creatXml();
        String returnXml = SendXmlUtil.sendRequest(sendXml);
        try {
            Envelope envelope = (Envelope) XmlUtil.xmlToBean(returnXml, Envelope.class);
            System.out.println(envelope);
        }catch (Exception e){
            e.printStackTrace();
        }
        System.out.println(returnXml);
    }

}
