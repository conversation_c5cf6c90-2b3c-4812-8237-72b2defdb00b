package com.caict.bsm.project.domain.security.service;

import com.caict.bsm.project.domain.security.mapper.RegionMapper;
import com.caict.bsm.project.system.data.service.BasicService;
import com.caict.bsm.project.system.model.dto.security.RegionDTO;
import com.caict.bsm.project.system.model.entity.security.Region;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by dengsy on 2019-05-06.
 */
@Service
public class RegionService extends BasicService<Region> {

    @Autowired
    private RegionMapper regionMapper;

    /**
     * 查询所有的RegionDTO
     * */
    public List<RegionDTO> findAllDto(){
        return regionMapper.findAllDto();
    }

    /**
     * 根据code查询
     * */
    public RegionDTO findOneByCode(String code){
        return regionMapper.findOneByCode(code);
    }

    /**
     * 查询所有的Region（根据parentId)
     * */
    public List<RegionDTO> findAllByParentId(int parentId){
        return regionMapper.findAllByParentId(parentId);
    }

    /**
     * 根据name查询
     * */
    public String findByName(String countyName){
        return regionMapper.findByName(countyName);
    }

    /**
     * 根据id查询自身和下级的所有数据
     * */
    public List<RegionDTO> findAllByIdOrParentId(int parentId){
        return regionMapper.findAllByIdOrParentId(parentId);
    }

    /**
     * 根据组织查询所有区
     * */
    public List<RegionDTO> findAllByOrg(String orgGuid){
        return regionMapper.findAllByOrg(orgGuid);
    }

    /**
     * 查询下属地区
     */
    public List<RegionDTO> findAreaByRegionId(String regionId){
        return regionMapper.findAreaByRegionId(regionId);
    }

    /**
     * 按照市名称查询区
     */
    public List<RegionDTO> findAreaByProvince(String name){
        return regionMapper.findAreaByProvince(name);
    }

    public List<String> findCodeArea(String regionId){
        return regionMapper.findCodeArea(regionId);
    }
}
