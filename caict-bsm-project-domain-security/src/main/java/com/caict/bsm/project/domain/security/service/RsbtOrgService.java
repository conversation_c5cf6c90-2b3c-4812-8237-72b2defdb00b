package com.caict.bsm.project.domain.security.service;

import com.caict.bsm.project.domain.security.mapper.RsbtOrgMapper;
import com.caict.bsm.project.system.data.service.BasicService;
import com.caict.bsm.project.system.model.dto.business.rule.FsaCheckRuleDTO;
import com.caict.bsm.project.system.model.dto.security.RsbtOrgDTO;
import com.caict.bsm.project.system.model.entity.security.RsbtOrg;
import com.caict.bsm.project.system.model.entity.security.RsbtOrgUsers;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Created by dengsy on 2019-10-24.
 */
@Service
public class RsbtOrgService extends BasicService<RsbtOrg> {

    @Autowired
    private RsbtOrgMapper rsbtOrgMapper;
    @Autowired
    private RsbtOrgAppendixService rsbtOrgAppendixService;

    /**
     * 查询组织机构详情
     * */
    public RsbtOrgDTO findOneDetails(String id){
        return rsbtOrgMapper.findOneDetails(id);
    }

    /**
     * 根据父级查询全部
     * */
    public List<RsbtOrgDTO> findAllBySupCode(String orgSupCode){
        return rsbtOrgMapper.findAllBySupCode(orgSupCode);
    }

    /**
     * 条件查询全部DTO
     * */
    public List<RsbtOrgDTO> findAllDtoByWhere(RsbtOrgDTO rsbtOrgDTO){
        return rsbtOrgMapper.findAllDtoByWhere(rsbtOrgDTO);
    }

    public List<String> getIdListByName(String companyKeyword) {
        return rsbtOrgMapper.getIdListByName(companyKeyword);
    }

    /**
     * 根据状态查询全部
     * */
    public List<RsbtOrgDTO> findAllByIsDeleted(String isDeleted){
        return rsbtOrgMapper.findAllByIsDeleted(isDeleted);
    }

    /**
     * 根据状态查询全部
     * */
    public List<RsbtOrgDTO> findAllToWeb(){
        return rsbtOrgMapper.findAllToWeb();
    }

    /**
     * 根据状态查询全部规则
     * */
    public List<FsaCheckRuleDTO> findAllCheckRuleByIsDeleted(){
        return rsbtOrgMapper.findAllCheckRuleByIsDeleted();
    }

    /**
     * 根据状态查询制式
     * */
    public List<FsaCheckRuleDTO> findDistinctCheckRuleByIsDeleted(){
        return rsbtOrgMapper.findDistinctCheckRuleByIsDeleted();
    }

    /**
     * 根据机构名字查询
     */
    public RsbtOrgDTO findOneByOrgName(String orgName){
        return rsbtOrgMapper.findOneByOrgName(orgName);
    }

    /**
     * 根据UserId 查询RsbtOrg
     */
    public RsbtOrgDTO findRsbtOrgByUserId(String userId){
        return rsbtOrgMapper.findRsbtOrgByUserId(userId);
    }

    /**
     *
     */
    public int selectOrgCount(){
        return rsbtOrgMapper.selectOrgCount();
    }

    /**
     * 根据地区码查询机构
     */
    public String findByAreaCode(String areaCode,String orgType){
        return rsbtOrgMapper.findByAreaCode(areaCode,orgType);
    }
}
