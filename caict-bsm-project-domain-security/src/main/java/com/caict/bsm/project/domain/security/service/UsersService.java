package com.caict.bsm.project.domain.security.service;

import com.caict.bsm.project.domain.security.mapper.UsersMapper;
import com.caict.bsm.project.system.data.service.BasicService;
import com.caict.bsm.project.system.model.dto.security.UsersDTO;
import com.caict.bsm.project.system.model.entity.security.Users;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by dengsy on 2019-10-24.
 */
@Service
public class UsersService extends BasicService<Users> {

    @Autowired
    private UsersMapper usersMapper;

    /**
     * 查询所有用户并附带出对应的角色信息
     * */
    public List<UsersDTO> findAllUsersDTO(){
        return usersMapper.findAllUsersDTO();
    }

    /**
     * 查询单个用户（根据id）并附带出对应的角色信息
     * */
    public UsersDTO findOneUsersDTO(String usersId){
        return usersMapper.findOneUsersDTO(usersId);
    }

    /**
     * 分页条件查询
     * */
    public List<UsersDTO> findAllWherePage(UsersDTO usersDTO){
        return usersMapper.findAllWherePage(usersDTO);
    }

    /**
     * 根据Id查询
     * */
    public Users getUsersById(String usersId){
        Users users = new Users();
        users.setId(usersId);
        return usersMapper.findOne(users);
    }
}
