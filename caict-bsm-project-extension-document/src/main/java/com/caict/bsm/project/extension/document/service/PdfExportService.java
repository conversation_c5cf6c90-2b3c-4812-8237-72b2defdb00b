package com.caict.bsm.project.extension.document.service;

import com.alibaba.fastjson.JSONObject;
import com.caict.bsm.project.extension.document.util.ZipUtil;
import com.caict.bsm.project.system.utils.util.QRCodeUtil;
import com.itextpdf.text.*;
import com.itextpdf.text.pdf.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/5/15
 */
@Service
public class PdfExportService {

    private static final Logger LOG = LoggerFactory.getLogger(PdfExportService.class);

    /**
     * 非模板写入，在原有pdf下继续写入
     * classpath;pdf生成路径
     * tempPath:已写入数据的pdf模板路径
     * fileName:生成文件名称
     * paragraph:生成的表格及内容
     * */
    public String savePdf(String classpath, String tempPath,String fileName,Paragraph paragraph){
        try {
            FileOutputStream outputStream = new FileOutputStream(classpath + File.separator + fileName+"pdf.pdf");
            // 读取pdf模板
            PdfReader reader = new PdfReader(tempPath);
            Rectangle pageSize = reader.getPageSize(1);
            Document document = new Document(pageSize);
            PdfWriter writer = PdfWriter.getInstance(document, outputStream);
            document.open();
            PdfContentByte cbUnder = writer.getDirectContentUnder();
            PdfImportedPage pageTemplate = writer.getImportedPage(reader, 1);
            cbUnder.addTemplate(pageTemplate, 0, 0);
            //新创建一页来存放后面生成的表格
            document.newPage();
            document.add(paragraph);
            document.close();
            reader.close();
            return fileName+"pdf.pdf";
        }catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * classpath：文件生成路径
     * tempPath:模板路径
     * fileName:生成文件名称
     * map:表单数据
     * codeMap：二维码数据
     * */
    public String savePdf(String classpath, String tempPath,String fileName, String type,Map<String, String> map,Map<String,String> codeMap) {
        Resource resource = new ClassPathResource(tempPath);
        try {
            FileOutputStream out = new FileOutputStream(classpath + File.separator + fileName+".pdf");
            PdfReader reader = new PdfReader(resource.getInputStream());
            PdfStamper stamper = new PdfStamper(reader, out);

            BaseFont chinessFont = BaseFont.createFont("STSongStd-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
            AcroFields form = stamper.getAcroFields();
            form.addSubstitutionFont(chinessFont);


            Iterator<String> it = form.getFields().keySet().iterator();
            while (it.hasNext()) {
                String name = it.next();
                String v = map.get(name);
                if (v!=null){
                    form.setField(name, v);
                }

            }

            //生成二维码
            if (codeMap != null){
                String QRCString = JSONObject.toJSONString(codeMap);
                String QRCodeName = String.valueOf(new Date().getTime());
                QRCodeUtil.encode(QRCodeName,QRCString, classpath, true);
                //设置二维码
                PushbuttonField ad = form.getNewPushbuttonFromField("QRCodeImage");
                ad.setLayout(PushbuttonField.LAYOUT_ICON_ONLY);
                ad.setProportionalIcon(true);
                ad.setImage(Image.getInstance(classpath + "/upload/qrcode/" + QRCodeName+".jpg"));
                form.replacePushbuttonField("QRCodeImage", ad.getField());
            }

            //添加水印
//            if ("template".equals(type)) addWaterWar(reader,stamper);

            //true代表生成的PDF文件不可编辑
            stamper.setFormFlattening(true);
            stamper.close();
            return fileName+".pdf";
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 添加水印
     * filePath:文件路径
     * */
    protected void addWaterWar(PdfReader reader,PdfStamper stamper) throws DocumentException, IOException {
        PdfContentByte content;
        // 创建字体,第一个参数是字体路径,itext有一些默认的字体比如说：
        BaseFont base = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.EMBEDDED);
        PdfGState gs = new PdfGState();
        // 获取PDF页数
        int total =  reader.getNumberOfPages();
        // 遍历每一页
        for (int i = 0; i < total; i++) {
            float width = reader.getPageSize(i + 1).getWidth(); // 页宽度
            //float height = reader.getPageSize(i + 1).getHeight(); // 页高度
            content = stamper.getOverContent(i + 1);// 内容
            content.beginText();//开始写入文本
            gs.setFillOpacity(0.6f);//水印透明度
            content.setGState(gs);
            content.setColorFill(BaseColor.LIGHT_GRAY);
            content.setTextMatrix(70, 200);//设置字体的输出位置

            //平行居中的3条水印
            content.setFontAndSize(base, 50); //字体大小
            //showTextAligned 方法的参数分别是（文字对齐方式，位置内容，输出水印X轴位置，Y轴位置，旋转角度）
            //content.showTextAligned(Element.ALIGN_CENTER, word, width / 2, 650, 30);
            content.showTextAligned(Element.ALIGN_CENTER, "贵州省经济和信息化委员会", width / 2, 470, 45);
            content.showTextAligned(Element.ALIGN_CENTER, "基站数据电子交互系统", width / 2, 400, 45);
            content.showTextAligned(Element.ALIGN_CENTER, "仅作申请记录凭证", width / 2, 330, 45);
            //content.showTextAligned(Element.ALIGN_CENTER, word, width / 2, 150, 30);
            content.endText();//结束写入文本
            //要打图片水印的话
            //Image image = Image.getInstance("c:/1.jpg");
            //content.addImage(image);
        }

        stamper.close();
        reader.close();
    }

    public String savePdfZip(String classpath,List<String> pdfFileList) {
        ZipUtil zipUtil = new ZipUtil();
        return zipUtil.zipFiles(classpath,pdfFileList);
    }
}
