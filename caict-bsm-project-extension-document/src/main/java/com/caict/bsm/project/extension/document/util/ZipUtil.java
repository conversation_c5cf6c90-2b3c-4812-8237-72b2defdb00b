package com.caict.bsm.project.extension.document.util;

import java.io.*;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Date;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR>
 * @date 2019/12/11
 */
public class ZipUtil {

    /**
     * 将多个Excel打包成zip文件
     *
     * @param srcFile 该路径下的所有需要打成Zip的文件
     * @param zipFile 压缩的Zip文件
     */
    public String zipFiles(String zipFile,List<String> srcFile) {
        try {

            String fileName = new Date().getTime() + ".zip";//使用时间戳作为名称
            Path targetFilePath = Paths.get(zipFile + File.separator + fileName);
            File targetFile = targetFilePath.toFile();

            BufferedOutputStream bos = new BufferedOutputStream(new FileOutputStream(targetFile));
            ZipOutputStream zos = new ZipOutputStream(bos);
            ZipEntry ze = null;
            for (String file : srcFile){
                Path licenseFilePath = Paths.get(file);
                File licenseFile = licenseFilePath.toFile();

                BufferedInputStream bis = new BufferedInputStream(new FileInputStream(licenseFile));
                ze = new ZipEntry(licenseFile.getName());
                zos.putNextEntry(ze);
                int s = -1;
                while ((s = bis.read()) != -1) {
                    zos.write(s);
                }
                bis.close();
            }
            zos.flush();
            zos.close();
            return fileName;
        }catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 删除目录下所有的文件;
     *
     * @param file 服务器目录地址 例如:
     */
    public boolean deleteExcelPath(File file) {
        if (file.isDirectory()) {
            String[] children = file.list();
            int length = children.length;
            //递归删除目录中的子目录下
            int number= 1;
            for (int i = 0; i < length; i++) {
                boolean success = deleteExcelPath(new File(file, children[i]));
                if (!success) {
                    return false;
                }
                number++;
            }
        }
        // 目录此时为空，可以删除
        return file.delete();
    }

}
