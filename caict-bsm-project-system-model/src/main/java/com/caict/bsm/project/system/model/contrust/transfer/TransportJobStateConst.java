package com.caict.bsm.project.system.model.contrust.transfer;

/**
 * Created by dengsy on 2020-04-22.
 *
 * state
 * 0：未提交
 * 1：异常
 * 2：正常（提交）
 *
 * compare
 * 0：待提交
 * 1：提交失败
 * 2：提交成功（待对比）
 * 3：对比中
 * 4：对比失败
 * 5：对比成功（无委待确认）
 * 6：无委确认中
 * 7：无委确认不通过
 * 8：无委确认通过（待审核）
 * 81:申请表待提交
 * 82:申请报提交失败
 * 83:申请表提交成功（待审核）
 * 9：审核中
 * 10：完结(未干扰协调)
 * 11：干扰协调中
 * 12：干扰协调成功
 * 13：干扰协调失败
 * 14：已撤销
 */
public class TransportJobStateConst {

    public static final long STATE_UPLOAD = 0;

    public static final long STATE_FAILURE = 1;

    public static final long STATE_SUCCUSS = 2;

    public static final String COMPARE_COMMIT_UPLOAD = "0";

    public static final String COMPARE_COMMIT_FAIL = "1";

    public static final String COMPARE_COMMIT_SUCCESS = "2";

    public static final String COMPARE_PROCESSING = "3";

    public static final String COMPARE_FAIL = "4";

    public static final String COMPARE_SUCCESS = "5";

    public static final String CONFIRM_PROCESSING = "6";

    public static final String CONFIRM_NOT_PASS = "7";

    public static final String CONFIRM_PASS = "8";

    public static final String BRANCH_SUBMIT = "81";

    public static final String BRANCH_FAILURE = "82";

    public static final String BRANCH_SUCCUSS = "83";

    public static final String CHECK_PROCESSING = "9";

    public static final String COMPLETE = "10";

    public static final String INTERFERE_ING = "11";

    public static final String INTERFERE_SUCCESS = "12";

    public static final String INTERFERE_FAIL = "13";

    public static final String IS_REVOLE = "20";
}
