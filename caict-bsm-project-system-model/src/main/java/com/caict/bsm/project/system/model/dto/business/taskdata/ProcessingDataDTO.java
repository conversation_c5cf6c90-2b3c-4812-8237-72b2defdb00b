package com.caict.bsm.project.system.model.dto.business.taskdata;


import com.caict.bsm.project.system.model.entity.business.dataconver.AsyncRawBts;
import com.caict.bsm.project.system.model.entity.business.station.*;
import com.caict.bsm.project.system.model.entity.business.stationbak.RsbtStationBak;
import com.caict.bsm.project.system.model.entity.business.transfer.LogTransportJob;
import com.caict.bsm.project.system.model.entity.business.transfer.TransportRawBts;
import com.caict.bsm.project.system.model.es.RsbtStationMainES;

import java.util.ArrayList;
import java.util.List;

/**
 * 入库参数
 * */
public class ProcessingDataDTO {

    private String userId;
    private String jobId;
    private String appGuid;
    private String dateType;

    private List<RsbtNet> rsbtNetList;

    //批量处理基站
    private List<RsbtStation> rsbtStationList;
    //批量处理基站状态
    private List<RsbtStationAppendix> rsbtStationAppendixList;

    private List<RsbtStationT> rsbtStationTList;

    private List<RsbtStationBak> rsbtStationBakList;

    //批量处理天线
    private List<RsbtAntfeed> rsbtAntfeedList;
    //批量处理天线状态
    private List<RsbtAntfeedAppendix> rsbtAntfeedAppendixList;

    private List<RsbtAntfeedT> rsbtAntfeedTList;

    //批量处理设备
    private List<RsbtEqu> rsbtEquList;
    //批量处理设备状态
    private List<RsbtEquAppendix> rsbtEquAppendixList;

    private List<RsbtEquT> rsbtEquTList;

    //批量处理参数
    private List<RsbtFreq> rsbtFreqList;
    //批量处理参数状态
    private List<RsbtFreqAppendix> rsbtFreqAppendixList;

    private List<RsbtFreqT> rsbtFreqTList;

    private List<RsbtEaf> rsbtEafList;

    //csv文件数据
    List<TransportRawBts> transportRawBtsList;
    //日志数据
    List<LogTransportJob> logTransportJobList;

    //批量添加总表
    private List<AsyncRawBts> asyncRawBtsList;

    private List<RsbtTraffic> rsbtTrafficList;

    private List<RsbtStationMainES> rsbtStationMainESList;

    public ProcessingDataDTO(){}

    public ProcessingDataDTO(List<TransportRawBts> transportRawBtsList,List<LogTransportJob> logTransportJobList){
        this.transportRawBtsList = transportRawBtsList;
        this.logTransportJobList = logTransportJobList;
    }

    public ProcessingDataDTO(String userId, String jobId, String appGuid, String dateType, List<RsbtNet> rsbtNetList,
                             List<RsbtStation> rsbtStationList, List<RsbtStationAppendix> rsbtStationAppendixList,List<RsbtStationT> rsbtStationTList,List<RsbtStationBak> rsbtStationBakList,
                             List<RsbtAntfeed> rsbtAntfeedList, List<RsbtAntfeedAppendix> rsbtAntfeedAppendixList,List<RsbtAntfeedT> rsbtAntfeedTList,
                             List<RsbtEqu> rsbtEquList, List<RsbtEquAppendix> rsbtEquAppendixList,List<RsbtEquT> rsbtEquTList,
                             List<RsbtFreq> rsbtFreqList, List<RsbtFreqAppendix> rsbtFreqAppendixList,List<RsbtFreqT> rsbtFreqTList,List<RsbtEaf> rsbtEafList,
                             List<AsyncRawBts> asyncRawBtsList,List<RsbtTraffic> rsbtTrafficList,List<RsbtStationMainES> rsbtStationMainESList){
        this.userId = userId;
        this.jobId = jobId;
        this.appGuid = appGuid;
        this.dateType = dateType;
        this.rsbtNetList = rsbtNetList;
        this.rsbtStationList = (rsbtStationList == null?new ArrayList<RsbtStation>():rsbtStationList);
        this.rsbtStationAppendixList = (rsbtStationAppendixList == null?new ArrayList<RsbtStationAppendix>():rsbtStationAppendixList);
        this.rsbtStationTList = (rsbtStationTList == null ? new ArrayList<>():rsbtStationTList);
        this.rsbtStationBakList = (rsbtStationBakList == null ? new ArrayList<>():rsbtStationBakList);

        this.rsbtAntfeedList = (rsbtAntfeedList == null?new ArrayList<RsbtAntfeed>():rsbtAntfeedList);
        this.rsbtAntfeedAppendixList = (rsbtAntfeedAppendixList == null?new ArrayList<RsbtAntfeedAppendix>():rsbtAntfeedAppendixList);
        this.rsbtAntfeedTList = (rsbtAntfeedTList == null? new ArrayList<>(): rsbtAntfeedTList);

        this.rsbtEquList = (rsbtEquList == null?new ArrayList<RsbtEqu>():rsbtEquList);
        this.rsbtEquAppendixList = (rsbtEquAppendixList == null?new ArrayList<RsbtEquAppendix>():rsbtEquAppendixList);
        this.rsbtEquTList = (rsbtEquTList == null? new ArrayList<>(): rsbtEquTList);

        this.rsbtFreqList = (rsbtFreqList == null?new ArrayList<RsbtFreq>():rsbtFreqList);
        this.rsbtFreqAppendixList = (rsbtFreqAppendixList == null?new ArrayList<RsbtFreqAppendix>():rsbtFreqAppendixList);
        this.rsbtFreqTList = (rsbtFreqTList == null ? new ArrayList<>(): rsbtFreqTList);
        this.rsbtEafList = rsbtEafList;
        this.asyncRawBtsList = (asyncRawBtsList == null?new ArrayList<AsyncRawBts>():asyncRawBtsList);
        this.rsbtTrafficList = (rsbtTrafficList == null?new ArrayList<RsbtTraffic>():rsbtTrafficList);
        this.rsbtStationMainESList = (rsbtStationMainESList ==null?new ArrayList<RsbtStationMainES>():rsbtStationMainESList);
    }

    public ProcessingDataDTO(String userId, String jobId, String appGuid, String dateType,
                             List<RsbtStation> rsbtStationList, List<RsbtAntfeed> rsbtAntfeedList, List<RsbtEqu> rsbtEquList, List<RsbtFreq> rsbtFreqList,
                             List<AsyncRawBts> asyncRawBtsList){
        this.userId = userId;
        this.jobId = jobId;
        this.appGuid = appGuid;
        this.dateType = dateType;
        this.rsbtStationList = (rsbtStationList == null?new ArrayList<RsbtStation>():rsbtStationList);
        this.rsbtAntfeedList = (rsbtAntfeedList == null?new ArrayList<RsbtAntfeed>():rsbtAntfeedList);
        this.rsbtEquList = (rsbtEquList == null?new ArrayList<RsbtEqu>():rsbtEquList);
        this.rsbtFreqList = (rsbtFreqList == null?new ArrayList<RsbtFreq>():rsbtFreqList);
        this.asyncRawBtsList = (asyncRawBtsList == null?new ArrayList<AsyncRawBts>():asyncRawBtsList);
    }

    public List<RsbtNet> getRsbtNetList() {
        return rsbtNetList;
    }

    public void setRsbtNetList(List<RsbtNet> rsbtNetList) {
        this.rsbtNetList = rsbtNetList;
    }

    public List<RsbtEaf> getRsbtEafList() {
        return rsbtEafList;
    }

    public void setRsbtEafList(List<RsbtEaf> rsbtEafList) {
        this.rsbtEafList = rsbtEafList;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getJobId() {
        return jobId;
    }

    public void setJobId(String jobId) {
        this.jobId = jobId;
    }

    public String getAppGuid() {
        return appGuid;
    }

    public void setAppGuid(String appGuid) {
        this.appGuid = appGuid;
    }

    public List<RsbtStation> getRsbtStationList() {
        return rsbtStationList;
    }

    public void setRsbtStationList(List<RsbtStation> rsbtStationList) {
        this.rsbtStationList = rsbtStationList;
    }

    public List<RsbtStationAppendix> getRsbtStationAppendixList() {
        return rsbtStationAppendixList;
    }

    public void setRsbtStationAppendixList(List<RsbtStationAppendix> rsbtStationAppendixList) {
        this.rsbtStationAppendixList = rsbtStationAppendixList;
    }

    public List<RsbtStationT> getRsbtStationTList() {
        return rsbtStationTList;
    }

    public void setRsbtStationTList(List<RsbtStationT> rsbtStationTList) {
        this.rsbtStationTList = rsbtStationTList;
    }

    public List<RsbtAntfeedT> getRsbtAntfeedTList() {
        return rsbtAntfeedTList;
    }

    public void setRsbtAntfeedTList(List<RsbtAntfeedT> rsbtAntfeedTList) {
        this.rsbtAntfeedTList = rsbtAntfeedTList;
    }

    public List<RsbtEquT> getRsbtEquTList() {
        return rsbtEquTList;
    }

    public void setRsbtEquTList(List<RsbtEquT> rsbtEquTList) {
        this.rsbtEquTList = rsbtEquTList;
    }

    public List<RsbtFreqT> getRsbtFreqTList() {
        return rsbtFreqTList;
    }

    public void setRsbtFreqTList(List<RsbtFreqT> rsbtFreqTList) {
        this.rsbtFreqTList = rsbtFreqTList;
    }

    public List<RsbtAntfeed> getRsbtAntfeedList() {
        return rsbtAntfeedList;
    }

    public void setRsbtAntfeedList(List<RsbtAntfeed> rsbtAntfeedList) {
        this.rsbtAntfeedList = rsbtAntfeedList;
    }

    public List<RsbtAntfeedAppendix> getRsbtAntfeedAppendixList() {
        return rsbtAntfeedAppendixList;
    }

    public void setRsbtAntfeedAppendixList(List<RsbtAntfeedAppendix> rsbtAntfeedAppendixList) {
        this.rsbtAntfeedAppendixList = rsbtAntfeedAppendixList;
    }

    public List<RsbtEqu> getRsbtEquList() {
        return rsbtEquList;
    }

    public void setRsbtEquList(List<RsbtEqu> rsbtEquList) {
        this.rsbtEquList = rsbtEquList;
    }

    public List<RsbtEquAppendix> getRsbtEquAppendixList() {
        return rsbtEquAppendixList;
    }

    public void setRsbtEquAppendixList(List<RsbtEquAppendix> rsbtEquAppendixList) {
        this.rsbtEquAppendixList = rsbtEquAppendixList;
    }

    public List<RsbtFreq> getRsbtFreqList() {
        return rsbtFreqList;
    }

    public void setRsbtFreqList(List<RsbtFreq> rsbtFreqList) {
        this.rsbtFreqList = rsbtFreqList;
    }

    public List<RsbtFreqAppendix> getRsbtFreqAppendixList() {
        return rsbtFreqAppendixList;
    }

    public void setRsbtFreqAppendixList(List<RsbtFreqAppendix> rsbtFreqAppendixList) {
        this.rsbtFreqAppendixList = rsbtFreqAppendixList;
    }

    public String getDateType() {
        return dateType;
    }

    public void setDateType(String dateType) {
        this.dateType = dateType;
    }

    public List<AsyncRawBts> getAsyncRawBtsList() {
        return asyncRawBtsList;
    }

    public void setAsyncRawBtsList(List<AsyncRawBts> asyncRawBtsList) {
        this.asyncRawBtsList = asyncRawBtsList;
    }

    public List<RsbtStationBak> getRsbtStationBakList() {
        return rsbtStationBakList;
    }

    public void setRsbtStationBakList(List<RsbtStationBak> rsbtStationBakList) {
        this.rsbtStationBakList = rsbtStationBakList;
    }

    public List<TransportRawBts> getTransportRawBtsList() {
        return transportRawBtsList;
    }

    public void setTransportRawBtsList(List<TransportRawBts> transportRawBtsList) {
        this.transportRawBtsList = transportRawBtsList;
    }

    public List<LogTransportJob> getLogTransportJobList() {
        return logTransportJobList;
    }

    public void setLogTransportJobList(List<LogTransportJob> logTransportJobList) {
        this.logTransportJobList = logTransportJobList;
    }

    public List<RsbtTraffic> getRsbtTrafficList() {
        return rsbtTrafficList;
    }

    public void setRsbtTrafficList(List<RsbtTraffic> rsbtTrafficList) {
        this.rsbtTrafficList = rsbtTrafficList;
    }

    public List<RsbtStationMainES> getRsbtStationMainESList() {
        return rsbtStationMainESList;
    }

    public void setRsbtStationMainESList(List<RsbtStationMainES> rsbtStationMainESList) {
        this.rsbtStationMainESList = rsbtStationMainESList;
    }
}
