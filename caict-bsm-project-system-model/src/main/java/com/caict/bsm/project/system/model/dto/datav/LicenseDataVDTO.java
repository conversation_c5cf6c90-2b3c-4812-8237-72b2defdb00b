package com.caict.bsm.project.system.model.dto.datav;

import java.util.Date;

/**
 * Created by dengsy on 2020-05-18.
 *
 * sumLicense:统计数量
 * orgName:组织名称
 * orgType:组织类型
 * licenseState:执照状态
 *
 * regularCount：已发数量
 * expireCount：过期数量
 * pauseCount：停用数量
 * deleteCount：注销数量
 */
public class LicenseDataVDTO {

    private Date sDateBegin;
    private Date eDateEnd;
    private String selectedArea;
    private String netType;
    private Integer sumLicense;
    private String orgName;
    private String orgType;
    private Long licenseState;

    private Integer regularCount;
    private Integer expireCount;
    private Integer pauseCount;
    private Integer deleteCount;

    private String regionId;
    private String isSync;

    public LicenseDataVDTO(){}

    public Integer getSumLicense() {
        return sumLicense;
    }

    public void setSumLicense(Integer sumLicense) {
        this.sumLicense = sumLicense;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getOrgType() {
        return orgType;
    }

    public void setOrgType(String orgType) {
        this.orgType = orgType;
    }

    public Long getLicenseState() {
        return licenseState;
    }

    public void setLicenseState(Long licenseState) {
        this.licenseState = licenseState;
    }

    public Integer getRegularCount() {
        return regularCount;
    }

    public void setRegularCount(Integer regularCount) {
        this.regularCount = regularCount;
    }

    public Integer getExpireCount() {
        return expireCount;
    }

    public void setExpireCount(Integer expireCount) {
        this.expireCount = expireCount;
    }

    public Integer getPauseCount() {
        return pauseCount;
    }

    public void setPauseCount(Integer pauseCount) {
        this.pauseCount = pauseCount;
    }

    public Integer getDeleteCount() {
        return deleteCount;
    }

    public void setDeleteCount(Integer deleteCount) {
        this.deleteCount = deleteCount;
    }

    public Date getsDateBegin() {
        return sDateBegin;
    }

    public void setsDateBegin(Date sDateBegin) {
        this.sDateBegin = sDateBegin;
    }

    public Date geteDateEnd() {
        return eDateEnd;
    }

    public void seteDateEnd(Date eDateEnd) {
        this.eDateEnd = eDateEnd;
    }

    public String getSelectedArea() {
        return selectedArea;
    }

    public void setSelectedArea(String selectedArea) {
        this.selectedArea = selectedArea;
    }

    public String getNetType() {
        return netType;
    }

    public void setNetType(String netType) {
        this.netType = netType;
    }

    public String getRegionId() {
        return regionId;
    }

    public void setRegionId(String regionId) {
        this.regionId = regionId;
    }

    public String getIsSync() {
        return isSync;
    }

    public void setIsSync(String isSync) {
        this.isSync = isSync;
    }
}
