package com.caict.bsm.project.system.model.entity.boer.push.addorup;


import javax.xml.bind.annotation.*;
import java.util.Date;

@XmlAccessorType(XmlAccessType.FIELD)
// XML文件中的根标识
@XmlRootElement(name = "fs",namespace = NameSpaceUrlConst.returnNamespaceURI)
// 控制JAXB 绑定类中属性和字段的排序
@XmlType(propOrder = {
        "stationGuid","appGuid","appCode","netSvn","statAppType","netTs","stClassCode",
        "orgName","orgAreaCode","statStatus","StatCount","isAgentFee","statType","statSetFlag",
        "equCount","statTdi","statName","statAreaCode","statAddr","statLg","statLa",
        "statAt","enableDate","stCCode","stCSum","stServR","stMemo","freqsData","equsData","antfeedsData"
})
public class FsStCommon implements java.io.Serializable{

    @XmlElement(name="STATION_GUID", namespace=NameSpaceUrlConst.returnNamespaceURI)
    private String stationGuid;

    @XmlElement(name="APP_GUID", namespace=NameSpaceUrlConst.returnNamespaceURI)
    private String appGuid;

    @XmlElement(name="APP_CODE", namespace=NameSpaceUrlConst.returnNamespaceURI)
    private String appCode;

    @XmlElement(name="NET_SVN", namespace=NameSpaceUrlConst.returnNamespaceURI)
    private String netSvn;

    @XmlElement(name="STAT_APP_TYPE", namespace=NameSpaceUrlConst.returnNamespaceURI)
    private String statAppType;

    @XmlElement(name="NET_TS", namespace=NameSpaceUrlConst.returnNamespaceURI)
    private String netTs;

    @XmlElement(name="ST_CLASS_CODE", namespace=NameSpaceUrlConst.returnNamespaceURI)
    private String stClassCode;

    @XmlElement(name="ORG_NAME", namespace=NameSpaceUrlConst.returnNamespaceURI)
    private String orgName;

    @XmlElement(name="ORG_AREA_CODE", namespace=NameSpaceUrlConst.returnNamespaceURI)
    private String orgAreaCode;

    @XmlElement(name="STAT_STATUS", namespace=NameSpaceUrlConst.returnNamespaceURI)
    private String statStatus;

    @XmlElement(name="STAT_COUNT", namespace=NameSpaceUrlConst.returnNamespaceURI)
    private String StatCount;

    @XmlElement(name="IS_AGENT_FEE", namespace=NameSpaceUrlConst.returnNamespaceURI)
    private String isAgentFee;

    @XmlElement(name="STAT_TYPE", namespace=NameSpaceUrlConst.returnNamespaceURI)
    private String statType;

    @XmlElement(name="STAT_SET_FLAG", namespace=NameSpaceUrlConst.returnNamespaceURI)
    private String statSetFlag;

    @XmlElement(name="EQU_COUNT", namespace=NameSpaceUrlConst.returnNamespaceURI)
    private String equCount;

    @XmlElement(name="STAT_TDI", namespace=NameSpaceUrlConst.returnNamespaceURI)
    private String statTdi;

    @XmlElement(name="STAT_NAME", namespace=NameSpaceUrlConst.returnNamespaceURI)
    private String statName;

    @XmlElement(name="STAT_AREA_CODE", namespace=NameSpaceUrlConst.returnNamespaceURI)
    private String statAreaCode;

    @XmlElement(name="STAT_ADDR", namespace=NameSpaceUrlConst.returnNamespaceURI)
    private String statAddr;

    @XmlElement(name="STAT_LG", namespace=NameSpaceUrlConst.returnNamespaceURI)
    private String statLg;

    @XmlElement(name="STAT_LA", namespace=NameSpaceUrlConst.returnNamespaceURI)
    private String statLa;

    @XmlElement(name="STAT_AT", namespace=NameSpaceUrlConst.returnNamespaceURI)
    private String statAt;

    @XmlElement(name="ENABLE_DATE", namespace=NameSpaceUrlConst.returnNamespaceURI)
    private Date enableDate;

    @XmlElement(name="ST_C_CODE", namespace=NameSpaceUrlConst.returnNamespaceURI)
    private String stCCode;

    @XmlElement(name="ST_C_SUM", namespace=NameSpaceUrlConst.returnNamespaceURI)
    private String stCSum;

    @XmlElement(name="ST_SERV_R", namespace=NameSpaceUrlConst.returnNamespaceURI)
    private String stServR;

    @XmlElement(name="ST_MEMO", namespace=NameSpaceUrlConst.returnNamespaceURI)
    private String stMemo;

    @XmlElement(name="FREQS_Data", namespace=NameSpaceUrlConst.returnNamespaceURI)
    private FreqsData freqsData;

    @XmlElement(name="EQUS_Data", namespace=NameSpaceUrlConst.returnNamespaceURI)
    private EqusData equsData;

    @XmlElement(name="ANTFEEDERS_Data", namespace=NameSpaceUrlConst.returnNamespaceURI)
    private AntfeedsData antfeedsData;

    public FsStCommon(){}

    public FsStCommon(String stationGuid, String appGuid, String appCode, String netSvn, String statAppType, String netTs, String stClassCode, String orgName, String orgAreaCode, String statStatus, String statCount, String isAgentFee, String statType, String statSetFlag, String equCount, String statTdi, String statName, String statAreaCode, String statAddr, String statLg, String statLa, String statAt, Date enableDate, String stCCode, String stCSum, String stServR, String stMemo, FreqsData freqsData, EqusData equsData, AntfeedsData antfeedsData) {
        this.stationGuid = stationGuid;
        this.appGuid = appGuid;
        this.appCode = appCode;
        this.netSvn = netSvn;
        this.statAppType = statAppType;
        this.netTs = netTs;
        this.stClassCode = stClassCode;
        this.orgName = orgName;
        this.orgAreaCode = orgAreaCode;
        this.statStatus = statStatus;
        this.StatCount = statCount;
        this.isAgentFee = isAgentFee;
        this.statType = statType;
        this.statSetFlag = statSetFlag;
        this.equCount = equCount;
        this.statTdi = statTdi;
        this.statName = statName;
        this.statAreaCode = statAreaCode;
        this.statAddr = statAddr;
        this.statLg = statLg;
        this.statLa = statLa;
        this.statAt = statAt;
        this.enableDate = enableDate;
        this.stCCode = stCCode;
        this.stCSum = stCSum;
        this.stServR = stServR;
        this.stMemo = stMemo;
        this.freqsData = freqsData;
        this.equsData = equsData;
        this.antfeedsData = antfeedsData;
    }

    public String getStationGuid() {
        return stationGuid;
    }

    public void setStationGuid(String stationGuid) {
        this.stationGuid = stationGuid;
    }

    public String getAppGuid() {
        return appGuid;
    }

    public void setAppGuid(String appGuid) {
        this.appGuid = appGuid;
    }

    public String getAppCode() {
        return appCode;
    }

    public void setAppCode(String appCode) {
        this.appCode = appCode;
    }

    public String getNetSvn() {
        return netSvn;
    }

    public void setNetSvn(String netSvn) {
        this.netSvn = netSvn;
    }

    public String getStatAppType() {
        return statAppType;
    }

    public void setStatAppType(String statAppType) {
        this.statAppType = statAppType;
    }

    public String getNetTs() {
        return netTs;
    }

    public void setNetTs(String netTs) {
        this.netTs = netTs;
    }

    public String getStClassCode() {
        return stClassCode;
    }

    public void setStClassCode(String stClassCode) {
        this.stClassCode = stClassCode;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getOrgAreaCode() {
        return orgAreaCode;
    }

    public void setOrgAreaCode(String orgAreaCode) {
        this.orgAreaCode = orgAreaCode;
    }

    public String getStatStatus() {
        return statStatus;
    }

    public void setStatStatus(String statStatus) {
        this.statStatus = statStatus;
    }

    public String getStatCount() {
        return StatCount;
    }

    public void setStatCount(String statCount) {
        StatCount = statCount;
    }

    public String getIsAgentFee() {
        return isAgentFee;
    }

    public void setIsAgentFee(String isAgentFee) {
        this.isAgentFee = isAgentFee;
    }

    public String getStatType() {
        return statType;
    }

    public void setStatType(String statType) {
        this.statType = statType;
    }

    public String getStatSetFlag() {
        return statSetFlag;
    }

    public void setStatSetFlag(String statSetFlag) {
        this.statSetFlag = statSetFlag;
    }

    public String getEquCount() {
        return equCount;
    }

    public void setEquCount(String equCount) {
        this.equCount = equCount;
    }

    public String getStatTdi() {
        return statTdi;
    }

    public void setStatTdi(String statTdi) {
        this.statTdi = statTdi;
    }

    public String getStatName() {
        return statName;
    }

    public void setStatName(String statName) {
        this.statName = statName;
    }

    public String getStatAreaCode() {
        return statAreaCode;
    }

    public void setStatAreaCode(String statAreaCode) {
        this.statAreaCode = statAreaCode;
    }

    public String getStatAddr() {
        return statAddr;
    }

    public void setStatAddr(String statAddr) {
        this.statAddr = statAddr;
    }

    public String getStatLg() {
        return statLg;
    }

    public void setStatLg(String statLg) {
        this.statLg = statLg;
    }

    public String getStatLa() {
        return statLa;
    }

    public void setStatLa(String statLa) {
        this.statLa = statLa;
    }

    public String getStatAt() {
        return statAt;
    }

    public void setStatAt(String statAt) {
        this.statAt = statAt;
    }

    public Date getEnableDate() {
        return enableDate;
    }

    public void setEnableDate(Date enableDate) {
        this.enableDate = enableDate;
    }

    public String getStCCode() {
        return stCCode;
    }

    public void setStCCode(String stCCode) {
        this.stCCode = stCCode;
    }

    public String getStCSum() {
        return stCSum;
    }

    public void setStCSum(String stCSum) {
        this.stCSum = stCSum;
    }

    public String getStServR() {
        return stServR;
    }

    public void setStServR(String stServR) {
        this.stServR = stServR;
    }

    public String getStMemo() {
        return stMemo;
    }

    public void setStMemo(String stMemo) {
        this.stMemo = stMemo;
    }

    public FreqsData getFreqsData() {
        return freqsData;
    }

    public void setFreqsData(FreqsData freqsData) {
        this.freqsData = freqsData;
    }

    public EqusData getEqusData() {
        return equsData;
    }

    public void setEqusData(EqusData equsData) {
        this.equsData = equsData;
    }

    public AntfeedsData getAntfeedsData() {
        return antfeedsData;
    }

    public void setAntfeedsData(AntfeedsData antfeedsData) {
        this.antfeedsData = antfeedsData;
    }
}
