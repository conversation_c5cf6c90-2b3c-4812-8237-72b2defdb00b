package com.caict.bsm.project.system.model.entity.boer.push.addorup;


import javax.xml.bind.annotation.*;

@XmlAccessorType(XmlAccessType.FIELD)
// XML文件中的根标识
@XmlRootElement(name = "fs",namespace = NameSpaceUrlConst.returnNamespaceURI)
// 控制JAXB 绑定类中属性和字段的排序
@XmlType(propOrder = {
        "serialNum","freqType","freqEUnitT","freqEbUnitT","ftFreqCcode","ftFreqCsgn","freqEfb",
        "freqEfe","freqRfb","freqRfe"
})
public class FsStFreqAll {

    @XmlElement(name="SERIAL_NUM", namespace=NameSpaceUrlConst.returnNamespaceURI)
    private String serialNum;

    @XmlElement(name="FREQ_TYPE", namespace=NameSpaceUrlConst.returnNamespaceURI)
    private String freqType;

    @XmlElement(name="FREQ_E_UNIT_T", namespace=NameSpaceUrlConst.returnNamespaceURI)
    private String freqEUnitT;

    @XmlElement(name="FREQ_EB_UNIT_T", namespace=NameSpaceUrlConst.returnNamespaceURI)
    private String freqEbUnitT;

    @XmlElement(name="FT_FREQ_CCODE", namespace=NameSpaceUrlConst.returnNamespaceURI)
    private String ftFreqCcode;

    @XmlElement(name="FT_FREQ_CSGN", namespace=NameSpaceUrlConst.returnNamespaceURI)
    private String ftFreqCsgn;

    @XmlElement(name="FREQ_EFB", namespace=NameSpaceUrlConst.returnNamespaceURI)
    private Double freqEfb;

    @XmlElement(name="FREQ_EFE", namespace=NameSpaceUrlConst.returnNamespaceURI)
    private Double freqEfe;

    @XmlElement(name="FREQ_RFB", namespace=NameSpaceUrlConst.returnNamespaceURI)
    private Double freqRfb;

    @XmlElement(name="FREQ_RFE", namespace=NameSpaceUrlConst.returnNamespaceURI)
    private Double freqRfe;

    public FsStFreqAll(){}

    public FsStFreqAll(String serialNum, String freqType, String freqEUnitT, String freqEbUnitT, String ftFreqCcode, String ftFreqCsgn, Double freqEfb, Double freqEfe, Double freqRfb, Double freqRfe) {
        this.serialNum = serialNum;
        this.freqType = freqType;
        this.freqEUnitT = freqEUnitT;
        this.freqEbUnitT = freqEbUnitT;
        this.ftFreqCcode = ftFreqCcode;
        this.ftFreqCsgn = ftFreqCsgn;
        this.freqEfb = freqEfb;
        this.freqEfe = freqEfe;
        this.freqRfb = freqRfb;
        this.freqRfe = freqRfe;
    }

    public String getSerialNum() {
        return serialNum;
    }

    public void setSerialNum(String serialNum) {
        this.serialNum = serialNum;
    }

    public String getFreqType() {
        return freqType;
    }

    public void setFreqType(String freqType) {
        this.freqType = freqType;
    }

    public String getFreqEUnitT() {
        return freqEUnitT;
    }

    public void setFreqEUnitT(String freqEUnitT) {
        this.freqEUnitT = freqEUnitT;
    }

    public String getFreqEbUnitT() {
        return freqEbUnitT;
    }

    public void setFreqEbUnitT(String freqEbUnitT) {
        this.freqEbUnitT = freqEbUnitT;
    }

    public String getFtFreqCcode() {
        return ftFreqCcode;
    }

    public void setFtFreqCcode(String ftFreqCcode) {
        this.ftFreqCcode = ftFreqCcode;
    }

    public String getFtFreqCsgn() {
        return ftFreqCsgn;
    }

    public void setFtFreqCsgn(String ftFreqCsgn) {
        this.ftFreqCsgn = ftFreqCsgn;
    }

    public Double getFreqEfb() {
        return freqEfb;
    }

    public void setFreqEfb(Double freqEfb) {
        this.freqEfb = freqEfb;
    }

    public Double getFreqEfe() {
        return freqEfe;
    }

    public void setFreqEfe(Double freqEfe) {
        this.freqEfe = freqEfe;
    }

    public Double getFreqRfb() {
        return freqRfb;
    }

    public void setFreqRfb(Double freqRfb) {
        this.freqRfb = freqRfb;
    }

    public Double getFreqRfe() {
        return freqRfe;
    }

    public void setFreqRfe(Double freqRfe) {
        this.freqRfe = freqRfe;
    }
}
