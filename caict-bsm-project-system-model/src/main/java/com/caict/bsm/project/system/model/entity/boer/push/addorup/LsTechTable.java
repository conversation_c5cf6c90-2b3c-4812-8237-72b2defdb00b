package com.caict.bsm.project.system.model.entity.boer.push.addorup;

import javax.xml.bind.annotation.*;
import java.util.Date;
import java.util.List;

/**
 * Created by yanchengpeng on 2021/9/28.
 */
@XmlAccessorType(XmlAccessType.FIELD)
// XML文件中的根标识
@XmlRootElement(name = "fs",namespace = NameSpaceUrlConst.returnNamespaceURI)
// 控制JAXB 绑定类中属性和字段的排序
@XmlType(propOrder = {
        "fsStCommonList"
})
public class LsTechTable {

    @XmlElement(name="FS_ST_CELL", namespace=NameSpaceUrlConst.returnNamespaceURI)
    private List<FsStCommon> fsStCommonList;

    public LsTechTable(){}

    public LsTechTable(List<FsStCommon> fsStCommonList) {
        this.fsStCommonList = fsStCommonList;
    }

    public List<FsStCommon> getFsStCommonList() {
        return fsStCommonList;
    }

    public void setFsStCommonList(List<FsStCommon> fsStCommonList) {
        this.fsStCommonList = fsStCommonList;
    }
}
