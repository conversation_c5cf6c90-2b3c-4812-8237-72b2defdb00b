package com.caict.bsm.project.system.model.entity.boer.push.addorup;

import javax.xml.bind.annotation.*;

@XmlAccessorType(XmlAccessType.FIELD)
// XML文件中的根标识
@XmlRootElement(name = "tem",namespace = NameSpaceUrlConst.stationNamespaceURI)
// 控制JAXB 绑定类中属性和字段的排序
@XmlType(propOrder = {
        "lsTechTable","isNew","strFeeGuid","strAreaCode","strLoginName","strLoginId","strAppGuid","strStClass1","strOrgName","lastType"
})
public class SaveTechTableData {

    @XmlElement(name="lsTechTable", namespace=NameSpaceUrlConst.stationNamespaceURI)
    private LsTechTable lsTechTable;

    @XmlElement(name="isNew", namespace=NameSpaceUrlConst.stationNamespaceURI)
    private boolean isNew;

    @XmlElement(name="strFeeGuid", namespace=NameSpaceUrlConst.stationNamespaceURI)
    private String strFeeGuid;

    @XmlElement(name="strAreaCode", namespace=NameSpaceUrlConst.stationNamespaceURI)
    private String strAreaCode;

    @XmlElement(name="strLoginName", namespace=NameSpaceUrlConst.stationNamespaceURI)
    private String strLoginName;

    @XmlElement(name="strLoginId", namespace=NameSpaceUrlConst.stationNamespaceURI)
    private String strLoginId;

    @XmlElement(name="strAPP_GUID", namespace=NameSpaceUrlConst.stationNamespaceURI)
    private String strAppGuid;

    @XmlElement(name="strST_CLASS1", namespace=NameSpaceUrlConst.stationNamespaceURI)
    private String strStClass1;

    @XmlElement(name="strORG_NAME", namespace=NameSpaceUrlConst.stationNamespaceURI)
    private String strOrgName;

    @XmlElement(name="lastType", namespace=NameSpaceUrlConst.stationNamespaceURI)
    private String lastType;

    public SaveTechTableData(){}

    public SaveTechTableData(LsTechTable lsTechTable, boolean isNew, String strFeeGuid, String strAreaCode, String strLoginName, String strLoginId, String strAppGuid, String strStClass1, String strOrgName, String lastType) {
        this.lsTechTable = lsTechTable;
        this.isNew = isNew;
        this.strFeeGuid = strFeeGuid;
        this.strAreaCode = strAreaCode;
        this.strLoginName = strLoginName;
        this.strLoginId = strLoginId;
        this.strAppGuid = strAppGuid;
        this.strStClass1 = strStClass1;
        this.strOrgName = strOrgName;
        this.lastType = lastType;
    }

    public LsTechTable getLsTechTable() {
        return lsTechTable;
    }

    public void setLsTechTable(LsTechTable lsTechTable) {
        this.lsTechTable = lsTechTable;
    }

    public boolean isNew() {
        return isNew;
    }

    public void setNew(boolean aNew) {
        isNew = aNew;
    }

    public String getStrFeeGuid() {
        return strFeeGuid;
    }

    public void setStrFeeGuid(String strFeeGuid) {
        this.strFeeGuid = strFeeGuid;
    }

    public String getStrAreaCode() {
        return strAreaCode;
    }

    public void setStrAreaCode(String strAreaCode) {
        this.strAreaCode = strAreaCode;
    }

    public String getStrLoginName() {
        return strLoginName;
    }

    public void setStrLoginName(String strLoginName) {
        this.strLoginName = strLoginName;
    }

    public String getStrLoginId() {
        return strLoginId;
    }

    public void setStrLoginId(String strLoginId) {
        this.strLoginId = strLoginId;
    }

    public String getStrAppGuid() {
        return strAppGuid;
    }

    public void setStrAppGuid(String strAppGuid) {
        this.strAppGuid = strAppGuid;
    }

    public String getStrStClass1() {
        return strStClass1;
    }

    public void setStrStClass1(String strStClass1) {
        this.strStClass1 = strStClass1;
    }

    public String getStrOrgName() {
        return strOrgName;
    }

    public void setStrOrgName(String strOrgName) {
        this.strOrgName = strOrgName;
    }

    public String getLastType() {
        return lastType;
    }

    public void setLastType(String lastType) {
        this.lastType = lastType;
    }
}
