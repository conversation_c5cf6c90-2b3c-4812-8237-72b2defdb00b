package com.caict.bsm.project.system.model.entity.boer.push.licensecha;

import javax.xml.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2020/5/13
 */
@XmlAccessorType(XmlAccessType.FIELD)
// XML文件中的根标识
@XmlRootElement(name = "soapenv",namespace = NameSpaceUrlConst.mainNamespaceURI)
// 控制JAXB 绑定类中属性和字段的排序
@XmlType(propOrder = {
        "lsStatLicense"
})
public class RequestBody {
    @XmlElement(name = "SetStationLicenseData",namespace = NameSpaceUrlConst.stationNamespaceURI)
    private LsStatLicense lsStatLicense;

    public RequestBody() {
    }

    public LsStatLicense getLsStatLicense() {
        return lsStatLicense;
    }

    public void setLsStatLicense(LsStatLicense lsStatLicense) {
        this.lsStatLicense = lsStatLicense;
    }
}
