package com.caict.bsm.project.system.model.entity.business.freqevaluation;

import com.caict.bsm.project.system.utils.annotation.TableFieId;
import com.caict.bsm.project.system.utils.annotation.TableId;
import com.caict.bsm.project.system.utils.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * <AUTHOR>
 * @description 省级公众移动通信系统用户数及用户流量任务
 * @date 2023年8月24日 11点13分
 */
@ApiModel(value = "freqEvaluationJob", description = "省级公众移动通信系统用户数及用户流量任务")
@TableName("BSM_FREQ_EVALUATION_JOB")
public class FreqEvaluationJob {

    @ApiModelProperty(value = "主键ID")
    @TableId("GUID")
    private String guid;

    @ApiModelProperty(value = "job提交方式1,系统自动提交 2,无管局提交文件")
    @TableFieId("TYPE")
    private String type;

    @ApiModelProperty(value = "job名")
    @TableFieId("JOB_NAME")
    private String jobName;

    @ApiModelProperty(value = "文件id")
    @TableFieId("FILE_GUID")
    private String fileGuid;

    @ApiModelProperty(value = "文件名")
    @TableFieId("FILE_NAME")
    private String fileName;

    @ApiModelProperty(value = "企业名称")
    @TableFieId("ORG_NAME")
    private String orgName;

    @ApiModelProperty(value = "企业类型")
    @TableFieId("ORG_TYPE")
    private String orgType;

    @ApiModelProperty(value = "创建日期")
    @TableFieId("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty(value = "备注")
    @TableFieId("MEMO")
    private String memo;

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getJobName() {
        return jobName;
    }

    public void setJobName(String jobName) {
        this.jobName = jobName;
    }

    public String getFileGuid() {
        return fileGuid;
    }

    public void setFileGuid(String fileGuid) {
        this.fileGuid = fileGuid;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getOrgType() {
        return orgType;
    }

    public void setOrgType(String orgType) {
        this.orgType = orgType;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }
}
