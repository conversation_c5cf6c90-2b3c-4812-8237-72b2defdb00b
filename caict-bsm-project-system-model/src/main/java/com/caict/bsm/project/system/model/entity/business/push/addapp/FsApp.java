package com.caict.bsm.project.system.model.entity.business.push.addapp;

import java.util.Date;

public class FsApp {

    private String appCode;
    private Date appDate;
    private Date appFtlb;
    private Date appFtle;
    private String appGuid;
    private String appStatus;
    private String appSubType;
    private String appType;
    private String businessFlag;
    private Date confirmDate;
    private String contactGuid;
    private Date createTime;
    private Date expiredDate;
    private String feeContactGuid;
    private String feeGuid;
    private String isNet;
    private String memo;
    private Date modifiedTime;
    private String netArea;
    private String netBand;
    private String netBandUnit;
    private String netLg;
    private String netName;
    private String netSatName;
    private String netSp;
    private String netSvn;
    private String netTs;
    private String netUse;
    private String netAreaCode;
    private String operator;
    private String orgGuid;
    private String stClass1;
    private Date startDate;
    private String taskGuid;

    public FsApp() {
    }

    public String getAppCode() {
        return appCode;
    }

    public void setAppCode(String appCode) {
        this.appCode = appCode;
    }

    public Date getAppDate() {
        return appDate;
    }

    public void setAppDate(Date appDate) {
        this.appDate = appDate;
    }

    public Date getAppFtlb() {
        return appFtlb;
    }

    public void setAppFtlb(Date appFtlb) {
        this.appFtlb = appFtlb;
    }

    public Date getAppFtle() {
        return appFtle;
    }

    public void setAppFtle(Date appFtle) {
        this.appFtle = appFtle;
    }

    public String getAppGuid() {
        return appGuid;
    }

    public void setAppGuid(String appGuid) {
        this.appGuid = appGuid;
    }

    public String getAppStatus() {
        return appStatus;
    }

    public void setAppStatus(String appStatus) {
        this.appStatus = appStatus;
    }

    public String getAppSubType() {
        return appSubType;
    }

    public void setAppSubType(String appSubType) {
        this.appSubType = appSubType;
    }

    public String getAppType() {
        return appType;
    }

    public void setAppType(String appType) {
        this.appType = appType;
    }

    public String getBusinessFlag() {
        return businessFlag;
    }

    public void setBusinessFlag(String businessFlag) {
        this.businessFlag = businessFlag;
    }

    public Date getConfirmDate() {
        return confirmDate;
    }

    public void setConfirmDate(Date confirmDate) {
        this.confirmDate = confirmDate;
    }

    public String getContactGuid() {
        return contactGuid;
    }

    public void setContactGuid(String contactGuid) {
        this.contactGuid = contactGuid;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getExpiredDate() {
        return expiredDate;
    }

    public void setExpiredDate(Date expiredDate) {
        this.expiredDate = expiredDate;
    }

    public String getFeeContactGuid() {
        return feeContactGuid;
    }

    public void setFeeContactGuid(String feeContactGuid) {
        this.feeContactGuid = feeContactGuid;
    }

    public String getFeeGuid() {
        return feeGuid;
    }

    public void setFeeGuid(String feeGuid) {
        this.feeGuid = feeGuid;
    }

    public String getIsNet() {
        return isNet;
    }

    public void setIsNet(String isNet) {
        this.isNet = isNet;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Date getModifiedTime() {
        return modifiedTime;
    }

    public void setModifiedTime(Date modifiedTime) {
        this.modifiedTime = modifiedTime;
    }

    public String getNetArea() {
        return netArea;
    }

    public void setNetArea(String netArea) {
        this.netArea = netArea;
    }

    public String getNetBand() {
        return netBand;
    }

    public void setNetBand(String netBand) {
        this.netBand = netBand;
    }

    public String getNetBandUnit() {
        return netBandUnit;
    }

    public void setNetBandUnit(String netBandUnit) {
        this.netBandUnit = netBandUnit;
    }

    public String getNetLg() {
        return netLg;
    }

    public void setNetLg(String netLg) {
        this.netLg = netLg;
    }

    public String getNetName() {
        return netName;
    }

    public void setNetName(String netName) {
        this.netName = netName;
    }

    public String getNetSatName() {
        return netSatName;
    }

    public void setNetSatName(String netSatName) {
        this.netSatName = netSatName;
    }

    public String getNetSp() {
        return netSp;
    }

    public void setNetSp(String netSp) {
        this.netSp = netSp;
    }

    public String getNetSvn() {
        return netSvn;
    }

    public void setNetSvn(String netSvn) {
        this.netSvn = netSvn;
    }

    public String getNetTs() {
        return netTs;
    }

    public void setNetTs(String netTs) {
        this.netTs = netTs;
    }

    public String getNetUse() {
        return netUse;
    }

    public void setNetUse(String netUse) {
        this.netUse = netUse;
    }

    public String getNetAreaCode() {
        return netAreaCode;
    }

    public void setNetAreaCode(String netAreaCode) {
        this.netAreaCode = netAreaCode;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getOrgGuid() {
        return orgGuid;
    }

    public void setOrgGuid(String orgGuid) {
        this.orgGuid = orgGuid;
    }

    public String getStClass1() {
        return stClass1;
    }

    public void setStClass1(String stClass1) {
        this.stClass1 = stClass1;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public String getTaskGuid() {
        return taskGuid;
    }

    public void setTaskGuid(String taskGuid) {
        this.taskGuid = taskGuid;
    }
}
