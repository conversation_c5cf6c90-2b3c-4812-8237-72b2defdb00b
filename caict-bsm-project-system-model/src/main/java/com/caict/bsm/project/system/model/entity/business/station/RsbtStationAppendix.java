package com.caict.bsm.project.system.model.entity.business.station;


import com.caict.bsm.project.system.utils.annotation.TableFieId;
import com.caict.bsm.project.system.utils.annotation.TableId;
import com.caict.bsm.project.system.utils.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

@TableName("RSBT_STATION_APPENDIX")
public class RsbtStationAppendix {


    /**
     * 系统ID
     */
    @TableId("GUID")
	private String guid;
	@TableFieId("GMT_CREATE")
	private Date gmtCreate;
	@TableFieId("GMT_MODIFIED")
	private Date gmtModified;
	@TableFieId("IS_DELETED")
	private Long isDeleted;
	/**
	 * 制式（2-2G;3-3G;4-4G;5-5G）
	 */
	@TableFieId("GEN_NUM")
	private String genNum;

	@TableFieId("COUNTY")
	private String county;

	@TableFieId("IS_SYNC")
	private String isSync;

	/**
	 * 是否生成执照
	 */
	@TableFieId("IS_LICENSE")
	private String isLicense;

	@TableFieId("USER_GUID")
	private String userGuid;

	@TableFieId("IS_APPLY")
	private String isApply;

	@TableFieId("TECH_TYPE")
	private String techType;

	@TableFieId("ORG_TYPE")
	private String orgType;

	@ApiModelProperty(value = "基站是否在所属区域内；1：是；2：否")
	@TableFieId("IS_AREA")
	private String isArea;

	@ApiModelProperty(value = "基站当前坐标所属区域编号")
	@TableFieId("COORDINATE_AREA_CODE")
	private String coordinateAreaCode;

	@ApiModelProperty(value = "台站校验状态")
	@TableFieId("IS_VALID")
	private String isValid;

	//1.宏站2.直放站
	@TableFieId("EXPAND_STATION")
	private String expandStation;

	//1.室内站2.室外站
	@TableFieId("ATTRIBUTE_STATION")
	private String attributeStation;

	//1-5G干扰分析通过；2-5G干扰分析不通过
	@TableFieId("ANALYSIS_STATUS")
	private String analysisStatus;

	//1-fast范围外2-fast范围内
	@TableFieId("FAST_STATUS")
	private String fastStatus;

	//与fast的距离（M）
	@TableFieId("DISTANCE")
	private String distance;

	public String getGuid() {
		return guid;
	}

	public void setGuid(String guid) {
		this.guid = guid;
	}

	public Date getGmtCreate() {
		return gmtCreate;
	}

	public void setGmtCreate(Date gmtCreate) {
		this.gmtCreate = gmtCreate;
	}

	public Date getGmtModified() {
		return gmtModified;
	}

	public void setGmtModified(Date gmtModified) {
		this.gmtModified = gmtModified;
	}

	public Long getIsDeleted() {
		return isDeleted;
	}

	public void setIsDeleted(Long isDeleted) {
		this.isDeleted = isDeleted;
	}

	public String getGenNum() {
		return genNum;
	}

	public void setGenNum(String genNum) {
		this.genNum = genNum;
	}

	public String getCounty() {
		return county;
	}

	public void setCounty(String county) {
		this.county = county;
	}

	public String getIsSync() {
		return isSync;
	}

	public void setIsSync(String isSync) {
		this.isSync = isSync;
	}

	public String getIsLicense() {
		return isLicense;
	}

	public void setIsLicense(String isLicense) {
		this.isLicense = isLicense;
	}

	public String getUserGuid() {
		return userGuid;
	}

	public void setUserGuid(String userGuid) {
		this.userGuid = userGuid;
	}

	public String getIsApply() {
		return isApply;
	}

	public void setIsApply(String isApply) {
		this.isApply = isApply;
	}

	public String getTechType() {
		return techType;
	}

	public void setTechType(String techType) {
		this.techType = techType;
	}

	public String getOrgType() {
		return orgType;
	}

	public void setOrgType(String orgType) {
		this.orgType = orgType;
	}

	public String getIsArea() {
		return isArea;
	}

	public void setIsArea(String isArea) {
		this.isArea = isArea;
	}

	public String getCoordinateAreaCode() {
		return coordinateAreaCode;
	}

	public void setCoordinateAreaCode(String coordinateAreaCode) {
		this.coordinateAreaCode = coordinateAreaCode;
	}

	public String getIsValid() {
		return isValid;
	}

	public void setIsValid(String isValid) {
		this.isValid = isValid;
	}

	public String getExpandStation() {
		return expandStation;
	}

	public void setExpandStation(String expandStation) {
		this.expandStation = expandStation;
	}

	public String getAttributeStation() {
		return attributeStation;
	}

	public void setAttributeStation(String attributeStation) {
		this.attributeStation = attributeStation;
	}

	public String getAnalysisStatus() {
		return analysisStatus;
	}

	public void setAnalysisStatus(String analysisStatus) {
		this.analysisStatus = analysisStatus;
	}

	public String getFastStatus() {
		return fastStatus;
	}

	public void setFastStatus(String fastStatus) {
		this.fastStatus = fastStatus;
	}

	public String getDistance() {
		return distance;
	}

	public void setDistance(String distance) {
		this.distance = distance;
	}
}
