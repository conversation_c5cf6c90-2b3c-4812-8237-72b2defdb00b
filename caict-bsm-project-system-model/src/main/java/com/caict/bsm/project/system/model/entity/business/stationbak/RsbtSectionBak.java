package com.caict.bsm.project.system.model.entity.business.stationbak;


import com.caict.bsm.project.system.utils.annotation.TableFieId;
import com.caict.bsm.project.system.utils.annotation.TableId;
import com.caict.bsm.project.system.utils.annotation.TableName;

import java.util.Date;

@TableName("RSBT_SECTION_BAK")
public class RsbtSectionBak {

    @TableId("GUID")
	private String guid;
	@TableFieId("STAT_NAME")
	private String statName;
	/**
	 * 扇区标识码
	 */
	@TableFieId("STAT_ADDR")
	private String statAddr;

	@TableFieId("STAT_LG")
	private String statLg;

	@TableFieId("STAT_LA")
	private String statLa;

	@TableFieId("STAT_DATE_START")
	private Date statDateStart;

	@TableFieId("ST_C_CODE")
	private String stCCode;

	@TableFieId("COUNTY")
	private String county;

	@TableFieId("USER_GUID")
	private String userGuid;

	@TableFieId("GEN_NUM")
	private String genNum;

	@TableFieId("NET_TS")
	private String netTs;

	@TableFieId("ORG_TYPE")
	private String orgType;

	@TableFieId("BAK_DATE")
	private Date bakDate;

	@TableFieId("APP_CODE")
	private String appCode;

	@TableFieId("APP_GUID")
	private String appGuid;

	@TableFieId("IS_VALID")
	private String isValid;

	@TableFieId("STAT_TDI")
	private String statTdi;
	@TableFieId("IS_SYNC")
	private String isSync;

	/**
	 * 数据类型（1-新增；2-变更；3-删除）
	 */
	@TableFieId("DATA_TYPE")
	private String dataType;

	public RsbtSectionBak() {
	}

	public String getGuid() {
		return guid;
	}

	public void setGuid(String guid) {
		this.guid = guid;
	}

	public String getStatName() {
		return statName;
	}

	public void setStatName(String statName) {
		this.statName = statName;
	}

	public String getStatAddr() {
		return statAddr;
	}

	public void setStatAddr(String statAddr) {
		this.statAddr = statAddr;
	}

	public String getStatLg() {
		return statLg;
	}

	public void setStatLg(String statLg) {
		this.statLg = statLg;
	}

	public String getStatLa() {
		return statLa;
	}

	public void setStatLa(String statLa) {
		this.statLa = statLa;
	}

	public Date getStatDateStart() {
		return statDateStart;
	}

	public void setStatDateStart(Date statDateStart) {
		this.statDateStart = statDateStart;
	}

	public String getStCCode() {
		return stCCode;
	}

	public void setStCCode(String stCCode) {
		this.stCCode = stCCode;
	}

	public String getCounty() {
		return county;
	}

	public void setCounty(String county) {
		this.county = county;
	}

	public String getUserGuid() {
		return userGuid;
	}

	public void setUserGuid(String userGuid) {
		this.userGuid = userGuid;
	}

	public String getGenNum() {
		return genNum;
	}

	public void setGenNum(String genNum) {
		this.genNum = genNum;
	}

	public String getNetTs() {
		return netTs;
	}

	public void setNetTs(String netTs) {
		this.netTs = netTs;
	}

	public String getOrgType() {
		return orgType;
	}

	public void setOrgType(String orgType) {
		this.orgType = orgType;
	}

	public Date getBakDate() {
		return bakDate;
	}

	public void setBakDate(Date bakDate) {
		this.bakDate = bakDate;
	}

	public String getAppCode() {
		return appCode;
	}

	public void setAppCode(String appCode) {
		this.appCode = appCode;
	}

	public String getAppGuid() {
		return appGuid;
	}

	public void setAppGuid(String appGuid) {
		this.appGuid = appGuid;
	}

	public String getIsValid() {
		return isValid;
	}

	public void setIsValid(String isValid) {
		this.isValid = isValid;
	}

	public String getStatTdi() {
		return statTdi;
	}

	public void setStatTdi(String statTdi) {
		this.statTdi = statTdi;
	}

	public String getIsSync() {
		return isSync;
	}

	public void setIsSync(String isSync) {
		this.isSync = isSync;
	}

	public String getDataType() {
		return dataType;
	}

	public void setDataType(String dataType) {
		this.dataType = dataType;
	}
}
