package com.caict.bsm.project.system.model.entity.business.transfer;


import com.caict.bsm.project.system.utils.annotation.TableFieId;
import com.caict.bsm.project.system.utils.annotation.TableId;
import com.caict.bsm.project.system.utils.annotation.TableName;

import java.util.Date;

@TableName("TRANSPORT_RAW_BTS_DEAL")
public class TransportRawBtsDeal{

    /**
     * 主键
     */
    @TableId("GUID")
    private String guid;
    /**
     * 中转任务id
     */
    @TableFieId("JOB_GUID")
    private String jobGuid;
    /**
     * 申请表id
     */
//    @TableField("APPLICATION_CODE")
//    private String applicationCode;
    /**
     * 是否为有效数据；1：有效。2：无效
     */
    @TableFieId("IS_VALID")
    private Long isValid;
    /**
     * 小区名称
     */
    @TableFieId("CELL_NAME")
    private String cellName;
    /**
     * 小区识别码
     */
    @TableFieId("CELL_ID")
    private String cellId;
    /**
     * 基站名称
     */
    @TableFieId("BTS_NAME")
    private String btsName;
    /**
     * 基站识别码
     */
    @TableFieId("BTS_ID")
    private String btsId;
    /**
     * 技术体制
     */
    @TableFieId("TECH_TYPE")
    private String techType;
    /**
     * 台址
     */
    @TableFieId("LOCATION")
    private String location;
    /**
     * 经度
     */
    @TableFieId("LONGITUDE")
    private String longitude;
    /**
     * 纬度
     */
    @TableFieId("LATITUDE")
    private String latitude;
    /**
     * 发射起始频率
     */
    @TableFieId("SEND_START_FREQ")
    private String sendStartFreq;
    /**
     * 发射终止频率
     */
    @TableFieId("SEND_END_FREQ")
    private String sendEndFreq;
    /**
     * 接收起始频率
     */
    @TableFieId("ACC_START_FREQ")
    private String accStartFreq;
    /**
     * 接收终止频率
     */
    @TableFieId("ACC_END_FREQ")
    private String accEndFreq;
    /**
     * 最大发射功率
     */
    @TableFieId("MAX_EMISSIVE_POWER")
    private String maxEmissivePower;
    /**
     * 天线距地高度
     */
    @TableFieId("HEIGHT")
    private String height;
    /**
     * 数据变更类型(1：新增；2：变更；3：删除。默认首次传送值为1)
     */
    @TableFieId("DATA_TYPE")
    private String dataType;

    @TableFieId("COUNTY")
    private String county;

    @TableFieId("REGION_CODE")
    private String regionCode;

    /**
     * 是否已处理 1-已处理 2-未处理
     */
    @TableFieId("IS_HANDLE")
    private String isHandle;

    /**
     * 上传时间
     */
    @TableFieId("UPLOAD_DATE")
    private Date uploadDate;

    /**
     * 是否已下载
     */
    @TableFieId("IS_DOWNLOAD")
    private String isDownload;

    @TableFieId("USER_GUID")
    private String userGuid;

    @TableFieId("VENDOR_NAME")
    private String vendorName;
    @TableFieId("DEVICE_MODEL")
    private String deviceModel;
    @TableFieId("MODEL_CODE")
    private String modelCode;
    @TableFieId("ANTENNA_GAIN")
    private String antennaGain;

    //天线类型
    @TableFieId("ANTENNA_MODEL")
    private String antennaModel;
    //天线生产厂家
    @TableFieId("ANTENNA_FACTORY")
    private String antennaFactory;
    //极化方式
    @TableFieId("POLARIZATION_MODE")
    private String polarizationMode;
    //天线方位角
    @TableFieId("ANTENNA_AZIMUTH")
    private String antennaAzimuth;
    //馈线系统总损耗
    @TableFieId("FEEDER_LOSS")
    private String feederLoss;
    //海拔高度
    @TableFieId("ALTITUDE")
    private String altitude;

    //制式
    @TableFieId("GEN_NUM")
    private String genNum;

    //设台年份
    @TableFieId("SET_YEAR")
    private String setYear;
    //设台月份
    @TableFieId("SET_MONTH")
    private String setMonth;

    //运营商类型
    @TableFieId("ORG_TYPE")
    private String orgType;

    //1.宏站2.直放站
    @TableFieId("EXPAND_STATION")
    private String expandStation;

    //1.室内站2.室外站
    @TableFieId("ATTRIBUTE_STATION")
    private String attributeStation;

    //收倾角
    @TableFieId("AT_RANG")
    private String atRang;

    //发倾角
    @TableFieId("AT_EANG")
    private String atEang;

    //服务半径
    @TableFieId("ST_SERV_R")
    private String stServR;

    /**
     * 基站更新类型（1：新增；2：变更；3：删除；4：不变）
     */
    @TableFieId("BTS_DATA_TYPE")
    private String btsDataType;

    //基站覆盖场景
    @TableFieId("ST_SCENE")
    private String stScene;
    //用户数或流量统计年份月份
    @TableFieId("TRF_DATE")
    private Date trfDate;
    //平均忙时激活用户数
    @TableFieId("TRF_USER")
    private Double trfUser;
    //平均忙时激活用户流量
    @TableFieId("TRF_DATA")
    private Double trfData;

    public String getBtsDataType() {
        return btsDataType;
    }

    public void setBtsDataType(String btsDataType) {
        this.btsDataType = btsDataType;
    }

    public String getExpandStation() {
        return expandStation;
    }

    public void setExpandStation(String expandStation) {
        this.expandStation = expandStation;
    }

    public String getAttributeStation() {
        return attributeStation;
    }

    public void setAttributeStation(String attributeStation) {
        this.attributeStation = attributeStation;
    }

    public String getAtRang() {
        return atRang;
    }

    public void setAtRang(String atRang) {
        this.atRang = atRang;
    }

    public String getAtEang() {
        return atEang;
    }

    public void setAtEang(String atEang) {
        this.atEang = atEang;
    }

    public String getStServR() {
        return stServR;
    }

    public void setStServR(String stServR) {
        this.stServR = stServR;
    }

    public String getOrgType() {
        return orgType;
    }

    public void setOrgType(String orgType) {
        this.orgType = orgType;
    }

    public String getAntennaModel() {
        return antennaModel;
    }

    public void setAntennaModel(String antennaModel) {
        this.antennaModel = antennaModel;
    }

    public String getAntennaFactory() {
        return antennaFactory;
    }

    public void setAntennaFactory(String antennaFactory) {
        this.antennaFactory = antennaFactory;
    }

    public String getPolarizationMode() {
        return polarizationMode;
    }

    public void setPolarizationMode(String polarizationMode) {
        this.polarizationMode = polarizationMode;
    }

    public String getAntennaAzimuth() {
        return antennaAzimuth;
    }

    public void setAntennaAzimuth(String antennaAzimuth) {
        this.antennaAzimuth = antennaAzimuth;
    }

    public String getFeederLoss() {
        return feederLoss;
    }

    public void setFeederLoss(String feederLoss) {
        this.feederLoss = feederLoss;
    }

    public String getAltitude() {
        return altitude;
    }

    public void setAltitude(String altitude) {
        this.altitude = altitude;
    }

    public String getVendorName() {
        return vendorName;
    }

    public void setVendorName(String vendorName) {
        this.vendorName = vendorName;
    }

    public String getDeviceModel() {
        return deviceModel;
    }

    public void setDeviceModel(String deviceModel) {
        this.deviceModel = deviceModel;
    }

    public String getModelCode() {
        return modelCode;
    }

    public void setModelCode(String modelCode) {
        this.modelCode = modelCode;
    }

    public String getAntennaGain() {
        return antennaGain;
    }

    public void setAntennaGain(String antennaGain) {
        this.antennaGain = antennaGain;
    }

    public String getUserGuid() {
        return userGuid;
    }

    public void setUserGuid(String userGuid) {
        this.userGuid = userGuid;
    }

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getJobGuid() {
        return jobGuid;
    }

    public void setJobGuid(String jobGuid) {
        this.jobGuid = jobGuid;
    }

    public Long getIsValid() {
        return isValid;
    }

    public void setIsValid(Long isValid) {
        this.isValid = isValid;
    }

    public String getCellName() {
        return cellName;
    }

    public void setCellName(String cellName) {
        this.cellName = cellName;
    }

    public String getCellId() {
        return cellId;
    }

    public void setCellId(String cellId) {
        this.cellId = cellId;
    }

    public String getBtsName() {
        return btsName;
    }

    public void setBtsName(String btsName) {
        this.btsName = btsName;
    }

    public String getBtsId() {
        return btsId;
    }

    public void setBtsId(String btsId) {
        this.btsId = btsId;
    }

    public String getTechType() {
        return techType;
    }

    public void setTechType(String techType) {
        this.techType = techType;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getSendStartFreq() {
        return sendStartFreq;
    }

    public void setSendStartFreq(String sendStartFreq) {
        this.sendStartFreq = sendStartFreq;
    }

    public String getSendEndFreq() {
        return sendEndFreq;
    }

    public void setSendEndFreq(String sendEndFreq) {
        this.sendEndFreq = sendEndFreq;
    }

    public String getAccStartFreq() {
        return accStartFreq;
    }

    public void setAccStartFreq(String accStartFreq) {
        this.accStartFreq = accStartFreq;
    }

    public String getAccEndFreq() {
        return accEndFreq;
    }

    public void setAccEndFreq(String accEndFreq) {
        this.accEndFreq = accEndFreq;
    }

    public String getMaxEmissivePower() {
        return maxEmissivePower;
    }

    public void setMaxEmissivePower(String maxEmissivePower) {
        this.maxEmissivePower = maxEmissivePower;
    }

    public String getHeight() {
        return height;
    }

    public void setHeight(String height) {
        this.height = height;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public String getCounty() {
        return county;
    }

    public void setCounty(String county) {
        this.county = county;
    }

    public String getIsHandle() {
        return isHandle;
    }

    public void setIsHandle(String isHandle) {
        this.isHandle = isHandle;
    }

    public Date getUploadDate() {
        return uploadDate;
    }

    public void setUploadDate(Date uploadDate) {
        this.uploadDate = uploadDate;
    }

    public String getIsDownload() {
        return isDownload;
    }

    public void setIsDownload(String isDownload) {
        this.isDownload = isDownload;
    }

    public String getGenNum() {
        return genNum;
    }

    public void setGenNum(String genNum) {
        this.genNum = genNum;
    }

    public String getSetYear() {
        return setYear;
    }

    public void setSetYear(String setYear) {
        this.setYear = setYear;
    }

    public String getSetMonth() {
        return setMonth;
    }

    public void setSetMonth(String setMonth) {
        this.setMonth = setMonth;
    }

    public String getRegionCode() {
        return regionCode;
    }

    public void setRegionCode(String regionCode) {
        this.regionCode = regionCode;
    }

    public String getStScene() {
        return stScene;
    }

    public void setStScene(String stScene) {
        this.stScene = stScene;
    }

    public Date getTrfDate() {
        return trfDate;
    }

    public void setTrfDate(Date trfDate) {
        this.trfDate = trfDate;
    }

    public Double getTrfUser() {
        return trfUser;
    }

    public void setTrfUser(Double trfUser) {
        this.trfUser = trfUser;
    }

    public Double getTrfData() {
        return trfData;
    }

    public void setTrfData(Double trfData) {
        this.trfData = trfData;
    }
}
