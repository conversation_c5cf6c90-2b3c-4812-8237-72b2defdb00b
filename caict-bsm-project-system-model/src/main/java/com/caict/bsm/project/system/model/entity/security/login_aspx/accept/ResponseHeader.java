package com.caict.bsm.project.system.model.entity.security.login_aspx.accept;

import javax.xml.bind.annotation.*;

/**
 * <AUTHOR>
 * @description ResponseHeader
 * @date 2022/6/17 17:38
 */
@XmlAccessorType(XmlAccessType.FIELD)
// XML文件中的根标识
@XmlRootElement(name = "Header",namespace = NameSpaceUrlConst.mainNamespaceURI)
// 控制JAXB 绑定类中属性和字段的排序
@XmlType(propOrder = {
        "providerResponse"
})
public class ResponseHeader {
    @XmlElement(name="ProviderResponse", namespace=NameSpaceUrlConst.aNamespaceURI)
    private ProviderResponse providerResponse;

    public ResponseHeader() {
    }

    public ProviderResponse getProviderResponse() {
        return providerResponse;
    }

    public void setProviderResponse(ProviderResponse providerResponse) {
        this.providerResponse = providerResponse;
    }
}
