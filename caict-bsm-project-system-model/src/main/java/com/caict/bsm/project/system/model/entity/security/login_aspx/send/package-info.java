@javax.xml.bind.annotation.XmlSchema(
    xmlns ={
            @javax.xml.bind.annotation.XmlNs(prefix = "srrc",namespaceURI = NameSpaceUrlConst.sendNamespaceURI),
            @javax.xml.bind.annotation.XmlNs(prefix = "soapenv",namespaceURI = NameSpaceUrlConst.mainNamespaceURI)
    },
        attributeFormDefault = XmlNsForm.UNQUALIFIED,
        elementFormDefault = XmlNsForm.QUALIFIED
)
package com.caict.bsm.project.system.model.entity.security.login_aspx.send;

import javax.xml.bind.annotation.XmlNsForm;
