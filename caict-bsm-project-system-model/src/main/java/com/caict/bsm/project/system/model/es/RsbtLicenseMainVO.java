package com.caict.bsm.project.system.model.es;

import com.caict.bsm.project.system.utils.annotation.TablePageNum;
import com.caict.bsm.project.system.utils.annotation.TablePageSize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Created by dengsy on 2022-6-14.
 * 执照es查询vo
 */
@ApiModel(value = "rsbtLicenseMainVO", description = "执照es查询vo")
public class RsbtLicenseMainVO {

    @ApiModelProperty(value = "当前页码")
    @TablePageNum
    private int page;

    @ApiModelProperty(value = "每页显示数量")
    @TablePageSize
    private int rows;

    @ApiModelProperty(value = "设台单位")
    private String orgName;

    @ApiModelProperty(value = "代数")
    private String genNum;

    @ApiModelProperty(value = "室内站/室外站")
    private String attributeStatio;

    @ApiModelProperty(value = "宏站/直放站")
    private String expandStation;

    @ApiModelProperty(value = "基站状态")
    private String isDelete;

    @ApiModelProperty(value = "台站名称")
    private String stationName;

    @ApiModelProperty(value = "基站识别码")
    private String stationCode;

    @ApiModelProperty(value = "是否生成执照")
    private String isLicense;

    @ApiModelProperty(value = "运营商类型")
    private String orgOperatorType;

    @ApiModelProperty(value = "纬度")
    private String latitude;

    @ApiModelProperty(value = "经度")
    private String longitude;

    @ApiModelProperty(value = "半径")
    private String radius;

    @ApiModelProperty(value = "发射频率下限")
    private String freqEfb;

    @ApiModelProperty(value = "发射频率上限")
    private String freqEfe;

    @ApiModelProperty(value = "接收频率上限")
    private String freqRfe;

    @ApiModelProperty(value = "接收频率下限")
    private String freqRfb;

    public RsbtLicenseMainVO(){}

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getRows() {
        return rows;
    }

    public void setRows(int rows) {
        this.rows = rows;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getGenNum() {
        return genNum;
    }

    public void setGenNum(String genNum) {
        this.genNum = genNum;
    }

    public String getAttributeStatio() {
        return attributeStatio;
    }

    public void setAttributeStatio(String attributeStatio) {
        this.attributeStatio = attributeStatio;
    }

    public String getExpandStation() {
        return expandStation;
    }

    public void setExpandStation(String expandStation) {
        this.expandStation = expandStation;
    }

    public String getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(String isDelete) {
        this.isDelete = isDelete;
    }

    public String getStationName() {
        return stationName;
    }

    public void setStationName(String stationName) {
        this.stationName = stationName;
    }

    public String getStationCode() {
        return stationCode;
    }

    public void setStationCode(String stationCode) {
        this.stationCode = stationCode;
    }

    public String getIsLicense() {
        return isLicense;
    }

    public void setIsLicense(String isLicense) {
        this.isLicense = isLicense;
    }

    public String getOrgOperatorType() {
        return orgOperatorType;
    }

    public void setOrgOperatorType(String orgOperatorType) {
        this.orgOperatorType = orgOperatorType;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getRadius() {
        return radius;
    }

    public void setRadius(String radius) {
        this.radius = radius;
    }

    public String getFreqEfb() {
        return freqEfb;
    }

    public void setFreqEfb(String freqEfb) {
        this.freqEfb = freqEfb;
    }

    public String getFreqEfe() {
        return freqEfe;
    }

    public void setFreqEfe(String freqEfe) {
        this.freqEfe = freqEfe;
    }

    public String getFreqRfe() {
        return freqRfe;
    }

    public void setFreqRfe(String freqRfe) {
        this.freqRfe = freqRfe;
    }

    public String getFreqRfb() {
        return freqRfb;
    }

    public void setFreqRfb(String freqRfb) {
        this.freqRfb = freqRfb;
    }
}
