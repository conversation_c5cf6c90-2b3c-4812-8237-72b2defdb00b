package com.caict.bsm.project.system.model.vo.freqevaluation;

import com.caict.bsm.project.system.model.entity.business.freqevaluation.FreqEvaluationJob;
import io.swagger.annotations.ApiModel;

import java.util.Date;

@ApiModel(value = "freqEvaluationJobVO", description = "省级公众移动通信系统用户数及用户流量任务VO")
public class FreqEvaluationJobVO extends FreqEvaluationJob {

    private Date startTime;
    private Date endTime;
    private String userId;
    private String jobId;
    //分页信息
    private Integer page;
    private Integer rows;

    public String getJobId() {
        return jobId;
    }

    public void setJobId(String jobId) {
        this.jobId = jobId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getRows() {
        return rows;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }
}
