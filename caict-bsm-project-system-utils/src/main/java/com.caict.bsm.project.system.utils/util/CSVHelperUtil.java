package com.caict.bsm.project.system.utils.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * CVSHelperUtil.
 *
 * <AUTHOR>
 * @version jdk_8u291
 * @since 6/16/21 2:37 PM
 **/
public class CSVHelperUtil {

    // 加载slf4j日志记录器。
    private static Logger logger = LoggerFactory.getLogger(CSVHelperUtil.class);


    /**
     * CSV文件生成方法.
     *
     * @param dataList   数据列表.
     * @param outPutPath 文件输出路径.
     * @param filename   文件名.
     * @return
     */
    public static void createCSVFile(List<List<Object>> dataList, String outPutPath, String filename, HttpServletResponse response) {
        File csvFile = null;
        BufferedWriter csvWriter = null;
        try {
            csvFile = new File(outPutPath + File.separator + filename + ".csv");
            File parent = csvFile.getParentFile();
            if (parent != null && !parent.exists()) {
                parent.mkdirs();
            }
            csvFile.createNewFile();

            // GB2312使正确读取分隔符",".
            csvWriter = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(csvFile), StandardCharsets.UTF_8), 1024);
            // 写入文件内容.
            for (List<Object> row : dataList) {
                writeRow(row, csvWriter);
            }
            csvWriter.flush();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (csvWriter != null)
                    csvWriter.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        if (csvFile != null && csvFile.exists()) {

            try {
                response.reset();
                response.setContentType("application/x-download");
                response.setHeader("Content-Disposition", "attachment;filename=\"" + new String((filename + ".csv").getBytes(StandardCharsets.UTF_8), StandardCharsets.UTF_8) + "\"");
                OutputStream out = response.getOutputStream();
                FileInputStream in = new FileInputStream(csvFile);
                byte[] b = new byte[10240];
                int n;
                //为了保证excel打开csv不出现中文乱码
                out.write(new byte[]{(byte) 0xEF, (byte) 0xBB, (byte) 0xBF});
                while ((n = in.read(b)) != -1) {
                    //每次写入out1024字节
                    out.write(b, 0, n);
                }
                in.close();
                out.flush();
                out.close();
                response.flushBuffer();
                csvFile.delete();
            } catch (IOException e) {
                logger.error("异常：{}", e);
            }
        }
    }

    /**
     * 写一行数据方法.
     *
     * @param row
     * @param csvWriter
     * @throws IOException
     */
    private static void writeRow(List<Object> row, BufferedWriter csvWriter) throws IOException {
        // 写入文件头部.
        for (Object obj : row) {
            String dataTemp = obj + "";
            String data;
//            if (dataTemp.length() > 9990) {
//                data = dataTemp.substring(0, 9990);
//            } else {
            data = dataTemp;
//            }

            //先判断字符里是否含有逗号.
            if (dataTemp.contains(",")) {
                //如果还有双引号，先将双引号转义，避免两边加了双引号后转义错误.
                if (dataTemp.contains("\"")) {
                    data = dataTemp.replace("\"", "\"\"");
                }
                //将逗号转义.
                data = "\"" + data + "\"";
            }
            String rowStr = data + ",";
            csvWriter.write(rowStr);
        }
        csvWriter.newLine();
    }
}
