package com.caict.bsm.project.system.utils.util;


import javafx.geometry.Point2D;

import java.util.List;

public class IsInPolygonUtil {

    /**
     * 判断点是否在多边形内
     * @Title: IsPointInPoly
     * @Description: TODO()
     * @param point 测试点
     * @param pts 多边形的点
     * @return
     * @return boolean
     * @throws
     */
    public static boolean isInPolygon(Point2D point, List<Point2D> pts){

        int N = pts.size();
        boolean boundOrVertex = true;
        int intersectCount = 0;//交叉点数量
        double precision = 2e-10; //浮点类型计算时候与0比较时候的容差
        Point2D p1, p2;//临近顶点
        Point2D p = point; //当前点

        p1 = pts.get(0);
        for(int i = 1; i <= N; ++i){
            if(p.equals(p1)){
                return boundOrVertex;
            }

            p2 = pts.get(i % N);
            if(p.getX() < Math.min(p1.getX(), p2.getX()) || p.getX() > Math.max(p1.getX(), p2.getX())){
                p1 = p2;
                continue;
            }

            //射线穿过算法
            if(p.getX() > Math.min(p1.getX(), p2.getX()) && p.getX() < Math.max(p1.getX(), p2.getX())){
                if(p.getY() <= Math.max(p1.getY(), p2.getY())){
                    if(p1.getX() == p2.getX() && p.getY() >= Math.min(p1.getY(), p2.getY())){
                        return boundOrVertex;
                    }

                    if(p1.getY() == p2.getY()){
                        if(p1.getY() == p.getY()){
                            return boundOrVertex;
                        }else{
                            ++intersectCount;
                        }
                    }else{
                        double xinters = (p.getX() - p1.getX()) * (p2.getY() - p1.getY()) / (p2.getX() - p1.getX()) + p1.getY();
                        if(Math.abs(p.getY() - xinters) < precision){
                            return boundOrVertex;
                        }

                        if(p.getY() < xinters){
                            ++intersectCount;
                        }
                    }
                }
            }else{
                if(p.getX() == p2.getX() && p.getY() <= p2.getY()){
                    Point2D p3 = pts.get((i+1) % N);
                    if(p.getX() >= Math.min(p1.getX(), p3.getX()) && p.getX() <= Math.max(p1.getX(), p3.getX())){
                        ++intersectCount;
                    }else{
                        intersectCount += 2;
                    }
                }
            }
            p1 = p2;
        }
        if(intersectCount % 2 == 0){//偶数在多边形外
            return false;
        } else { //奇数在多边形内
            return true;
        }
    }
}
