package com.caict.bsm.project.system.utils.util;

import com.alibaba.fastjson.JSONObject;

/**
 * Created by dengsy on 2019-10-23.
 * 工具类：JSON格式化
 */
public class JSONResult {
    private final static float API_VERSION = 1.0f;

    public static JSONObject getJson(boolean result, Object data, String msg){
        JSONObject json = new JSONObject();
        json.put("success",result);
        json.put("message",msg);
        json.put("version", API_VERSION);
        json.put("data", JSONObject.toJSON(data));
        return json;
    }

    public static JSONObject getApprovedSucessJson(Object deal, Object undeal, String msg){
        JSONObject json = new JSONObject();
        json.put("success",true);
        json.put("message",msg);
        json.put("version", API_VERSION);
        json.put("deal", JSONObject.toJSON(deal));
        json.put("undeal", JSONObject.toJSON(undeal));
        return json;
    }

    public static JSONObject getSuccessJson(Object data, Object single, String msg){
        JSONObject json = new JSONObject();
        json.put("success",true);
        json.put("message",msg);
        json.put("version", API_VERSION);
        json.put("data", JSONObject.toJSON(data));
        json.put("single", JSONObject.toJSON(single));
        return json;
    }

    public static JSONObject getSuccessJson(Object data, String msg){
        return getJson(true,data,msg);
    }

    public static JSONObject getSuccessJson(Object data){
        JSONObject json = new JSONObject();
        json.put("success",true);
        json.put("message","");
        json.put("version", API_VERSION);
        json.put("data", JSONObject.toJSON(data));
        return json;
    }

    public static JSONObject getPageSuccessJson(int page, int current, int rows, int total, Object data){
        JSONObject json = new JSONObject();
        json.put("success",true);
        json.put("message","");
        json.put("version", API_VERSION);
        json.put("page",page);
        json.put("current",current);
        json.put("rows",rows);
        json.put("total",total);
        json.put("data", JSONObject.toJSON(data));
        return json;
    }

    public static JSONObject getStationHistorySucessJson(Object add,Object update,Object delete,String msg){
        JSONObject json = new JSONObject();
        json.put("success",true);
        json.put("message",msg);
        json.put("version", API_VERSION);
        json.put("add", JSONObject.toJSON(add));
        json.put("update", JSONObject.toJSON(update));
        json.put("delete", JSONObject.toJSON(delete));
        return json;
    }

    public static JSONObject getSuccessJson(String msg){
        JSONObject json = new JSONObject();
        json.put("success",true);
        json.put("message",msg);
        json.put("version", API_VERSION);
        json.put("data", "");
        return json;
    }

    public static JSONObject getFailureJson(String failMsg){
        JSONObject json = new JSONObject();
        json.put("success",false);
        json.put("message",failMsg);
        json.put("version", API_VERSION);
        json.put("data", "");
        return json;
    }

    public static JSONObject getPageFailureJson(int page, int current, int rows, int total, String failMsg){
        JSONObject json = new JSONObject();
        json.put("success",false);
        json.put("message",failMsg);
        json.put("version", API_VERSION);
        json.put("page",page);
        json.put("current",current);
        json.put("rows",rows);
        json.put("total",total);
        json.put("data", "");
        return json;
    }

    public static JSONObject getFailureJson(Object data, String failMsg) {
        JSONObject json = new JSONObject();
        json.put("success", false);
        json.put("message", failMsg);
        json.put("version", API_VERSION);
        json.put("data", data);
        return json;
    }
}
