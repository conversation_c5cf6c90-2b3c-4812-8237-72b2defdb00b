package com.caict.bsm.project.system.utils.util;

import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.UsernamePasswordCredentials;
import org.apache.commons.httpclient.auth.AuthScope;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.httpclient.methods.RequestEntity;
import org.apache.commons.httpclient.methods.StringRequestEntity;
import org.apache.cxf.helpers.IOUtils;

import java.io.InputStream;

/**
 * Created by yanchengpeng on 2020/11/27.
 */
public class SendXmlUtil {

    //发送请求
    public static String sendRequest(String xml){
        try {
            int timeout = 5000;
            HttpClient client = new HttpClient();
            UsernamePasswordCredentials creds = new UsernamePasswordCredentials("520000-01-0151", "12d99732");
            client.getState().setCredentials(AuthScope.ANY, creds);
            PostMethod postMethod = new PostMethod("http://172.18.130.83/SRRC_GZ/webservice/selectPlatformUserinfosAdd");
            // 设置连接超时
            client.getHttpConnectionManager().getParams().setConnectionTimeout(timeout);
            // 设置读取时间超时
            client.getHttpConnectionManager().getParams().setSoTimeout(timeout);
            // 然后把Soap请求数据添加到PostMethod中
            RequestEntity requestEntity = new StringRequestEntity(xml, "text/xml", "UTF-8");
            //设置请求头部，否则可能会报 “no SOAPAction header” 的错误
//            postMethod.setRequestHeader("SOAPAction","http://tempuri.org/IOrganization/GetUsers");
//            postMethod.setRequestHeader("Username","520000-01-0151");
//            postMethod.setRequestHeader("Password","12d99732");

            // 设置请求体
            postMethod.setRequestEntity(requestEntity);
            int status = client.executeMethod(postMethod);
            // 打印请求状态码
            System.out.println("status:" + status);
            // 获取响应体输入流
            InputStream is = postMethod.getResponseBodyAsStream();
            // 获取请求结果字符串
            String result = IOUtils.toString(is);
            //System.out.println("result: " + result);
            return result;
        }catch (Exception e){
            e.printStackTrace();
        }

        return null;
    }

    //发送请求
    public static String sendBeRequest(String xml,String httpPath,String method){
        try {
            int timeout = 5000;
            HttpClient client = new HttpClient();
//            UsernamePasswordCredentials creds = new UsernamePasswordCredentials("500000-01-0007", "2ff1cfec");
//            client.getState().setCredentials(AuthScope.ANY, creds);
            PostMethod postMethod = new PostMethod("http://172.18.130.18:8744/"+httpPath);
            // 设置连接超时
            client.getHttpConnectionManager().getParams().setConnectionTimeout(timeout);
            // 设置读取时间超时
            client.getHttpConnectionManager().getParams().setSoTimeout(timeout);
            // 然后把Soap请求数据添加到PostMethod中
            RequestEntity requestEntity = new StringRequestEntity(xml, "text/xml", "UTF-8");
            //设置请求头部，否则可能会报 “no SOAPAction header” 的错误
            postMethod.setRequestHeader("SOAPAction","http://tempuri.org/"+httpPath+"/"+method);

            // 设置请求体
            postMethod.setRequestEntity(requestEntity);
            int status = client.executeMethod(postMethod);
            // 打印请求状态码
            System.out.println("status:" + status);
            // 获取响应体输入流
            InputStream is = postMethod.getResponseBodyAsStream();
            // 获取请求结果字符串
            String result = IOUtils.toString(is);
            //System.out.println("result: " + result);
            return result;
        }catch (Exception e){
            e.printStackTrace();
        }

        return null;
    }
}
