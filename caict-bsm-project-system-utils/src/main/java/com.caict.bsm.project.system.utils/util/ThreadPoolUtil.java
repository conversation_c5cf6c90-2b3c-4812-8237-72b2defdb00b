package com.caict.bsm.project.system.utils.util;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description ThreadPoolUtil
 * @date 2023/10/18 9:32
 */
public class ThreadPoolUtil {

    private static final int coreSize = 5;
    private static final int maxPoolSize = 10;
    private static final int keepAliveTime = 100;

    //获取线程资源
    public static ThreadPoolExecutor getThread(){
        return new ThreadPoolExecutor(coreSize, maxPoolSize, keepAliveTime, TimeUnit.SECONDS, new LinkedBlockingQueue<>(10));
    }

    //获取线程资源(自定义参数)
    public static ThreadPoolExecutor getThread(int coreSize,int maxPoolSize,int keepAliveTime){
        return new ThreadPoolExecutor(coreSize, maxPoolSize, keepAliveTime, TimeUnit.SECONDS, new LinkedBlockingQueue<>(10));
    }
}
