package com.caict.bsm.project.system.utils.util;

import java.util.Random;
import java.util.UUID;

/**
 * Created by dengsy on 2019-10-23.
 * 随机获取n位数字作为验证码
 */
public class VerificationCode {

    //根据字符串+时间戳+自定义位数随机码生成随机编码
    public static String idGet(String s, int n){
        StringBuffer stringBuffer = new StringBuffer(s);
        //拼接时间戳
        stringBuffer.append(System.currentTimeMillis());
        //拼接随机码
        stringBuffer.append(codeGet(n));
        return stringBuffer.toString();
    }

    //根据位数生成验证码
    public static String codeGet(int n) {
        if (n < 1 || n > 10) {
            throw new IllegalArgumentException("cannot random " + n + " bit number");
        }
        Random ran = new Random();
        if (n == 1) {
            return String.valueOf(ran.nextInt(10));
        }
        int bitField = 0;
        char[] chs = new char[n];
        for (int i = 0; i < n; i++) {
            while(true) {
                int k = ran.nextInt(10);
                if( (bitField & (1 << k)) == 0) {
                    bitField |= 1 << k;
                    chs[i] = (char)(k + '0');
                    break;
                }
            }
        }
        return new String(chs);
    }

    //获取UUID
    public static String myUUID(){
        return UUID.randomUUID().toString();
    }

    //获取32位UUID
    public static String myUUID32(){
        return UUID.randomUUID().toString().trim().replaceAll("-", "");
    }

    /**
     * 字符串前面补齐
     *  str：原字符串
     *  fill：补齐字符
     *  length： 补位数
     * */
    public static String fillStringBeforeString(String str ,String fill, int length){
        if(str.length() < length) {
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < length - str.length() ; i++){
                sb.append(fill);
            }
            sb.append(str);
            return sb.toString();
        }else {
            return str;
        }
    }

    /**
     * 字符串后面补齐
     *  str：原字符串
     *  fill：补齐字符
     *  length： 补位数
     * */
    public static String fillStringAfterString(String str ,String fill, int length){
        if(str.length() < length) {
            StringBuilder sb = new StringBuilder();
            sb.append(str);
            for (int i = 0; i < length - str.length() ; i++){
                sb.append(fill);
            }
            return sb.toString();
        }else {
            return str;
        }
    }
}
