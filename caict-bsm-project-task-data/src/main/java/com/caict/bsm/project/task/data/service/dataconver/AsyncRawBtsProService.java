package com.caict.bsm.project.task.data.service.dataconver;

import com.caict.bsm.project.domain.business.service.dataconver.AsyncRawBtsService;
import com.caict.bsm.project.system.model.entity.business.dataconver.AsyncRawBts;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/24
 */
@Service
public class AsyncRawBtsProService {

    @Autowired
    private AsyncRawBtsService asyncRawBtsService;

    /**
     * 批量添加
     * */
    public int insertBatch(List<AsyncRawBts> asyncRawBtsInsert){
        return asyncRawBtsService.insertBatch(asyncRawBtsInsert);
    }

    /**
     * 批量修改
     * */
    public int updateBatch(List<AsyncRawBts> asyncRawBtsUpdate){
        return asyncRawBtsService.updateBatch(asyncRawBtsUpdate);
    }

    /**
     * 批量删除
     * */
    public int deleteBatch(List<AsyncRawBts> asyncRawBtsDelete){
        List<Object> idList = new ArrayList<>();
        for (AsyncRawBts asyncRawBts :asyncRawBtsDelete){
            idList.add(asyncRawBts.getGuid());
        }
        return asyncRawBtsService.deleteBatch(idList);
    }

    /**
     * 根据运营商、数据状态删除
     * */
    public int deleteAllByUserDataType(String userId, String dataType){
        return asyncRawBtsService.deleteAllByUserDataType(userId,dataType);
    }

    /**
     * 根据btsIds 查询总表数据
     *
     * @param btsIds btsIds
     * @return list
     */
    public List<AsyncRawBts> findByBtsIds(List<String> btsIds) {
        return asyncRawBtsService.findByBtsIds(btsIds);
    }
}
