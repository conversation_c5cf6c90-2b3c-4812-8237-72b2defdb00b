package com.caict.bsm.project.task.data.service.station;

import com.caict.bsm.project.domain.business.service.station.RsbtFreqService;
import com.caict.bsm.project.system.model.entity.business.station.RsbtFreq;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * Created by dengsy on 2020-04-22.
 */
@Service
public class RsbtFreqProService {

    @Autowired
    private RsbtFreqService rsbtFreqService;

    /**
     * 批量添加
     * */
    public int insertBatch(List<RsbtFreq> rsbtFreqListInsert){
        return rsbtFreqService.insertBatch(rsbtFreqListInsert);
    }

    /**
     * 批量修改
     * */
    public int updateBatch(List<RsbtFreq> rsbtFreqListUpdate){
        return rsbtFreqService.updateBatch(rsbtFreqListUpdate);
    }

    /**
     * 批量删除
     */
    public int deleteBatch(List<RsbtFreq> rsbtFreqListUpdate){
        return rsbtFreqService.deleteBatch(Collections.singletonList(rsbtFreqListUpdate));
    }
}
