package com.caict.bsm.project.task.data.service.station;

import com.caict.bsm.project.domain.business.service.station.RsbtNetService;
import com.caict.bsm.project.system.model.entity.business.station.RsbtNet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class RsbtNetProService {

    @Autowired
    private RsbtNetService rsbtNetService;

    /**
     * 批量添加
     * */
    public int insertBatch(List<RsbtNet> rsbtNetListInsert){
        return rsbtNetService.insertBatch(rsbtNetListInsert);
    }

    /**
     * 批量修改
     * */
    public int updateBatch(List<RsbtNet> rsbtNetListInsert){
        return rsbtNetService.updateBatch(rsbtNetListInsert);
    }
}
