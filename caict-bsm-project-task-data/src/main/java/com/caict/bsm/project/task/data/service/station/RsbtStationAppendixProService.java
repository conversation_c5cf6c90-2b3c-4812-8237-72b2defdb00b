package com.caict.bsm.project.task.data.service.station;

import com.caict.bsm.project.domain.business.service.station.RsbtStationAppendixService;
import com.caict.bsm.project.system.model.entity.business.station.RsbtStationAppendix;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/23
 */
@Service
public class RsbtStationAppendixProService {

    @Autowired
    private RsbtStationAppendixService rsbtStationAppendixService;

    /**
     * 批量添加
     * */
    public int insertBatch(List<RsbtStationAppendix> rsbtStationAppendixListInsert){
        return rsbtStationAppendixService.insertBatch(rsbtStationAppendixListInsert);
    }

    /**
     * 批量修改
     * */
    public int updateBatch(List<RsbtStationAppendix> rsbtStationAppendixListUpdate){
        return rsbtStationAppendixService.updateBatch(rsbtStationAppendixListUpdate);
    }

    /**
     * 获取需要注销的基站数据
     * */
    public List<RsbtStationAppendix> findAllByNotSection(){
        return rsbtStationAppendixService.findAllByNotSection();
    }

    /**
     * 批量删除（修改状态）
     * */
    public int deleteBatchByStationGuid(List<RsbtStationAppendix> rsbtStationListDeleted){
        List<RsbtStationAppendix> rsbtStationAppendices = new ArrayList<>();
        for (RsbtStationAppendix rsbtStationAppendix : rsbtStationListDeleted){
            String guid = rsbtStationAppendix.getGuid();
            rsbtStationAppendix = new RsbtStationAppendix();
            rsbtStationAppendix.setGuid(guid);
            rsbtStationAppendix.setIsDeleted(1L);
            rsbtStationAppendices.add(rsbtStationAppendix);
        }
        return rsbtStationAppendixService.updateBatch(rsbtStationAppendices);
    }
}
