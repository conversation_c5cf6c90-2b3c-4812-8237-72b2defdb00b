server:
  port: 8082
  tomcat:
    basedir: D:/caict/tomcat

caict:
  myFilePath: D:\caict/bsm/3.0/file/
  myFilePathTmp: D:\caict/bsm/3.0/tmp/
  myFileExportPath: D:\caict/bsm/3.0/file/export/
  myRabbitMQ: caict.bsm
  mySqlInsertsNumber: 50
  mySqlDeleteNumber: 50

spring:
  application:
    name: caict-task-storm
  mvc:
    static-path-pattern: /**
  main:
    allow-circular-references: true
  resources:
    static-locations: classpath:/META-INF/resources/,classpath:/resources/,classpath:/static/,classpath:/public/,file:${caict.myFilePath}
  datasource:
    url: *************************************
    username: BSM_GZ
    password: caict
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      connection-timeout: 60000
      minimum-idle: 5
      maximum-pool-size: 10
      idle-timeout: 300000
      max-lifetime: 1200000
      auto-commit: true
      connection-test-query: SELECT 1 FROM DUAL
      validation-timeout: 3000
      read-only: false
      login-timeout: 5

mybatis:
  configuration:
    map-underscore-to-camel-case: true
