server:
  port: 8082
  tomcat:
    basedir: /home/<USER>

caict:
  myFilePath: /home/<USER>/caict/bsm/3.0/file/
  myFilePathTmp: /home/<USER>/caict/bsm/3.0/tmp/
  myFileExportPath: /home/<USER>/caict/bsm/3.0/file/export/
  myRabbitMQ: caict.bsm
  mySqlInsertsNumber: 50
  mySqlDeleteNumber: 50

spring:
  application:
    name: caict-task-storm
  mvc:
    static-path-pattern: /**
  resources:
    static-locations: classpath:/META-INF/resources/,classpath:/resources/,classpath:/static/,classpath:/public/,file:${caict.myFilePath}
  datasource:
    url: *****************************************
    username: BSM_UP
    password: caict
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      connection-timeout: 60000
      minimum-idle: 5
      maximum-pool-size: 10
      idle-timeout: 300000
      max-lifetime: 1200000
      auto-commit: true
      connection-test-query: SELECT 1 FROM DUAL
      validation-timeout: 3000
      read-only: false
      login-timeout: 5

mybatis:
  configuration:
    map-underscore-to-camel-case: true
