package com.caict.bsm.project.task.processing;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.data.elasticsearch.repository.config.EnableElasticsearchRepositories;
import org.springframework.web.client.RestTemplate;

@SpringBootApplication
@ComponentScan("com.caict.bsm.project")
@MapperScan({"com.caict.bsm.project.domain.security.mapper",
        "com.caict.bsm.project.domain.business.mapper",
        "com.caict.bsm.project.system.data.mapper"})
@EnableElasticsearchRepositories(basePackages = "com.caict.bsm")
public class ProcessingApplication {

    public static void main(String args[]){
        SpringApplication.run(ProcessingApplication.class,args);
    }

    /*@Bean
    public RestTemplate restTemplate(){
        return new RestTemplate();
    }*/
}
