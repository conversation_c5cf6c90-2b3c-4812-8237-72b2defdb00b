package com.caict.bsm.project.task.processing.component;

import com.alibaba.fastjson.JSONObject;
import com.caict.bsm.project.system.model.contrust.rabbit.RabbitMessageConst;
import com.caict.bsm.project.task.processing.service.license.BsmStationLicenseProService;
import com.caict.bsm.project.task.processing.service.transfer.ApprovalTransportJobProService;
import com.caict.bsm.project.task.processing.service.TransportJobProcessingService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created by dsy62 on 2019-05-06.
 */
@Component
@RabbitListener(queues = "task.processing.message")
public class ReceiverSimple {

    private static final Logger LOG = LoggerFactory.getLogger(ReceiverSimple.class);

    @Autowired
    private TransportJobProcessingService transportJobProcessingService;
    @Autowired
    private ApprovalTransportJobProService approvalTransportJobProService;
    @Autowired
    private BsmStationLicenseProService bsmStationLicenseProService;

    @RabbitHandler
    public void process(String rabbitString){
        LOG.info("接收到简单模式的消息："+rabbitString);
        if(rabbitString != null && !"".equals(rabbitString)){
            JSONObject jsonObject = JSONObject.parseObject(rabbitString);
            String type = jsonObject.getString("type");

            //CSV文件处理
            if (RabbitMessageConst.FILE_TYPE_CSV.equals(type)) transportJobProcessingService.processing(jsonObject.getString("content"),jsonObject.getString("from"),jsonObject.getString("remark"));

            //审核同意数据处理
            if (RabbitMessageConst.JOB_APPROVVAL_PROCESSING_INSERT.equals(type)) approvalTransportJobProService.approve(jsonObject.getString("content"),jsonObject.getString("from"),"1");
            if (RabbitMessageConst.JOB_APPROVVAL_PROCESSING_UPDATE.equals(type)) approvalTransportJobProService.approve(jsonObject.getString("content"),jsonObject.getString("from"),"2");
            if (RabbitMessageConst.JOB_APPROVVAL_PROCESSING_DELETE.equals(type)) approvalTransportJobProService.approve(jsonObject.getString("content"),jsonObject.getString("from"),"3");
            if (RabbitMessageConst.JOB_APPROVVAL_PROCESSING_CONTINUE.equals(type)) approvalTransportJobProService.approve(jsonObject.getString("content"),jsonObject.getString("from"),"6");

            //生成执照
            if (RabbitMessageConst.STATION_lICENSE.equals(type)) bsmStationLicenseProService.bsmStationLicense();
        }
    }
}
