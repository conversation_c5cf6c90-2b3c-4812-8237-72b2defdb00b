package com.caict.bsm.project.task.processing.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.caict.bsm.project.system.model.dto.business.rule.FsaCheckRuleDTO;
import com.caict.bsm.project.system.model.dto.business.transfer.NetTsValidDTO;
import com.caict.bsm.project.system.model.entity.business.transfer.TransportRawBts;
import com.caict.bsm.project.system.utils.service.RedisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 频段校验规则
 * Created by dengsy on 2020-04-22.
 * */
@Service
public class BtsDataCheckRuleService {

    @Autowired
    private RedisService redisService;

    private JSONObject checkRuleRedisJson;
    private JSONObject genNumRedisJson;

    public void init(){
        //获取校验规则
        this.checkRuleRedisJson = JSONObject.parseObject(redisService.get("Caict:checkRule:netTs"));
        this.genNumRedisJson = JSONObject.parseObject(redisService.get("Caict:checkRule:genNum"));
    }

    /**
     * 检查制式频段是否符合要求
     * 数据来源 校验规范 暂存redis
     * */
    public NetTsValidDTO validParameter(TransportRawBts transportRawBts, String userType){
        NetTsValidDTO netTsValidDTO = new NetTsValidDTO();
        if (this.checkRuleRedisJson != null){
            int i = 0;
            JSONObject netTsJson = checkRuleRedisJson.getJSONObject(userType);
            if (netTsJson != null){
                String netTs = transportRawBts.getTechType();
                //获取规则
                List<FsaCheckRuleDTO> fsaCheckRuleDTOList = JSONArray.parseArray(netTsJson.getString(netTs),FsaCheckRuleDTO.class);
                if (fsaCheckRuleDTOList != null && fsaCheckRuleDTOList.size() > 0){
                    for (FsaCheckRuleDTO fsaCheckRuleDTO : fsaCheckRuleDTOList){
                        //发射频率下限
                        Double freqEfb = Double.valueOf(transportRawBts.getSendStartFreq());
                        //发射频率上限
                        Double freqEfe = Double.valueOf(transportRawBts.getSendEndFreq());
                        //接收频率下限
                        Double freqRfe = Double.valueOf(transportRawBts.getAccEndFreq());
                        //接收频率上限
                        Double freqRfb = Double.valueOf(transportRawBts.getAccStartFreq());
                        if ((fsaCheckRuleDTO.getFreqEfb() <= freqEfb && fsaCheckRuleDTO.getFreqEfe() >= freqEfe) && (fsaCheckRuleDTO.getFreqRfb() <= freqRfb && fsaCheckRuleDTO.getFreqRfe() >= freqRfe)){
                            i = 0;
                            netTsValidDTO.setExpireDate(fsaCheckRuleDTO.getExpireDate());
                            break;
                        }else {
                            i = 1;
                        }
                    }
                }else {
                    netTsValidDTO.setMessage("规则获取失败，请联系管理员");
                    return netTsValidDTO;
                }
            }
            //判断不符合所有条件
            if (i == 1){
                netTsValidDTO.setMessage("频段错误，"+userType+"运营商的"+transportRawBts.getTechType()+",频段不符合最新规范");
                return netTsValidDTO;
            }
        }else {
            netTsValidDTO.setMessage("规则获取失败，请联系管理员");
            return netTsValidDTO;
        }
        return netTsValidDTO;
    }

    /**
     * 制式对应信号
     * */
    public String genParameter(String userType,String techType){
        //获取校验规则
        this.genNumRedisJson = JSONObject.parseObject(redisService.get("Caict:checkRule:genNum"));
        if (this.genNumRedisJson != null){
            JSONObject genNumJson = genNumRedisJson.getJSONObject(userType);
            if (genNumJson != null){
                return genNumJson.getString(techType);
            }
        }
        return null;
    }
}
