package com.caict.bsm.project.task.processing.service;

import com.alibaba.fastjson.JSONObject;
import com.caict.bsm.project.datacenter.realtime.service.RabbitService;
import com.caict.bsm.project.system.model.contrust.DBBoolConst;
import com.caict.bsm.project.system.model.contrust.rabbit.RabbitMessageConst;
import com.caict.bsm.project.system.model.contrust.transfer.LogTransportJobTypeConst;
import com.caict.bsm.project.system.model.contrust.transfer.TransportFileStateConst;
import com.caict.bsm.project.system.model.dto.business.taskdata.ProcessingDataDTO;
import com.caict.bsm.project.system.model.dto.business.transfer.NetTsValidDTO;
import com.caict.bsm.project.system.model.dto.business.transfer.TransportRawBtsCsvDTO;
import com.caict.bsm.project.system.model.dto.security.UsersDTO;
import com.caict.bsm.project.system.model.entity.business.transfer.LogTransportJob;
import com.caict.bsm.project.system.model.entity.business.transfer.TransportFile;
import com.caict.bsm.project.system.model.entity.business.transfer.TransportJob;
import com.caict.bsm.project.system.model.entity.business.transfer.TransportRawBts;
import com.caict.bsm.project.system.model.entity.message.rabbit.RabbitMessage;
import com.caict.bsm.project.system.utils.service.RedisService;
import com.caict.bsm.project.system.utils.util.DateUtils;
import com.caict.bsm.project.system.utils.util.RedisLock;
import com.caict.bsm.project.system.utils.util.VerificationCode;
import com.caict.bsm.project.task.processing.service.transfer.LogTransportJobProService;
import com.caict.bsm.project.task.processing.service.transfer.TransportFileProService;
import com.caict.bsm.project.task.processing.service.transfer.TransportJobProService;
import com.caict.bsm.project.task.processing.util.BtsDataUtil;
import com.opencsv.bean.CsvToBeanBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.FileInputStream;
import java.io.InputStreamReader;
import java.io.Reader;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class TransportJobProcessingService {

    private static final Logger LOG = LoggerFactory.getLogger(TransportJobProcessingService.class);
    @Autowired
    private BtsDataCheckRuleService btsDataCheckRuleService;
    @Autowired(required = false)
    private RedisService redisService;
    @Autowired
    private RabbitService rabbitService;
    @Autowired
    private TransportFileProService transportFileProService;
    @Autowired
    private TransportJobProService transportJobProService;
    @Autowired
    private LogTransportJobProService logTransportJobProService;

    @Value("${caict.key.processingCSV}")
    private String processingCSVKey;
    @Value("${caict.myRabbitKey.taskData}")
    private String taskDataKey;
    @Value("${caict.key.processingDataSchedule}")
    private String processingDataScheduleKey;
    @Value("${caict.myRabbitKey.taskProcessing}")
    private String taskProcessingKey;
    @Value("${caict.myFilePath}")
    private String myFilePath;

    /**
     * csv文件在线处理
     * */
    public Map<String,Integer> processing(String fileId, String usersDtoJson,String dataType){
        //初始化规则
        btsDataCheckRuleService.init();
        //获取用户信息
        UsersDTO usersDTO = JSONObject.parseObject(usersDtoJson, UsersDTO.class);
        String orgType = usersDTO.getType();

        //获取校验规则
        JSONObject checkRuleRedisJson = JSONObject.parseObject(redisService.get("Caict:checkRule:netTs"));
        JSONObject netTsJson = checkRuleRedisJson.getJSONObject(orgType);

        Map<String,Integer> map = new HashMap<String,Integer>();
        map.put("result",0);    //返回结果：0为失败，1为成功
        map.put("csvSize",0);   //csv文件大小
        map.put("type",0);      //类型：0为未校验每条数据，1为已校验每条数据

        String orgRegionJson = redisService.get("Caict:regionCounty:orgRegion");
        if (orgRegionJson == null){
            LOG.error("未获取到系统所属省的所有行政区信息");
            return map;
        }

        TransportFile transportFile = transportFileProService.findById(fileId);
        if (transportFile == null) return map;
        TransportJob transportJob = transportJobProService.findById(transportFile.getJobGuid());
        if (transportJob == null) return map;
        if ("2".equals(String.valueOf(transportFile.getFileState()))){
            logTransportJobProService.addLog(transportJob.getGuid(), LogTransportJobTypeConst.ERROR, "csv文件异常", "csv文件已经处理",fileId);
            map.put("result",0);
            map.put("type",0);
            return map;
        }
        try{
            //从csv文件中读取数据
            Reader reader = new InputStreamReader(new FileInputStream(myFilePath + transportFile.getFilePath()), "GBK");
            List<TransportRawBtsCsvDTO> transportRawBtsCsvDTOList = new CsvToBeanBuilder<TransportRawBtsCsvDTO>(reader).withType(TransportRawBtsCsvDTO.class).build().parse();
            int csvSize = transportRawBtsCsvDTOList.size();
            reader.close();
            //序列化,否者多管道处理将导致数据丢失
            List<TransportRawBts> transportRawBtsList = Collections.synchronizedList(new ArrayList<TransportRawBts>());
            List<LogTransportJob> logTransportJobList = Collections.synchronizedList(new ArrayList<LogTransportJob>());

            //根据cellId去重
            List<TransportRawBtsCsvDTO> transportRawBtsDTOListR = transportRawBtsCsvDTOList.stream().collect(
                    Collectors.collectingAndThen(
                            Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(TransportRawBtsCsvDTO::getCellId))), ArrayList::new)
            );

            transportRawBtsDTOListR.parallelStream().forEach(t -> {
                TransportRawBts transportRawBts = new TransportRawBts();
                //转换bean
                BeanUtils.copyProperties(t,transportRawBts);
                transportRawBts.setGuid(VerificationCode.myUUID());
                transportRawBts.setJobGuid(transportJob.getGuid());
                transportRawBts.setIsHandle("0");
                transportRawBts.setIsDownload("0");
                transportRawBts.setUserGuid(usersDTO.getUserId());
                transportRawBts.setUploadDate(new Date());
                transportRawBts.setUploadFlag("1");
                transportRawBts.setFileGuid(fileId);
                transportRawBts.setOrgType(orgType);
                transportRawBts.setDataType(dataType);
                if (t.getTrfUser()!=null){
                    transportRawBts.setTrfUser(Double.parseDouble(t.getTrfUser()));
                }
                if (t.getTrfData()!=null){
                    transportRawBts.setTrfData(Double.parseDouble(t.getTrfData()));
                }
                // 修改成数字
                if ("直放站".equals(transportRawBts.getExpandStation())) {
                    transportRawBts.setExpandStation("2");
                } else if ("宏站".equals(transportRawBts.getExpandStation())) {
                    transportRawBts.setExpandStation("1");
                }
                if ("室内站".equals(transportRawBts.getAttributeStation())) {
                    transportRawBts.setAttributeStation("1");
                } else if ("室外站".equals(transportRawBts.getAttributeStation())) {
                    transportRawBts.setAttributeStation("2");
                }

                //校验参数
                Map<String,Object> parameterErrorMessageMap = BtsDataUtil.validParameter(transportRawBts,netTsJson,orgRegionJson);
                String isValid = (String) parameterErrorMessageMap.get("isValid");
                if ("4".equals(isValid)) {
                    List<String> parameterErrorMessageList = (List) parameterErrorMessageMap.get("parameterErrorMessage");
                    for (String parameterErrorMessage : parameterErrorMessageList){
                        //写入错误日志
                        logTransportJobList.add(logTransportJobProService.addLogValue(transportJob.getGuid(), LogTransportJobTypeConst.ERROR, "文件格式错误", "基站id（"+transportRawBts.getBtsId()+"）的数据参数校验。" + parameterErrorMessage,fileId,transportRawBts.getBtsId(),transportRawBts.getCellId()));
                    }
                    transportRawBts.setIsValid(DBBoolConst.WARNING);
                }else {
                    //检查频段
                    NetTsValidDTO netTsValidDTO = btsDataCheckRuleService.validParameter(transportRawBts,usersDTO.getType());
                    if (netTsValidDTO.getMessage() != null){
                        logTransportJobList.add(logTransportJobProService.addLogValue(transportRawBts.getJobGuid(), LogTransportJobTypeConst.ERROR,"数据校验过程存在异常",netTsValidDTO.getMessage(),transportRawBts.getFileGuid(),transportRawBts.getBtsId(),transportRawBts.getCellId()));
                        Calendar cal = Calendar.getInstance();
                        cal.add(Calendar.YEAR, 3);
                        transportRawBts.setExpireDate(cal.getTime());
                        transportRawBts.setIsValid(DBBoolConst.DOUBT);
                    }else {
                        transportRawBts.setExpireDate(netTsValidDTO.getExpireDate());
                        transportRawBts.setIsValid(DBBoolConst.TURE);
                    }

                    //判断制式插入
                    if (getGen(usersDTO.getType(),transportRawBts.getTechType(),transportRawBts.getJobGuid(),transportRawBts.getFileGuid(),transportRawBts,logTransportJobList) != null){
                        transportRawBts.setIsValid(DBBoolConst.WARNING);
                    }
                }

                transportRawBtsList.add(transportRawBts);
            });

            //批量传递数据
            int  transportRawBtsNo = 0;
            int logTransportNo = 0;
            if (transportRawBtsList != null) transportRawBtsNo = transportRawBtsList.size();
            if (logTransportJobList != null) logTransportNo = logTransportJobList.size();
            if (transportRawBtsNo != 0 || logTransportNo != 0){
                //创建key
                String key = "processing.csv.data." + fileId;
                //每10000条数据一批次
                int rows = 10000;
                int count = (transportRawBtsNo > logTransportNo ? transportRawBtsNo : logTransportNo);

                //创建同步锁
                int pageLock = count / rows;
                if(count % rows > 0){
                    pageLock += 1;
                }
                RedisLock redisLock = new RedisLock();
                redisLock.setLock("1");
                redisLock.setContent(pageLock);
                //存入处理数量锁
                redisService.set(key + "number",JSONObject.toJSONString(redisLock));

                int page = 0;
                do {
                    //封装数据传递
                    ProcessingDataDTO processingDataDTO = new ProcessingDataDTO();
                    if ((page * rows) <= transportRawBtsNo) processingDataDTO.setTransportRawBtsList(transportRawBtsList.subList(page * rows,transportRawBtsNo < ((page + 1) * rows) ? transportRawBtsNo : (page + 1) * rows));
                    if ((page * rows) <= logTransportNo) processingDataDTO.setLogTransportJobList(logTransportJobList.subList(page * rows,logTransportNo < ((page + 1) * rows) ? logTransportNo : (page + 1) * rows));
                    //存入redis
                    redisService.lpush(key, JSONObject.toJSON(processingDataDTO));
                    //发送rabbit任务启动入库操作
                    sendRabbit(taskDataKey,"CSV文件数据入库",key,dataType, RabbitMessageConst.JOB_COMMIT_CSV_DATA,fileId);
                    page ++;
                }while (page < pageLock);
            }

            map.put("result",1);
            map.put("type",1);
            map.put("csvSize",csvSize);
            return map;
        }catch (Exception e){
            e.printStackTrace();
            StringBuffer errorDetailStringBuffer = new StringBuffer("文件格式不符合《综合资源系统与无委外部接口需求说明书》。");
            logTransportJobProService.addLog(transportJob.getGuid(), LogTransportJobTypeConst.ERROR, "csv文件格式错误", errorDetailStringBuffer.toString(),fileId);
            transportFile.setFileState(TransportFileStateConst.UPLOAD_FAILURE);
            transportFileProService.update(transportFile);
            map.put("result",0);
            map.put("type",0);
            return map;
        }
    }

    public String getGen(String userType,String techType,String jobGuid,String fielGuid,TransportRawBts transportRawBts,List<LogTransportJob> logTransportJobList){
        String genNum = btsDataCheckRuleService.genParameter(userType,techType);
        if (genNum != null){
            transportRawBts.setGenNum(genNum);
            return null;
        }else {
            logTransportJobList.add(logTransportJobProService.addLogValue(jobGuid, LogTransportJobTypeConst.ERROR,"数据校验过程存在异常","未检测到制式对应的信号",fielGuid,transportRawBts.getBtsId(),transportRawBts.getCellId()));
            return "数据校验过程存在异常";
        }
    }


    //发送rabbit
    protected void sendRabbit(String key,String title,String content,String remark,String type,String from){
        RabbitMessage rabbitMessage = new RabbitMessage(VerificationCode.idGet("rabbit",4),title,content, DateUtils.getDateTime(),from,remark,type);
        rabbitService.sendSimple(key,JSONObject.toJSONString(rabbitMessage));
    }

}
