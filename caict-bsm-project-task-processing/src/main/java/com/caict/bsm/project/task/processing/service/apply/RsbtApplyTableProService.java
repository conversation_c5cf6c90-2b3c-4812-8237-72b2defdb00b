package com.caict.bsm.project.task.processing.service.apply;

import com.caict.bsm.project.domain.business.service.applytable.RsbtApplyAppendixService;
import com.caict.bsm.project.domain.business.service.applytable.RsbtApplyService;
import com.caict.bsm.project.domain.business.service.license.RuleCodeService;
import com.caict.bsm.project.domain.business.service.station.RsbtStationAppendixService;
import com.caict.bsm.project.domain.business.service.station.RsbtStationService;
import com.caict.bsm.project.system.model.dto.business.station.StationDTO;
import com.caict.bsm.project.system.model.entity.business.applytable.RsbtApply;
import com.caict.bsm.project.system.model.entity.business.applytable.RsbtApplyAppendix;
import com.caict.bsm.project.system.model.entity.business.license.RuleCode;
import com.caict.bsm.project.system.model.entity.business.station.RsbtStation;
import com.caict.bsm.project.system.model.entity.business.station.RsbtStationAppendix;
import com.caict.bsm.project.system.utils.util.ListUtil;
import com.caict.bsm.project.system.utils.util.VerificationCode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/9/16
 */
@Service
public class RsbtApplyTableProService {

    @Autowired
    private RsbtStationService rsbtStationService;
    @Autowired
    private RsbtStationAppendixService rsbtStationAppendixService;
    @Autowired
    private RuleCodeService ruleCodeService;
    @Autowired
    private RsbtApplyService rsbtApplyService;
    @Autowired
    private RsbtApplyAppendixService rsbtApplyAppendixService;
    /**
     * 申请表自动生成
     * */
    public void bsmApplyTableAutomatic(){
        //查询未生成申请表的基站数据
        List<StationDTO> stationDTOList = rsbtStationService.selectAllNotApplyTable();
        if (stationDTOList!=null && stationDTOList.size()!=0){
            List<RuleCode> ruleList = ruleCodeService.selectList();
            Map<String, RuleCode> ruleMap = ruleList.stream().collect(Collectors.toMap(RuleCode::getRuleType, Function.identity()));
            //以一个申请表最多9000条基站数据创建申请表
            for (StationDTO stationDTO:stationDTOList){
                RuleCode ruleCode = createFileNo(ruleMap);
                addApplyTable(stationDTO,ruleCode);
            }
        }
    }

    private void addApplyTable(StationDTO stationDTO,RuleCode ruleCode) {
        List<RsbtStation> stationTemp = Collections.synchronizedList(new ArrayList<RsbtStation>());
        List<RsbtStationAppendix> stationAppendixTemp = Collections.synchronizedList(new ArrayList<RsbtStationAppendix>());
        //将基站集合按9000分组
        List<List<RsbtStation>> lists = ListUtil.subList(stationDTO.getRsbtStations(), 9000);
        int num = ruleCode.getNextNum();
        for (List<RsbtStation> stations:lists){
            String appCode = "520000"+ruleCode.getYear()+String.format("%04d", num);
            addNewApply(appCode,stations.size(),stationDTO);
            stations.parallelStream().forEach(station->{
                station.setAppCode(appCode);

                RsbtStationAppendix rsbtStationAppendix = new RsbtStationAppendix();
                rsbtStationAppendix.setGuid(station.getGuid());
                rsbtStationAppendix.setIsApply("1");

                stationTemp.add(station);
                stationAppendixTemp.add(rsbtStationAppendix);
            });
            rsbtStationService.updateBatch(stationTemp);
            rsbtStationAppendixService.updateBatch(stationAppendixTemp);
            num++;
        }
        ruleCode.setNextNum(num);
        ruleCodeService.updateById(ruleCode);
    }

    private void addNewApply(String appCode,int count,StationDTO stationDTO) {
        RsbtApply rsbtApply = new RsbtApply();
        RsbtApplyAppendix applyAppendix = new RsbtApplyAppendix();

        //todo net_guid还需斟酌
        rsbtApply.setNetGuid("1111111");
        rsbtApply.setOrgGuid(stationDTO.getOrgId());
        rsbtApply.setAppCode(appCode);
        //申请表类型为T：台站
        rsbtApply.setAppType("T");
        //申请类型为暂时为0：新增
        rsbtApply.setAppSubType("0");
        //申请对象类型为1：台站
        rsbtApply.setAppObjectType("1");
        rsbtApply.setAppDate(new Date());
        rsbtApply.setAppFtlb(new Date());
        rsbtApply.setAppFtle(new Date());
        String guid = rsbtApplyService.save(rsbtApply);
        if (guid!=null){
            applyAppendix.setGuid(guid);
            applyAppendix.setIsDeleted(Long.valueOf(0));
            applyAppendix.setAppName("申请表"+appCode);
            applyAppendix.setNetType(stationDTO.getNetType());
            applyAppendix.setStationCount(Long.valueOf(count));
            applyAppendix.setIsSync("0");
            rsbtApplyAppendixService.saveOne(applyAppendix);
        }

    }

    /**
     * 创建编号
     *
     * @return
     */
    public RuleCode createFileNo(Map<String, RuleCode> ruleMap) {
        RuleCode rule = ruleMap.get("applyCode");
        Integer num = rule.getNextNum();
        Integer noYear = rule.getYear(); // 目前编号的年份
        Integer year = LocalDateTime.now().getYear(); // 当前年份
        if (year > noYear) {
            num = 1;
            rule.setYear(year);
        }
        rule.setNextNum(num);
        return rule;
    }
}
