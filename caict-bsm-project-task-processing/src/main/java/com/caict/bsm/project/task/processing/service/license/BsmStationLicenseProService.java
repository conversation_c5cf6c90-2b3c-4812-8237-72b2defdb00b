package com.caict.bsm.project.task.processing.service.license;

import com.caict.bsm.project.domain.business.service.es.RsbtLicenseMainWebService;
import com.caict.bsm.project.domain.business.service.license.LicenseService;
import com.caict.bsm.project.domain.business.service.license.LicenseTService;
import com.caict.bsm.project.domain.business.service.license.RuleCodeService;
import com.caict.bsm.project.domain.business.service.station.RsbtStationService;
import com.caict.bsm.project.system.model.contrust.license.LicenseStateConst;
import com.caict.bsm.project.system.model.dto.business.station.StationLicenseDTO;
import com.caict.bsm.project.system.model.entity.business.license.RsbtLicense;
import com.caict.bsm.project.system.model.entity.business.license.RsbtLicenseT;
import com.caict.bsm.project.system.model.entity.business.license.RuleCode;
import com.caict.bsm.project.system.model.es.RsbtLicenseMainES;
import com.caict.bsm.project.system.utils.util.DateUtils;
import com.caict.bsm.project.system.utils.util.VerificationCode;
import com.caict.bsm.project.task.processing.service.transfer.AsyncRawBtsProService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class BsmStationLicenseProService {

    @Autowired
    private RsbtStationService rsbtStationService;
    @Autowired
    private RuleCodeService ruleCodeService;
    @Autowired
    private LicenseService licenseService;
    @Autowired
    private LicenseTService licenseTService;
    @Autowired
    private AsyncRawBtsProService asyncRawBtsProService;
    @Autowired
    private RsbtLicenseMainWebService rsbtLicenseMainWebService;

    private Integer num = 0;

    public void bsmStationLicenseAutomatic(){
        Thread t = new Thread(new Runnable(){
            public void run(){
                bsmStationLicense();
            }});
        t.start();
    }

    /**
     * 生成执照
     * */
    public void bsmStationLicense(){
        List<StationLicenseDTO> rsbtStationList = rsbtStationService.selectAllNotLicense();
        if(rsbtStationList.size() > 0){
            List<RsbtLicense> bsmStationLicenseList = new ArrayList<RsbtLicense>();
            List<RsbtLicenseT> licenseTList = new ArrayList<RsbtLicenseT>();
            List<RsbtLicenseMainES> rsbtLicenseMainESList = new ArrayList<RsbtLicenseMainES>();
            //开始生成执照编号，这里考虑数据量过大时，避免重复查询数据库
            List<RuleCode> ruleList = ruleCodeService.selectList();
            Map<String, RuleCode> ruleMap = ruleList.stream().collect(Collectors.toMap(RuleCode::getRuleType, Function.identity()));
            RuleCode rule = createFileNo(ruleMap);
            num = rule.getNextNum();

            for(StationLicenseDTO rsbtStation : rsbtStationList){
                if(num < 9999999){
                    String guid = VerificationCode.myUUID();
                    num++;
                    RsbtLicense bsmStationLicense = new RsbtLicense();
                    RsbtLicenseT licenseT = new RsbtLicenseT();
                    RsbtLicenseMainES rsbtLicenseMainES = new RsbtLicenseMainES();
                    bsmStationLicense.setGuid(guid);
                    licenseT.setGuid(guid);
                    Date current = new Date();
                    bsmStationLicense.setStationGuid(rsbtStation.getGuid());
                    //String code = applyTable.getApplytableCode() + String.format("/C%04d", lastLicenseCount);
                    String code = rule.getYear()+"S5200FB";
                    code = code +String.format("%07d", num);
                    bsmStationLicense.setLicenseCode(code);
                    bsmStationLicense.setLicenseDate(current);
//                    bsmStationLicense.setLicenseDate(current);
                    bsmStationLicense.setLicenseDateB(current);
                    Date expireDate = getExpireDate(rsbtStation);
                    if (expireDate==null){
                        bsmStationLicense.setLicenseType("1");//纸质
                        Calendar cal = Calendar.getInstance();
                        cal.add(Calendar.YEAR, 3);//增加三年
                        bsmStationLicense.setLicenseDateE(cal.getTime());
                        rsbtLicenseMainES.setLicenseDateE(DateUtils.dateToLocalDateTime(cal.getTime()));
                    }else {
                        bsmStationLicense.setLicenseDateE(expireDate);
                        rsbtLicenseMainES.setLicenseDateE(DateUtils.dateToLocalDateTime(expireDate));
                    }
                    licenseT.setLicenseState(1L);
                    licenseT.setIsDownLoad(LicenseStateConst.LICENSE_UNDOWNLOAD);
                    licenseT.setGuid(bsmStationLicense.getGuid());
                    licenseT.setLicenseCounty(rsbtStation.getCounty());
                    licenseT.setTechType(rsbtStation.getTechType());
                    licenseT.setOrgType(rsbtStation.getOrgType());

                    //es插入执照
                    rsbtLicenseMainES.setGuid(guid);
                    rsbtLicenseMainES.setApplyTableCode(rsbtStation.getAppCode());
                    rsbtLicenseMainES.setOrgName(rsbtStation.getOrgType());
                    rsbtLicenseMainES.setLicenseCode(code);
                    rsbtLicenseMainES.setLocation(rsbtStation.getStatAddr());
                    rsbtLicenseMainES.setStationGuid(rsbtStation.getGuid());
                    rsbtLicenseMainES.setStationName(rsbtStation.getStatName());
                    rsbtLicenseMainES.setStationCode(rsbtStation.getStCCode());
                    rsbtLicenseMainES.setLatitude(String.valueOf(rsbtStation.getStatLa()));
                    rsbtLicenseMainES.setLongitude(String.valueOf(rsbtStation.getStatLg()));
                    rsbtLicenseMainES.setIsDownload("2");
                    rsbtLicenseMainES.setLicenseState("1");
                    rsbtLicenseMainES.setCounty(rsbtStation.getCounty());
                    rsbtLicenseMainES.setNetType(rsbtStation.getTechType());
                    rsbtLicenseMainES.setLicenseDate(DateUtils.dateToLocalDateTime(current));
                    rsbtLicenseMainES.setLicenseDateB(DateUtils.dateToLocalDateTime(current));

                    if(num == 9999999){
                        break;
                    }
//                    insert(bsmStationLicense);
                    bsmStationLicenseList.add(bsmStationLicense);
                    licenseTList.add(licenseT);
                    //修改基站执照状态为已发布
                    //rsbtStation.setIsLicense("1");
                    rsbtLicenseMainESList.add(rsbtLicenseMainES);
                }
            }
            //添加执照
            licenseService.insertBatch(bsmStationLicenseList);
            licenseTService.insertBatch(licenseTList);
            rsbtLicenseMainWebService.saveBatch(rsbtLicenseMainESList);
            updateStation(rsbtStationList);
            rule.setNextNum(num);
            ruleCodeService.updateById(rule);
        }
    }

    /**
     * 批量修改基站表
     * */
    public void updateStation(List<StationLicenseDTO> rsbtStationList){
//        for (RsbtStation rsbtStation : rsbtStationList){
//            rsbtStationService.save(rsbtStation);
//        }
        rsbtStationService.updateStationLicenseStatus(rsbtStationList);
    }

    /**
     * 创建编号
     *
     * @return
     */
    public RuleCode createFileNo(Map<String, RuleCode> ruleMap) {
        RuleCode rule = ruleMap.get("LicenseCode");
        Integer num = rule.getNextNum();
        Integer noYear = rule.getYear(); // 目前编号的年份
        Integer year = LocalDateTime.now().getYear(); // 当前年份
        if (year > noYear) {
            num = 1;
            rule.setYear(year);
        }
        rule.setNextNum(num);
        return rule;
    }

    /**
     * 获取执照到期时间
     */
    public Date getExpireDate(StationLicenseDTO rsbtStation){
        //获取此基站下频率最近的到期时间
        return asyncRawBtsProService.getExpireDate(rsbtStation.getStCCode());
    }
}
