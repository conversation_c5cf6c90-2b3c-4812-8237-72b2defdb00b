package com.caict.bsm.project.task.processing.service.station;

import com.caict.bsm.project.domain.business.service.twotable.EarthTableService;
import com.caict.bsm.project.system.model.dto.business.keystation.StationInterfereDTO;
import com.caict.bsm.project.system.model.entity.business.transfer.ApprovalSchedule;
import com.caict.bsm.project.system.model.entity.business.twotable.EarthTable;
import com.caict.bsm.project.system.utils.util.CalculationUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Service
public class InterfereProService {

    @Autowired
    private EarthTableService earthTableService;

    private static int effectiveDistance;

    private static boolean result = true;

    public StationInterfereDTO interfere(List<ApprovalSchedule> approvalScheduleList,String genNum) {
        try {
            //List<ApprovalSchedule> approvalSchedules = Collections.synchronizedList(new ArrayList<>());
            StationInterfereDTO stationInterfereDTO = new StationInterfereDTO();
            //初始值
            stationInterfereDTO.setStatus("1");
            stationInterfereDTO.setIsValid("1");
            approvalScheduleList.parallelStream().forEach(approvalSchedule -> {
                if("5".equals(genNum)){
                    //基站经度
                    String staLongitude = approvalSchedule.getLongitude();
                    //基站纬度
                    String staLatitude = approvalSchedule.getLatitude();

                    List<EarthTable> earthTableList = earthTableService.findAll();

                    for (EarthTable earthTable : earthTableList) {
                        //地球站经度
                        String earthLongitude = earthTable.getLongitude();
                        //地球站纬度
                        String earthLatitude = earthTable.getLatitude();

                        //地球站与基站经纬度之间的距离
                        double distance = CalculationUtil.getDistance(staLongitude, staLatitude, earthLongitude, earthLatitude);

                        String freqRfb = "";
                        if (earthTable.getFreqRfb().contains(" ")) {
                            String[] s = earthTable.getFreqRfb().split(" ");
                            freqRfb = s[1];
                        }else {
                            freqRfb =  earthTable.getFreqRfb();
                        }
                        double freq = Double.parseDouble(freqRfb);
                        //是否改造
                        String newTech = "0";

                        Double accStartFreq = Double.parseDouble(approvalSchedule.getAccStartFreq());
                        Double accEndFreq = Double.parseDouble(approvalSchedule.getAccEndFreq());
                        if (3400 <= accStartFreq && accEndFreq <= 3600) {
                            if (freq >= 3400 && freq <= 3600) {
                                effectiveDistance = 42500;
                            } else if (freq >= 3600 && freq <= 3700) {
                                effectiveDistance = 4000;
                            } else if (freq >= 3700 && freq <= 4200) {
                                if ("1".equals(newTech)) {
                                    effectiveDistance = 100;
                                } else if ("0".equals(newTech)) {
                                    effectiveDistance = 2000;
                                }
                            }else {
                                effectiveDistance = 15000;
                            }
                        } else if (4800 <= accStartFreq && accEndFreq <= 4900) {
                            if (freq >= 4500 && freq <= 4800) {
                                if ("1".equals(newTech)) {
                                    effectiveDistance = 100;
                                } else if ("0".equals(newTech)) {
                                    effectiveDistance = 2000;
                                }
                            }else {
                                effectiveDistance = 15000;
                            }
                        }else {
                            effectiveDistance = 15000;
                        }

                        if (!judgeResult(effectiveDistance, distance)){
                            result = false;
                            stationInterfereDTO.setStatus("2");
                            break;
                        }
                    }
                    approvalSchedule.setAnalysisStatus(result?"1":"2");
                }else {
                    approvalSchedule.setAnalysisStatus("1");
                }
                if (approvalSchedule.getIsValid()==3){
                    stationInterfereDTO.setIsValid("3");
                }

            });
            stationInterfereDTO.setApprovalScheduleList(approvalScheduleList);
            return stationInterfereDTO;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     *判断是否在干扰范围之内
     */
    public boolean judgeResult(int effectiveDistance,double distance){
        if ((double)effectiveDistance>=distance){
            return false;
        }
        return true;
    }
}
