package com.caict.bsm.project.task.processing.service.station;

import com.caict.bsm.project.domain.business.service.station.RsbtStationService;
import com.caict.bsm.project.domain.security.service.RegionService;
import com.caict.bsm.project.system.model.dto.security.RegionDTO;
import com.caict.bsm.project.system.model.entity.business.station.RsbtStation;
import com.caict.bsm.project.system.utils.util.GPSTransform;
import com.caict.bsm.project.system.utils.util.IsInPolygonUtil;
import javafx.geometry.Point2D;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/24
 */
@Service
public class RsbtStationProService {

    @Autowired
    private RsbtStationService rsbtStationService;
    @Autowired
    private RegionService regionService;

    /**
     * 根据基站识别号下载
     * */
    public RsbtStation querySattionByBts(String bts, String techType, String orgCode){
        return rsbtStationService.querySattionByBts(bts,techType,orgCode);
    }

    /**
     * 判断是否与自身上报的区域相符合
     */
    public boolean judgeIsArea(String regionCode, Double latitude, Double longitude){
        try {
            RegionDTO regionDTO = regionService.findOneByCode(regionCode);
            double[] p1d = GPSTransform.bd09_To_gps84(latitude,longitude);
            Point2D p1 =new Point2D(p1d[0],p1d[1]);
            String str = regionDTO.getCoordinate();
            String[] trs = str.split(";");
            List<Point2D> pts = new ArrayList<>();
            for (String tr : trs) {
                String[] trbs = tr.split(",");
                double[] p3d = GPSTransform.bd09_To_gps84(Double.parseDouble(trbs[1].trim()), Double.parseDouble(trbs[0].trim()));
                Point2D p3 = new Point2D(p3d[0], p3d[1]);
                pts.add(p3);
            }
            return IsInPolygonUtil.isInPolygon(p1, pts);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
}
