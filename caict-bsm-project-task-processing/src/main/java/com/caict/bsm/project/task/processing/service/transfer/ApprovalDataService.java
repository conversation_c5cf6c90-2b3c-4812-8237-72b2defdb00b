package com.caict.bsm.project.task.processing.service.transfer;

import com.caict.bsm.project.domain.business.service.es.RsbtStationMainWebService;
import com.caict.bsm.project.domain.business.service.station.RsbtTrafficService;
import com.caict.bsm.project.system.model.contrust.DBBoolConst;
import com.caict.bsm.project.system.model.dto.business.keystation.StationInterfereDTO;
import com.caict.bsm.project.system.model.dto.business.station.StationScheduleDataDTO;
import com.caict.bsm.project.system.model.dto.business.station.StationSecDTO;
import com.caict.bsm.project.system.model.dto.security.RsbtOrgDTO;
import com.caict.bsm.project.system.model.entity.business.dataconver.AsyncRawBts;
import com.caict.bsm.project.system.model.entity.business.station.*;
import com.caict.bsm.project.system.model.entity.business.stationbak.RsbtStationBak;
import com.caict.bsm.project.system.model.entity.business.transfer.ApprovalSchedule;
import com.caict.bsm.project.system.model.entity.security.RsbtOrg;
import com.caict.bsm.project.system.model.es.RsbtStationMainES;
import com.caict.bsm.project.system.utils.util.CalculationUtil;
import com.caict.bsm.project.system.utils.util.VerificationCode;
import com.caict.bsm.project.task.processing.service.station.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.DecimalFormat;
import java.util.Date;
import java.util.List;

@Service
public class ApprovalDataService {

    @Autowired
    private RsbtStationProService rsbtStationProService;
    @Autowired
    private ApprovalScheduleProService approvalScheduleProService;
    @Autowired
    private AsyncRawBtsProService asyncRawBtsProService;
    @Autowired
    private RsbtEquProService rsbtEquProService;
    @Autowired
    private RsbtFreqProService rsbtFreqProService;
    @Autowired
    private RsbtAntfeedProService rsbtAntfeedProService;
    @Autowired
    private RsbtNetProService rsbtNetProService;
    @Autowired
    private InterfereProService interfereProService;
    @Autowired
    private RsbtTrafficService rsbtTrafficService;

    // 天眼经纬度
    private static final String FAST_LNG = "106.85555555555555";
    private static final String FAST_LAT = "25.65277777777778";

    //批量数据处理
    public void HandleAdded(String appGuid, RsbtOrgDTO rsbtOrg, StationScheduleDataDTO stationScheduleDataDTO, List<RsbtNet> rsbtNetList, List<RsbtStation> rsbtStationList, List<RsbtStationAppendix> rsbtStationAppendixList, List<RsbtStationT> rsbtStationTList, List<RsbtStationBak> rsbtStationBakList,
                            List<RsbtAntfeed> rsbtAntfeedList, List<RsbtAntfeedAppendix> rsbtAntfeedAppendixList, List<RsbtAntfeedT> rsbtAntfeedTList, List<RsbtEqu> rsbtEquList, List<RsbtEquAppendix> rsbtEquAppendixList, List<RsbtEquT> rsbtEquTList,
                            List<RsbtFreq> rsbtFreqList, List<RsbtFreqAppendix> rsbtFreqAppendixList, List<RsbtFreqT> rsbtFreqTList, List<RsbtEaf> rsbtEafList, List<AsyncRawBts> asyncRawBtsListInsert, String appCode,List<RsbtTraffic> rsbtTrafficInsert,List<RsbtStationMainES> rsbtStationMainESList) {
        //构建台站,台站->申请表关系,台站，扇区，频率
        addStation(appGuid, rsbtOrg, stationScheduleDataDTO, rsbtNetList, rsbtStationList, rsbtStationAppendixList, rsbtStationTList, rsbtStationBakList,
                rsbtAntfeedList, rsbtAntfeedAppendixList, rsbtAntfeedTList, rsbtEquList, rsbtEquAppendixList, rsbtEquTList,
                rsbtFreqList, rsbtFreqAppendixList, rsbtFreqTList, rsbtEafList, asyncRawBtsListInsert, appCode,rsbtTrafficInsert,rsbtStationMainESList);
    }

    /**
     * 新增台站、扇区、台站、申请表关系
     */
    protected void addStation(String appGuid, RsbtOrgDTO rsbtOrg, StationScheduleDataDTO stationScheduleDataDTO, List<RsbtNet> rsbtNetList, List<RsbtStation> rsbtStationList, List<RsbtStationAppendix> rsbtStationAppendixList, List<RsbtStationT> rsbtStationTList, List<RsbtStationBak> rsbtStationBakList,
                              List<RsbtAntfeed> rsbtAntfeedList, List<RsbtAntfeedAppendix> rsbtAntfeedAppendixList, List<RsbtAntfeedT> rsbtAntfeedTList, List<RsbtEqu> rsbtEquList, List<RsbtEquAppendix> rsbtEquAppendixList, List<RsbtEquT> rsbtEquTList,
                              List<RsbtFreq> rsbtFreqList, List<RsbtFreqAppendix> rsbtFreqAppendixList, List<RsbtFreqT> rsbtFreqTList, List<RsbtEaf> rsbtEafList, List<AsyncRawBts> asyncRawBtsList, String appCode,List<RsbtTraffic> rsbtTrafficInsert,List<RsbtStationMainES> rsbtStationMainESList) {
        RsbtStation rsbtStation = new RsbtStation();
        RsbtStationAppendix rsbtStationAppendix = new RsbtStationAppendix();
        RsbtStationT rsbtStationT = new RsbtStationT();
        RsbtStationBak rsbtStationBak = new RsbtStationBak();
        RsbtNet rsbtNet = new RsbtNet();
        RsbtStationMainES rsbtStationMainES = new RsbtStationMainES();

        rsbtStationBak.setDataStatus("1");//初始值为正常状态
        rsbtStationBak.setStatDateStart(new Date());
        rsbtStationBak.setBakDate(new Date());
        //构建net
        buildRsbtNet(rsbtNet, rsbtOrg, stationScheduleDataDTO);
        rsbtStation.setNetGuid(rsbtNet.getGuid());
        rsbtNetList.add(rsbtNet);
        //构建station
        buildRsbStation(rsbtStation, rsbtStationAppendix, rsbtStationT, rsbtStationBak, rsbtOrg, appCode, appGuid, stationScheduleDataDTO,rsbtStationMainES);
        //添加专网申请表编号，用于专网和内网申请表关联
        rsbtStation.setAppCode(appCode);

        List<ApprovalSchedule> approvalScheduleListRe = approvalScheduleProService.findAllByAppGuid(appGuid, stationScheduleDataDTO.getBtsId(),
                stationScheduleDataDTO.getExpandStation(),stationScheduleDataDTO.getAttributeStation(),stationScheduleDataDTO.getDataType());
        if (approvalScheduleListRe != null && approvalScheduleListRe.size() != 0) {
            StationInterfereDTO interfereDTO = new StationInterfereDTO();
            interfereDTO.setIsValid("1");//设置一个初始值
            //5g干扰分析
            interfereDTO = interfereProService.interfere(approvalScheduleListRe,stationScheduleDataDTO.getGenNum());
            rsbtStationAppendix.setAnalysisStatus(interfereDTO.getStatus());
            rsbtStationBak.setAnalysisStatus(interfereDTO.getStatus());
            if (!"1".equals(interfereDTO.getStatus())) {
                rsbtStationBak.setDataStatus("2");
            }


            if (interfereDTO.getApprovalScheduleList() != null && interfereDTO.getApprovalScheduleList().size() != 0) {
                for (ApprovalSchedule approvalSchedule : interfereDTO.getApprovalScheduleList()) {
                    AsyncRawBts asyncRawBts = getAsyncRawBts(approvalSchedule, approvalSchedule.getDataType());
                    addOther(approvalSchedule.getDataType(), new StationSecDTO(rsbtStation.getGuid(), asyncRawBts), rsbtAntfeedList, rsbtAntfeedAppendixList, rsbtAntfeedTList,
                            rsbtEquList, rsbtEquAppendixList, rsbtEquTList, rsbtFreqList, rsbtFreqAppendixList, rsbtFreqTList, rsbtEafList,rsbtTrafficInsert);

                    asyncRawBtsList.add(asyncRawBts);
                }
            }
            rsbtStationBak.setIsValid(interfereDTO.getIsValid());
            if (!"1".equals(interfereDTO.getIsValid())) {
                rsbtStationBak.setDataStatus("2");
            }
        }

        rsbtStationList.add(rsbtStation);
        rsbtStationAppendixList.add(rsbtStationAppendix);
        rsbtStationTList.add(rsbtStationT);
        rsbtStationBakList.add(rsbtStationBak);
        rsbtStationMainESList.add(rsbtStationMainES);

    }

    /**
     * 构建Net表
     */
    protected void buildRsbtNet(RsbtNet rsbtNet, RsbtOrg rsbtOrg, StationScheduleDataDTO stationScheduleDataDTO) {
        if ("1".equals(stationScheduleDataDTO.getDataType())) {
            rsbtNet.setGuid(VerificationCode.myUUID());
            rsbtNet.setNetArea("4");  // 4 省内
            rsbtNet.setNetStartDate(new Date());    // 启用日期
            rsbtNet.setNetConfirmDate(new Date()); // 批准日期
        } else if("2".equals(stationScheduleDataDTO.getDataType())){
            RsbtNet rsbtNetUpdate = rsbtNetProService.getRsbtNetByStationGuid(stationScheduleDataDTO.getStationGuid());
            rsbtNet.setGuid(rsbtNetUpdate.getGuid());
            rsbtNet.setNetConfirmDate(new Date()); // 批准日期
        }
        rsbtNet.setOrgGuid(rsbtOrg.getGuid());
        rsbtNet.setOrgCode(rsbtOrg.getOrgCode());
        rsbtNet.setNetTs(stationScheduleDataDTO.getTechType());

    }

    /**
     * 构建新的RsbtStation对象
     *
     * @param stationScheduleDataDTO
     * @return
     */
    protected void buildRsbStation(RsbtStation rsbtStation, RsbtStationAppendix rsbtStationAppendix, RsbtStationT rsbtStationT, RsbtStationBak rsbtStationBak,
                                   RsbtOrgDTO rsbtOrg, String appCode, String appGuid, StationScheduleDataDTO stationScheduleDataDTO,RsbtStationMainES rsbtStationMainES) {
        if ("1".equals(stationScheduleDataDTO.getDataType())) {
            rsbtStation.setGuid(stationScheduleDataDTO.getStationGuid());
            rsbtStation.setStatAppType("C"); // T 无线电台（站）设置申请表
            rsbtStation.setStatStatus("1");  // 1 在用
            rsbtStation.setStatDateStart(new Date());
            rsbtStation.setStatEquSum(0);
            // 036 中华人民共和国
            rsbtStation.setMemo("036");

            rsbtStationAppendix.setIsLicense("0");
            rsbtStationAppendix.setIsApply("0");
            rsbtStationAppendix.setGmtCreate(new Date());
            rsbtStationAppendix.setGmtModified(new Date());
            rsbtStationAppendix.setIsDeleted(DBBoolConst.FALSE);
        } else {
            RsbtStation rsbtStationUpdate = rsbtStationProService.querySattionByBts(stationScheduleDataDTO.getBtsId(), stationScheduleDataDTO.getTechType(), rsbtOrg.getOrgCode());
            rsbtStation.setGuid(rsbtStationUpdate.getGuid());
            rsbtStationAppendix.setGmtModified(new Date());
        }
        rsbtStationBak.setIsSync("0");
        rsbtStation.setOrgCode(rsbtOrg.getOrgCode());
        rsbtStation.setStatName(stationScheduleDataDTO.getBtsName());
        rsbtStation.setStatAddr(stationScheduleDataDTO.getLocation());
        rsbtStation.setStatAreaCode(stationScheduleDataDTO.getRegionCode());
        rsbtStation.setStScene(stationScheduleDataDTO.getStScene());

        if (!("".equals(stationScheduleDataDTO.getAltitude()) || stationScheduleDataDTO.getAltitude() == null || "null".equals(stationScheduleDataDTO.getAltitude()))) {
            rsbtStation.setStatAt(Double.parseDouble(stationScheduleDataDTO.getAltitude())); //海拔
            rsbtStationBak.setStatAt(Double.parseDouble(stationScheduleDataDTO.getAltitude())); //海拔
        } else {
            rsbtStation.setStatAt(0d);
            rsbtStationBak.setStatAt(0d);
        }

        if (null != stationScheduleDataDTO.getLongitude()) {
            rsbtStation.setStatLg(stationScheduleDataDTO.getLongitude());
        } else {
            rsbtStation.setStatLg(0.0);
        }

        if (null != stationScheduleDataDTO.getLatitude()) {
            rsbtStation.setStatLa(stationScheduleDataDTO.getLatitude());
        } else {
            rsbtStation.setStatLa(0.0);
        }

        //构建备份表
        BeanUtils.copyProperties(rsbtStation, rsbtStationBak);
        BeanUtils.copyProperties(rsbtStation, rsbtStationMainES);
        // RSBT_STATION_T
        rsbtStationT.setGuid(rsbtStation.getGuid());  // GUID
        rsbtStationT.setStDTecType(stationScheduleDataDTO.getTechType()); // 技术体制
        rsbtStationT.setStCCode(stationScheduleDataDTO.getBtsId());  // 基站编号
        //写入es
        rsbtStationMainES.setStationId(rsbtStation.getGuid());
        rsbtStationMainES.setNetType(stationScheduleDataDTO.getTechType());
        rsbtStationMainES.setStationName(rsbtStation.getStatName());
        rsbtStationMainES.setStationCode(stationScheduleDataDTO.getBtsId());
        rsbtStationMainES.setLocation(stationScheduleDataDTO.getLocation());
        rsbtStationMainES.setLongitude(String.valueOf(stationScheduleDataDTO.getLongitude()));
        rsbtStationMainES.setLatitude(String.valueOf(stationScheduleDataDTO.getLatitude()));
        rsbtStationMainES.setExpandStation(stationScheduleDataDTO.getExpandStation());
        rsbtStationMainES.setAttributeStation(stationScheduleDataDTO.getAttributeStation());
        rsbtStationMainES.setIsLicense("0");
        rsbtStationMainES.setStationState("0");
        rsbtStationMainES.setOrgType(rsbtOrg.getUserType());
        rsbtStationMainES.setCounty(stationScheduleDataDTO.getCounty());
        if (stationScheduleDataDTO.getStServR() != null) {
            rsbtStationT.setStServR(Double.valueOf(stationScheduleDataDTO.getStServR()));  // 服务半径
        }
        rsbtStationBak.setStCCode(stationScheduleDataDTO.getBtsId());  // 基站编号
        //备份表与基站表关联guid
        rsbtStationBak.setGuid(VerificationCode.myUUID());
        rsbtStationBak.setStationGuid(rsbtStation.getGuid());
//        rsbtStationBak.setStScene(stationScheduleDataDTO.getgetStScene());

        // RsbtStationAppendix
        rsbtStationAppendix.setGuid(rsbtStation.getGuid());
        //信号计算
        rsbtStationAppendix.setGenNum(stationScheduleDataDTO.getGenNum());
        rsbtStationBak.setGenNum(stationScheduleDataDTO.getGenNum());
        rsbtStationAppendix.setUserGuid(stationScheduleDataDTO.getUserGuid());
        rsbtStationBak.setUserGuid(stationScheduleDataDTO.getUserGuid());
        rsbtStationAppendix.setCounty(stationScheduleDataDTO.getCounty());
        rsbtStationAppendix.setExpandStation(stationScheduleDataDTO.getExpandStation());
        rsbtStationAppendix.setAttributeStation(stationScheduleDataDTO.getAttributeStation());
        if (rsbtStationProService.judgeIsArea(stationScheduleDataDTO.getRegionCode(), stationScheduleDataDTO.getLatitude(), stationScheduleDataDTO.getLongitude())){
            rsbtStationAppendix.setIsArea("1");
        }else {
            rsbtStationAppendix.setIsArea("2");
        }
        rsbtStationBak.setCounty(stationScheduleDataDTO.getCounty());
        rsbtStationAppendix.setTechType(stationScheduleDataDTO.getTechType());
        rsbtStationBak.setNetTs(stationScheduleDataDTO.getTechType());
        rsbtStationAppendix.setOrgType(rsbtOrg.getUserType());

        // 惠水 罗甸 平塘 长顺
        if ("522729".equals(stationScheduleDataDTO.getRegionCode()) || "522727".equals(stationScheduleDataDTO.getRegionCode())
                || "522728".equals(stationScheduleDataDTO.getRegionCode()) || "522731".equals(stationScheduleDataDTO.getRegionCode())) {

            String[] strs = CalculationUtil.transform2Mars(stationScheduleDataDTO.getLongitude(), stationScheduleDataDTO.getLatitude());
            // 1-fast范围外  2-fast范围内
            double distance = CalculationUtil.getDistance(FAST_LNG, FAST_LAT, strs[1], strs[0]);
            if (distance <= 30000.0) {
                DecimalFormat df = new DecimalFormat("#.0");
                rsbtStationAppendix.setDistance(df.format(distance));
                rsbtStationAppendix.setFastStatus("2");
                rsbtStationBak.setFastStatus("2");
                rsbtStationBak.setDataStatus("2");
            } else {
                rsbtStationAppendix.setFastStatus("1");
                rsbtStationBak.setFastStatus("1");
            }
        } else {
            rsbtStationAppendix.setFastStatus("1");
            rsbtStationBak.setFastStatus("1");
        }

        rsbtStationBak.setSetYear(stationScheduleDataDTO.getSetYear());
        rsbtStationBak.setOrgType(rsbtOrg.getUserType());
        rsbtStationBak.setDataType(stationScheduleDataDTO.getDataType());
        rsbtStationBak.setAppCode(appCode);
        rsbtStationBak.setAppGuid(appGuid);
        //增加校验状态
        rsbtStationBak.setIsValid(stationScheduleDataDTO.getIsValid());
    }

    /**
     * 添加一个扇区、频率、天线
     *
     * @param stationSecDto
     */
    protected void addOther(String dataType, StationSecDTO stationSecDto, List<RsbtAntfeed> rsbtAntfeedList, List<RsbtAntfeedAppendix> rsbtAntfeedAppendixList, List<RsbtAntfeedT> rsbtAntfeedTList,
                            List<RsbtEqu> rsbtEquList, List<RsbtEquAppendix> rsbtEquAppendixList, List<RsbtEquT> rsbtEquTList, List<RsbtFreq> rsbtFreqList,
                            List<RsbtFreqAppendix> rsbtFreqAppendixList, List<RsbtFreqT> rsbtFreqTList, List<RsbtEaf> rsbtEafList,List<RsbtTraffic> rsbtTrafficInsert) {

        String guid = VerificationCode.myUUID();
        RsbtEaf rsbtEaf = new RsbtEaf();
        rsbtEaf.setGuid(guid);
        addEqu(dataType, stationSecDto, rsbtEquList, rsbtEquAppendixList, rsbtEquTList, rsbtEaf);

        addFreq(dataType, stationSecDto, rsbtFreqList, rsbtFreqAppendixList, rsbtFreqTList, rsbtEaf);

        addAntfeed(dataType, stationSecDto, rsbtAntfeedList, rsbtAntfeedAppendixList, rsbtAntfeedTList, rsbtEaf);
        rsbtEaf.setStationGuid(stationSecDto.getStationGuid());

        addTraffic(dataType, new StationSecDTO(stationSecDto.getStationGuid(),stationSecDto.getAsyncRawBts()), rsbtTrafficInsert);
        if ("1".equals(dataType)) {
            rsbtEafList.add(rsbtEaf);
        }

    }

    //添加天线数据
    protected void addAntfeed(String dataType, StationSecDTO stationSecDto, List<RsbtAntfeed> rsbtAntfeedList,
                              List<RsbtAntfeedAppendix> rsbtAntfeedAppendixList, List<RsbtAntfeedT> rsbtAntfeedTList, RsbtEaf rsbtEaf) {
        String guid = VerificationCode.myUUID();
        if (!"1".equals(dataType)) {
            RsbtAntfeed rsbtAntfeedUpdate = rsbtAntfeedProService.findOneByStationCell(stationSecDto.getStationGuid(), stationSecDto.getAsyncRawBts().getCellId());
            guid = rsbtAntfeedUpdate.getGuid();
        }
        RsbtAntfeed rsbtAntfeed = new RsbtAntfeed();
        RsbtAntfeedAppendix rsbtAntfeedAppendix = new RsbtAntfeedAppendix();
        RsbtAntfeedT rsbtAntfeedT = new RsbtAntfeedT();
        buidAntFeed(guid, stationSecDto.getStationGuid(), stationSecDto.getAsyncRawBts(), rsbtAntfeed, rsbtAntfeedAppendix, rsbtAntfeedT);

        rsbtAntfeedList.add(rsbtAntfeed);
        rsbtAntfeedAppendixList.add(rsbtAntfeedAppendix);
        rsbtAntfeedTList.add(rsbtAntfeedT);
        rsbtEaf.setAntGuid(guid);
    }

    //构造天线数据

    /**
     * @param guid        RsbtAntfeed
     * @param stationGuid 台站GUID
     * @param asyncRawBts 基本信息
     * @return
     * @throws RuntimeException
     */
    protected void buidAntFeed(String guid, String stationGuid, AsyncRawBts asyncRawBts, RsbtAntfeed rsbtAntfeed,
                               RsbtAntfeedAppendix rsbtAntfeedAppendix, RsbtAntfeedT rsbtAntfeedT) throws RuntimeException {

        rsbtAntfeed.setGuid(guid);
        rsbtAntfeed.setStationGuid(stationGuid);
        rsbtAntfeed.setAntWorkType("");
        //天线据地高度
        if (!("".equals(asyncRawBts.getHeight()) || asyncRawBts.getHeight() == null)) {
            rsbtAntfeed.setAntHight(Double.parseDouble(asyncRawBts.getHeight()));
        } else {
            rsbtAntfeed.setAntHight(0d);
        }
        //天线增益
        if (!("".equals(asyncRawBts.getAntennaGain()) || asyncRawBts.getAntennaGain() == null || "null".equals(asyncRawBts.getAntennaGain()))) {
            rsbtAntfeed.setAntGain(Double.parseDouble(asyncRawBts.getAntennaGain()));
        } else {
            rsbtAntfeed.setAntGain(0d);
        }

        //天线方位角
        if (!("".equals(asyncRawBts.getAntennaAzimuth()) || asyncRawBts.getAntennaAzimuth() == null || "null".equals(asyncRawBts.getAntennaAzimuth()))) {
            rsbtAntfeed.setAntAngle(Double.parseDouble(asyncRawBts.getAntennaAzimuth()));
        }
        //馈线系统总损耗
        if (!("".equals(asyncRawBts.getFeederLoss()) || asyncRawBts.getFeederLoss() == null || "null".equals(asyncRawBts.getFeederLoss()))) {
            rsbtAntfeed.setFeedLose(Double.parseDouble(asyncRawBts.getFeederLoss()));
        }

        //天线生产厂家
        rsbtAntfeed.setAntMenu(asyncRawBts.getAntennaFactory());
        //天线类型
        rsbtAntfeed.setAntType(asyncRawBts.getAntennaModel());
        //极化方式
        rsbtAntfeed.setAntPole(asyncRawBts.getPolarizationMode());
        rsbtAntfeed.setAntRpole(asyncRawBts.getPolarizationMode());
        rsbtAntfeed.setAntEpole(asyncRawBts.getPolarizationMode());

        rsbtAntfeedAppendix.setGuid(guid);
        rsbtAntfeedAppendix.setGmtCreate(new Date());
        rsbtAntfeedAppendix.setGmtModified(new Date());
        rsbtAntfeedAppendix.setIsDeleted(DBBoolConst.FALSE);

        rsbtAntfeedT.setGuid(guid);
        rsbtAntfeedT.setAtCcode(asyncRawBts.getCellId());
        //收倾角
        if (!"".equals(asyncRawBts.getAtRang()) && asyncRawBts.getAtRang() != null) {
            rsbtAntfeedT.setAtRang(Double.valueOf(asyncRawBts.getAtRang()));
        }
        //发倾角
        if (!"".equals(asyncRawBts.getAtEang()) && asyncRawBts.getAtEang() != null) {
            rsbtAntfeedT.setAtEang(Double.valueOf(asyncRawBts.getAtEang()));
        }
    }

    //添加参数数据
    protected void addFreq(String dataType, StationSecDTO stationSecDto, List<RsbtFreq> rsbtFreqList, List<RsbtFreqAppendix> rsbtFreqAppendixList, List<RsbtFreqT> rsbtFreqTList, RsbtEaf rsbtEaf) {
        String guid = VerificationCode.myUUID();
        if (!"1".equals(dataType)) {
            RsbtFreq rsbtFreqUpdate = rsbtFreqProService.findOneByStationCell(stationSecDto.getStationGuid(), stationSecDto.getAsyncRawBts().getCellId());
            guid = rsbtFreqUpdate.getGuid();
        }
        RsbtFreq rsbtFreq = new RsbtFreq();
        RsbtFreqAppendix rsbtFreqAppendix = new RsbtFreqAppendix();
        RsbtFreqT rsbtFreqT = new RsbtFreqT();
        buildFreq(guid, stationSecDto.getStationGuid(), stationSecDto.getAsyncRawBts(), rsbtFreq, rsbtFreqAppendix, rsbtFreqT);
        rsbtFreqAppendix.setIsValid(String.valueOf(stationSecDto.getAsyncRawBts().getIsValid()));
        rsbtFreqList.add(rsbtFreq);
        rsbtFreqAppendixList.add(rsbtFreqAppendix);
        rsbtFreqTList.add(rsbtFreqT);
        rsbtEaf.setFreqGuid(guid);
    }

    //构造参数
    protected void buildFreq(String freqGuid, String stationGuid, AsyncRawBts asyncRawBts, RsbtFreq rsbtFreq, RsbtFreqAppendix rsbtFreqAppendix, RsbtFreqT rsbtFreqT) {

        rsbtFreq.setGuid(freqGuid);
        rsbtFreq.setStationGuid(stationGuid);
        //发射频率下线
        rsbtFreq.setFreqEfb(Double.parseDouble((asyncRawBts.getSendStartFreq() == null || "".equals(asyncRawBts.getSendStartFreq())) ? "0" : asyncRawBts.getSendStartFreq()));
        //发射频率上限
        rsbtFreq.setFreqEfe(Double.parseDouble((asyncRawBts.getSendEndFreq() == null || "".equals(asyncRawBts.getSendEndFreq())) ? "0" : asyncRawBts.getSendEndFreq()));
        //接受频率上限
        rsbtFreq.setFreqRfb(Double.parseDouble((asyncRawBts.getAccStartFreq() == null || "".equals(asyncRawBts.getAccStartFreq())) ? "0" : asyncRawBts.getAccStartFreq()));
        //接受频率下线
        rsbtFreq.setFreqRfe(Double.parseDouble((asyncRawBts.getAccEndFreq() == null || "".equals(asyncRawBts.getAccEndFreq())) ? "0" : asyncRawBts.getAccEndFreq()));
        //频率频点
        rsbtFreq.setFreqType("");
        //发射必要带宽
        rsbtFreq.setFreqEBand(0.0);
        //接收必要带宽
        rsbtFreq.setFreqRBand(0.0);

        rsbtFreqAppendix.setGuid(freqGuid);
        rsbtFreqAppendix.setGmtCreate(new Date());
        rsbtFreqAppendix.setGmtModified(new Date());
        rsbtFreqAppendix.setIsDeleted(DBBoolConst.FALSE);
        rsbtFreqAppendix.setSectionName(asyncRawBts.getCellName());
        if (asyncRawBts.getAnalysisStatus() != null) {
            rsbtFreqAppendix.setAnalysisStatus(asyncRawBts.getAnalysisStatus());
        }

        rsbtFreqT.setGuid(freqGuid);
        rsbtFreqT.setFtFreqCcode(asyncRawBts.getCellId());
    }

    //添加用户数及流量
    protected void addTraffic(String dataType, StationSecDTO stationSecDto, List<RsbtTraffic> rsbtTrafficList) {
        String guid = VerificationCode.myUUID();
        if (!"1".equals(dataType)) {
            RsbtTraffic rsbtTraffic = rsbtTrafficService.findOneByStationCell(stationSecDto.getStationGuid(), stationSecDto.getAsyncRawBts().getCellId());
            if (rsbtTraffic != null && !"".equals(rsbtTraffic.getTrafficGuid())) {
                guid = rsbtTraffic.getTrafficGuid();
            }
        }
        RsbtTraffic rsbtTraffic = buildTraffic(guid, stationSecDto.getStationGuid(), stationSecDto.getAsyncRawBts());
        rsbtTrafficList.add(rsbtTraffic);
    }

    //构造参数
    protected RsbtTraffic buildTraffic(String guid, String stationGuid, AsyncRawBts asyncRawBts) {
        RsbtTraffic rsbtTraffic = new RsbtTraffic();
        rsbtTraffic.setTrafficGuid(guid);
        rsbtTraffic.setStationGuid(stationGuid);
        rsbtTraffic.setSectionCode(asyncRawBts.getCellId());
        rsbtTraffic.setFreqEfbC(Double.parseDouble(asyncRawBts.getSendEndFreq()));
        rsbtTraffic.setFreqEfeC(Double.parseDouble(asyncRawBts.getSendStartFreq()));
        rsbtTraffic.setNetTs(asyncRawBts.getTechType());
        rsbtTraffic.setTrfDate(asyncRawBts.getTrfDate());
        rsbtTraffic.setTrfUser(asyncRawBts.getTrfUser() != null ? asyncRawBts.getTrfUser() : 0d);
        rsbtTraffic.setTrfData(asyncRawBts.getTrfData() != null ? asyncRawBts.getTrfData() : 0d);
        return rsbtTraffic;
    }

    //添加设备数据
    protected void addEqu(String dataType, StationSecDTO stationSecDTO, List<RsbtEqu> rsbtEquList, List<RsbtEquAppendix> rsbtEquAppendixList, List<RsbtEquT> rsbtEquTList, RsbtEaf rsbtEaf) {
        String guid = VerificationCode.myUUID();
        if (!"1".equals(dataType)) {
            RsbtEqu rsbtEquUpdate = rsbtEquProService.findOneByStationSection(stationSecDTO.getStationGuid(), stationSecDTO.getAsyncRawBts().getCellId());
            guid = rsbtEquUpdate.getGuid();
        }
        RsbtEqu rsbtEqu = new RsbtEqu();
        RsbtEquAppendix rsbtEquAppendix = new RsbtEquAppendix();
        RsbtEquT rsbtEquT = new RsbtEquT();
        buidRsbtEqu(guid, stationSecDTO.getStationGuid(), stationSecDTO.getAsyncRawBts(), rsbtEqu, rsbtEquAppendix, rsbtEquT);

        rsbtEquList.add(rsbtEqu);
        rsbtEquAppendixList.add(rsbtEquAppendix);
        rsbtEquTList.add(rsbtEquT);
        rsbtEaf.setEquGuid(guid);
    }


    //构造设备数据
    protected void buidRsbtEqu(String equGuid, String stationGuid, AsyncRawBts asyncRawBts, RsbtEqu rsbtEqu, RsbtEquAppendix rsbtEquAppendix, RsbtEquT rsbtEquT) {
        rsbtEqu.setGuid(equGuid);
        rsbtEqu.setStationGuid(stationGuid);
        //设备型号
        rsbtEqu.setEquModel(asyncRawBts.getDeviceModel());
        //型号核准代码
        if (asyncRawBts.getModelCode() != null) {
            int len = asyncRawBts.getModelCode().getBytes().length;  // 长度
            // 字节长度 > 40 ? 把中文去掉
            String auth = len > 40 ? asyncRawBts.getModelCode().replaceAll("[^\\x00-\\xff]", "") : asyncRawBts.getModelCode();

            rsbtEqu.setEquAuth(auth.length() > 40 ? auth.substring(auth.length() - 40) : auth);

        }
        //设备生产厂家
        rsbtEqu.setEquMenu(asyncRawBts.getVendorName());

        rsbtEquAppendix.setGuid(equGuid);
        rsbtEquAppendix.setGmtCreate(new Date());
        rsbtEquAppendix.setGmtModified(new Date());
        rsbtEquAppendix.setIsDeleted(DBBoolConst.FALSE);

        rsbtEquT.setGuid(equGuid);
        //上行发射功率
        if (null != asyncRawBts.getMaxEmissivePower()) {
            rsbtEquT.setEtEquUpow(Double.parseDouble(asyncRawBts.getMaxEmissivePower()));
        } else {
            rsbtEquT.setEtEquUpow(0.0);
        }
        //下行发射功率
        rsbtEquT.setEtEquDpow(0.0);

        rsbtEquT.setEtEquCcode(asyncRawBts.getCellId());

    }

    protected AsyncRawBts getAsyncRawBts(ApprovalSchedule approvalSchedule, String dataType) {
        AsyncRawBts asyncRawBts = new AsyncRawBts();
        BeanUtils.copyProperties(approvalSchedule, asyncRawBts);
        if ("1".equals(dataType)) {
            asyncRawBts.setGuid(VerificationCode.myUUID());
        } else {
            AsyncRawBts asyncRawBtsUpdate = asyncRawBtsProService.getOneByCellId(asyncRawBts.getBtsId(),asyncRawBts.getCellId(), asyncRawBts.getTechType());
            // 基站变更里边的扇区增加，需要给一个新的ID,否则把旧ID赋值给它
            if (!"1".equals(approvalSchedule.getDataType())) {
                asyncRawBts.setGuid(asyncRawBtsUpdate.getGuid());
            }
        }

        return asyncRawBts;
    }
}
