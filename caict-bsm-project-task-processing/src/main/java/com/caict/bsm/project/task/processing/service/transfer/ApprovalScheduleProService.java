package com.caict.bsm.project.task.processing.service.transfer;

import com.caict.bsm.project.domain.business.service.transfer.ApprovalScheduleService;
import com.caict.bsm.project.system.model.entity.business.transfer.ApprovalRawBts;
import com.caict.bsm.project.system.model.entity.business.transfer.ApprovalSchedule;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by dengsy on 2020-04-22.
 */
@Service
public class ApprovalScheduleProService {

    @Autowired
    private ApprovalScheduleService approvalScheduleService;

    /**
     * 批量删除
     * */
    public int deleteBatch(List<Object> guidList){
        return approvalScheduleService.deleteBatch(guidList);
    }

    /**
     * 根据appGuid查询ApprovalRawBts
     * */
    public List<ApprovalRawBts> findAllByAppGuid(String appGuid){
        return approvalScheduleService.findAllByAppGuid(appGuid);
    }

    /**
     * 根据appGuid查询全部
     * */
    public List<ApprovalSchedule> findAllByAppGuid(String appGuid, String btsId,String expandStation,String attributeStation,String dataType){
        ApprovalSchedule approvalSchedule = new ApprovalSchedule();
        approvalSchedule.setAppGuid(appGuid);
        approvalSchedule.setBtsId(btsId);
        approvalSchedule.setExpandStation(expandStation);
        approvalSchedule.setAttributeStation(attributeStation);
        approvalSchedule.setBtsDataType(dataType);
        return approvalScheduleService.findAllByWhere(approvalSchedule);
    }

    /**
     * 根据btsid查询校验状态
     */
    public String selectIsValidByBtsId(String btsId,String userGuid,String techType){
        return approvalScheduleService.selectIsValidByBtsId(btsId,userGuid,techType);
    }
}
