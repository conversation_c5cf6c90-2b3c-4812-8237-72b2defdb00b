package com.caict.bsm.project.task.processing.service.transfer;


import com.alibaba.fastjson.JSONObject;
import com.caict.bsm.project.datacenter.realtime.service.RabbitService;
import com.caict.bsm.project.domain.business.service.transfer.*;
import com.caict.bsm.project.domain.security.service.RsbtOrgService;
import com.caict.bsm.project.system.model.contrust.rabbit.RabbitMessageConst;
import com.caict.bsm.project.system.model.contrust.transfer.ApprovalTransportJobStateConst;
import com.caict.bsm.project.system.model.contrust.transfer.LogTransportJobTypeConst;
import com.caict.bsm.project.system.model.contrust.transfer.TransportJobStateConst;
import com.caict.bsm.project.system.model.dto.business.station.StationScheduleDataDTO;
import com.caict.bsm.project.system.model.dto.business.taskdata.ProcessingDataDTO;
import com.caict.bsm.project.system.model.dto.business.transfer.TransportJobBranchDTO;
import com.caict.bsm.project.system.model.dto.security.RsbtOrgDTO;
import com.caict.bsm.project.system.model.entity.business.dataconver.AsyncRawBts;
import com.caict.bsm.project.system.model.entity.business.station.*;
import com.caict.bsm.project.system.model.entity.business.stationbak.RsbtStationBak;
import com.caict.bsm.project.system.model.entity.business.transfer.ApprovalSchedule;
import com.caict.bsm.project.system.model.entity.business.transfer.ApprovalTransportJob;
import com.caict.bsm.project.system.model.entity.business.transfer.LogTransportJob;
import com.caict.bsm.project.system.model.entity.message.Message;
import com.caict.bsm.project.system.model.entity.message.rabbit.RabbitMessage;
import com.caict.bsm.project.system.model.es.RsbtStationMainES;
import com.caict.bsm.project.system.utils.repository.MyRestRepository;
import com.caict.bsm.project.system.utils.service.RedisService;
import com.caict.bsm.project.system.utils.util.DateUtils;
import com.caict.bsm.project.system.utils.util.VerificationCode;
import com.caict.bsm.project.task.processing.service.security.RsbtOrgProService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class ApprovalTransportJobProService {

    @Autowired
    private ApprovalTransportJobService approvalTransportJobService;

    @Autowired
    private RsbtOrgProService rsbtOrgProService;

    @Autowired
    private ApprovalDataService approvalDataService;
    @Autowired
    private LogTransportJobProService logTransportJobProService;
    @Autowired
    private TransportScheduleProService transportScheduleProService;
    @Autowired
    private TransportJobProService transportJobProService;
    @Autowired
    private TransportJobBranchProService transportJobBranchProService;
    @Autowired
    private ApprovalScheduleProService approvalScheduleProService;

    @Autowired
    private MyRestRepository<Message> myRestRepository;
    @Autowired
    private RedisService redisService;
    @Autowired
    private RabbitService rabbitService;

    @Value("${caict.message.url}")
    private String messageUrl;
    @Value("${caict.myRabbitKey.taskData}")
    private String taskData;


    //新增处理
    public void approve(String redisKey,String appGuid,String dataType){
        //获取redis数据
        List<StationScheduleDataDTO> stationScheduleDataDTOList = JSONObject.parseArray(redisService.rpop(redisKey), StationScheduleDataDTO.class);
        if (stationScheduleDataDTOList != null){
            ApprovalTransportJob approvalTransportJob = approvalTransportJobService.findById(appGuid);
            TransportJobBranchDTO transportJobBranchDTO = transportJobBranchProService.findOneByJobUserTypeGen(approvalTransportJob.getJobGuid(),approvalTransportJob.getUserGuid(),approvalTransportJob.getDataType(),approvalTransportJob.getGenNum(),approvalTransportJob.getRegionCode(),approvalTransportJob.getTechType());
            String appCode = transportJobBranchDTO.getAppCode();

            //批量添加基站
            List<RsbtStation> rsbtStationListInsert = Collections.synchronizedList(new ArrayList<>());
            //批量添加基站状态
            List<RsbtStationAppendix> rsbtStationAppendixListInsert = Collections.synchronizedList(new ArrayList<>());
            //批量添加基站冗余表
            List<RsbtStationT> rsbtStationTListInsert = Collections.synchronizedList(new ArrayList<>());
            //批量添加基站历史备份表
            List<RsbtStationBak> rsbtStationBakListInsert = Collections.synchronizedList(new ArrayList<>());
            //批量添加扇区
            List<RsbtTraffic> rsbtTrafficInsert = Collections.synchronizedList(new ArrayList<>());
            //批量添加天线
            List<RsbtAntfeed> rsbtAntfeedListInsert = Collections.synchronizedList(new ArrayList<>());
            //批量添加天线状态
            List<RsbtAntfeedAppendix> rsbtAntfeedAppendixListInsert = Collections.synchronizedList(new ArrayList<>());
            List<RsbtAntfeedT> rsbtAntfeedTListInsert = Collections.synchronizedList(new ArrayList<>());

            //批量添加设备
            List<RsbtEqu> rsbtEquListInsert = Collections.synchronizedList(new ArrayList<>());
            //批量添加设备状态
            List<RsbtEquAppendix> rsbtEquAppendixListInsert = Collections.synchronizedList(new ArrayList<>());
            List<RsbtEquT> rsbtEquTLis = Collections.synchronizedList(new ArrayList<>());

            //批量添加参数
            List<RsbtFreq> rsbtFreqListInsert = Collections.synchronizedList(new ArrayList<>());
            //批量添加参数状态
            List<RsbtFreqAppendix> rsbtFreqAppendixListInsert = Collections.synchronizedList(new ArrayList<>());
            List<RsbtFreqT> rsbtFreqTList = Collections.synchronizedList(new ArrayList<>());

            List<RsbtEaf> rsbtEafListInsert = Collections.synchronizedList(new ArrayList<>());
            //Net 表
            List<RsbtNet> rsbtNetListInsert = Collections.synchronizedList(new ArrayList<>());

            //批量添加总表
            List<AsyncRawBts> asyncRawBtsListInsert = Collections.synchronizedList(new ArrayList<>());

            //es list
            List<RsbtStationMainES> rsbtStationMainESList = Collections.synchronizedList(new ArrayList<>());

            //查询营运商对应的公司
            RsbtOrgDTO rsbtOrg = rsbtOrgProService.findRsbtOrgByUserId(approvalTransportJob.getUserGuid());

            if (rsbtOrg != null){
                stationScheduleDataDTOList.parallelStream().forEach(stationScheduleDataDTO -> approvalDataService.HandleAdded(approvalTransportJob.getGuid(),rsbtOrg,stationScheduleDataDTO,rsbtNetListInsert,rsbtStationListInsert,rsbtStationAppendixListInsert,rsbtStationTListInsert,rsbtStationBakListInsert,rsbtAntfeedListInsert,rsbtAntfeedAppendixListInsert,rsbtAntfeedTListInsert,
                        rsbtEquListInsert,rsbtEquAppendixListInsert,rsbtEquTLis,rsbtFreqListInsert,rsbtFreqAppendixListInsert,rsbtFreqTList,rsbtEafListInsert,asyncRawBtsListInsert,appCode,rsbtTrafficInsert,rsbtStationMainESList));
            }else {
                LogTransportJob logTransportJob = logTransportJobProService.setLogTransportJob(appGuid, LogTransportJobTypeConst.ERROR, "审核失败",appGuid+"未检测运营商所属的组织机构","");
                logTransportJobProService.save(logTransportJob);
            }

            //将持久化数据存入redis，又持久化程序处理
            ProcessingDataDTO processingDataDTO = new ProcessingDataDTO(approvalTransportJob.getUserGuid(),approvalTransportJob.getJobGuid(),appGuid,dataType,rsbtNetListInsert,
                    rsbtStationListInsert,rsbtStationAppendixListInsert,rsbtStationTListInsert,rsbtStationBakListInsert,rsbtAntfeedListInsert,rsbtAntfeedAppendixListInsert,rsbtAntfeedTListInsert,
                    rsbtEquListInsert,rsbtEquAppendixListInsert,rsbtEquTLis,rsbtFreqListInsert,rsbtFreqAppendixListInsert,rsbtFreqTList,rsbtEafListInsert,
                    asyncRawBtsListInsert,rsbtTrafficInsert,rsbtStationMainESList);

            //数据处理
            approveData(appGuid,approvalTransportJob.getGuid(),processingDataDTO);
        }else {
            LogTransportJob logTransportJob = logTransportJobProService.setLogTransportJob(appGuid, LogTransportJobTypeConst.ERROR, "审核失败",appGuid+"未检测到审核数据","");
            logTransportJobProService.save(logTransportJob);
        }
    }

    //数据处理
    protected void approveData(String appGuid,String jobGuid,ProcessingDataDTO processingDataDTO){
        if(processingDataDTO == null){
            //审核失败
            LogTransportJob logTransportJob = logTransportJobProService.setLogTransportJob(appGuid, LogTransportJobTypeConst.ERROR, "审核失败",appGuid+"审核记录处理失败","");
            logTransportJobProService.save(logTransportJob);
            //判断是否还有待办任务
            if (transportScheduleProService.selectCountByJobIdDataType(jobGuid,null,null) == 0){
                //修改任务状态为完结
                transportJobProService.updateIsCompareByGuid(TransportJobStateConst.COMPLETE,jobGuid);
                //删除待办
                transportScheduleProService.deleteByJobId(jobGuid);
                //删除消息通知
                myRestRepository.getForStringNpList(messageUrl+"deleteByFromGuid/"+jobGuid);
            }
            //审核任务改为异常
            approvalTransportJobService.updateIsCompareByGuid(ApprovalTransportJobStateConst.CHECKIN_FAIL,appGuid);
            ApprovalTransportJob approvalTransportJob = approvalTransportJobService.findById(appGuid);
            if (approvalTransportJob!=null){
                transportJobBranchProService.updateByComfirm(approvalTransportJob.getJobGuid(),approvalTransportJob.getDataType(),approvalTransportJob.getGenNum(),ApprovalTransportJobStateConst.CHECKIN_FAIL);
            }
        }else {
            //审核任务改为完结
            if(approvalTransportJobService.updateIsCompareByGuid(ApprovalTransportJobStateConst.COMPLETE,appGuid) > 0){
                ApprovalTransportJob approvalTransportJob = approvalTransportJobService.findById(appGuid);
                if (approvalTransportJob!=null){
                    transportJobBranchProService.updateByComfirm(approvalTransportJob.getJobGuid(),approvalTransportJob.getDataType(),approvalTransportJob.getGenNum(),ApprovalTransportJobStateConst.COMPLETE);

                }
                String key = "processing.data."+appGuid;
                redisService.lpush(key, JSONObject.toJSON(processingDataDTO));
                //发送rabbit任务启动入库操作
                sendRabbit(taskData,"审核入库",key,"审核任务数据入库", RabbitMessageConst.JOB_APPROVVAL_DATA,jobGuid);
            }
        }
    }

    //发送rabbit
    protected void sendRabbit(String key,String title,String content,String remark,String type,String from){
        RabbitMessage rabbitMessage = new RabbitMessage(VerificationCode.idGet("rabbit",4),title,content, DateUtils.getDateTime(),from,remark,type);
        rabbitService.sendSimple(key,JSONObject.toJSONString(rabbitMessage));
    }

}
