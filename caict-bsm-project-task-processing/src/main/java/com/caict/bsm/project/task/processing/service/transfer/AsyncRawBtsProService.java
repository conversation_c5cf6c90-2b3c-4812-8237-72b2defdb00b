package com.caict.bsm.project.task.processing.service.transfer;

import com.caict.bsm.project.domain.business.service.dataconver.AsyncRawBtsService;
import com.caict.bsm.project.system.model.entity.business.dataconver.AsyncRawBts;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/4/24
 */
@Service
public class AsyncRawBtsProService {

    @Autowired
    private AsyncRawBtsService asyncRawBtsService;

    /**
     * 根据扇区id查询一条记录
     * */
    public AsyncRawBts getOneByCellId(String btsId,String cellId, String techType){
        return asyncRawBtsService.getOneByCellId(btsId,cellId,techType);
    }

    /**
     * 根据基站编号和扇区号拆线呢
     * */
    public AsyncRawBts selectOneByBtsIdCellId(String btsId,String cellId,String techType){
        return asyncRawBtsService.selectOneByBtsIdCellId(btsId,cellId,techType);
    }

    /**
     * 查询上传数据中没有的数据
     * */
    public List<AsyncRawBts> findAllNotTransport(String jobGuid, String userId){
        return asyncRawBtsService.findAllNotTransport(jobGuid,userId);
    }

    /**
     * 获取执照到期时间
     */
    public Date getExpireDate(String statCode){
        //获取此基站下频率最近的到期时间
        return asyncRawBtsService.getExpireDate(statCode);
    }
}
