package com.caict.bsm.project.task.processing.service.transfer;

import com.caict.bsm.project.domain.business.service.transfer.TransportJobBranchService;
import com.caict.bsm.project.system.model.dto.business.transfer.TransportJobBranchDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2020/6/15
 */
@Service
public class TransportJobBranchProService {

    @Autowired
    private TransportJobBranchService transportJobBranchService;

    public int updateByComfirm(String jobGuid,String dataType,String genNum,String isCompare){
        return transportJobBranchService.updateByComfirm(jobGuid,dataType,genNum,isCompare);
    }

    /**
     * 根据任务、运营商、类型、制式查询
     * */
    public TransportJobBranchDTO findOneByJobUserTypeGen(String jobGuid, String userGuid, String dataType, String genNum,String regionCode,String techType){
        return transportJobBranchService.findOneByJobUserTypeGen(jobGuid,userGuid,dataType,genNum,regionCode,techType);
    }
}
