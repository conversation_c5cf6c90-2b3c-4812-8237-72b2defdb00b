package com.caict.bsm.project.task.processing.service.transfer;

import com.caict.bsm.project.domain.business.service.transfer.TransportRawBtsService;
import com.caict.bsm.project.system.model.dto.business.transfer.TransportRawBtsDTO;
import com.caict.bsm.project.system.model.entity.business.transfer.TransportRawBts;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by dengsy on 2020-04-22.
 */
@Service
public class TransportRawBtsProService {

    @Autowired
    private TransportRawBtsService transportRawBtsService;

    /**
     * 根据fileId查询总数
     * */
    public int selectCountByFileId(String fileId){
        return transportRawBtsService.selectCountByFileId(fileId);
    }

    /**
     * 批量添加
     * */
    public int insertBatch(List<TransportRawBts> transportRawBtsList){
        return transportRawBtsService.insertBatch(transportRawBtsList);
    }

    /**
     * 批量修改
     * */
    public int updateBatch(List<TransportRawBts> transportRawBtsList){
        return transportRawBtsService.updateBatch(transportRawBtsList);
    }

    /**
     * 查询基站基础数据不一致的基站识别号
     * */
    public List<TransportRawBtsDTO> findAllBtsId(String jobGuid,String fileGuid){
        return transportRawBtsService.findAllBtsId(jobGuid,fileGuid);
    }

    /**
     * 根据基站批量修改状态
     * */
    public int updateIsValid(String jobGuid,String fileGuid,long isValid){
        return transportRawBtsService.updateIsValid(jobGuid,fileGuid,isValid);
    }

    /**
     * 批量处理直放站，修改bts_id为cell_id
     * */
    public int updateBtsFromCell(String jobGuid,String fileGuid){
        return transportRawBtsService.updateBtsFromCell(jobGuid,fileGuid);
    }

    /**
     * 识别新增（方法待优化）
     * */
//    public int updateBtsDataTypeAdd(String jobGuid){
//        return transportRawBtsService.updateBtsDataTypeAdd(jobGuid);
//    }

    /**
     * 识别变更和不变（方法待优化）
     * */
//    public int updateBtsDataTypeUpdate(String jobGuid){
//        return transportRawBtsService.updateBtsDataTypeUpdate(jobGuid);
//    }
}
